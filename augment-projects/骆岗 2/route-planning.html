<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路径规划 - 骆岗公园</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80') center/cover;
            background-attachment: fixed;
            min-height: 100vh;
            color: #2c3e50;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 600"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="%23e0e0e0" stroke-width="0.5" opacity="0.3"/></pattern></defs><rect width="400" height="600" fill="rgba(248,255,254,0.9)"/><rect width="400" height="600" fill="url(%23grid)"/><path d="M50 100 Q150 80 250 100 T350 100" stroke="%233498db" stroke-width="2" fill="none" opacity="0.6"/><circle cx="80" cy="100" r="6" fill="%23e74c3c" opacity="0.7"/><circle cx="200" cy="100" r="6" fill="%23f39c12" opacity="0.7"/><circle cx="320" cy="100" r="6" fill="%2327ae60" opacity="0.7"/><rect x="40" y="200" width="60" height="40" rx="6" fill="%23ecf0f1" stroke="%233498db" stroke-width="1" opacity="0.8"/><text x="70" y="218" text-anchor="middle" font-size="8" fill="%232c3e50">信标台</text><rect x="150" y="180" width="60" height="40" rx="6" fill="%23ecf0f1" stroke="%232ecc71" stroke-width="1" opacity="0.8"/><text x="180" y="198" text-anchor="middle" font-size="8" fill="%232c3e50">科普馆</text><rect x="260" y="220" width="60" height="40" rx="6" fill="%23ecf0f1" stroke="%23f39c12" stroke-width="1" opacity="0.8"/><text x="290" y="238" text-anchor="middle" font-size="8" fill="%232c3e50">大草坪</text><path d="M100 350 L300 350" stroke="%23666" stroke-width="4" opacity="0.4"/><path d="M100 350 L300 350" stroke="%23fff" stroke-width="2" stroke-dasharray="10,5" opacity="0.6"/><circle cx="120" cy="350" r="4" fill="%23e74c3c" opacity="0.8"/><circle cx="180" cy="350" r="4" fill="%23e74c3c" opacity="0.8"/><circle cx="240" cy="350" r="4" fill="%23e74c3c" opacity="0.8"/><circle cx="280" cy="350" r="4" fill="%23e74c3c" opacity="0.8"/></svg>') center/cover;
            z-index: -1;
            pointer-events: none;
        }

        .app-container {
            max-width: 414px;
            margin: 0 auto;
            background: rgba(255,255,255,0.95);
            min-height: 100vh;
            position: relative;
            backdrop-filter: blur(10px);
        }

        .header {
            background: linear-gradient(135deg, #3498db, #2ecc71);
            color: white;
            padding: 20px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }

        .header-title {
            font-size: 20px;
            font-weight: bold;
        }

        .content {
            padding: 20px 16px;
        }

        .section {
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 12px;
            color: #2c3e50;
        }

        .route-types {
            display: flex;
            gap: 6px;
            margin-bottom: 12px;
            overflow-x: auto;
            padding-bottom: 4px;
        }

        .route-type-card {
            background: rgba(255,255,255,0.9);
            border-radius: 6px;
            padding: 6px 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            border: 2px solid transparent;
            min-width: 70px;
            flex-shrink: 0;
            backdrop-filter: blur(10px);
        }

        .route-type-card.active {
            border-color: #3498db;
            background: rgba(52, 152, 219, 0.1);
        }

        .route-type-card:hover {
            transform: translateY(-1px);
            background: rgba(255,255,255,0.95);
        }

        .route-type-icon {
            font-size: 16px;
            margin-bottom: 2px;
        }

        .route-type-title {
            font-weight: bold;
            margin-bottom: 1px;
            font-size: 11px;
        }

        .route-type-desc {
            font-size: 9px;
            color: #7f8c8d;
        }

        .route-options {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 16px;
        }

        .route-option {
            background: white;
            border-radius: 8px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.3s;
            border: 2px solid #ecf0f1;
        }

        .route-option.active {
            border-color: #3498db;
            background: #e3f2fd;
        }

        .route-option-title {
            font-weight: bold;
            margin-bottom: 4px;
        }

        .route-option-desc {
            font-size: 12px;
            color: #7f8c8d;
        }

        .ai-planning {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #2c3e50;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            outline: none;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            border-color: #3498db;
        }

        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.3s;
            width: 100%;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #ecf0f1;
            color: #2c3e50;
        }

        .btn-secondary:hover {
            background: #d5dbdb;
        }

        .generated-route {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-top: 20px;
            display: none;
        }

        .route-step {
            display: flex;
            align-items: flex-start;
            padding: 12px 0;
            border-bottom: 1px solid #ecf0f1;
            position: relative;
        }

        .route-step:last-child {
            border-bottom: none;
        }

        .step-number {
            width: 24px;
            height: 24px;
            background: #3498db;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-weight: bold;
            margin-bottom: 4px;
        }

        .step-desc {
            font-size: 12px;
            color: #7f8c8d;
            margin-bottom: 4px;
        }

        .step-tags {
            display: flex;
            gap: 4px;
            margin-bottom: 4px;
        }

        .step-tag {
            background: #e3f2fd;
            color: #3498db;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
        }

        .step-time {
            font-size: 12px;
            color: #27ae60;
            font-weight: bold;
        }

        .step-cost {
            font-size: 12px;
            color: #e74c3c;
            font-weight: bold;
        }

        .step-actions {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }

        .step-btn {
            background: #ecf0f1;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            cursor: pointer;
            color: #2c3e50;
        }

        .step-btn:hover {
            background: #d5dbdb;
        }

        .route-actions {
            display: flex;
            gap: 12px;
            margin-top: 20px;
        }

        .map-view {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #a8edea, #fed6e3);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
            font-size: 16px;
            margin: 16px 0;
            position: relative;
            overflow: hidden;
        }

        .map-view::before {
            content: '🗺️';
            font-size: 48px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #27ae60;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 3000;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .notification.show {
            opacity: 1;
        }

        .notification.error {
            background: #e74c3c;
        }

        .notification.warning {
            background: #f39c12;
        }

        /* Route Points Display */
        .route-points {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .points-container {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding-bottom: 8px;
        }

        .point-card {
            min-width: 120px;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            border: 2px solid transparent;
        }

        .point-card:hover {
            border-color: #3498db;
            transform: translateY(-2px);
        }

        .point-card.active {
            border-color: #3498db;
            background: #e3f2fd;
        }

        .point-image {
            width: 80px;
            height: 60px;
            background-size: cover;
            background-position: center;
            border-radius: 6px;
            margin-bottom: 8px;
        }

        .point-name {
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 4px;
            color: #2c3e50;
        }

        .point-tags {
            display: flex;
            gap: 2px;
            justify-content: center;
            margin-bottom: 4px;
        }

        .point-tag {
            background: #3498db;
            color: white;
            padding: 1px 4px;
            border-radius: 8px;
            font-size: 8px;
        }

        .point-desc {
            font-size: 10px;
            color: #7f8c8d;
            line-height: 1.2;
        }

        /* Route Names */
        .route-names {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
            overflow-x: auto;
            padding-bottom: 4px;
        }

        .route-name-btn {
            background: rgba(255,255,255,0.9);
            border: 1px solid #ddd;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
            white-space: nowrap;
            flex-shrink: 0;
            backdrop-filter: blur(10px);
        }

        .route-name-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .route-name-btn:hover {
            border-color: #3498db;
        }

        /* Map Route Display */
        .map-route-display {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-top-left-radius: 16px;
            border-top-right-radius: 16px;
            box-shadow: 0 -4px 16px rgba(0,0,0,0.2);
            z-index: 1000;
            display: none;
            max-height: 40vh;
            overflow-y: auto;
        }

        .map-route-header {
            padding: 12px 16px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .map-route-title {
            font-weight: bold;
            color: #2c3e50;
        }

        .map-route-close {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #7f8c8d;
        }

        /* AI Options - Horizontal Layout */
        .ai-option-row {
            background: rgba(255,255,255,0.9);
            border-radius: 8px;
            padding: 8px 12px;
            margin-bottom: 8px;
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .ai-option-label {
            font-weight: bold;
            font-size: 12px;
            color: #2c3e50;
            min-width: 60px;
            flex-shrink: 0;
        }

        .ai-option-buttons {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
            flex: 1;
        }

        .ai-option-btn {
            background: white;
            border: 1px solid #ddd;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s;
            white-space: nowrap;
        }

        .ai-option-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .ai-option-btn:hover {
            border-color: #3498db;
        }

        /* Route Name with Cost */
        .route-name-with-cost {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .route-cost-badge {
            background: #27ae60;
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 9px;
            cursor: pointer;
        }

        .route-cost-badge:hover {
            background: #229954;
        }

        /* Payment and Route Info */
        .route-info {
            background: rgba(255,255,255,0.9);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
            backdrop-filter: blur(10px);
        }

        .route-cost {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .cost-label {
            font-size: 12px;
            color: #7f8c8d;
        }

        .cost-value {
            font-size: 14px;
            font-weight: bold;
            color: #e74c3c;
        }

        .payment-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            width: 100%;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">←</button>
            <div class="header-title">🗺️ 路径规划</div>
        </div>

        <div class="content">
            <!-- Route Type Selection -->
            <div class="section">
                <div class="section-title">选择路线类型</div>
                <div class="route-types">
                    <div class="route-type-card active" onclick="selectRouteType('recommended')">
                        <div class="route-type-icon">⭐</div>
                        <div class="route-type-title">推荐路线</div>
                        <div class="route-type-desc">研学·文化</div>
                    </div>
                    <div class="route-type-card" onclick="selectRouteType('shuttle')">
                        <div class="route-type-icon">🚌</div>
                        <div class="route-type-title">接驳车</div>
                        <div class="route-type-desc">路线1·2·3</div>
                    </div>
                    <div class="route-type-card" onclick="selectRouteType('bus')">
                        <div class="route-type-icon">🚐</div>
                        <div class="route-type-title">无人巴士</div>
                        <div class="route-type-desc">智能循环</div>
                    </div>
                    <div class="route-type-card" onclick="selectRouteType('ai')">
                        <div class="route-type-icon">🤖</div>
                        <div class="route-type-title">AI规划</div>
                        <div class="route-type-desc">个性化</div>
                    </div>
                </div>
            </div>

            <!-- Route Names -->
            <div id="routeNames" class="section" style="display: none;">
                <div class="route-names" id="routeNamesContainer">
                    <!-- Route names will be populated here -->
                </div>
            </div>



            <!-- AI Planning -->
            <div id="aiPlanning" class="section" style="display: none;">
                <div class="section-title">AI智能规划</div>
                <div class="ai-planning">
                    <div class="ai-option-row">
                        <div class="ai-option-label">游玩时间</div>
                        <div class="ai-option-buttons">
                            <button class="ai-option-btn" onclick="selectAIOption('duration', '2')">2小时</button>
                            <button class="ai-option-btn" onclick="selectAIOption('duration', '4')">4小时</button>
                            <button class="ai-option-btn active" onclick="selectAIOption('duration', '6')">6小时</button>
                            <button class="ai-option-btn" onclick="selectAIOption('duration', '8')">8小时</button>
                        </div>
                    </div>
                    <div class="ai-option-row">
                        <div class="ai-option-label">人数</div>
                        <div class="ai-option-buttons">
                            <button class="ai-option-btn" onclick="selectAIOption('size', '1')">1人</button>
                            <button class="ai-option-btn active" onclick="selectAIOption('size', '2-4')">2-4人</button>
                            <button class="ai-option-btn" onclick="selectAIOption('size', '5-10')">5-10人</button>
                            <button class="ai-option-btn" onclick="selectAIOption('size', '10+')">10+人</button>
                        </div>
                    </div>
                    <div class="ai-option-row">
                        <div class="ai-option-label">游览类型</div>
                        <div class="ai-option-buttons">
                            <button class="ai-option-btn active" onclick="selectAIOption('type', 'leisure')">休闲观光</button>
                            <button class="ai-option-btn" onclick="selectAIOption('type', 'family')">亲子游</button>
                            <button class="ai-option-btn" onclick="selectAIOption('type', 'photography')">摄影打卡</button>
                            <button class="ai-option-btn" onclick="selectAIOption('type', 'education')">科普教育</button>
                        </div>
                    </div>
                    <div class="ai-option-row">
                        <div class="ai-option-label">预算范围</div>
                        <div class="ai-option-buttons">
                            <button class="ai-option-btn active" onclick="selectAIOption('budget', '0-50')">0-50元</button>
                            <button class="ai-option-btn" onclick="selectAIOption('budget', '50-100')">50-100元</button>
                            <button class="ai-option-btn" onclick="selectAIOption('budget', '100-200')">100-200元</button>
                            <button class="ai-option-btn" onclick="selectAIOption('budget', '200+')">200元以上</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">其他需求</label>
                        <textarea class="form-textarea" id="otherRequirements" placeholder="请描述您的特殊需求或偏好..."></textarea>
                    </div>
                    <button class="btn" onclick="generateAIRoute()">生成个性化路线</button>
                </div>
            </div>

            <!-- Map Route Display (Bottom Sheet) -->
            <div id="mapRouteDisplay" class="map-route-display">
                <div class="map-route-header">
                    <div class="map-route-title" id="mapRouteTitle">路线详情</div>
                    <button class="map-route-close" onclick="closeMapRoute()">×</button>
                </div>
                <div class="points-container" id="mapRoutePoints">
                    <!-- Route points will be displayed here -->
                </div>
            </div>

            <!-- Generated Route -->
            <div id="generatedRoute" class="generated-route">
                <div class="section-title">🎯 推荐路线</div>
                <div id="routeContent">
                    <!-- Route content will be inserted here -->
                </div>
                <div class="map-view">
                    <div style="position: relative; z-index: 2;">手绘地图路线展示</div>
                </div>
                <div class="route-actions">
                    <button class="btn" onclick="saveRoute()" style="flex: 1;">保存行程</button>
                    <button class="btn btn-secondary" onclick="payForRoute()" style="flex: 1;">一键付费</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentRouteType = 'recommended';
        let currentRoute = null;
        let aiOptions = {
            duration: '6',
            size: '2-4',
            type: 'leisure',
            budget: '0-50'
        };

        function goBack() {
            window.history.back();
        }

        function selectRouteType(type) {
            currentRouteType = type;

            // Update UI
            document.querySelectorAll('.route-type-card').forEach(card => {
                card.classList.remove('active');
            });
            event.target.closest('.route-type-card').classList.add('active');

            // Show route names for selected type
            showRouteNames(type);

            // Show/hide sections
            document.getElementById('routeNames').style.display = ['recommended', 'shuttle', 'bus'].includes(type) ? 'block' : 'none';
            document.getElementById('aiPlanning').style.display = type === 'ai' ? 'block' : 'none';

            // Hide generated route and map display
            document.getElementById('generatedRoute').style.display = 'none';
            closeMapRoute();
        }

        function showRouteNames(type) {
            const container = document.getElementById('routeNamesContainer');
            const routes = {
                'recommended': [
                    { id: 'study', name: '研学路线' },
                    { id: 'culture', name: '文化路线' }
                ],
                'shuttle': [
                    { id: 'shuttle1', name: '路线1' },
                    { id: 'shuttle2', name: '路线2' },
                    { id: 'shuttle3', name: '路线3' }
                ],
                'bus': [
                    { id: 'bus1', name: '智能循环线' }
                ]
            };

            const routeList = routes[type] || [];
            container.innerHTML = routeList.map((route, index) => `
                <button class="route-name-btn ${index === 0 ? 'active' : ''}" onclick="selectRouteName('${route.id}')">
                    ${route.name}
                </button>
            `).join('');

            // Auto-select first route
            if (routeList.length > 0) {
                setTimeout(() => selectRouteName(routeList[0].id), 100);
            }
        }

        function selectAIOption(category, value) {
            aiOptions[category] = value;

            // Update UI
            const group = event.target.closest('.ai-option-group');
            group.querySelectorAll('.ai-option-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        function selectRouteName(routeId) {
            // Update UI
            document.querySelectorAll('.route-name-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // Show route on map
            showRouteOnMap(routeId);
        }

        function showRouteOnMap(routeId) {
            const routes = {
                'study': {
                    name: '研学路线',
                    points: [
                        {
                            name: '航空科普馆',
                            image: 'https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
                            tags: ['科普', '体验'],
                            desc: '了解航空发展历史，体验飞行模拟器'
                        },
                        {
                            name: '城市馆',
                            image: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
                            tags: ['文化', '历史'],
                            desc: '参观合肥城市发展展览'
                        },
                        {
                            name: '昆虫博物馆',
                            image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
                            tags: ['自然', '教育'],
                            desc: '观察各类昆虫标本，学习自然知识'
                        }
                    ]
                },
                'culture': {
                    name: '文化路线',
                    points: [
                        {
                            name: '信标台',
                            image: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
                            tags: ['地标', '历史'],
                            desc: '登顶俯瞰全园，了解机场历史'
                        },
                        {
                            name: '航站楼',
                            image: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
                            tags: ['建筑', '历史'],
                            desc: '参观保留的历史建筑'
                        },
                        {
                            name: '展园区域',
                            image: 'https://images.unsplash.com/photo-1522383225653-ed111181a951?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
                            tags: ['园林', '摄影'],
                            desc: '欣赏园林景观和花卉展示'
                        }
                    ]
                },
                'shuttle1': {
                    name: '接驳车路线1',
                    cost: '5元',
                    points: [
                        { name: '东门', desc: '上车点', tags: ['起点'] },
                        { name: '信标台', desc: '中途站', tags: ['景点'] },
                        { name: '航站楼', desc: '中途站', tags: ['景点'] },
                        { name: '西门', desc: '下车点', tags: ['终点'] }
                    ]
                },
                'shuttle2': {
                    name: '接驳车路线2',
                    cost: '5元',
                    points: [
                        { name: '南门', desc: '上车点', tags: ['起点'] },
                        { name: '科普馆', desc: '中途站', tags: ['景点'] },
                        { name: '大草坪', desc: '中途站', tags: ['景点'] },
                        { name: '北门', desc: '下车点', tags: ['终点'] }
                    ]
                },
                'shuttle3': {
                    name: '接驳车路线3',
                    cost: '8元',
                    points: [
                        { name: '东门', desc: '上车点', tags: ['起点'] },
                        { name: '全园环线', desc: '主要景点', tags: ['环线'] },
                        { name: '东门', desc: '下车点', tags: ['终点'] }
                    ]
                },
                'bus1': {
                    name: '无人巴士智能循环线',
                    cost: '免费',
                    points: [
                        { name: '东门站', desc: '15分钟一班', tags: ['站点'] },
                        { name: '信标台站', desc: '15分钟一班', tags: ['站点'] },
                        { name: '科普馆站', desc: '15分钟一班', tags: ['站点'] },
                        { name: '大草坪站', desc: '15分钟一班', tags: ['站点'] }
                    ]
                }
            };

            const route = routes[routeId];
            if (route) {
                displayMapRoute(route);
                if (route.cost) {
                    document.getElementById('routeCost').textContent = route.cost;
                }
            }
        }

        function displayMapRoute(route) {
            document.getElementById('mapRouteTitle').textContent = `${route.name} ${route.cost ? `(${route.cost})` : ''}`;
            const container = document.getElementById('mapRoutePoints');

            container.innerHTML = route.points.map((point, index) => `
                <div class="point-card" onclick="focusOnPoint('${point.name}')">
                    ${point.image ? `<div class="point-image" style="background-image: url('${point.image}')"></div>` : ''}
                    <div class="point-name">${point.name}</div>
                    <div class="point-tags">
                        ${point.tags.map(tag => `<span class="point-tag">${tag}</span>`).join('')}
                    </div>
                    <div class="point-desc">${point.desc}</div>
                </div>
            `).join('');

            document.getElementById('mapRouteDisplay').style.display = 'block';
            showNotification(`${route.name}已在地图上标注`, 'success');
        }

        function closeMapRoute() {
            document.getElementById('mapRouteDisplay').style.display = 'none';
        }

        function focusOnPoint(pointName) {
            // Update point cards
            document.querySelectorAll('#mapRoutePoints .point-card').forEach(card => {
                card.classList.remove('active');
            });
            event.target.closest('.point-card').classList.add('active');

            // Simulate map focus
            showNotification(`地图聚焦到${pointName}`, 'info');
        }

        function payForTransport() {
            const cost = document.getElementById('routeCost').textContent;
            showNotification(`正在跳转到支付页面，费用：${cost}`, 'info');
        }

        function selectRoute(routeId) {
            // Update UI
            document.querySelectorAll('.route-option').forEach(option => {
                option.classList.remove('active');
            });
            event.target.closest('.route-option').classList.add('active');

            // Generate predefined route
            generatePredefinedRoute(routeId);
        }

        function generatePredefinedRoute(routeId) {
            const routes = {
                'study': {
                    name: '研学路线',
                    totalTime: '4小时',
                    estimatedCost: '60元',
                    steps: [
                        {
                            title: '航空科普馆',
                            desc: '了解航空发展历史，体验飞行模拟器',
                            time: '90分钟',
                            cost: '20元',
                            tags: ['科普', '体验']
                        },
                        {
                            title: '城市馆',
                            desc: '参观合肥城市发展展览',
                            time: '60分钟',
                            cost: '免费',
                            tags: ['文化', '历史']
                        },
                        {
                            title: '昆虫博物馆',
                            desc: '观察各类昆虫标本，学习自然知识',
                            time: '90分钟',
                            cost: '15元',
                            tags: ['自然', '教育']
                        }
                    ]
                },
                'culture': {
                    name: '文化路线',
                    totalTime: '3小时',
                    estimatedCost: '免费',
                    steps: [
                        {
                            title: '信标台',
                            desc: '登顶俯瞰全园，了解机场历史',
                            time: '45分钟',
                            cost: '免费',
                            tags: ['地标', '历史']
                        },
                        {
                            title: '航站楼',
                            desc: '参观保留的历史建筑',
                            time: '60分钟',
                            cost: '免费',
                            tags: ['建筑', '历史']
                        },
                        {
                            title: '展园区域',
                            desc: '欣赏园林景观和花卉展示',
                            time: '75分钟',
                            cost: '免费',
                            tags: ['园林', '摄影']
                        }
                    ]
                }
            };

            const route = routes[routeId];
            if (route) {
                displayRoute(route);
            }
        }

        function generateAIRoute() {
            const requirements = document.getElementById('otherRequirements').value;

            showNotification('AI正在生成个性化路线...', 'info');

            // Simulate AI processing
            setTimeout(() => {
                const route = generateRouteBasedOnPreferences(aiOptions.duration, aiOptions.size, aiOptions.type, aiOptions.budget, requirements);
                displayRoute(route);
                showNotification('路线生成完成！', 'success');
            }, 2000);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Default to recommended routes
            selectRouteType('recommended');
        });

        function generateRouteBasedOnPreferences(duration, groupSize, tourType, budget, requirements) {
            const routes = {
                'leisure': {
                    name: '休闲观光路线',
                    totalTime: duration + '小时',
                    estimatedCost: budget === '0-50' ? '免费' : '50元以内',
                    steps: [
                        {
                            title: '信标台',
                            desc: '登顶俯瞰全景，拍照留念',
                            time: '30分钟',
                            cost: '免费',
                            tags: ['观景', '拍照']
                        },
                        {
                            title: '梦想大草坪',
                            desc: '草坪漫步，享受自然',
                            time: '45分钟',
                            cost: '免费',
                            tags: ['休闲', '自然']
                        },
                        {
                            title: '航站楼咖啡厅',
                            desc: '品尝咖啡，休息放松',
                            time: '30分钟',
                            cost: '30元',
                            tags: ['休闲', '美食']
                        }
                    ]
                },
                'family': {
                    name: '亲子游路线',
                    totalTime: duration + '小时',
                    estimatedCost: '80元以内',
                    steps: [
                        {
                            title: '昆虫博物馆',
                            desc: '观察昆虫标本，科普教育',
                            time: '60分钟',
                            cost: '15元',
                            tags: ['亲子', '科普']
                        },
                        {
                            title: '航空科普馆',
                            desc: '飞行模拟体验',
                            time: '60分钟',
                            cost: '20元',
                            tags: ['体验', '科普']
                        },
                        {
                            title: '儿童游乐区',
                            desc: '儿童游乐设施',
                            time: '90分钟',
                            cost: '25元',
                            tags: ['儿童', '游乐']
                        }
                    ]
                }
            };

            return routes[tourType] || routes['leisure'];
        }

        function displayRoute(route) {
            currentRoute = route;
            const content = document.getElementById('routeContent');
            
            content.innerHTML = `
                <div style="margin-bottom: 16px;">
                    <h3 style="color: #3498db; margin-bottom: 8px;">${route.name}</h3>
                    <div style="display: flex; justify-content: space-between; font-size: 14px; color: #7f8c8d; margin-bottom: 16px;">
                        <span>总时长: ${route.totalTime}</span>
                        <span>预计费用: ${route.estimatedCost}</span>
                    </div>
                </div>
                <div style="margin-bottom: 16px;">
                    ${route.steps.map((step, index) => `
                        <div class="route-step" id="step-${index}">
                            <div class="step-number">${index + 1}</div>
                            <div class="step-content">
                                <div class="step-title">${step.title}</div>
                                <div class="step-desc">${step.desc}</div>
                                <div class="step-tags">
                                    ${step.tags.map(tag => `<span class="step-tag">${tag}</span>`).join('')}
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-top: 4px;">
                                    <span class="step-time">⏱️ ${step.time}</span>
                                    <span class="step-cost">💰 ${step.cost}</span>
                                </div>
                                <div class="step-actions">
                                    <button class="step-btn" onclick="removeStep(${index})">删除</button>
                                    <button class="step-btn" onclick="addStepAfter(${index})">新增</button>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;

            document.getElementById('generatedRoute').style.display = 'block';
            document.getElementById('generatedRoute').scrollIntoView({ behavior: 'smooth' });
        }

        function removeStep(index) {
            if (currentRoute && currentRoute.steps.length > 1) {
                currentRoute.steps.splice(index, 1);
                displayRoute(currentRoute);
                showNotification('景点已删除', 'success');
            } else {
                showNotification('至少需要保留一个景点', 'warning');
            }
        }

        function addStepAfter(index) {
            const newStep = {
                title: '新景点',
                desc: '请编辑景点信息',
                time: '30分钟',
                cost: '免费',
                tags: ['新增']
            };
            
            if (currentRoute) {
                currentRoute.steps.splice(index + 1, 0, newStep);
                displayRoute(currentRoute);
                showNotification('新景点已添加', 'success');
            }
        }

        function saveRoute() {
            if (currentRoute) {
                // Save to localStorage
                const savedRoutes = JSON.parse(localStorage.getItem('savedRoutes') || '[]');
                const newRoute = {
                    id: Date.now(),
                    name: currentRoute.name,
                    date: new Date().toISOString().split('T')[0],
                    route: currentRoute
                };
                savedRoutes.push(newRoute);
                localStorage.setItem('savedRoutes', JSON.stringify(savedRoutes));
                
                showNotification('行程已保存！', 'success');
            }
        }

        function payForRoute() {
            if (currentRoute) {
                showNotification('跳转到支付页面...', 'info');
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }
    </script>
</body>
</html>
