<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>伴游导览 - 骆岗公园</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80') center/cover;
            background-attachment: fixed;
            min-height: 100vh;
            color: #2c3e50;
        }

        .app-container {
            max-width: 414px;
            margin: 0 auto;
            background: rgba(255,255,255,0.95);
            min-height: 100vh;
            position: relative;
            backdrop-filter: blur(10px);
        }

        .header {
            background: linear-gradient(135deg, #3498db, #2ecc71);
            color: white;
            padding: 20px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }

        .header-title {
            font-size: 20px;
            font-weight: bold;
        }

        .content {
            padding: 20px 16px;
        }

        .map-container {
            width: 100%;
            height: 300px;
            background: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80') center/cover;
            border-radius: 12px;
            position: relative;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .map-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(52, 152, 219, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .map-content {
            text-align: center;
            color: white;
            background: rgba(0,0,0,0.5);
            padding: 16px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }

        .current-location {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .location-desc {
            font-size: 14px;
            opacity: 0.9;
        }

        .attraction-marker {
            position: absolute;
            width: 30px;
            height: 30px;
            background: #e74c3c;
            border: 3px solid white;
            border-radius: 50%;
            cursor: pointer;
            animation: pulse 2s infinite;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .attraction-marker.active {
            background: #f39c12;
            animation: blink 1s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .marker-1 { top: 20%; left: 30%; }
        .marker-2 { top: 40%; left: 60%; }
        .marker-3 { top: 70%; left: 25%; }
        .marker-4 { top: 60%; left: 75%; }

        .section {
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 12px;
            color: #2c3e50;
        }

        .nearby-attractions {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .attraction-list {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding-bottom: 8px;
        }

        .attraction-item {
            min-width: 140px;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.3s;
            flex-shrink: 0;
            text-align: center;
        }

        .attraction-item:hover {
            background: #e3f2fd;
            transform: translateY(-2px);
        }

        .attraction-image {
            width: 100%;
            height: 80px;
            background-size: cover;
            background-position: center;
            border-radius: 6px;
            margin-bottom: 8px;
        }

        .attraction-name {
            font-weight: bold;
            margin-bottom: 4px;
            font-size: 12px;
            color: #2c3e50;
        }

        .attraction-tags {
            display: flex;
            gap: 2px;
            justify-content: center;
            margin-bottom: 4px;
            flex-wrap: wrap;
        }

        .attraction-tag {
            background: #3498db;
            color: white;
            padding: 1px 4px;
            border-radius: 6px;
            font-size: 8px;
        }

        .attraction-distance {
            font-size: 11px;
            color: #7f8c8d;
            margin-bottom: 6px;
        }



        .interaction-section {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            margin-bottom: 12px;
        }

        .search-input:focus {
            border-color: #3498db;
        }

        .quick-questions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .quick-btn {
            background: #ecf0f1;
            border: none;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            cursor: pointer;
            color: #2c3e50;
            transition: all 0.3s;
        }

        .quick-btn:hover {
            background: #3498db;
            color: white;
        }

        .navigation-section {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .destination-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            outline: none;
            margin-bottom: 12px;
        }

        .destination-input:focus {
            border-color: #3498db;
        }

        .transport-options {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
        }

        .transport-btn {
            flex: 1;
            background: #ecf0f1;
            border: none;
            padding: 12px 8px;
            border-radius: 8px;
            font-size: 12px;
            cursor: pointer;
            color: #2c3e50;
            transition: all 0.3s;
            text-align: center;
        }

        .transport-btn.active {
            background: #3498db;
            color: white;
        }

        .transport-btn:hover {
            background: #3498db;
            color: white;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.3s;
            width: 100%;
        }

        .btn:hover {
            background: #2980b9;
        }

        .response-area {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            margin-top: 12px;
            display: none;
        }

        .response-text {
            font-size: 14px;
            line-height: 1.4;
            color: #2c3e50;
        }

        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #27ae60;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 3000;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .notification.show {
            opacity: 1;
        }

        .notification.error {
            background: #e74c3c;
        }

        .notification.warning {
            background: #f39c12;
        }

        /* Navigation Options Modal */
        .nav-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .nav-modal-content {
            background: white;
            border-radius: 12px;
            padding: 20px;
            max-width: 300px;
            width: 90%;
        }

        .nav-modal-title {
            font-weight: bold;
            margin-bottom: 16px;
            text-align: center;
            color: #2c3e50;
        }

        .nav-options {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .nav-option {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .nav-option:hover {
            background: #e3f2fd;
        }

        .nav-option-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-option-icon {
            font-size: 20px;
        }

        .nav-option-details {
            flex: 1;
        }

        .nav-option-name {
            font-weight: bold;
            font-size: 12px;
        }

        .nav-option-time {
            font-size: 10px;
            color: #7f8c8d;
        }

        .nav-option-cost {
            background: #27ae60;
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 9px;
            cursor: pointer;
        }

        .nav-option-cost.free {
            background: #95a5a6;
        }

        .nav-option-cost:hover {
            background: #229954;
        }

        .nav-option-cost.free:hover {
            background: #7f8c8d;
        }

        .modal-close {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">←</button>
            <div class="header-title">🎧 伴游导览</div>
        </div>

        <div class="content">
            <!-- Hand-drawn Map -->
            <div class="map-container">
                <div class="map-overlay">
                    <div class="map-content">
                        <div class="current-location">📍 当前位置：信标台附近</div>
                        <div class="location-desc">手绘地图导览</div>
                    </div>
                </div>
                <!-- Attraction Markers -->
                <div class="attraction-marker marker-1" onclick="selectAttraction('信标台')" title="信标台">1</div>
                <div class="attraction-marker marker-2" onclick="selectAttraction('航站楼')" title="航站楼">2</div>
                <div class="attraction-marker marker-3" onclick="selectAttraction('梦想大草坪')" title="梦想大草坪">3</div>
                <div class="attraction-marker marker-4" onclick="selectAttraction('科普馆')" title="科普馆">4</div>
            </div>

            <!-- Nearby Attractions -->
            <div class="section">
                <div class="section-title">📍 附近景点</div>
                <div class="attraction-list" id="attractionList">
                    <div class="attraction-item" onclick="focusOnAttraction('信标台')">
                        <div class="attraction-image" style="background-image: url('https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80')"></div>
                        <div class="attraction-name">信标台</div>
                        <div class="attraction-tags">
                            <span class="attraction-tag">地标</span>
                            <span class="attraction-tag">观景</span>
                        </div>
                        <div class="attraction-distance">距离: 50米</div>
                    </div>
                    <div class="attraction-item" onclick="focusOnAttraction('航站楼')">
                        <div class="attraction-image" style="background-image: url('https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80')"></div>
                        <div class="attraction-name">航站楼</div>
                        <div class="attraction-tags">
                            <span class="attraction-tag">历史</span>
                            <span class="attraction-tag">展览</span>
                        </div>
                        <div class="attraction-distance">距离: 200米</div>
                    </div>
                    <div class="attraction-item" onclick="focusOnAttraction('梦想大草坪')">
                        <div class="attraction-image" style="background-image: url('https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80')"></div>
                        <div class="attraction-name">梦想大草坪</div>
                        <div class="attraction-tags">
                            <span class="attraction-tag">休闲</span>
                            <span class="attraction-tag">野餐</span>
                        </div>
                        <div class="attraction-distance">距离: 300米</div>
                    </div>
                    <div class="attraction-item" onclick="focusOnAttraction('航空科普馆')">
                        <div class="attraction-image" style="background-image: url('https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80')"></div>
                        <div class="attraction-name">航空科普馆</div>
                        <div class="attraction-tags">
                            <span class="attraction-tag">科普</span>
                            <span class="attraction-tag">体验</span>
                        </div>
                        <div class="attraction-distance">距离: 400米</div>
                    </div>
                </div>
            </div>

            <!-- Interactive Search -->
            <div class="section">
                <div class="section-title">🎤 智能问答</div>
                <div class="interaction-section">
                    <input type="text" class="search-input" id="searchInput" placeholder="问我任何关于公园的问题..." onkeypress="handleEnter(event)">
                    <div class="quick-questions">
                        <button class="quick-btn" onclick="quickQuestion('最近的火锅店')">最近的火锅店</button>
                        <button class="quick-btn" onclick="quickQuestion('卫生间在哪')">卫生间在哪</button>
                        <button class="quick-btn" onclick="quickQuestion('拍照地点')">拍照地点</button>
                        <button class="quick-btn" onclick="quickQuestion('儿童游乐')">儿童游乐</button>
                    </div>
                    <div class="response-area" id="responseArea">
                        <div class="response-text" id="responseText"></div>
                    </div>
                </div>
            </div>


        </div>

        <!-- Navigation Options Modal -->
        <div class="nav-modal" id="navModal">
            <div class="nav-modal-content">
                <button class="modal-close" onclick="closeNavModal()">×</button>
                <div class="nav-modal-title" id="navModalTitle">选择前往信标台的方式</div>
                <div class="nav-options">
                    <div class="nav-option" onclick="startNavigation('walking')">
                        <div class="nav-option-info">
                            <div class="nav-option-icon">🚶</div>
                            <div class="nav-option-details">
                                <div class="nav-option-name">步行</div>
                                <div class="nav-option-time" id="walkingTime">约5分钟</div>
                            </div>
                        </div>
                        <div class="nav-option-cost free">免费</div>
                    </div>
                    <div class="nav-option" onclick="startNavigation('bus')">
                        <div class="nav-option-info">
                            <div class="nav-option-icon">🚐</div>
                            <div class="nav-option-details">
                                <div class="nav-option-name">无人巴士</div>
                                <div class="nav-option-time">约3分钟</div>
                            </div>
                        </div>
                        <div class="nav-option-cost free">免费</div>
                    </div>
                    <div class="nav-option" onclick="startNavigation('shuttle')">
                        <div class="nav-option-info">
                            <div class="nav-option-icon">🚌</div>
                            <div class="nav-option-details">
                                <div class="nav-option-name">接驳车</div>
                                <div class="nav-option-time">约2分钟</div>
                            </div>
                        </div>
                        <div class="nav-option-cost" onclick="payForTransport(event, '5元')">5元</div>
                    </div>
                    <div class="nav-option" onclick="startNavigation('electric')">
                        <div class="nav-option-info">
                            <div class="nav-option-icon">🛵</div>
                            <div class="nav-option-details">
                                <div class="nav-option-name">电动车</div>
                                <div class="nav-option-time">约2分钟</div>
                            </div>
                        </div>
                        <div class="nav-option-cost" onclick="payForTransport(event, '10元')">10元</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentTransport = 'walk';
        let selectedAttraction = null;

        function goBack() {
            window.history.back();
        }

        function selectAttraction(name) {
            selectedAttraction = name;
            
            // Remove active class from all markers
            document.querySelectorAll('.attraction-marker').forEach(marker => {
                marker.classList.remove('active');
            });
            
            // Add active class to clicked marker
            event.target.classList.add('active');
            
            // Simulate entering attraction area
            setTimeout(() => {
                playAttractionAudio(name);
            }, 500);
        }

        function playAttractionAudio(name) {
            const descriptions = {
                '信标台': '欢迎来到信标台！这里是骆岗公园的标志性建筑，高约50米。您可以登顶俯瞰整个公园的美景。',
                '航站楼': '这里是保留的原机场航站楼建筑，现在改造为展览和活动空间，展示着合肥机场的历史。',
                '梦想大草坪': '欢迎来到梦想大草坪！这里是占地约100亩的开阔区域，是野餐和休闲的理想场所。',
                '科普馆': '欢迎来到航空科普馆！这里有丰富的航空知识展示和飞行模拟体验。'
            };
            
            showNotification(`🔊 ${descriptions[name]}`, 'info');
        }

        function navigateToAttraction(name) {
            showNotification(`正在为您导航到${name}，预计步行时间3分钟`, 'info');
            
            // Highlight the attraction on map
            selectAttractionByName(name);
        }

        function selectAttractionByName(name) {
            const markerMap = {
                '信标台': '.marker-1',
                '航站楼': '.marker-2',
                '梦想大草坪': '.marker-3',
                '航空科普馆': '.marker-4'
            };
            
            // Remove active class from all markers
            document.querySelectorAll('.attraction-marker').forEach(marker => {
                marker.classList.remove('active');
            });
            
            // Add active class to target marker
            const targetMarker = document.querySelector(markerMap[name]);
            if (targetMarker) {
                targetMarker.classList.add('active');
            }
        }

        function quickQuestion(question) {
            document.getElementById('searchInput').value = question;
            handleQuestion(question);
        }

        function handleEnter(event) {
            if (event.key === 'Enter') {
                const question = event.target.value.trim();
                if (question) {
                    handleQuestion(question);
                }
            }
        }

        function handleQuestion(question) {
            const responses = {
                '最近的火锅店': '最近的火锅店在公园东门外200米处，有"蜀香园火锅"和"老码头火锅"两家，营业时间11:00-22:00。',
                '卫生间在哪': '最近的卫生间在信标台东侧50米处，24小时开放，设有无障碍设施和母婴室。',
                '拍照地点': '推荐拍照地点：1.信标台顶部-全景视角 2.航站楼前广场-建筑摄影 3.梦想大草坪-人像摄影 4.展园花海-花卉摄影',
                '儿童游乐': '儿童游乐区位于梦想大草坪南侧，有滑梯、秋千、沙坑等设施，适合3-12岁儿童，开放时间8:00-18:00。'
            };
            
            let response = responses[question];
            if (!response) {
                // Simple keyword matching
                if (question.includes('餐厅') || question.includes('吃饭')) {
                    response = '园区内有骆岗咖啡厅，位于航站楼内。园区外东门有多家餐厅可选择。';
                } else if (question.includes('停车')) {
                    response = '公园设有大型停车场，位于东门和西门，停车费5元/小时，可通过手机支付。';
                } else if (question.includes('门票')) {
                    response = '骆岗公园免费开放！部分体验项目收费：航空科普馆20元，VR体验30元。';
                } else {
                    response = '抱歉，我没有理解您的问题。您可以问我关于景点、设施、餐饮、交通等信息。';
                }
            }
            
            showResponse(response);
            
            // Mark locations on map if relevant
            if (question.includes('火锅') || question.includes('餐厅')) {
                markLocationOnMap('restaurant');
            }
        }

        function showResponse(text) {
            const responseArea = document.getElementById('responseArea');
            const responseText = document.getElementById('responseText');
            
            responseText.textContent = text;
            responseArea.style.display = 'block';
            
            // Scroll to response
            responseArea.scrollIntoView({ behavior: 'smooth' });
        }

        function markLocationOnMap(type) {
            // This would add markers for restaurants, facilities, etc.
            showNotification('已在地图上标注相关位置', 'success');
        }

        function selectTransport(type) {
            currentTransport = type;
            
            // Update UI
            document.querySelectorAll('.transport-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        function startNavigation() {
            const destination = document.getElementById('destinationInput').value.trim();
            if (!destination) {
                showNotification('请输入目的地', 'warning');
                return;
            }
            
            const transportNames = {
                'walk': '步行',
                'bus': '无人巴士',
                'shuttle': '接驳车'
            };
            
            const transportTimes = {
                'walk': '5-10分钟',
                'bus': '3-5分钟',
                'shuttle': '2-4分钟'
            };
            
            showNotification(`正在规划${transportNames[currentTransport]}路线到${destination}，预计用时${transportTimes[currentTransport]}`, 'info');
            
            // Simulate navigation
            setTimeout(() => {
                showNotification('导航路线已生成，请按照地图指示前进', 'success');
            }, 2000);
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 4000);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Simulate user entering different attraction areas
            setInterval(() => {
                if (Math.random() > 0.95) { // 5% chance every interval
                    const attractions = ['信标台', '航站楼', '梦想大草坪', '科普馆'];
                    const randomAttraction = attractions[Math.floor(Math.random() * attractions.length)];
                    selectAttractionByName(randomAttraction);
                }
            }, 5000);
        });
        let currentDestination = '';
        let currentDistance = 0;

        function showNavigationOptions(event, destination, distance) {
            event.stopPropagation();
            currentDestination = destination;
            currentDistance = distance;

            document.getElementById('navModalTitle').textContent = `选择前往${destination}的方式`;

            // Calculate walking time (assuming 80m/min walking speed)
            const walkingMinutes = Math.ceil(distance / 80);
            document.getElementById('walkingTime').textContent = `约${walkingMinutes}分钟`;

            document.getElementById('navModal').style.display = 'flex';
        }

        function closeNavModal() {
            document.getElementById('navModal').style.display = 'none';
        }

        function startNavigation(method) {
            const methods = {
                'walking': '步行',
                'bus': '无人巴士',
                'shuttle': '接驳车',
                'electric': '电动车'
            };

            closeNavModal();
            showNotification(`开始${methods[method]}导航到${currentDestination}`, 'success');

            // Simulate route display on map
            setTimeout(() => {
                showNotification(`路线已在地图上标注`, 'info');
            }, 1000);
        }

        function payForTransport(event, cost) {
            event.stopPropagation();
            showNotification(`正在跳转到支付页面，费用：${cost}`, 'info');
        }
    </script>
</body>
</html>
