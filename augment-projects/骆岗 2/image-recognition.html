<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>看图识景 - 骆岗公园</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #2c3e50;
        }

        .app-container {
            max-width: 414px;
            margin: 0 auto;
            background: rgba(255,255,255,0.95);
            min-height: 100vh;
            position: relative;
            backdrop-filter: blur(10px);
        }

        .header {
            background: linear-gradient(135deg, #3498db, #2ecc71);
            color: white;
            padding: 20px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }

        .header-title {
            font-size: 20px;
            font-weight: bold;
        }

        .content {
            padding: 20px 16px;
        }

        .camera-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            text-align: center;
        }

        .camera-area {
            width: 100%;
            height: 250px;
            border: 2px dashed #3498db;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            cursor: pointer;
            transition: all 0.3s;
            background: #f8f9fa;
            position: relative;
            overflow: hidden;
        }

        .camera-area:hover {
            border-color: #2980b9;
            background: #e3f2fd;
        }

        .camera-placeholder {
            text-align: center;
            color: #7f8c8d;
        }

        .camera-icon {
            font-size: 48px;
            margin-bottom: 12px;
            color: #3498db;
        }

        .camera-text {
            font-size: 16px;
            margin-bottom: 8px;
        }

        .camera-hint {
            font-size: 12px;
            color: #95a5a6;
        }

        .uploaded-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 10px;
        }

        .camera-buttons {
            display: flex;
            gap: 12px;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.3s;
            flex: 1;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #ecf0f1;
            color: #2c3e50;
        }

        .btn-secondary:hover {
            background: #d5dbdb;
        }

        .recognition-result {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: none;
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .result-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }

        .play-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .play-btn:hover {
            background: #229954;
        }

        .attraction-info {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
        }

        .attraction-image {
            width: 100px;
            height: 100px;
            border-radius: 8px;
            background-size: cover;
            background-position: center;
            flex-shrink: 0;
        }

        .attraction-details {
            flex: 1;
        }

        .attraction-name {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .attraction-tags {
            display: flex;
            gap: 6px;
            margin-bottom: 8px;
            flex-wrap: wrap;
        }

        .attraction-tag {
            background: #e3f2fd;
            color: #3498db;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
        }

        .attraction-meta {
            font-size: 12px;
            color: #7f8c8d;
            line-height: 1.4;
        }

        .attraction-description {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 16px;
        }

        .description-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .description-text {
            font-size: 14px;
            line-height: 1.6;
            color: #34495e;
        }

        .language-selector {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
        }

        .language-label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #2c3e50;
        }

        .language-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            outline: none;
        }

        .language-select:focus {
            border-color: #3498db;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #ecf0f1;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 12px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #27ae60;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 3000;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .notification.show {
            opacity: 1;
        }

        .notification.error {
            background: #e74c3c;
        }

        .notification.warning {
            background: #f39c12;
        }

        .hidden {
            display: none !important;
        }

        /* Recognition Type Selection */
        .recognition-types {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .type-cards {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 16px;
        }

        .type-card {
            background: #f8f9fa;
            border: 2px solid transparent;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .type-card.active {
            border-color: #3498db;
            background: #e3f2fd;
        }

        .type-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .type-icon {
            width: 60px;
            height: 60px;
            background-size: cover;
            background-position: center;
            border-radius: 8px;
            margin: 0 auto 8px;
        }

        .type-title {
            font-weight: bold;
            margin-bottom: 4px;
            color: #2c3e50;
        }

        .type-desc {
            font-size: 11px;
            color: #7f8c8d;
        }

        .type-examples {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            padding-bottom: 4px;
        }

        .example-item {
            min-width: 80px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            flex-shrink: 0;
        }

        .example-item:hover {
            transform: translateY(-2px);
        }

        .example-image {
            width: 60px;
            height: 60px;
            background-size: cover;
            background-position: center;
            border-radius: 8px;
            border: 2px solid transparent;
            margin-bottom: 4px;
            transition: all 0.3s;
        }

        .example-image:hover {
            border-color: #3498db;
        }

        .example-name {
            font-size: 10px;
            color: #7f8c8d;
        }

        /* Recognition Result Enhancements */
        .recognition-accuracy {
            background: #e8f5e8;
            color: #27ae60;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            margin-left: 8px;
        }

        .feedback-section {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 8px;
            margin-top: 16px;
        }

        .feedback-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
            font-size: 12px;
        }

        .feedback-buttons {
            display: flex;
            gap: 8px;
        }

        .feedback-btn {
            background: white;
            border: 1px solid #ddd;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s;
            flex: 1;
            text-align: center;
        }

        .feedback-btn.correct {
            border-color: #27ae60;
            color: #27ae60;
        }

        .feedback-btn.incorrect {
            border-color: #e74c3c;
            color: #e74c3c;
        }

        .feedback-btn:hover {
            transform: translateY(-1px);
        }

        .feedback-btn.selected {
            background: #27ae60;
            color: white;
        }

        .feedback-btn.selected.incorrect {
            background: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">←</button>
            <div class="header-title">📷 看图识景</div>
        </div>

        <div class="content">
            <!-- Recognition Type Selection -->
            <div class="recognition-types">
                <div class="section-title">选择识别类型</div>
                <div class="type-cards">
                    <div class="type-card active" onclick="selectRecognitionType('building')">
                        <div class="type-icon" style="background-image: url('https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80')"></div>
                        <div class="type-title">特色建筑</div>
                        <div class="type-desc">识别园区内的标志性建筑</div>
                    </div>
                    <div class="type-card" onclick="selectRecognitionType('plant')">
                        <div class="type-icon" style="background-image: url('https://images.unsplash.com/photo-1522383225653-ed111181a951?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80')"></div>
                        <div class="type-title">植物科普</div>
                        <div class="type-desc">识别园区内的花草树木</div>
                    </div>
                </div>
                <div class="type-examples" id="typeExamples">
                    <!-- Examples will be populated here -->
                </div>
            </div>

            <!-- Camera Section -->
            <div class="camera-section">
                <div class="camera-area" onclick="selectImage()" id="cameraArea">
                    <div class="camera-placeholder" id="cameraPlaceholder">
                        <div class="camera-icon">📷</div>
                        <div class="camera-text">点击拍照或上传图片</div>
                        <div class="camera-hint">支持JPG、PNG格式</div>
                    </div>
                </div>
                <input type="file" id="imageInput" accept="image/*" style="display: none;" onchange="processImage()">
                <div class="camera-buttons">
                    <button class="btn" onclick="selectImage()">📷 选择图片</button>
                    <button class="btn btn-secondary" onclick="clearImage()">🗑️ 清除</button>
                    <button class="btn" onclick="recognizeImage()" id="recognizeBtn" style="display: none;">🔍 开始识别</button>
                </div>
            </div>

            <!-- Loading -->
            <div class="loading" id="loadingSection">
                <div class="loading-spinner"></div>
                <div>AI正在识别图片...</div>
            </div>

            <!-- Recognition Result -->
            <div class="recognition-result" id="recognitionResult">
                <div class="result-header">
                    <div class="result-title">🎯 识别结果</div>
                    <div style="display: flex; align-items: center;">
                        <span class="recognition-accuracy" id="recognitionAccuracy">95%</span>
                        <button class="play-btn" onclick="playAudio()">
                            🔊 语音播报
                        </button>
                    </div>
                </div>

                <div class="attraction-info">
                    <div class="attraction-image" id="resultImage"></div>
                    <div class="attraction-details">
                        <div class="attraction-name" id="attractionName">信标台</div>
                        <div class="attraction-tags" id="attractionTags">
                            <span class="attraction-tag">地标建筑</span>
                            <span class="attraction-tag">观景台</span>
                            <span class="attraction-tag">拍照打卡</span>
                        </div>
                        <div class="attraction-meta" id="attractionMeta">
                            高度：约50米<br>
                            开放时间：全天<br>
                            门票：免费
                        </div>
                    </div>
                </div>

                <div class="attraction-description">
                    <div class="description-title">详细介绍</div>
                    <div class="description-text" id="attractionDescription">
                        信标台是骆岗公园的标志性建筑，高约50米，原为合肥机场的导航设施。改造后成为公园的观景台，游客可以登顶俯瞰整个公园的美景。建筑设计融合了现代与历史元素，是拍照打卡的热门地点。
                    </div>
                </div>

                <div class="language-selector">
                    <label class="language-label">语音播报语言</label>
                    <select class="language-select" id="languageSelect" onchange="changeLanguage()">
                        <option value="mandarin" selected>普通话</option>
                        <option value="hefei">合肥话</option>
                        <option value="english">English</option>
                    </select>
                </div>

                <div class="feedback-section">
                    <div class="feedback-title">识别结果准确吗？</div>
                    <div class="feedback-buttons">
                        <button class="feedback-btn correct" onclick="submitFeedback(true)">✓ 正确</button>
                        <button class="feedback-btn incorrect" onclick="submitFeedback(false)">✗ 不正确</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentImage = null;
        let currentLanguage = 'mandarin';
        let currentRecognitionType = 'building';
        let recognitionData = null;

        const recognitionExamples = {
            'building': [
                { name: '信标台', image: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80' },
                { name: '航站楼', image: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80' },
                { name: '科普馆', image: 'https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80' }
            ],
            'plant': [
                { name: '樱花', image: 'https://images.unsplash.com/photo-1522383225653-ed111181a951?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80' },
                { name: '银杏', image: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80' },
                { name: '梧桐', image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80' }
            ]
        };

        function goBack() {
            window.history.back();
        }

        function selectRecognitionType(type) {
            currentRecognitionType = type;

            // Update UI
            document.querySelectorAll('.type-card').forEach(card => {
                card.classList.remove('active');
            });
            event.target.classList.add('active');

            // Show examples
            showTypeExamples(type);
        }

        function showTypeExamples(type) {
            const container = document.getElementById('typeExamples');
            const examples = recognitionExamples[type] || [];

            container.innerHTML = examples.map(example => `
                <div class="example-item" onclick="useExampleImage('${example.image}', '${example.name}')">
                    <div class="example-image" style="background-image: url('${example.image}')"></div>
                    <div class="example-name">${example.name}</div>
                </div>
            `).join('');
        }

        function useExampleImage(imageUrl, name) {
            const cameraArea = document.getElementById('cameraArea');
            const placeholder = document.getElementById('cameraPlaceholder');

            cameraArea.style.backgroundImage = `url('${imageUrl}')`;
            cameraArea.style.backgroundSize = 'cover';
            cameraArea.style.backgroundPosition = 'center';
            placeholder.style.display = 'none';

            currentImage = { url: imageUrl, name: name };
            document.getElementById('recognizeBtn').style.display = 'inline-block';

            showNotification(`已选择示例图片：${name}`, 'success');
        }

        function selectImage() {
            document.getElementById('imageInput').click();
        }

        function processImage() {
            const input = document.getElementById('imageInput');
            if (input.files && input.files[0]) {
                const file = input.files[0];
                const reader = new FileReader();

                reader.onload = function(e) {
                    currentImage = e.target.result;
                    displayUploadedImage(e.target.result);
                    document.getElementById('recognizeBtn').style.display = 'inline-block';
                    showNotification('图片上传成功，点击识别按钮开始识别', 'success');
                };

                reader.readAsDataURL(file);
            }
        }

        function displayUploadedImage(imageSrc) {
            const cameraArea = document.getElementById('cameraArea');
            const placeholder = document.getElementById('cameraPlaceholder');

            placeholder.style.display = 'none';
            cameraArea.style.background = `url('${imageSrc}') center/cover`;
        }

        function recognizeImage() {
            if (!currentImage) {
                showNotification('请先选择或上传图片', 'warning');
                return;
            }

            showNotification('正在识别中...', 'info');

            // Simulate recognition process
            setTimeout(() => {
                const mockResults = {
                    'building': {
                        '信标台': { accuracy: 95, tags: ['地标建筑', '观景台', '拍照打卡'], desc: '信标台是骆岗公园的标志性建筑，高约50米，原为合肥机场的导航设施。改造后成为公园的观景台，游客可以登顶俯瞰整个公园的美景。' },
                        '航站楼': { accuracy: 92, tags: ['历史建筑', '航空主题', '文化遗产'], desc: '保留的历史航站楼建筑，见证了合肥机场的发展历程，现已改造为展览空间。' },
                        '科普馆': { accuracy: 88, tags: ['科普教育', '互动体验', '航空知识'], desc: '航空科普馆提供丰富的航空知识和互动体验项目，是了解航空发展的绝佳场所。' }
                    },
                    'plant': {
                        '樱花': { accuracy: 93, tags: ['春季花卉', '观赏植物', '摄影热点'], desc: '樱花是春季最受欢迎的观赏花卉，花期短暂而美丽，盛开时如云似霞。' },
                        '银杏': { accuracy: 90, tags: ['秋季观叶', '古树名木', '黄叶美景'], desc: '银杏树叶在秋季变成金黄色，是著名的观叶植物，有"活化石"之称。' },
                        '梧桐': { accuracy: 87, tags: ['行道树', '遮阴植物', '城市绿化'], desc: '梧桐是常见的行道树，具有良好的遮阴效果，叶大荫浓。' }
                    }
                };

                const typeResults = mockResults[currentRecognitionType];
                const resultNames = Object.keys(typeResults);
                const randomResult = resultNames[Math.floor(Math.random() * resultNames.length)];
                const result = typeResults[randomResult];

                recognitionData = {
                    name: randomResult,
                    accuracy: result.accuracy,
                    tags: result.tags,
                    description: result.desc,
                    image: recognitionExamples[currentRecognitionType].find(ex => ex.name === randomResult)?.image || currentImage
                };

                displayRecognitionResult(recognitionData);
                showNotification('识别完成！', 'success');
            }, 2000);
        }

        function clearImage() {
            currentImage = null;
            const cameraArea = document.getElementById('cameraArea');
            const placeholder = document.getElementById('cameraPlaceholder');
            
            cameraArea.style.background = '#f8f9fa';
            placeholder.style.display = 'block';
            document.getElementById('recognitionResult').style.display = 'none';
            document.getElementById('imageInput').value = '';
        }

        function recognizeImage() {
            if (!currentImage) return;

            // Show loading
            document.getElementById('loadingSection').style.display = 'block';
            document.getElementById('recognitionResult').style.display = 'none';

            // Simulate AI recognition process
            setTimeout(() => {
                const recognitionData = simulateRecognition();
                displayRecognitionResult(recognitionData);
                
                document.getElementById('loadingSection').style.display = 'none';
                document.getElementById('recognitionResult').style.display = 'block';
                
                showNotification('图片识别完成！', 'success');
            }, 2000);
        }

        function simulateRecognition() {
            // Simulate different recognition results
            const attractions = [
                {
                    name: '信标台',
                    image: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                    tags: ['地标建筑', '观景台', '拍照打卡'],
                    meta: '高度：约50米<br>开放时间：全天<br>门票：免费',
                    description: '信标台是骆岗公园的标志性建筑，高约50米，原为合肥机场的导航设施。改造后成为公园的观景台，游客可以登顶俯瞰整个公园的美景。建筑设计融合了现代与历史元素，是拍照打卡的热门地点。'
                },
                {
                    name: '梦想大草坪',
                    image: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                    tags: ['休闲区域', '野餐', '活动场地'],
                    meta: '面积：约100亩<br>开放时间：全天<br>门票：免费',
                    description: '梦想大草坪是骆岗公园最大的开阔区域，占地约100亩。这里是举办大型活动和市民休闲的理想场所，也是野餐、放风筝、户外运动的热门地点。草坪四季常绿，为游客提供了亲近自然的绝佳空间。'
                },
                {
                    name: '航空科普馆',
                    image: 'https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
                    tags: ['科普教育', '航空主题', '互动体验'],
                    meta: '开放时间：9:00-17:00<br>门票：20元<br>适合年龄：全年龄',
                    description: '航空科普馆以航空为主题，展示了航空发展历史和现代航空技术。馆内设有飞行模拟器、航空知识互动展示等设施，是进行科普教育和亲子游览的理想场所。'
                }
            ];

            // Randomly select an attraction for demo
            return attractions[Math.floor(Math.random() * attractions.length)];
        }

        function displayRecognitionResult(data) {
            document.getElementById('attractionName').textContent = data.name;
            document.getElementById('resultImage').style.backgroundImage = `url('${data.image}')`;
            document.getElementById('attractionMeta').innerHTML = data.meta;
            document.getElementById('attractionDescription').textContent = data.description;
            
            // Update tags
            const tagsContainer = document.getElementById('attractionTags');
            tagsContainer.innerHTML = data.tags.map(tag => 
                `<span class="attraction-tag">${tag}</span>`
            ).join('');
        }

        function playAudio() {
            const attractionName = document.getElementById('attractionName').textContent;
            const language = document.getElementById('languageSelect').value;
            
            const messages = {
                'mandarin': `正在播放${attractionName}的普通话介绍...`,
                'hefei': `正在播放${attractionName}的合肥话介绍...`,
                'english': `Playing English introduction for ${attractionName}...`
            };
            
            showNotification(messages[language], 'info');
            
            // Simulate audio playback
            setTimeout(() => {
                showNotification('语音播报完成', 'success');
            }, 3000);
        }

        function changeLanguage() {
            const language = document.getElementById('languageSelect').value;
            currentLanguage = language;
            
            const languageNames = {
                'mandarin': '普通话',
                'hefei': '合肥话',
                'english': '英语'
            };
            
            showNotification(`已切换到${languageNames[language]}模式`, 'info');
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Add drag and drop functionality
            const cameraArea = document.getElementById('cameraArea');
            
            cameraArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                cameraArea.style.borderColor = '#2980b9';
                cameraArea.style.background = '#e3f2fd';
            });
            
            cameraArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                cameraArea.style.borderColor = '#3498db';
                cameraArea.style.background = '#f8f9fa';
            });
            
            cameraArea.addEventListener('drop', function(e) {
                e.preventDefault();
                cameraArea.style.borderColor = '#3498db';
                cameraArea.style.background = '#f8f9fa';
                
                const files = e.dataTransfer.files;
                if (files.length > 0 && files[0].type.startsWith('image/')) {
                    const file = files[0];
                    const reader = new FileReader();
                    
                    reader.onload = function(e) {
                        currentImage = e.target.result;
                        displayUploadedImage(e.target.result);
                        recognizeImage();
                    };
                    
                    reader.readAsDataURL(file);
                }
            });
        });

        function submitFeedback(isCorrect) {
            // Update UI
            document.querySelectorAll('.feedback-btn').forEach(btn => {
                btn.classList.remove('selected');
            });

            const selectedBtn = event.target;
            selectedBtn.classList.add('selected');

            const feedbackText = isCorrect ? '感谢您的反馈！' : '感谢反馈，我们会持续改进识别准确性';
            showNotification(feedbackText, 'success');

            // Here you would typically send feedback to server
            console.log('Feedback submitted:', {
                result: recognitionData,
                isCorrect: isCorrect,
                timestamp: new Date().toISOString()
            });
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            selectRecognitionType('building');
        });
    </script>
</body>
</html>
