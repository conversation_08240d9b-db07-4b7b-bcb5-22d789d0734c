<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 骆岗公园</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #2c3e50;
        }

        .app-container {
            max-width: 414px;
            margin: 0 auto;
            background: #f8fffe;
            min-height: 100vh;
            position: relative;
        }

        .header {
            background: white;
            padding: 16px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .back-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            margin-right: 16px;
            color: #3498db;
        }

        .header-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }

        .content {
            padding: 16px;
        }

        .section {
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 12px;
            color: #2c3e50;
        }

        /* User Info */
        .user-info {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
            margin-bottom: 20px;
        }

        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3498db, #2ecc71);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
            margin: 0 auto 12px;
        }

        .user-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .user-stats {
            display: flex;
            justify-content: space-around;
            margin-top: 16px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 20px;
            font-weight: bold;
            color: #3498db;
        }

        .stat-label {
            font-size: 12px;
            color: #7f8c8d;
        }

        /* Travel Logs */
        .travel-log-item {
            background: white;
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            gap: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .travel-log-item:hover {
            transform: translateY(-2px);
        }

        .log-image {
            width: 80px;
            height: 60px;
            background-size: cover;
            background-position: center;
            border-radius: 8px;
            flex-shrink: 0;
        }

        .log-info {
            flex: 1;
        }

        .log-title {
            font-weight: bold;
            margin-bottom: 4px;
            font-size: 14px;
        }

        .log-date {
            font-size: 12px;
            color: #7f8c8d;
        }

        /* Orders */
        .order-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s;
        }

        .order-item:hover {
            transform: translateY(-2px);
        }

        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .order-type {
            font-weight: bold;
            color: #2c3e50;
        }

        .order-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            color: white;
        }

        .status-paid {
            background: #27ae60;
        }

        .status-used {
            background: #95a5a6;
        }

        .order-time {
            font-size: 12px;
            color: #7f8c8d;
        }

        /* QR Code Modal */
        .qr-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .qr-modal-content {
            background: white;
            border-radius: 12px;
            padding: 24px;
            text-align: center;
            max-width: 280px;
            width: 90%;
        }

        .qr-code {
            width: 200px;
            height: 200px;
            background: #f8f9fa;
            border: 2px dashed #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 16px auto;
            border-radius: 8px;
            font-size: 48px;
        }

        .qr-title {
            font-weight: bold;
            margin-bottom: 8px;
        }

        .qr-desc {
            font-size: 12px;
            color: #7f8c8d;
            margin-bottom: 16px;
        }

        .close-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #7f8c8d;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .empty-text {
            font-size: 14px;
        }

        /* Notifications */
        .notification {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 12px 24px;
            border-radius: 8px;
            background: #3498db;
            color: white;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            animation: fadeIn 0.3s, fadeOut 0.3s 2.7s;
        }

        .notification.success {
            background: #27ae60;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translate(-50%, 20px); }
            to { opacity: 1; transform: translate(-50%, 0); }
        }

        @keyframes fadeOut {
            from { opacity: 1; transform: translate(-50%, 0); }
            to { opacity: 0; transform: translate(-50%, 20px); }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">←</button>
            <div class="header-title">👤 个人中心</div>
        </div>

        <div class="content">
            <!-- User Info -->
            <div class="user-info">
                <div class="user-avatar">👤</div>
                <div class="user-name">游客用户</div>
                <div class="user-stats">
                    <div class="stat-item">
                        <div class="stat-number">3</div>
                        <div class="stat-label">游记</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">1250</div>
                        <div class="stat-label">积分</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">8</div>
                        <div class="stat-label">打卡</div>
                    </div>
                </div>
            </div>

            <!-- My Travel Logs -->
            <div class="section">
                <div class="section-title">我的游记</div>
                <div id="travelLogsList">
                    <!-- Travel logs will be loaded here -->
                </div>
            </div>

            <!-- My Orders -->
            <div class="section">
                <div class="section-title">我的订单</div>
                <div id="ordersList">
                    <!-- Orders will be loaded here -->
                </div>
            </div>
        </div>

        <!-- QR Code Modal -->
        <div class="qr-modal" id="qrModal">
            <div class="qr-modal-content">
                <div class="qr-title" id="qrTitle">无人巴士乘车码</div>
                <div class="qr-desc" id="qrDesc">请向司机出示此二维码</div>
                <div class="qr-code">📱</div>
                <button class="close-btn" onclick="closeQRModal()">关闭</button>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }

        // Sample data
        const userTravelLogs = [
            {
                title: "骆岗公园春日游记",
                image: "https://images.unsplash.com/photo-1522383225653-ed111181a951?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                date: "2024-03-18"
            },
            {
                title: "带娃逛骆岗",
                image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                date: "2024-03-16"
            },
            {
                title: "骆岗夜景摄影",
                image: "https://images.unsplash.com/photo-1519501025264-65ba15a82390?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                date: "2024-03-15"
            }
        ];

        const userOrders = [
            {
                type: "无人巴士",
                status: "paid",
                time: "2024-04-15 09:30",
                canShowQR: true
            },
            {
                type: "接驳车",
                status: "used",
                time: "2024-04-10 14:20",
                canShowQR: false
            },
            {
                type: "无人巴士",
                status: "paid",
                time: "2024-04-08 16:45",
                canShowQR: true
            }
        ];

        function loadTravelLogs() {
            const container = document.getElementById('travelLogsList');
            
            if (userTravelLogs.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">📝</div>
                        <div class="empty-text">还没有发布游记<br>快去记录你的美好时光吧！</div>
                    </div>
                `;
                return;
            }

            container.innerHTML = userTravelLogs.map(log => `
                <div class="travel-log-item" onclick="viewTravelLog('${log.title}')">
                    <div class="log-image" style="background-image: url('${log.image}')"></div>
                    <div class="log-info">
                        <div class="log-title">${log.title}</div>
                        <div class="log-date">发布于 ${log.date}</div>
                    </div>
                </div>
            `).join('');
        }

        function loadOrders() {
            const container = document.getElementById('ordersList');
            
            if (userOrders.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">📋</div>
                        <div class="empty-text">暂无订单记录</div>
                    </div>
                `;
                return;
            }

            const statusText = {
                'paid': '已支付',
                'used': '已使用'
            };

            const statusClass = {
                'paid': 'status-paid',
                'used': 'status-used'
            };

            container.innerHTML = userOrders.map(order => `
                <div class="order-item" onclick="handleOrderClick('${order.type}', ${order.canShowQR})">
                    <div class="order-header">
                        <div class="order-type">${order.type}</div>
                        <div class="order-status ${statusClass[order.status]}">${statusText[order.status]}</div>
                    </div>
                    <div class="order-time">${order.time}</div>
                </div>
            `).join('');
        }

        function viewTravelLog(title) {
            showNotification(`查看游记: ${title}`, 'success');
        }

        function handleOrderClick(type, canShowQR) {
            if (type === '无人巴士' && canShowQR) {
                showQRCode(type);
            } else {
                showNotification(`查看${type}订单详情`, 'success');
            }
        }

        function showQRCode(type) {
            document.getElementById('qrTitle').textContent = `${type}乘车码`;
            document.getElementById('qrDesc').textContent = '请向司机出示此二维码';
            document.getElementById('qrModal').style.display = 'flex';
        }

        function closeQRModal() {
            document.getElementById('qrModal').style.display = 'none';
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }

        // Load data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadTravelLogs();
            loadOrders();
        });
    </script>
</body>
</html>
