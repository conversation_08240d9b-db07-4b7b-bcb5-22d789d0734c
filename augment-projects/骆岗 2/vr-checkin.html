<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VR打卡 - 骆岗公园</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #2c3e50;
        }

        .app-container {
            max-width: 414px;
            margin: 0 auto;
            background: rgba(255,255,255,0.95);
            min-height: 100vh;
            position: relative;
            backdrop-filter: blur(10px);
        }

        .header {
            background: linear-gradient(135deg, #3498db, #2ecc71);
            color: white;
            padding: 20px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }

        .header-title {
            font-size: 20px;
            font-weight: bold;
        }

        .content {
            padding: 20px 16px;
        }

        .section {
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 12px;
            color: #2c3e50;
        }

        .scene-selection {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .scene-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .scene-card {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            color: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }

        .scene-card:nth-child(2) {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .scene-card:nth-child(3) {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }

        .scene-card:nth-child(4) {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
        }

        .scene-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .scene-card.completed {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
        }

        .scene-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }

        .scene-title {
            font-weight: bold;
            margin-bottom: 4px;
        }

        .scene-desc {
            font-size: 12px;
            opacity: 0.9;
        }

        .scene-status {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 20px;
            height: 20px;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .runway-path {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: none;
        }

        .runway-map {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #a8edea, #fed6e3);
            border-radius: 12px;
            position: relative;
            margin-bottom: 16px;
            overflow: hidden;
        }

        .runway-line {
            position: absolute;
            top: 50%;
            left: 10%;
            right: 10%;
            height: 4px;
            background: #2c3e50;
            transform: translateY(-50%);
        }

        .runway-line::before,
        .runway-line::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 2px;
            height: 20px;
            background: white;
            transform: translateY(-50%);
        }

        .runway-line::before {
            left: 25%;
        }

        .runway-line::after {
            right: 25%;
        }

        .checkin-point {
            position: absolute;
            width: 16px;
            height: 16px;
            background: #e74c3c;
            border: 2px solid white;
            border-radius: 50%;
            cursor: pointer;
            animation: pulse 2s infinite;
            top: 50%;
            transform: translateY(-50%);
        }

        .checkin-point.completed {
            background: #27ae60;
            animation: none;
        }

        .checkin-point:nth-child(2) { left: 15%; }
        .checkin-point:nth-child(3) { left: 35%; }
        .checkin-point:nth-child(4) { left: 55%; }
        .checkin-point:nth-child(5) { left: 75%; }

        @keyframes pulse {
            0% { transform: translateY(-50%) scale(1); }
            50% { transform: translateY(-50%) scale(1.2); }
            100% { transform: translateY(-50%) scale(1); }
        }

        .checkin-info {
            text-align: center;
            margin-bottom: 16px;
        }

        .checkin-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .checkin-desc {
            font-size: 14px;
            color: #7f8c8d;
            line-height: 1.4;
        }

        .progress-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .progress-title {
            font-weight: bold;
            color: #2c3e50;
        }

        .points-display {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: bold;
        }

        .progress-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 16px;
        }

        .stat-item {
            text-align: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .stat-value {
            font-size: 20px;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #7f8c8d;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: #ecf0f1;
            border-radius: 6px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            transition: width 0.3s ease;
            border-radius: 6px;
        }

        .progress-text {
            text-align: center;
            font-size: 12px;
            color: #7f8c8d;
        }

        .vr-experience {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-top: 20px;
            display: none;
        }

        .vr-viewer {
            width: 100%;
            height: 250px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            margin-bottom: 16px;
            position: relative;
            overflow: hidden;
        }

        .vr-viewer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="white" opacity="0.3"/><circle cx="20" cy="30" r="1" fill="white" opacity="0.5"/><circle cx="80" cy="20" r="1.5" fill="white" opacity="0.4"/><circle cx="70" cy="80" r="1" fill="white" opacity="0.6"/></svg>');
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .vr-controls {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.3s;
            flex: 1;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #ecf0f1;
            color: #2c3e50;
        }

        .btn-secondary:hover {
            background: #d5dbdb;
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
        }

        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #27ae60;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 3000;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .notification.show {
            opacity: 1;
        }

        .notification.error {
            background: #e74c3c;
        }

        .notification.warning {
            background: #f39c12;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">←</button>
            <div class="header-title">🥽 VR打卡体验</div>
        </div>

        <div class="content">
            <!-- Scene Selection -->
            <div class="section">
                <div class="section-title">🎮 选择VR场景</div>
                <div class="scene-selection">
                    <div class="scene-grid">
                        <div class="scene-card" onclick="selectScene('runway')">
                            <div class="scene-status" id="runway-status">○</div>
                            <div class="scene-icon">🛫</div>
                            <div class="scene-title">机场跑道</div>
                            <div class="scene-desc">沿跑道VR漫步</div>
                        </div>
                        <div class="scene-card" onclick="selectScene('tower')">
                            <div class="scene-status" id="tower-status">○</div>
                            <div class="scene-icon">🗼</div>
                            <div class="scene-title">信标台顶部</div>
                            <div class="scene-desc">360度全景体验</div>
                        </div>
                        <div class="scene-card" onclick="selectScene('grassland')">
                            <div class="scene-status" id="grassland-status">○</div>
                            <div class="scene-icon">🌱</div>
                            <div class="scene-title">梦想大草坪</div>
                            <div class="scene-desc">草坪野餐体验</div>
                        </div>
                        <div class="scene-card" onclick="selectScene('museum')">
                            <div class="scene-status" id="museum-status">○</div>
                            <div class="scene-icon">✈️</div>
                            <div class="scene-title">航空科普馆</div>
                            <div class="scene-desc">飞行模拟体验</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Runway Path (Special Scene) -->
            <div class="runway-path" id="runwayPath">
                <div class="section-title">🛫 机场跑道打卡路径</div>
                <div class="runway-map">
                    <div class="runway-line"></div>
                    <div class="checkin-point" onclick="checkinAtPoint(1)" title="起点"></div>
                    <div class="checkin-point" onclick="checkinAtPoint(2)" title="跑道中段1"></div>
                    <div class="checkin-point" onclick="checkinAtPoint(3)" title="跑道中段2"></div>
                    <div class="checkin-point" onclick="checkinAtPoint(4)" title="终点"></div>
                </div>
                <div class="checkin-info">
                    <div class="checkin-title">沿着机场跑道进行VR体验</div>
                    <div class="checkin-desc">点击地图上的打卡点开始VR体验，完成所有打卡点可获得额外积分奖励</div>
                </div>
            </div>

            <!-- Progress Section -->
            <div class="section">
                <div class="section-title">🎯 打卡进度</div>
                <div class="progress-section">
                    <div class="progress-header">
                        <div class="progress-title">我的积分</div>
                        <div class="points-display" id="pointsDisplay">1,250分</div>
                    </div>
                    
                    <div class="progress-stats">
                        <div class="stat-item">
                            <div class="stat-value" id="completedScenes">0</div>
                            <div class="stat-label">已完成场景</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="totalPoints">0</div>
                            <div class="stat-label">本次获得积分</div>
                        </div>
                    </div>

                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                    </div>
                    <div class="progress-text" id="progressText">已完成 0/4 个VR场景</div>
                </div>
            </div>

            <!-- VR Experience -->
            <div class="vr-experience" id="vrExperience">
                <div class="section-title" id="vrTitle">🥽 VR体验中</div>
                <div class="vr-viewer" id="vrViewer">
                    <div style="position: relative; z-index: 2;">VR场景加载中...</div>
                </div>
                <div class="vr-controls">
                    <button class="btn btn-secondary" onclick="exitVR()">退出VR</button>
                    <button class="btn btn-success" onclick="completeCheckin()" id="checkinBtn">完成打卡</button>
                </div>
                <div style="text-align: center; font-size: 12px; color: #7f8c8d; margin-top: 8px;">
                    请将手机横屏并使用VR眼镜获得最佳体验
                </div>
            </div>
        </div>
    </div>

    <script>
        let userProgress = {
            points: 1250,
            completedScenes: [],
            runwayPoints: [],
            totalEarned: 0
        };

        let currentScene = null;
        let currentRunwayPoint = null;

        function goBack() {
            window.history.back();
        }

        function selectScene(sceneType) {
            currentScene = sceneType;
            
            if (sceneType === 'runway') {
                document.getElementById('runwayPath').style.display = 'block';
                document.getElementById('runwayPath').scrollIntoView({ behavior: 'smooth' });
            } else {
                document.getElementById('runwayPath').style.display = 'none';
                startVRExperience(sceneType);
            }
        }

        function checkinAtPoint(pointIndex) {
            currentRunwayPoint = pointIndex;
            currentScene = 'runway';
            startVRExperience('runway', pointIndex);
        }

        function startVRExperience(sceneType, pointIndex = null) {
            const sceneNames = {
                'runway': pointIndex ? `机场跑道 - 打卡点${pointIndex}` : '机场跑道VR体验',
                'tower': '信标台360度全景',
                'grassland': '梦想大草坪VR漫步',
                'museum': '航空科普馆虚拟参观'
            };

            const sceneDescriptions = {
                'runway': pointIndex ? `正在体验跑道第${pointIndex}个打卡点...` : '正在体验机场跑道VR漫步...',
                'tower': '正在加载信标台360度全景体验...',
                'grassland': '正在加载梦想大草坪VR体验...',
                'museum': '正在加载航空科普馆VR体验...'
            };

            document.getElementById('vrTitle').textContent = `🥽 ${sceneNames[sceneType]}`;
            document.getElementById('vrViewer').innerHTML = `
                <div style="position: relative; z-index: 2;">${sceneDescriptions[sceneType]}</div>
            `;
            
            document.getElementById('vrExperience').style.display = 'block';
            document.getElementById('vrExperience').scrollIntoView({ behavior: 'smooth' });

            // Simulate VR loading
            setTimeout(() => {
                document.getElementById('vrViewer').innerHTML = `
                    <div style="position: relative; z-index: 2;">VR体验进行中... 🥽</div>
                `;
                showNotification(`${sceneNames[sceneType]}体验开始！`, 'info');
            }, 2000);
        }

        function completeCheckin() {
            if (!currentScene) return;

            let pointsEarned = 50;
            let sceneKey = currentScene;

            if (currentScene === 'runway' && currentRunwayPoint) {
                sceneKey = `runway-${currentRunwayPoint}`;
                if (!userProgress.runwayPoints.includes(currentRunwayPoint)) {
                    userProgress.runwayPoints.push(currentRunwayPoint);
                    
                    // Mark runway point as completed
                    const points = document.querySelectorAll('.checkin-point');
                    if (points[currentRunwayPoint - 1]) {
                        points[currentRunwayPoint - 1].classList.add('completed');
                    }

                    // Bonus for completing all runway points
                    if (userProgress.runwayPoints.length === 4) {
                        pointsEarned += 100; // Bonus points
                        showNotification('🎉 完成所有跑道打卡点，获得额外100积分奖励！', 'success');
                    }
                } else {
                    showNotification('该打卡点已完成', 'warning');
                    return;
                }
            } else {
                if (userProgress.completedScenes.includes(sceneKey)) {
                    showNotification('该场景已完成', 'warning');
                    return;
                }
                userProgress.completedScenes.push(sceneKey);
            }

            // Update points
            userProgress.points += pointsEarned;
            userProgress.totalEarned += pointsEarned;

            // Update UI
            updateProgressDisplay();
            markSceneCompleted(currentScene);

            showNotification(`VR体验完成，获得${pointsEarned}积分！`, 'success');

            // Save progress
            localStorage.setItem('vrProgress', JSON.stringify(userProgress));

            // Exit VR
            setTimeout(() => {
                exitVR();
            }, 2000);
        }

        function exitVR() {
            document.getElementById('vrExperience').style.display = 'none';
            currentScene = null;
            currentRunwayPoint = null;
        }

        function markSceneCompleted(sceneType) {
            const statusElement = document.getElementById(`${sceneType}-status`);
            if (statusElement) {
                statusElement.textContent = '✓';
                statusElement.parentElement.classList.add('completed');
            }
        }

        function updateProgressDisplay() {
            document.getElementById('pointsDisplay').textContent = userProgress.points + '分';
            
            // Count unique scenes (runway counts as one scene regardless of points)
            const uniqueScenes = new Set(userProgress.completedScenes.map(scene => 
                scene.startsWith('runway') ? 'runway' : scene
            ));
            
            document.getElementById('completedScenes').textContent = uniqueScenes.size;
            document.getElementById('totalPoints').textContent = userProgress.totalEarned;
            
            const progress = (uniqueScenes.size / 4) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('progressText').textContent = `已完成 ${uniqueScenes.size}/4 个VR场景`;
        }

        function loadProgress() {
            const saved = localStorage.getItem('vrProgress');
            if (saved) {
                userProgress = { ...userProgress, ...JSON.parse(saved) };
                updateProgressDisplay();
                
                // Mark completed scenes
                userProgress.completedScenes.forEach(scene => {
                    const sceneType = scene.startsWith('runway') ? 'runway' : scene;
                    markSceneCompleted(sceneType);
                });

                // Mark completed runway points
                userProgress.runwayPoints.forEach(pointIndex => {
                    const points = document.querySelectorAll('.checkin-point');
                    if (points[pointIndex - 1]) {
                        points[pointIndex - 1].classList.add('completed');
                    }
                });
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadProgress();
            
            // Add device orientation change handler for better VR experience
            window.addEventListener('orientationchange', function() {
                if (document.getElementById('vrExperience').style.display === 'block') {
                    setTimeout(() => {
                        showNotification('请保持横屏模式以获得最佳VR体验', 'info');
                    }, 500);
                }
            });
        });
    </script>
</body>
</html>
