/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #FFF8E1 0%, #FFE0B2 100%);
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部导航 */
.header {
    background: linear-gradient(135deg, #FF8F00 0%, #FFA726 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(255, 143, 0, 0.3);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
}

.logo i {
    margin-right: 0.5rem;
    font-size: 1.8rem;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.points {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
}

.avatar {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

/* 主要内容区域 */
.main {
    margin-top: 80px;
    padding: 2rem 0;
}

.section {
    display: none;
}

.section.active {
    display: block;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    color: #FF8F00;
    margin-bottom: 2rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

/* 统计数据面板 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #FF8F00, #FFA726);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-info h3 {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #FF8F00;
    margin-bottom: 0.25rem;
}

.stat-change {
    font-size: 0.8rem;
    color: #666;
}

.stat-change.positive {
    color: #4CAF50;
}

/* 热力图谱 */
.heatmap-section {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 3rem;
}

.heatmap-section h2 {
    color: #FF8F00;
    margin-bottom: 1.5rem;
    text-align: center;
}

.heatmap-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.time-btn {
    padding: 0.5rem 1.5rem;
    border: 2px solid #FF8F00;
    background: transparent;
    color: #FF8F00;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.time-btn.active,
.time-btn:hover {
    background: #FF8F00;
    color: white;
}

.heatmap-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.heatmap-item {
    display: grid;
    grid-template-columns: 2fr 1fr 2fr 1fr;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.heatmap-item.high {
    background: linear-gradient(135deg, #FFEBEE, #FFCDD2);
    border-left: 4px solid #F44336;
}

.heatmap-item.medium {
    background: linear-gradient(135deg, #FFF3E0, #FFE0B2);
    border-left: 4px solid #FF9800;
}

.heatmap-item.low {
    background: linear-gradient(135deg, #E8F5E8, #C8E6C9);
    border-left: 4px solid #4CAF50;
}

.location {
    font-weight: bold;
    color: #333;
}

.visitors {
    text-align: center;
    font-weight: bold;
}

.capacity-bar {
    background: #E0E0E0;
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
}

.capacity-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #FF9800, #F44336);
    transition: width 0.3s ease;
}

.status {
    text-align: center;
    font-size: 0.9rem;
    font-weight: bold;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
}

.status.normal {
    background: #E8F5E8;
    color: #4CAF50;
}

.status.warning {
    background: #FFF3E0;
    color: #FF9800;
}

.status.low {
    background: #E8F5E8;
    color: #4CAF50;
}

/* 线路工厂 */
.route-factory {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.route-factory h2 {
    color: #FF8F00;
    margin-bottom: 1.5rem;
    text-align: center;
}

.route-themes {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.theme-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid #FF8F00;
    background: transparent;
    color: #FF8F00;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.theme-btn.active,
.theme-btn:hover {
    background: #FF8F00;
    color: white;
    transform: translateY(-2px);
}

.route-preview {
    display: flex;
    justify-content: center;
}

.route-card {
    background: linear-gradient(135deg, #FFF8E1, #FFE0B2);
    padding: 2rem;
    border-radius: 15px;
    border: 2px solid #FF8F00;
    max-width: 500px;
    width: 100%;
}

.route-card h3 {
    color: #FF8F00;
    margin-bottom: 1rem;
    text-align: center;
}

.route-path {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.route-stop {
    background: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    color: #FF8F00;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.route-path i {
    color: #FF8F00;
}

.route-info {
    display: flex;
    justify-content: space-around;
    margin-bottom: 1.5rem;
    text-align: center;
}

.route-info span {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    color: #666;
}

.route-info i {
    color: #FF8F00;
}

/* 按钮样式 */
.btn-primary {
    background: linear-gradient(135deg, #FF8F00, #FFA726);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    display: block;
    margin: 0 auto;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 143, 0, 0.4);
}

.btn-secondary {
    background: transparent;
    color: #FF8F00;
    border: 2px solid #FF8F00;
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #FF8F00;
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #F44336, #E57373);
    color: white;
    border: none;
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(244, 67, 54, 0.4);
}

/* 智能助手浮动按钮 */
.ai-assistant {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background: linear-gradient(135deg, #FF8F00, #FFA726);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 50px;
    cursor: pointer;
    box-shadow: 0 5px 20px rgba(255, 143, 0, 0.4);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: bold;
    transition: all 0.3s ease;
    z-index: 1000;
}

.ai-assistant:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 143, 0, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav {
        gap: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .heatmap-item {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 0.5rem;
    }
    
    .route-themes {
        flex-direction: column;
        align-items: center;
    }
    
    .route-path {
        flex-direction: column;
    }
    
    .ai-assistant span {
        display: none;
    }
}

/* 骆岗公园页面样式 */
.park-header {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.park-header h1 {
    color: #FF8F00;
    text-align: center;
    margin-bottom: 1.5rem;
}

.park-status {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.status-item {
    text-align: center;
    padding: 1rem;
    background: linear-gradient(135deg, #FFF8E1, #FFE0B2);
    border-radius: 10px;
}

.status-label {
    display: block;
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.status-value {
    font-weight: bold;
    color: #FF8F00;
    font-size: 1.1rem;
}

.status-value.warning {
    color: #F44336;
}

/* 标签页样式 */
.tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.tab-btn {
    padding: 0.75rem 2rem;
    border: 2px solid #FF8F00;
    background: transparent;
    color: #FF8F00;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.tab-btn.active,
.tab-btn:hover {
    background: #FF8F00;
    color: white;
    transform: translateY(-2px);
}

.tab-content {
    display: none;
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.tab-content.active {
    display: block;
}

/* 全景导览样式 */
.panorama-container {
    text-align: center;
}

.view-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.view-btn {
    padding: 0.5rem 1.5rem;
    border: 2px solid #FF8F00;
    background: transparent;
    color: #FF8F00;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-btn.active,
.view-btn:hover {
    background: #FF8F00;
    color: white;
}

.panorama-viewer {
    position: relative;
    height: 400px;
    background: linear-gradient(135deg, #E3F2FD, #BBDEFB);
    border-radius: 15px;
    overflow: hidden;
}

.panorama-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #666;
}

.panorama-placeholder i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #FF8F00;
}

.panorama-hotspots {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.hotspot {
    position: absolute;
    width: 40px;
    height: 40px;
    background: #FF8F00;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
}

.hotspot:hover {
    transform: scale(1.2);
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 143, 0, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 143, 0, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 143, 0, 0); }
}

/* 智能规划样式 */
.planning-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
}

.planning-sidebar {
    background: #FFF8E1;
    padding: 1.5rem;
    border-radius: 10px;
}

.planning-sidebar h3 {
    color: #FF8F00;
    margin-bottom: 1rem;
}

.mode-selector {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.mode-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.mode-option:hover {
    background: rgba(255, 143, 0, 0.1);
}

.budget-calculator {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.budget-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #E0E0E0;
}

.budget-slider {
    width: 100px;
    margin: 0 0.5rem;
}

.transport-select {
    padding: 0.25rem 0.5rem;
    border: 1px solid #E0E0E0;
    border-radius: 5px;
}

.budget-total {
    background: #FF8F00;
    color: white;
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
    margin-top: 1rem;
}

.planning-main {
    background: #FAFAFA;
    padding: 1.5rem;
    border-radius: 10px;
}

.route-timeline {
    margin-bottom: 2rem;
}

.timeline-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 30px;
    top: 60px;
    width: 2px;
    height: 40px;
    background: #FF8F00;
}

.timeline-time {
    background: #FF8F00;
    color: white;
    padding: 0.5rem;
    border-radius: 10px;
    font-weight: bold;
    min-width: 60px;
    text-align: center;
}

.timeline-content {
    flex: 1;
    background: white;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.timeline-content h4 {
    color: #FF8F00;
    margin-bottom: 0.5rem;
}

.timeline-tag {
    background: #FFE0B2;
    color: #FF8F00;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
}

.route-optimize {
    width: 100%;
}

/* 深度体验样式 */
.experience-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.experience-card {
    background: linear-gradient(135deg, #FFF8E1, #FFE0B2);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    transition: transform 0.3s ease;
}

.experience-card:hover {
    transform: translateY(-5px);
}

.experience-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #FF8F00, #FFA726);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 2rem;
}

.experience-card h3 {
    color: #FF8F00;
    margin-bottom: 1rem;
}

.experience-info {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin: 1rem 0;
    color: #666;
}

.upload-area {
    border: 2px dashed #FF8F00;
    padding: 2rem;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 1rem 0;
}

.upload-area:hover {
    background: rgba(255, 143, 0, 0.1);
}

.upload-area i {
    font-size: 2rem;
    color: #FF8F00;
    margin-bottom: 0.5rem;
}

/* 园区服务样式 */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-category {
    background: linear-gradient(135deg, #FFF8E1, #FFE0B2);
    padding: 1.5rem;
    border-radius: 15px;
}

.service-category h3 {
    color: #FF8F00;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.service-category ul {
    list-style: none;
}

.service-category li {
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 143, 0, 0.2);
    color: #666;
}

.service-category li:last-child {
    border-bottom: none;
}

/* 智能服务页面样式 */
.ai-chat-section {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 3rem;
}

.ai-chat-section h2 {
    color: #FF8F00;
    text-align: center;
    margin-bottom: 1.5rem;
}

.chat-container {
    max-width: 800px;
    margin: 0 auto;
}

.chat-messages {
    height: 300px;
    overflow-y: auto;
    padding: 1rem;
    background: #FAFAFA;
    border-radius: 10px;
    margin-bottom: 1rem;
}

.message {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.message.bot {
    justify-content: flex-start;
}

.message.user {
    justify-content: flex-end;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.message.bot .message-avatar {
    background: linear-gradient(135deg, #FF8F00, #FFA726);
}

.message.user .message-avatar {
    background: linear-gradient(135deg, #2196F3, #64B5F6);
}

.message-content {
    max-width: 70%;
    padding: 1rem;
    border-radius: 15px;
    background: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.chat-input-area {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.input-modes {
    display: flex;
    gap: 0.5rem;
}

.mode-btn {
    width: 40px;
    height: 40px;
    border: 2px solid #FF8F00;
    background: transparent;
    color: #FF8F00;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mode-btn.active,
.mode-btn:hover {
    background: #FF8F00;
    color: white;
}

.chat-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid #E0E0E0;
    border-radius: 25px;
    outline: none;
    transition: border-color 0.3s ease;
}

.chat-input:focus {
    border-color: #FF8F00;
}

.send-btn {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #FF8F00, #FFA726);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.send-btn:hover {
    transform: scale(1.1);
}

/* 快捷服务样式 */
.quick-services {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 3rem;
}

.quick-services h2 {
    color: #FF8F00;
    text-align: center;
    margin-bottom: 2rem;
}

.quick-services .services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.service-card {
    background: linear-gradient(135deg, #FFF8E1, #FFE0B2);
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #FF8F00, #FFA726);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 1.5rem;
}

.service-card h3 {
    color: #FF8F00;
    margin-bottom: 0.5rem;
}

.service-card p {
    color: #666;
    margin-bottom: 1rem;
}

.weather-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    margin: 1rem 0;
}

.weather-info span {
    font-weight: bold;
    color: #FF8F00;
}

.weather-info small {
    color: #666;
}

/* 智能游记生成样式 */
.travel-journal {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.travel-journal h2 {
    color: #FF8F00;
    text-align: center;
    margin-bottom: 2rem;
}

.journal-creator {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.upload-section h3 {
    color: #FF8F00;
    margin-bottom: 1.5rem;
}

.upload-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.upload-item {
    background: #FFF8E1;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    border: 2px dashed #FF8F00;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-item:hover {
    background: rgba(255, 143, 0, 0.1);
}

.upload-item i {
    font-size: 2rem;
    color: #FF8F00;
    margin-bottom: 0.5rem;
}

.upload-item p {
    color: #666;
    margin-bottom: 0.5rem;
}

.upload-item input[type="file"] {
    display: none;
}

.upload-item textarea {
    width: 100%;
    height: 60px;
    border: 1px solid #E0E0E0;
    border-radius: 5px;
    padding: 0.5rem;
    resize: none;
}

.generate-journal {
    width: 100%;
}

.journal-preview h3 {
    color: #FF8F00;
    margin-bottom: 1.5rem;
}

.journal-template {
    background: #FAFAFA;
    padding: 1.5rem;
    border-radius: 10px;
    border: 1px solid #E0E0E0;
}

.journal-cover {
    text-align: center;
    margin-bottom: 1rem;
}

.journal-cover img {
    width: 100%;
    max-width: 200px;
    height: 120px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 0.5rem;
}

.journal-cover h4 {
    color: #FF8F00;
}

.journal-content {
    color: #666;
    line-height: 1.6;
}

/* 积分中心样式 */
.points-overview {
    margin-bottom: 3rem;
}

.points-card.main {
    background: linear-gradient(135deg, #FF8F00, #FFA726);
    color: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(255, 143, 0, 0.3);
    display: flex;
    align-items: center;
    gap: 2rem;
    max-width: 600px;
    margin: 0 auto;
}

.points-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
}

.points-info {
    flex: 1;
}

.points-info h2 {
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.points-balance {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.points-level {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.9rem;
}

.points-progress {
    flex: 1;
}

.progress-bar {
    background: rgba(255, 255, 255, 0.2);
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: white;
    transition: width 0.3s ease;
}

.points-progress small {
    color: rgba(255, 255, 255, 0.8);
}

/* 积分获取样式 */
.points-earn {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 3rem;
}

.points-earn h2 {
    color: #FF8F00;
    text-align: center;
    margin-bottom: 2rem;
}

.earn-methods {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.earn-item {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #FFF8E1, #FFE0B2);
    border-radius: 15px;
    transition: transform 0.3s ease;
}

.earn-item:hover {
    transform: translateX(5px);
}

.earn-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #FF8F00, #FFA726);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.earn-info {
    flex: 1;
}

.earn-info h3 {
    color: #FF8F00;
    margin-bottom: 0.5rem;
}

.earn-info p {
    color: #666;
    margin-bottom: 0.5rem;
}

.earn-points {
    background: #4CAF50;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: bold;
    font-size: 0.9rem;
}

/* 积分兑换样式 */
.points-exchange {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 3rem;
}

.points-exchange h2 {
    color: #FF8F00;
    text-align: center;
    margin-bottom: 2rem;
}

.exchange-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.exchange-item {
    background: linear-gradient(135deg, #FFF8E1, #FFE0B2);
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    transition: transform 0.3s ease;
    border: 2px solid transparent;
}

.exchange-item:hover {
    transform: translateY(-5px);
    border-color: #FF8F00;
}

.exchange-image {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #FF8F00, #FFA726);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
    font-size: 2rem;
}

.exchange-item h3 {
    color: #FF8F00;
    margin-bottom: 0.5rem;
}

.exchange-desc {
    color: #666;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.exchange-price {
    margin-bottom: 1.5rem;
}

.points-cost {
    display: block;
    font-size: 1.2rem;
    font-weight: bold;
    color: #FF8F00;
    margin-bottom: 0.25rem;
}

.original-price {
    font-size: 0.8rem;
    color: #999;
    text-decoration: line-through;
}

/* 积分记录样式 */
.points-history {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.points-history h2 {
    color: #FF8F00;
    text-align: center;
    margin-bottom: 2rem;
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.history-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #FAFAFA;
    border-radius: 10px;
    transition: background 0.3s ease;
}

.history-item:hover {
    background: #F0F0F0;
}

.history-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.history-icon.earn {
    background: #4CAF50;
}

.history-icon.spend {
    background: #F44336;
}

.history-info {
    flex: 1;
}

.history-info h4 {
    color: #333;
    margin-bottom: 0.25rem;
}

.history-info p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.history-time {
    color: #999;
    font-size: 0.8rem;
}

.history-points {
    font-weight: bold;
    font-size: 1.1rem;
}

.history-points.earn {
    color: #4CAF50;
}

.history-points.spend {
    color: #F44336;
}

/* AI助手模态框样式 */
.ai-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.ai-modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.ai-modal-content {
    background: white;
    border-radius: 20px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.ai-modal-header {
    background: linear-gradient(135deg, #FF8F00, #FFA726);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-modal-header h3 {
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.ai-modal-body {
    padding: 2rem;
}

.ai-suggestions p {
    color: #666;
    margin-bottom: 1rem;
}

.suggestion-btn {
    display: block;
    width: 100%;
    background: transparent;
    border: 2px solid #FF8F00;
    color: #FF8F00;
    padding: 0.75rem;
    border-radius: 10px;
    cursor: pointer;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    text-align: left;
}

.suggestion-btn:hover {
    background: #FF8F00;
    color: white;
}

/* 响应式设计补充 */
@media (max-width: 768px) {
    .planning-container {
        grid-template-columns: 1fr;
    }

    .journal-creator {
        grid-template-columns: 1fr;
    }

    .points-card.main {
        flex-direction: column;
        text-align: center;
    }

    .earn-item {
        flex-direction: column;
        text-align: center;
    }

    .history-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
}
