<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>骆岗公园游玩助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fffe;
            min-height: 100vh;
            color: #333;
        }

        .app-container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        /* Header Banner */
        .header-banner {
            position: relative;
            height: 180px;
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.7), rgba(46, 204, 113, 0.7)),
                        url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80') center/cover;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
        }

        .banner-content {
            z-index: 2;
            width: 100%;
            padding: 0 20px;
        }

        .banner-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
            text-shadow: 0 2px 6px rgba(0,0,0,0.4);
        }

        .banner-description {
            font-size: 12px;
            line-height: 1.4;
            margin-bottom: 12px;
            text-shadow: 0 1px 3px rgba(0,0,0,0.3);
            opacity: 0.95;
        }

        .banner-tags {
            display: flex;
            gap: 6px;
            justify-content: center;
            flex-wrap: nowrap;
            overflow-x: auto;
            padding-bottom: 4px;
        }

        .banner-tag {
            background: linear-gradient(135deg, rgba(255,255,255,0.25), rgba(255,255,255,0.15));
            border: 1px solid rgba(255,255,255,0.3);
            padding: 4px 10px;
            border-radius: 16px;
            font-size: 10px;
            font-weight: 500;
            backdrop-filter: blur(15px);
            white-space: nowrap;
            flex-shrink: 0;
        }

        /* Content */
        .content {
            padding: 20px 16px 80px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .section {
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 12px;
            color: #2c3e50;
        }

        /* Activity Carousel */
        .activity-carousel {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding-bottom: 8px;
        }

        .activity-card {
            min-width: 280px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .activity-card:hover {
            transform: translateY(-2px);
        }

        .activity-image {
            width: 100%;
            height: 120px;
            background-size: cover;
            background-position: center;
        }

        .activity-info {
            padding: 12px;
        }

        .activity-title {
            font-weight: bold;
            margin-bottom: 4px;
            color: #2c3e50;
        }

        .activity-desc {
            font-size: 12px;
            color: #7f8c8d;
            margin-bottom: 8px;
        }

        .activity-status {
            background: #27ae60;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            display: inline-block;
        }

        /* Smart Services - Single Row */
        .smart-services {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            padding-bottom: 8px;
        }

        .service-card {
            min-width: 70px;
            background: linear-gradient(135deg, #3498db, #2ecc71);
            color: white;
            border-radius: 12px;
            padding: 12px 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
        }

        .service-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(52, 152, 219, 0.4);
        }

        .service-icon {
            font-size: 24px;
            margin-bottom: 6px;
        }

        .service-title {
            font-size: 11px;
            font-weight: bold;
        }

        /* Featured Attractions - 3 items */
        .attraction-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
        }

        .attraction-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s;
        }

        .attraction-card:hover {
            transform: translateY(-2px);
        }

        .attraction-image {
            width: 100%;
            height: 80px;
            background-size: cover;
            background-position: center;
        }

        .attraction-info {
            padding: 8px;
        }

        .attraction-name {
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 4px;
            color: #2c3e50;
        }

        .attraction-desc {
            font-size: 10px;
            color: #7f8c8d;
        }

        /* Route Recommendations */
        .route-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .route-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s;
        }

        .route-card:hover {
            transform: translateY(-2px);
        }

        .route-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .route-name {
            font-weight: bold;
            color: #2c3e50;
        }

        .route-duration {
            color: #3498db;
            font-size: 12px;
        }

        .route-stops {
            font-size: 12px;
            color: #7f8c8d;
            margin-bottom: 8px;
        }

        .route-tags {
            display: flex;
            gap: 6px;
        }

        .route-tag {
            background: #ecf0f1;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            color: #7f8c8d;
        }

        /* Tab Navigation for Explore and Community */
        .tab-nav {
            display: flex;
            background: white;
            border-radius: 12px;
            margin-bottom: 16px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
        }

        .tab-nav-item {
            flex: 1;
            padding: 12px 16px;
            text-align: center;
            background: none;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
            font-weight: 500;
            color: #7f8c8d;
            position: relative;
            z-index: 2;
        }

        .tab-nav-item.active {
            background: linear-gradient(135deg, #3498db, #2ecc71);
            color: white;
            border-radius: 8px;
            margin: 4px;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
        }

        .tab-nav-item:hover:not(.active) {
            background: #f8f9fa;
            color: #3498db;
        }

        /* Filter Options */
        .filter-options {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            flex-wrap: wrap;
        }

        .filter-btn {
            background: white;
            border: 1px solid #ddd;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
            color: #7f8c8d;
        }

        .filter-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .filter-btn:hover {
            border-color: #3498db;
            color: #3498db;
        }

        /* List Items */
        .list-item {
            background: white;
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .list-item:hover {
            transform: translateY(-2px);
        }

        .list-item-image {
            width: 80px;
            height: 60px;
            background-size: cover;
            background-position: center;
            border-radius: 8px;
            flex-shrink: 0;
        }

        .list-item-content {
            flex: 1;
            min-width: 0;
        }

        .list-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 6px;
        }

        .list-item-title {
            font-weight: bold;
            color: #2c3e50;
            font-size: 14px;
        }

        .list-item-meta {
            font-size: 11px;
            color: #7f8c8d;
            flex-shrink: 0;
        }

        .list-item-desc {
            font-size: 12px;
            color: #7f8c8d;
            line-height: 1.4;
            margin-bottom: 6px;
        }

        .list-item-stats {
            display: flex;
            gap: 12px;
            font-size: 11px;
            color: #95a5a6;
        }

        .list-item-stats span {
            display: flex;
            align-items: center;
            gap: 2px;
        }

        /* Itinerary Styles */
        .itinerary-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s;
        }

        .itinerary-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .itinerary-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .itinerary-title {
            font-weight: bold;
            color: #2c3e50;
            font-size: 16px;
        }

        .itinerary-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            color: white;
        }

        .status-upcoming {
            background: #3498db;
        }

        .status-ongoing {
            background: #27ae60;
        }

        .status-completed {
            background: #95a5a6;
        }

        .itinerary-time {
            font-size: 12px;
            color: #7f8c8d;
            margin-bottom: 12px;
        }

        .itinerary-route {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            overflow-x: auto;
            padding-bottom: 4px;
        }

        .route-point {
            font-size: 12px;
            color: #3498db;
            white-space: nowrap;
        }

        .route-arrow {
            margin: 0 4px;
            color: #95a5a6;
            font-size: 10px;
        }

        .itinerary-cost {
            font-size: 12px;
            color: #e74c3c;
            font-weight: bold;
            text-align: right;
        }

        .itinerary-details {
            display: none;
            margin-top: 12px;
            border-top: 1px solid #ecf0f1;
            padding-top: 12px;
        }

        .point-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .point-item {
            display: flex;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            position: relative;
        }

        .point-image {
            width: 60px;
            height: 60px;
            background-size: cover;
            background-position: center;
            border-radius: 8px;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .point-info {
            flex: 1;
        }

        .point-name {
            font-weight: bold;
            margin-bottom: 4px;
            font-size: 14px;
        }

        .point-tags {
            display: flex;
            gap: 4px;
            margin-bottom: 4px;
            flex-wrap: wrap;
        }

        .point-tag {
            background: #3498db;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 8px;
        }

        .point-time {
            font-size: 11px;
            color: #7f8c8d;
        }

        .point-cost {
            font-size: 11px;
            color: #e74c3c;
            margin-top: 4px;
        }

        .delete-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            border: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
        }

        .delete-btn:hover {
            background: rgba(231, 76, 60, 0.2);
        }

        /* Profile Styles */
        .user-info {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
            margin-bottom: 20px;
        }

        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3498db, #2ecc71);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
            margin: 0 auto 12px;
        }

        .user-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .user-stats {
            display: flex;
            justify-content: space-around;
            margin-top: 16px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 20px;
            font-weight: bold;
            color: #3498db;
        }

        .stat-label {
            font-size: 12px;
            color: #7f8c8d;
        }

        .travel-log-item {
            background: white;
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            gap: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .travel-log-item:hover {
            transform: translateY(-2px);
        }

        .log-image {
            width: 80px;
            height: 60px;
            background-size: cover;
            background-position: center;
            border-radius: 8px;
            flex-shrink: 0;
        }

        .log-info {
            flex: 1;
        }

        .log-title {
            font-weight: bold;
            margin-bottom: 4px;
            font-size: 14px;
        }

        .log-date {
            font-size: 12px;
            color: #7f8c8d;
        }

        .order-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s;
        }

        .order-item:hover {
            transform: translateY(-2px);
        }

        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .order-type {
            font-weight: bold;
            color: #2c3e50;
        }

        .order-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            color: white;
        }

        .status-paid {
            background: #27ae60;
        }

        .status-used {
            background: #95a5a6;
        }

        .order-time {
            font-size: 12px;
            color: #7f8c8d;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #7f8c8d;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .empty-text {
            font-size: 14px;
        }

        /* QR Code Modal */
        .qr-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .qr-modal-content {
            background: white;
            border-radius: 12px;
            padding: 24px;
            text-align: center;
            max-width: 280px;
            width: 90%;
        }

        .qr-code {
            width: 200px;
            height: 200px;
            background: #f8f9fa;
            border: 2px dashed #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 16px auto;
            border-radius: 8px;
            font-size: 48px;
        }

        .qr-title {
            font-weight: bold;
            margin-bottom: 8px;
        }

        .qr-desc {
            font-size: 12px;
            color: #7f8c8d;
            margin-bottom: 16px;
        }

        .close-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
        }

        /* Chatbot Float Button */
        .chatbot-float {
            position: fixed;
            bottom: 90px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #3498db, #2ecc71);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: white;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(52, 152, 219, 0.4);
            z-index: 999;
            transition: all 0.3s ease;
            animation: pulse 2s infinite;
        }

        .chatbot-float:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.6);
        }

        .chatbot-float:active {
            transform: scale(0.95);
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 4px 16px rgba(52, 152, 219, 0.4);
            }
            50% {
                box-shadow: 0 4px 16px rgba(52, 152, 219, 0.4), 0 0 0 10px rgba(52, 152, 219, 0.1);
            }
            100% {
                box-shadow: 0 4px 16px rgba(52, 152, 219, 0.4);
            }
        }

        .chatbot-tooltip {
            position: absolute;
            bottom: 70px;
            right: 0;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
            pointer-events: none;
        }

        .chatbot-float:hover .chatbot-tooltip {
            opacity: 1;
            transform: translateY(0);
        }

        .chatbot-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            right: 20px;
            border: 5px solid transparent;
            border-top-color: rgba(0,0,0,0.8);
        }

        /* User Header */
        .user-header {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
            margin-bottom: 20px;
        }

        .user-header .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3498db, #2ecc71);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin: 0 auto 12px;
        }

        .user-header .user-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 16px;
            color: #2c3e50;
        }

        .user-stats-simple {
            display: flex;
            justify-content: space-around;
        }

        .stat-simple {
            text-align: center;
        }

        .stat-simple .stat-number {
            font-size: 20px;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 4px;
        }

        .stat-simple .stat-label {
            font-size: 12px;
            color: #7f8c8d;
        }

        /* Settings List */
        .settings-list {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .setting-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #f8f9fa;
            cursor: pointer;
            transition: all 0.3s;
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-item:hover {
            background: #f8f9fa;
        }

        .setting-icon {
            font-size: 20px;
            margin-right: 12px;
            width: 24px;
            text-align: center;
        }

        .setting-text {
            flex: 1;
            font-size: 14px;
            color: #2c3e50;
        }

        .setting-arrow {
            color: #95a5a6;
            font-size: 14px;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #ecf0f1;
            display: flex;
            padding: 8px 0;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px 4px;
            cursor: pointer;
            transition: color 0.3s;
            color: #7f8c8d;
        }

        .nav-item.active {
            color: #3498db;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .nav-label {
            font-size: 10px;
        }

        /* Buttons */
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #ecf0f1;
            color: #2c3e50;
        }

        .btn-secondary:hover {
            background: #d5dbdb;
        }

        /* Notification */
        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #27ae60;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 3000;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .notification.show {
            opacity: 1;
        }

        .notification.error {
            background: #e74c3c;
        }

        .notification.warning {
            background: #f39c12;
        }

        /* Responsive */
        @media (max-width: 375px) {
            .attraction-grid {
                grid-template-columns: 1fr;
            }
            
            .smart-services {
                gap: 6px;
            }
            
            .service-card {
                min-width: 60px;
                padding: 10px 6px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header Banner -->
        <div class="header-banner">
            <div class="banner-content">
                <div class="banner-title">骆岗公园</div>
                <div class="banner-description">
                    集生态涵养、城市休闲与科技体验于一体的超大型城市中央公园
                </div>
                <div class="banner-tags">
                    <span class="banner-tag">🌿 生态涵养</span>
                    <span class="banner-tag">🏙️ 城市休闲</span>
                    <span class="banner-tag">🚀 科技体验</span>
                    <span class="banner-tag">✈️ 航空主题</span>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Home Tab -->
            <div class="tab-content active" id="home">
                <!-- Current Activities -->
                <div class="section">
                    <div class="section-title">🎯 近期活动</div>
                    <div class="activity-carousel" id="activityCarousel">
                        <!-- Activities will be loaded here -->
                    </div>
                </div>

                <!-- Smart Services -->
                <div class="section">
                    <div class="section-title">🤖 智能服务</div>
                    <div class="smart-services">
                        <div class="service-card" onclick="goToRoutePlanning()">
                            <div class="service-icon">🗺️</div>
                            <div class="service-title">路径规划</div>
                        </div>
                        <div class="service-card" onclick="goToImageRecognition()">
                            <div class="service-icon">📷</div>
                            <div class="service-title">看图识景</div>
                        </div>
                        <div class="service-card" onclick="goToTourGuide()">
                            <div class="service-icon">🎧</div>
                            <div class="service-title">伴游导览</div>
                        </div>
                        <div class="service-card" onclick="goToTravelLog()">
                            <div class="service-icon">📝</div>
                            <div class="service-title">游记生成</div>
                        </div>
                        <div class="service-card" onclick="goToVRCheckin()">
                            <div class="service-icon">🥽</div>
                            <div class="service-title">VR打卡</div>
                        </div>
                    </div>
                </div>

                <!-- Featured Attractions -->
                <div class="section">
                    <div class="section-title">⭐ 特色景点</div>
                    <div class="attraction-grid" id="attractionGrid">
                        <!-- Attractions will be loaded here -->
                    </div>
                </div>

                <!-- Route Recommendations -->
                <div class="section">
                    <div class="section-title">🚶 推荐路线</div>
                    <div class="route-list" id="routeList">
                        <!-- Routes will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Explore Tab -->
            <div class="tab-content" id="explore">
                <div class="tab-nav">
                    <button class="tab-nav-item active" onclick="switchExploreTab('attractions')">景点</button>
                    <button class="tab-nav-item" onclick="switchExploreTab('activities')">活动</button>
                    <button class="tab-nav-item" onclick="switchExploreTab('food')">美食</button>
                </div>
                
                <div id="exploreContent">
                    <!-- Content will be loaded based on selected tab -->
                </div>
            </div>

            <!-- Itinerary Tab -->
            <div class="tab-content" id="itinerary">
                <div class="section">
                    <button class="btn" onclick="createNewItinerary()" style="width: 100%; margin-bottom: 16px;">
                        ➕ 新建行程规划
                    </button>
                </div>

                <div class="section">
                    <div class="section-title">📅 我的行程安排</div>
                    <div id="myItineraries">
                        <!-- User itineraries will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Community Tab -->
            <div class="tab-content" id="community">
                <div class="tab-nav">
                    <button class="tab-nav-item active" onclick="switchCommunityTab('logs')">游记</button>
                    <button class="tab-nav-item" onclick="switchCommunityTab('guides')">攻略</button>
                </div>
                
                <div id="communityContent">
                    <!-- Content will be loaded based on selected tab -->
                </div>
            </div>

            <!-- Profile Tab -->
            <div class="tab-content" id="profile">
                <!-- User Header -->
                <div class="user-header">
                    <div class="user-avatar">👤</div>
                    <div class="user-name">游客用户</div>
                    <div class="user-stats-simple">
                        <div class="stat-simple">
                            <div class="stat-number">3</div>
                            <div class="stat-label">游记</div>
                        </div>
                        <div class="stat-simple">
                            <div class="stat-number">8</div>
                            <div class="stat-label">打卡</div>
                        </div>
                        <div class="stat-simple">
                            <div class="stat-number">1250</div>
                            <div class="stat-label">积分</div>
                        </div>
                    </div>
                </div>

                <!-- My Travel Logs -->
                <div class="section">
                    <div class="section-title">我的游记</div>
                    <div id="userTravelLogsList">
                        <!-- Travel logs will be loaded here -->
                    </div>
                </div>

                <!-- My Orders -->
                <div class="section">
                    <div class="section-title">我的订单</div>
                    <div id="userOrdersList">
                        <!-- Orders will be loaded here -->
                    </div>
                </div>

                <!-- Settings -->
                <div class="section">
                    <div class="section-title">设置</div>
                    <div class="settings-list">
                        <div class="setting-item" onclick="showNotification('个人信息设置', 'info')">
                            <div class="setting-icon">👤</div>
                            <div class="setting-text">个人信息</div>
                            <div class="setting-arrow">></div>
                        </div>
                        <div class="setting-item" onclick="showNotification('通知设置', 'info')">
                            <div class="setting-icon">🔔</div>
                            <div class="setting-text">通知设置</div>
                            <div class="setting-arrow">></div>
                        </div>
                        <div class="setting-item" onclick="showNotification('隐私设置', 'info')">
                            <div class="setting-icon">🔒</div>
                            <div class="setting-text">隐私设置</div>
                            <div class="setting-arrow">></div>
                        </div>
                        <div class="setting-item" onclick="showNotification('关于我们', 'info')">
                            <div class="setting-icon">ℹ️</div>
                            <div class="setting-text">关于我们</div>
                            <div class="setting-arrow">></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <div class="nav-item active" data-tab="home">
                <div class="nav-icon">🏠</div>
                <div class="nav-label">首页</div>
            </div>
            <div class="nav-item" data-tab="explore">
                <div class="nav-icon">🔍</div>
                <div class="nav-label">探索</div>
            </div>
            <div class="nav-item" data-tab="itinerary">
                <div class="nav-icon">📅</div>
                <div class="nav-label">行程</div>
            </div>
            <div class="nav-item" data-tab="community">
                <div class="nav-icon">👥</div>
                <div class="nav-label">社区</div>
            </div>
            <div class="nav-item" data-tab="profile">
                <div class="nav-icon">👤</div>
                <div class="nav-label">我的</div>
            </div>
        </div>

        <!-- QR Code Modal -->
        <div class="qr-modal" id="qrModal">
            <div class="qr-modal-content">
                <div class="qr-title" id="qrTitle">无人巴士乘车码</div>
                <div class="qr-desc" id="qrDesc">请向司机出示此二维码</div>
                <div class="qr-code">📱</div>
                <button class="close-btn" onclick="closeQRModal()">关闭</button>
            </div>
        </div>

        <!-- Chatbot Float Button -->
        <div class="chatbot-float" onclick="openChatbot()">
            🦌
            <div class="chatbot-tooltip">小骆助手</div>
        </div>
    </div>

    <script>
        // App State Management
        const AppState = {
            user: {
                points: 1250,
                visitedAttractions: [],
                savedItineraries: [],
                travelLogs: []
            },
            currentExploreTab: 'attractions',
            currentCommunityTab: 'logs'
        };

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
            loadUserData();
        });

        function initializeApp() {
            setupNavigation();
            loadHomeContent();
            loadExploreContent();
            loadItineraryContent();
            loadCommunityContent();
            updateUserInterface();
        }

        function loadUserData() {
            const savedData = localStorage.getItem('luogangParkUserData');
            if (savedData) {
                const userData = JSON.parse(savedData);
                AppState.user = { ...AppState.user, ...userData };
                updateUserInterface();
            }
        }

        function saveUserData() {
            localStorage.setItem('luogangParkUserData', JSON.stringify(AppState.user));
        }

        function updateUserInterface() {
            const pointsElement = document.getElementById('userPoints');
            if (pointsElement) {
                pointsElement.textContent = AppState.user.points + '分';
            }
        }

        // Navigation
        function setupNavigation() {
            const navItems = document.querySelectorAll('.nav-item');
            const tabContents = document.querySelectorAll('.tab-content');

            function switchTab(tabName) {
                navItems.forEach(item => {
                    item.classList.toggle('active', item.dataset.tab === tabName);
                });

                tabContents.forEach(content => {
                    content.classList.toggle('active', content.id === tabName);
                });

                // Load content based on tab
                if (tabName === 'itinerary') {
                    loadItineraries();
                } else if (tabName === 'profile') {
                    loadUserTravelLogs();
                    loadUserOrders();
                }
            }

            navItems.forEach(item => {
                item.addEventListener('click', () => switchTab(item.dataset.tab));
            });
        }

        // Load content for each tab
        function loadHomeContent() {
            loadActivities();
            loadAttractions();
            loadRoutes();
        }

        function loadActivities() {
            const activities = [
                {
                    title: "春季花展",
                    desc: "樱花、桃花盛开，最佳观赏期",
                    status: "进行中",
                    image: "https://images.unsplash.com/photo-1522383225653-ed111181a951?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                },
                {
                    title: "航空科普展",
                    desc: "了解航空历史，体验飞行模拟",
                    status: "进行中",
                    image: "https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                },
                {
                    title: "夜间灯光秀",
                    desc: "信标台夜间灯光表演",
                    status: "进行中",
                    image: "https://images.unsplash.com/photo-1519501025264-65ba15a82390?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                },
                {
                    title: "周末市集",
                    desc: "本地特色商品和美食",
                    status: "进行中",
                    image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                }
            ];

            const carousel = document.getElementById('activityCarousel');
            carousel.innerHTML = activities.map(activity => `
                <div class="activity-card" onclick="showActivityDetail('${activity.title}')">
                    <div class="activity-image" style="background-image: url('${activity.image}')"></div>
                    <div class="activity-info">
                        <div class="activity-title">${activity.title}</div>
                        <div class="activity-desc">${activity.desc}</div>
                        <div class="activity-status">${activity.status}</div>
                    </div>
                </div>
            `).join('');
        }

        function loadAttractions() {
            const attractions = [
                { 
                    name: "信标台", 
                    desc: "地标建筑", 
                    image: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                },
                { 
                    name: "梦想大草坪", 
                    desc: "休闲野餐", 
                    image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                },
                { 
                    name: "航空科普馆", 
                    desc: "科普教育", 
                    image: "https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                }
            ];

            const grid = document.getElementById('attractionGrid');
            grid.innerHTML = attractions.map(attraction => `
                <div class="attraction-card" onclick="showAttractionDetail('${attraction.name}')">
                    <div class="attraction-image" style="background-image: url('${attraction.image}')"></div>
                    <div class="attraction-info">
                        <div class="attraction-name">${attraction.name}</div>
                        <div class="attraction-desc">${attraction.desc}</div>
                    </div>
                </div>
            `).join('');
        }

        function loadRoutes() {
            const routes = [
                {
                    name: "经典一日游",
                    duration: "6小时",
                    stops: "信标台 → 航站楼 → 梦想大草坪",
                    tags: ["推荐", "经典"]
                },
                {
                    name: "亲子游路线",
                    duration: "4小时",
                    stops: "科普馆 → 梦想大草坪 → 儿童区",
                    tags: ["亲子", "科普"]
                }
            ];

            const routeList = document.getElementById('routeList');
            routeList.innerHTML = routes.map(route => `
                <div class="route-card" onclick="showRouteOnMap('${route.name}')">
                    <div class="route-header">
                        <div class="route-name">${route.name}</div>
                        <div class="route-duration">${route.duration}</div>
                    </div>
                    <div class="route-stops">${route.stops}</div>
                    <div class="route-tags">
                        ${route.tags.map(tag => `<span class="route-tag">${tag}</span>`).join('')}
                    </div>
                </div>
            `).join('');
        }

        // Smart Service Navigation Functions
        function goToRoutePlanning() {
            window.open('route-planning.html', '_blank');
        }

        function goToImageRecognition() {
            window.open('image-recognition.html', '_blank');
        }

        function goToTourGuide() {
            window.open('tour-guide.html', '_blank');
        }

        function goToTravelLog() {
            window.open('travel-log.html', '_blank');
        }

        function goToVRCheckin() {
            window.open('vr-checkin.html', '_blank');
        }

        // Explore Tab Functions
        function switchExploreTab(tab) {
            AppState.currentExploreTab = tab;
            
            // Update tab navigation
            document.querySelectorAll('#explore .tab-nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
            
            loadExploreContent();
        }

        function loadExploreContent() {
            const content = document.getElementById('exploreContent');
            
            switch(AppState.currentExploreTab) {
                case 'attractions':
                    content.innerHTML = generateAttractionsContent();
                    break;
                case 'activities':
                    content.innerHTML = generateActivitiesContent();
                    break;
                case 'food':
                    content.innerHTML = generateFoodContent();
                    break;
            }
        }

        function generateAttractionsContent() {
            const attractions = [
                {
                    name: "信标台",
                    desc: "骆岗公园标志性建筑，高约50米",
                    type: "景点",
                    image: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                },
                {
                    name: "梦想大草坪",
                    desc: "开阔草坪，适合野餐和休闲",
                    type: "景点",
                    image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                },
                {
                    name: "航空科普馆",
                    desc: "航空知识科普教育基地",
                    type: "体验",
                    image: "https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                },
                {
                    name: "城市馆",
                    desc: "合肥城市发展展示",
                    type: "体验",
                    image: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                }
            ];

            return `
                <div class="filter-options">
                    <button class="filter-btn active" onclick="filterAttractions('all')">全部类型</button>
                    <button class="filter-btn" onclick="filterAttractions('景点')">景点</button>
                    <button class="filter-btn" onclick="filterAttractions('体验')">体验</button>
                </div>
                ${attractions.map(attraction => `
                    <div class="list-item" onclick="showAttractionDetail('${attraction.name}')" data-type="${attraction.type}">
                        <div class="list-item-image" style="background-image: url('${attraction.image}')"></div>
                        <div class="list-item-content">
                            <div class="list-item-header">
                                <div class="list-item-title">${attraction.name}</div>
                                <div class="list-item-meta">${attraction.type}</div>
                            </div>
                            <div class="list-item-desc">${attraction.desc}</div>
                        </div>
                    </div>
                `).join('')}
            `;
        }

        function generateActivitiesContent() {
            const activities = [
                {
                    name: "春季花展",
                    desc: "樱花、桃花等春季花卉展示",
                    date: "3月-4月",
                    status: "进行中",
                    image: "https://images.unsplash.com/photo-1522383225653-ed111181a951?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                },
                {
                    name: "航空科普展",
                    desc: "航空历史展览和飞行模拟体验",
                    date: "长期展出",
                    status: "进行中",
                    image: "https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                },
                {
                    name: "夜间灯光秀",
                    desc: "信标台夜间灯光表演",
                    date: "每日19:30",
                    status: "进行中",
                    image: "https://images.unsplash.com/photo-1519501025264-65ba15a82390?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                }
            ];

            return `
                <div class="filter-options">
                    <button class="filter-btn active" onclick="filterActivities('all')">全部活动</button>
                    <button class="filter-btn" onclick="filterActivities('进行中')">进行中</button>
                    <button class="filter-btn" onclick="filterActivities('即将开始')">即将开始</button>
                </div>
                ${activities.map(activity => `
                    <div class="list-item" onclick="showActivityDetail('${activity.name}')" data-status="${activity.status}">
                        <div class="list-item-image" style="background-image: url('${activity.image}')"></div>
                        <div class="list-item-content">
                            <div class="list-item-header">
                                <div class="list-item-title">${activity.name}</div>
                                <div class="list-item-meta">${activity.date}</div>
                            </div>
                            <div class="list-item-desc">${activity.desc}</div>
                        </div>
                    </div>
                `).join('')}
            `;
        }

        function generateFoodContent() {
            const foods = [
                {
                    name: "骆岗咖啡厅",
                    desc: "位于航站楼内，提供精品咖啡",
                    type: "咖啡",
                    image: "https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                },
                {
                    name: "草坪野餐区",
                    desc: "可自带食物在指定区域野餐",
                    type: "野餐",
                    image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                },
                {
                    name: "园区便利店",
                    desc: "提供饮料、零食等便民商品",
                    type: "零售",
                    image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
                }
            ];

            return `
                <div class="filter-options">
                    <button class="filter-btn active" onclick="filterFood('all')">全部类型</button>
                    <button class="filter-btn" onclick="filterFood('餐饮')">餐饮</button>
                    <button class="filter-btn" onclick="filterFood('零售')">零售</button>
                    <button class="filter-btn" onclick="filterFood('野餐')">野餐</button>
                </div>
                ${foods.map(food => `
                    <div class="list-item" onclick="showFoodDetail('${food.name}')" data-type="${food.type}">
                        <div class="list-item-image" style="background-image: url('${food.image}')"></div>
                        <div class="list-item-content">
                            <div class="list-item-header">
                                <div class="list-item-title">${food.name}</div>
                                <div class="list-item-meta">${food.type}</div>
                            </div>
                            <div class="list-item-desc">${food.desc}</div>
                        </div>
                    </div>
                `).join('')}
            `;
        }

        // Community Tab Functions
        function switchCommunityTab(tab) {
            AppState.currentCommunityTab = tab;
            
            // Update tab navigation
            document.querySelectorAll('#community .tab-nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
            
            loadCommunityContent();
        }

        function loadCommunityContent() {
            const content = document.getElementById('communityContent');
            
            switch(AppState.currentCommunityTab) {
                case 'logs':
                    content.innerHTML = generateTravelLogsContent();
                    break;
                case 'guides':
                    content.innerHTML = generateGuidesContent();
                    break;
            }
        }

        function generateTravelLogsContent() {
            const logs = [
                {
                    title: "骆岗公园春日游记",
                    author: "旅行达人",
                    date: "2024-03-18",
                    category: "最新发布",
                    image: "https://images.unsplash.com/photo-1522383225653-ed111181a951?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                    views: 1256,
                    likes: 89
                },
                {
                    title: "带娃逛骆岗",
                    author: "亲子游妈妈",
                    date: "2024-03-16",
                    category: "最多点赞",
                    image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                    views: 2341,
                    likes: 156
                },
                {
                    title: "骆岗夜景摄影",
                    author: "摄影爱好者",
                    date: "2024-03-15",
                    category: "最多评论",
                    image: "https://images.unsplash.com/photo-1519501025264-65ba15a82390?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                    views: 1876,
                    likes: 134
                }
            ];

            return `
                <div class="filter-options">
                    <button class="filter-btn active" onclick="filterTravelLogs('all')">全部</button>
                    <button class="filter-btn" onclick="filterTravelLogs('最新发布')">最新发布</button>
                    <button class="filter-btn" onclick="filterTravelLogs('最多点赞')">最多点赞</button>
                    <button class="filter-btn" onclick="filterTravelLogs('最多评论')">最多评论</button>
                </div>
                ${logs.map(log => `
                    <div class="list-item" onclick="showTravelLogDetail('${log.title}')" data-category="${log.category}">
                        <div class="list-item-image" style="background-image: url('${log.image}')"></div>
                        <div class="list-item-content">
                            <div class="list-item-header">
                                <div class="list-item-title">${log.title}</div>
                                <div class="list-item-meta">${log.date}</div>
                            </div>
                            <div class="list-item-desc">作者: ${log.author}</div>
                            <div class="list-item-stats">
                                <span>👁️ ${log.views}</span>
                                <span>❤️ ${log.likes}</span>
                            </div>
                        </div>
                    </div>
                `).join('')}
            `;
        }

        function generateGuidesContent() {
            const guides = [
                {
                    title: "骆岗公园完整游玩攻略",
                    author: "官方攻略",
                    date: "2024-03-10",
                    type: "官方攻略",
                    image: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                    views: 3452,
                    likes: 278
                },
                {
                    title: "摄影打卡点推荐",
                    author: "摄影师小李",
                    date: "2024-03-08",
                    type: "用户攻略",
                    image: "https://images.unsplash.com/photo-1519501025264-65ba15a82390?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                    views: 2156,
                    likes: 189
                },
                {
                    title: "亲子游攻略",
                    author: "亲子游专家",
                    date: "2024-03-05",
                    type: "用户攻略",
                    image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                    views: 1876,
                    likes: 142
                }
            ];

            return `
                <div class="filter-options">
                    <button class="filter-btn active" onclick="filterGuides('all')">全部攻略</button>
                    <button class="filter-btn" onclick="filterGuides('官方攻略')">官方攻略</button>
                    <button class="filter-btn" onclick="filterGuides('用户攻略')">用户攻略</button>
                </div>
                ${guides.map(guide => `
                    <div class="list-item" onclick="showGuideDetail('${guide.title}')" data-type="${guide.type}">
                        <div class="list-item-image" style="background-image: url('${guide.image}')"></div>
                        <div class="list-item-content">
                            <div class="list-item-header">
                                <div class="list-item-title">${guide.title}</div>
                                <div class="list-item-meta">${guide.date}</div>
                            </div>
                            <div class="list-item-desc">作者: ${guide.author}</div>
                            <div class="list-item-stats">
                                <span>👁️ ${guide.views}</span>
                                <span>❤️ ${guide.likes}</span>
                            </div>
                        </div>
                    </div>
                `).join('')}
            `;
        }

        // Itinerary Functions
        function createNewItinerary() {
            goToRoutePlanning();
        }

        function loadItineraryContent() {
            loadItineraries();
        }

        function toggleItineraryDetail(id) {
            const detail = document.getElementById(`itinerary-detail-${id}`);
            detail.style.display = detail.style.display === 'none' ? 'block' : 'none';
        }

        function editItinerary(id) {
            showNotification('跳转到行程编辑页面...', 'info');
        }

        function deleteItinerary(id) {
            if (confirm('确定要删除这个行程吗？')) {
                showNotification('行程已删除', 'success');
                loadItineraryContent();
            }
        }

        // Route Map Display
        function showRouteOnMap(routeName) {
            showNotification(`正在手绘地图上展示${routeName}路线...`, 'info');
            // This would open a map view with the route highlighted
        }

        // Detail Functions
        function showActivityDetail(title) {
            showNotification(`查看${title}详情`, 'info');
        }

        function showAttractionDetail(name) {
            showNotification(`查看${name}详情`, 'info');
        }

        function showFoodDetail(name) {
            showNotification(`查看${name}详情`, 'info');
        }

        function showTravelLogDetail(title) {
            showNotification(`查看游记: ${title}`, 'info');
        }

        function showGuideDetail(title) {
            showNotification(`查看攻略: ${title}`, 'info');
        }

        // Sample itinerary data
        const itineraries = [
            {
                id: 1,
                title: "骆岗公园一日游",
                status: "upcoming",
                startTime: "2024-04-15 09:00",
                endTime: "2024-04-15 17:00",
                cost: "¥35",
                points: [
                    {
                        name: "信标台",
                        image: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                        tags: ["地标", "观景"],
                        time: "09:30-10:30",
                        cost: ""
                    },
                    {
                        name: "航空科普馆",
                        image: "https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                        tags: ["科普", "体验"],
                        time: "11:00-12:30",
                        cost: "¥20"
                    },
                    {
                        name: "骆岗咖啡厅",
                        image: "https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                        tags: ["餐饮", "休息"],
                        time: "12:30-13:30",
                        cost: "¥15"
                    },
                    {
                        name: "梦想大草坪",
                        image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                        tags: ["休闲", "野餐"],
                        time: "14:00-16:00",
                        cost: ""
                    }
                ]
            },
            {
                id: 2,
                title: "周末亲子游",
                status: "ongoing",
                startTime: "2024-04-10 10:00",
                endTime: "2024-04-10 16:00",
                cost: "¥50",
                points: [
                    {
                        name: "航空科普馆",
                        image: "https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                        tags: ["科普", "体验"],
                        time: "10:00-11:30",
                        cost: "¥20"
                    },
                    {
                        name: "儿童游乐区",
                        image: "https://images.unsplash.com/photo-1519331379826-f10be5486c6f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                        tags: ["游乐", "亲子"],
                        time: "11:30-13:00",
                        cost: "¥30"
                    },
                    {
                        name: "梦想大草坪",
                        image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                        tags: ["休闲", "野餐"],
                        time: "13:30-15:30",
                        cost: ""
                    }
                ]
            },
            {
                id: 3,
                title: "摄影打卡之旅",
                status: "completed",
                startTime: "2024-04-05 08:00",
                endTime: "2024-04-05 12:00",
                cost: "¥0",
                points: [
                    {
                        name: "信标台",
                        image: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                        tags: ["地标", "观景"],
                        time: "08:00-09:00",
                        cost: ""
                    },
                    {
                        name: "航站楼",
                        image: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                        tags: ["历史", "建筑"],
                        time: "09:30-10:30",
                        cost: ""
                    },
                    {
                        name: "展园花海",
                        image: "https://images.unsplash.com/photo-1522383225653-ed111181a951?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                        tags: ["花卉", "摄影"],
                        time: "11:00-12:00",
                        cost: ""
                    }
                ]
            }
        ];

        // Sample user data
        const userTravelLogs = [
            {
                title: "骆岗公园春日游记",
                image: "https://images.unsplash.com/photo-1522383225653-ed111181a951?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                date: "2024-03-18"
            },
            {
                title: "带娃逛骆岗",
                image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                date: "2024-03-16"
            },
            {
                title: "骆岗夜景摄影",
                image: "https://images.unsplash.com/photo-1519501025264-65ba15a82390?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                date: "2024-03-15"
            }
        ];

        const userOrders = [
            {
                type: "无人巴士",
                status: "paid",
                time: "2024-04-15 09:30",
                canShowQR: true
            },
            {
                type: "接驳车",
                status: "used",
                time: "2024-04-10 14:20",
                canShowQR: false
            },
            {
                type: "无人巴士",
                status: "paid",
                time: "2024-04-08 16:45",
                canShowQR: true
            }
        ];

        function loadItineraries() {
            const container = document.getElementById('myItineraries');
            container.innerHTML = '';

            itineraries.forEach(itinerary => {
                const statusText = {
                    'upcoming': '即将开始',
                    'ongoing': '进行中',
                    'completed': '已完成'
                };

                const statusClass = {
                    'upcoming': 'status-upcoming',
                    'ongoing': 'status-ongoing',
                    'completed': 'status-completed'
                };

                // Create route points display
                const routePoints = itinerary.points.map(point => point.name);
                const routeDisplay = routePoints.map((point, index) => {
                    if (index === routePoints.length - 1) {
                        return `<span class="route-point">${point}</span>`;
                    }
                    return `<span class="route-point">${point}</span><span class="route-arrow">→</span>`;
                }).join('');

                const card = document.createElement('div');
                card.className = 'itinerary-card';
                card.setAttribute('data-id', itinerary.id);
                card.innerHTML = `
                    <div class="itinerary-header">
                        <div class="itinerary-title">${itinerary.title}</div>
                        <div class="itinerary-status ${statusClass[itinerary.status]}">${statusText[itinerary.status]}</div>
                    </div>
                    <div class="itinerary-time">${formatDate(itinerary.startTime)} - ${formatTime(itinerary.endTime)}</div>
                    <div class="itinerary-route">${routeDisplay}</div>
                    <div class="itinerary-cost">预计费用: ${itinerary.cost}</div>
                    <div class="itinerary-details" id="details-${itinerary.id}">
                        <div class="point-list">
                            ${itinerary.points.map(point => `
                                <div class="point-item">
                                    <div class="point-image" style="background-image: url('${point.image}')"></div>
                                    <div class="point-info">
                                        <div class="point-name">${point.name}</div>
                                        <div class="point-tags">
                                            ${point.tags.map(tag => `<span class="point-tag">${tag}</span>`).join('')}
                                        </div>
                                        <div class="point-time">${point.time}</div>
                                        ${point.cost ? `<div class="point-cost">费用: ${point.cost}</div>` : ''}
                                    </div>
                                    <button class="delete-btn" onclick="deletePoint(event, ${itinerary.id}, '${point.name}')">×</button>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;

                card.addEventListener('click', function(e) {
                    if (e.target.classList.contains('delete-btn')) return;
                    toggleDetails(itinerary.id);
                });

                container.appendChild(card);
            });
        }

        function toggleDetails(id) {
            const details = document.getElementById(`details-${id}`);
            if (details.style.display === 'block') {
                details.style.display = 'none';
            } else {
                // Hide all other details
                document.querySelectorAll('.itinerary-details').forEach(el => {
                    el.style.display = 'none';
                });
                details.style.display = 'block';
            }
        }

        function deletePoint(event, itineraryId, pointName) {
            event.stopPropagation();
            showNotification(`已从行程中删除: ${pointName}`, 'success');

            // In a real app, you would update the data here
            // For demo purposes, we'll just hide the element
            event.target.closest('.point-item').style.display = 'none';
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return `${date.getMonth() + 1}月${date.getDate()}日 ${formatTime(dateString)}`;
        }

        function formatTime(dateString) {
            const date = new Date(dateString);
            return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
        }

        function loadUserTravelLogs() {
            const container = document.getElementById('userTravelLogsList');

            if (userTravelLogs.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">📝</div>
                        <div class="empty-text">还没有发布游记<br>快去记录你的美好时光吧！</div>
                    </div>
                `;
                return;
            }

            container.innerHTML = userTravelLogs.map(log => `
                <div class="travel-log-item" onclick="viewTravelLog('${log.title}')">
                    <div class="log-image" style="background-image: url('${log.image}')"></div>
                    <div class="log-info">
                        <div class="log-title">${log.title}</div>
                        <div class="log-date">发布于 ${log.date}</div>
                    </div>
                </div>
            `).join('');
        }

        function loadUserOrders() {
            const container = document.getElementById('userOrdersList');

            if (userOrders.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">📋</div>
                        <div class="empty-text">暂无订单记录</div>
                    </div>
                `;
                return;
            }

            const statusText = {
                'paid': '已支付',
                'used': '已使用'
            };

            const statusClass = {
                'paid': 'status-paid',
                'used': 'status-used'
            };

            container.innerHTML = userOrders.map(order => `
                <div class="order-item" onclick="handleOrderClick('${order.type}', ${order.canShowQR})">
                    <div class="order-header">
                        <div class="order-type">${order.type}</div>
                        <div class="order-status ${statusClass[order.status]}">${statusText[order.status]}</div>
                    </div>
                    <div class="order-time">${order.time}</div>
                </div>
            `).join('');
        }

        function viewTravelLog(title) {
            showNotification(`查看游记: ${title}`, 'success');
        }

        function handleOrderClick(type, canShowQR) {
            if (type === '无人巴士' && canShowQR) {
                showQRCode(type);
            } else {
                showNotification(`查看${type}订单详情`, 'success');
            }
        }

        function showQRCode(type) {
            document.getElementById('qrTitle').textContent = `${type}乘车码`;
            document.getElementById('qrDesc').textContent = '请向司机出示此二维码';
            document.getElementById('qrModal').style.display = 'flex';
        }

        function closeQRModal() {
            document.getElementById('qrModal').style.display = 'none';
        }

        function openChatbot() {
            window.location.href = 'chatbot.html';
        }

        // Filter functions
        function filterAttractions(type) {
            updateFilterButtons(event.target);
            const items = document.querySelectorAll('#exploreContent .list-item[data-type]');
            items.forEach(item => {
                if (type === 'all' || item.dataset.type === type) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function filterActivities(status) {
            updateFilterButtons(event.target);
            const items = document.querySelectorAll('#exploreContent .list-item[data-status]');
            items.forEach(item => {
                if (status === 'all' || item.dataset.status === status) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function filterFood(type) {
            updateFilterButtons(event.target);
            const items = document.querySelectorAll('#exploreContent .list-item[data-type]');
            items.forEach(item => {
                if (type === 'all' || item.dataset.type === type) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function filterTravelLogs(category) {
            updateFilterButtons(event.target);
            const items = document.querySelectorAll('#communityContent .list-item[data-category]');
            items.forEach(item => {
                if (category === 'all' || item.dataset.category === category) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function filterGuides(type) {
            updateFilterButtons(event.target);
            const items = document.querySelectorAll('#communityContent .list-item[data-type]');
            items.forEach(item => {
                if (type === 'all' || item.dataset.type === type) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function updateFilterButtons(activeButton) {
            const container = activeButton.closest('.filter-options');
            if (container) {
                container.querySelectorAll('.filter-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                activeButton.classList.add('active');
            }
        }

        // Utility Functions
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }
    </script>
</body>
</html>
