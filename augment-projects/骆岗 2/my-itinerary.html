<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的行程 - 骆岗公园</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #2c3e50;
        }

        .app-container {
            max-width: 414px;
            margin: 0 auto;
            background: #f8fffe;
            min-height: 100vh;
            position: relative;
        }

        .header {
            background: white;
            padding: 16px;
            display: flex;
            align-items: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .back-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            margin-right: 16px;
            color: #3498db;
        }

        .header-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }

        .content {
            padding: 16px;
        }

        .section {
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 12px;
            color: #2c3e50;
        }

        /* Itinerary Cards */
        .itinerary-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s;
        }

        .itinerary-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .itinerary-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .itinerary-title {
            font-weight: bold;
            color: #2c3e50;
            font-size: 16px;
        }

        .itinerary-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            color: white;
        }

        .status-upcoming {
            background: #3498db;
        }

        .status-ongoing {
            background: #27ae60;
        }

        .status-completed {
            background: #95a5a6;
        }

        .itinerary-time {
            font-size: 12px;
            color: #7f8c8d;
            margin-bottom: 12px;
        }

        .itinerary-route {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            overflow-x: auto;
            padding-bottom: 4px;
        }

        .route-point {
            font-size: 12px;
            color: #3498db;
            white-space: nowrap;
        }

        .route-arrow {
            margin: 0 4px;
            color: #95a5a6;
            font-size: 10px;
        }

        .itinerary-cost {
            font-size: 12px;
            color: #e74c3c;
            font-weight: bold;
            text-align: right;
        }

        /* Expanded Itinerary */
        .itinerary-details {
            display: none;
            margin-top: 12px;
            border-top: 1px solid #ecf0f1;
            padding-top: 12px;
        }

        .point-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .point-item {
            display: flex;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            position: relative;
        }

        .point-image {
            width: 60px;
            height: 60px;
            background-size: cover;
            background-position: center;
            border-radius: 8px;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .point-info {
            flex: 1;
        }

        .point-name {
            font-weight: bold;
            margin-bottom: 4px;
            font-size: 14px;
        }

        .point-tags {
            display: flex;
            gap: 4px;
            margin-bottom: 4px;
            flex-wrap: wrap;
        }

        .point-tag {
            background: #3498db;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 8px;
        }

        .point-time {
            font-size: 11px;
            color: #7f8c8d;
        }

        .point-cost {
            font-size: 11px;
            color: #e74c3c;
            margin-top: 4px;
        }

        .delete-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            border: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
        }

        .delete-btn:hover {
            background: rgba(231, 76, 60, 0.2);
        }

        /* Notifications */
        .notification {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 12px 24px;
            border-radius: 8px;
            background: #3498db;
            color: white;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            animation: fadeIn 0.3s, fadeOut 0.3s 2.7s;
        }

        .notification.success {
            background: #27ae60;
        }

        .notification.error {
            background: #e74c3c;
        }

        .notification.warning {
            background: #f39c12;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translate(-50%, 20px); }
            to { opacity: 1; transform: translate(-50%, 0); }
        }

        @keyframes fadeOut {
            from { opacity: 1; transform: translate(-50%, 0); }
            to { opacity: 0; transform: translate(-50%, 20px); }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">←</button>
            <div class="header-title">📅 我的行程</div>
        </div>

        <div class="content">
            <div class="section">
                <div class="section-title">我的行程安排</div>
                <div id="itineraryList">
                    <!-- Itineraries will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }

        // Sample itinerary data
        const itineraries = [
            {
                id: 1,
                title: "骆岗公园一日游",
                status: "upcoming",
                startTime: "2024-04-15 09:00",
                endTime: "2024-04-15 17:00",
                cost: "¥35",
                points: [
                    {
                        name: "信标台",
                        image: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                        tags: ["地标", "观景"],
                        time: "09:30-10:30",
                        cost: ""
                    },
                    {
                        name: "航空科普馆",
                        image: "https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                        tags: ["科普", "体验"],
                        time: "11:00-12:30",
                        cost: "¥20"
                    },
                    {
                        name: "骆岗咖啡厅",
                        image: "https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                        tags: ["餐饮", "休息"],
                        time: "12:30-13:30",
                        cost: "¥15"
                    },
                    {
                        name: "梦想大草坪",
                        image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                        tags: ["休闲", "野餐"],
                        time: "14:00-16:00",
                        cost: ""
                    }
                ]
            },
            {
                id: 2,
                title: "周末亲子游",
                status: "ongoing",
                startTime: "2024-04-10 10:00",
                endTime: "2024-04-10 16:00",
                cost: "¥50",
                points: [
                    {
                        name: "航空科普馆",
                        image: "https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                        tags: ["科普", "体验"],
                        time: "10:00-11:30",
                        cost: "¥20"
                    },
                    {
                        name: "儿童游乐区",
                        image: "https://images.unsplash.com/photo-1519331379826-f10be5486c6f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                        tags: ["游乐", "亲子"],
                        time: "11:30-13:00",
                        cost: "¥30"
                    },
                    {
                        name: "梦想大草坪",
                        image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                        tags: ["休闲", "野餐"],
                        time: "13:30-15:30",
                        cost: ""
                    }
                ]
            },
            {
                id: 3,
                title: "摄影打卡之旅",
                status: "completed",
                startTime: "2024-04-05 08:00",
                endTime: "2024-04-05 12:00",
                cost: "¥0",
                points: [
                    {
                        name: "信标台",
                        image: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                        tags: ["地标", "观景"],
                        time: "08:00-09:00",
                        cost: ""
                    },
                    {
                        name: "航站楼",
                        image: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                        tags: ["历史", "建筑"],
                        time: "09:30-10:30",
                        cost: ""
                    },
                    {
                        name: "展园花海",
                        image: "https://images.unsplash.com/photo-1522383225653-ed111181a951?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
                        tags: ["花卉", "摄影"],
                        time: "11:00-12:00",
                        cost: ""
                    }
                ]
            }
        ];

        function loadItineraries() {
            const container = document.getElementById('itineraryList');
            container.innerHTML = '';

            itineraries.forEach(itinerary => {
                const statusText = {
                    'upcoming': '即将开始',
                    'ongoing': '进行中',
                    'completed': '已完成'
                };

                const statusClass = {
                    'upcoming': 'status-upcoming',
                    'ongoing': 'status-ongoing',
                    'completed': 'status-completed'
                };

                // Create route points display
                const routePoints = itinerary.points.map(point => point.name);
                const routeDisplay = routePoints.map((point, index) => {
                    if (index === routePoints.length - 1) {
                        return `<span class="route-point">${point}</span>`;
                    }
                    return `<span class="route-point">${point}</span><span class="route-arrow">→</span>`;
                }).join('');

                const card = document.createElement('div');
                card.className = 'itinerary-card';
                card.setAttribute('data-id', itinerary.id);
                card.innerHTML = `
                    <div class="itinerary-header">
                        <div class="itinerary-title">${itinerary.title}</div>
                        <div class="itinerary-status ${statusClass[itinerary.status]}">${statusText[itinerary.status]}</div>
                    </div>
                    <div class="itinerary-time">${formatDate(itinerary.startTime)} - ${formatTime(itinerary.endTime)}</div>
                    <div class="itinerary-route">${routeDisplay}</div>
                    <div class="itinerary-cost">预计费用: ${itinerary.cost}</div>
                    <div class="itinerary-details" id="details-${itinerary.id}">
                        <div class="point-list">
                            ${itinerary.points.map(point => `
                                <div class="point-item">
                                    <div class="point-image" style="background-image: url('${point.image}')"></div>
                                    <div class="point-info">
                                        <div class="point-name">${point.name}</div>
                                        <div class="point-tags">
                                            ${point.tags.map(tag => `<span class="point-tag">${tag}</span>`).join('')}
                                        </div>
                                        <div class="point-time">${point.time}</div>
                                        ${point.cost ? `<div class="point-cost">费用: ${point.cost}</div>` : ''}
                                    </div>
                                    <button class="delete-btn" onclick="deletePoint(event, ${itinerary.id}, '${point.name}')">×</button>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;

                card.addEventListener('click', function(e) {
                    if (e.target.classList.contains('delete-btn')) return;
                    toggleDetails(itinerary.id);
                });

                container.appendChild(card);
            });
        }

        function toggleDetails(id) {
            const details = document.getElementById(`details-${id}`);
            if (details.style.display === 'block') {
                details.style.display = 'none';
            } else {
                // Hide all other details
                document.querySelectorAll('.itinerary-details').forEach(el => {
                    el.style.display = 'none';
                });
                details.style.display = 'block';
            }
        }

        function deletePoint(event, itineraryId, pointName) {
            event.stopPropagation();
            showNotification(`已从行程中删除: ${pointName}`, 'success');
            
            // In a real app, you would update the data here
            // For demo purposes, we'll just hide the element
            event.target.closest('.point-item').style.display = 'none';
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return `${date.getMonth() + 1}月${date.getDate()}日 ${formatTime(dateString)}`;
        }

        function formatTime(dateString) {
            const date = new Date(dateString);
            return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }

        // Load itineraries when page loads
        document.addEventListener('DOMContentLoaded', loadItineraries);
    </script>
</body>
</html>
