// 乐游合肥移动端应用 - 完整交互版本
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 乐游合肥应用启动...');
    
    // 初始化基础功能
    initializeApp();
    
    // 初始化交互功能
    initializeInteractions();
    
    console.log('✅ 应用初始化完成！');
});

// 初始化应用基础功能
function initializeApp() {
    console.log('📱 初始化应用基础功能...');
    
    // 检查设备支持
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/sw.js').catch(console.error);
    }
    
    // 初始化定位
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            position => {
                console.log('📍 位置获取成功:', position.coords);
                showToast('📍 位置获取成功，为您推荐附近景点');
            },
            error => {
                console.log('❌ 位置获取失败:', error);
                showToast('📍 位置获取失败，部分功能可能受限');
            }
        );
    }
}

// 初始化所有交互功能
function initializeInteractions() {
    console.log('🔧 初始化交互功能...');
    
    // 1. 底部导航切换
    initializeNavigation();
    
    // 2. 智能规划功能
    initializePlanning();
    
    // 3. 语音助手功能
    initializeVoiceAssistant();
    
    // 4. 快捷入口功能
    initializeQuickAccess();
    
    // 5. 景点卡片功能
    initializeAttractionCards();
    
    // 6. 地图标记功能
    initializeMapMarkers();
    
    // 7. 悬浮助手功能
    initializeFloatingAssistant();
    
    // 8. AI工具功能
    initializeAITools();
}

// 1. 底部导航切换
function initializeNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    const pages = document.querySelectorAll('.page');
    
    console.log(`🧭 找到 ${navItems.length} 个导航项，${pages.length} 个页面`);
    
    navItems.forEach((item, index) => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const targetPage = this.dataset.page;
            console.log(`🔄 切换到页面: ${targetPage}`);
            
            // 更新导航状态
            navItems.forEach(nav => nav.classList.remove('active'));
            this.classList.add('active');
            
            // 切换页面
            pages.forEach(page => page.classList.remove('active'));
            const targetPageElement = document.getElementById(targetPage);
            if (targetPageElement) {
                targetPageElement.classList.add('active');
            }
            
            showToast(`切换到${getPageName(targetPage)}`);
        });
    });
}

// 2. 智能规划功能
function initializePlanning() {
    const planningBtn = document.getElementById('startPlanning');
    console.log('🤖 智能规划按钮:', planningBtn ? '✅' : '❌');
    
    if (planningBtn) {
        planningBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('🎯 启动智能规划');
            showPlanningInterface();
        });
    }
}

// 3. 语音助手功能
function initializeVoiceAssistant() {
    const voiceBtn = document.getElementById('voiceSearch');
    console.log('🎤 语音按钮:', voiceBtn ? '✅' : '❌');
    
    if (voiceBtn) {
        voiceBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('🗣️ 启动语音识别');
            startVoiceRecognition();
        });
    }
}

// 4. 快捷入口功能
function initializeQuickAccess() {
    const accessItems = document.querySelectorAll('.access-item');
    console.log(`⚡ 找到 ${accessItems.length} 个快捷入口`);
    
    accessItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const page = this.dataset.page;
            const tab = this.dataset.tab;
            const tool = this.dataset.tool;
            
            console.log('🔗 点击快捷入口:', { page, tab, tool });
            
            if (page) {
                switchToPage(page, tab);
            } else if (tool) {
                handleToolClick(tool);
            }
        });
    });
}

// 5. 景点卡片功能
function initializeAttractionCards() {
    const attractionCards = document.querySelectorAll('.attraction-card');
    console.log(`🏞️ 找到 ${attractionCards.length} 个景点卡片`);
    
    attractionCards.forEach(card => {
        card.addEventListener('click', function(e) {
            e.preventDefault();
            const attractionId = this.dataset.id;
            console.log('🎫 点击景点卡片:', attractionId);
            
            if (attractionId === 'luogang') {
                showLuogangDetail();
            } else {
                showToast(`正在加载${attractionId}详情...`);
            }
        });
    });
}

// 6. 地图标记功能
function initializeMapMarkers() {
    const mapMarkers = document.querySelectorAll('.map-marker');
    console.log(`🗺️ 找到 ${mapMarkers.length} 个地图标记`);
    
    mapMarkers.forEach(marker => {
        marker.addEventListener('click', function(e) {
            e.preventDefault();
            const spot = this.dataset.spot;
            console.log('📍 点击地图标记:', spot);
            
            if (spot === 'luogang') {
                showLuogangDetail();
            } else {
                showSpotInfo(spot);
            }
        });
    });
}

// 7. 悬浮助手功能
function initializeFloatingAssistant() {
    const floatingAssistant = document.getElementById('floatingAssistant');
    console.log('🤖 悬浮助手:', floatingAssistant ? '✅' : '❌');
    
    if (floatingAssistant) {
        floatingAssistant.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('💬 启动AI助手');
            showAIAssistant();
        });
    }
}

// 8. AI工具功能
function initializeAITools() {
    const aiToolItems = document.querySelectorAll('.ai-tool-item');
    console.log(`🔧 找到 ${aiToolItems.length} 个AI工具`);
    
    aiToolItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const tool = this.dataset.tool;
            console.log('🛠️ 点击AI工具:', tool);
            handleAITool(tool);
        });
    });
}

// 页面切换函数
function switchToPage(page, tab) {
    console.log('🔄 切换页面:', page, tab);
    
    const navItems = document.querySelectorAll('.nav-item');
    const pages = document.querySelectorAll('.page');
    
    // 更新导航状态
    navItems.forEach(nav => nav.classList.remove('active'));
    const targetNav = document.querySelector(`[data-page="${page}"]`);
    if (targetNav) {
        targetNav.classList.add('active');
    }
    
    // 切换页面
    pages.forEach(p => p.classList.remove('active'));
    const targetPage = document.getElementById(page);
    if (targetPage) {
        targetPage.classList.add('active');
    }
    
    showToast(`切换到${getPageName(page)}`);
}

// 工具点击处理
function handleToolClick(tool) {
    console.log('🔧 处理工具点击:', tool);
    
    switch (tool) {
        case 'camera':
            showToast('📷 启动AI看图识景...');
            startAIImageRecognition();
            break;
        case 'points':
            switchToPage('discover', 'points');
            break;
        case 'navigation':
            showToast('🧭 启动智能导航...');
            break;
        case 'emergency':
            showEmergencyHelp();
            break;
        case 'voice':
            startVoiceRecognition();
            break;
        default:
            showToast(`🔧 ${tool} 功能启动中...`);
    }
}

// AI工具处理
function handleAITool(tool) {
    console.log('🤖 处理AI工具:', tool);
    
    switch (tool) {
        case 'camera':
            startAIImageRecognition();
            break;
        case 'voice':
            startVoiceRecognition();
            break;
        default:
            showToast(`🤖 ${tool} AI功能启动中...`);
    }
}

// 语音识别功能
function startVoiceRecognition() {
    console.log('🎤 启动语音识别');
    
    if ('webkitSpeechRecognition' in window) {
        const recognition = new webkitSpeechRecognition();
        recognition.lang = 'zh-CN';
        recognition.continuous = false;
        recognition.interimResults = false;
        
        recognition.onstart = function() {
            showToast('🎤 小乐正在听取您的语音...');
        };
        
        recognition.onresult = function(event) {
            const result = event.results[0][0].transcript;
            showToast(`🤖 识别结果：${result}`);
            
            // 模拟AI回复
            setTimeout(() => {
                const response = generateAIResponse(result);
                showToast(response);
            }, 1000);
        };
        
        recognition.onerror = function() {
            showToast('❌ 语音识别失败，请重试');
        };
        
        recognition.start();
    } else {
        showToast('❌ 您的浏览器不支持语音识别');
    }
}

// AI回复生成
function generateAIResponse(query) {
    const responses = {
        '骆岗公园': '🌟 骆岗公园是合肥最大的城市中央公园，今天天气不错，人流较少，很适合游玩！',
        '天气': '☀️ 今天合肥天气晴朗，24°C，适合户外活动',
        '停车': '🅿️ 骆岗公园P3停车场还有87个空位',
        '美食': '🍽️ 推荐骆岗公园内的航空主题咖啡厅',
        '活动': '🎪 今晚19:30有骆岗公园主题灯光秀'
    };
    
    for (let key in responses) {
        if (query.includes(key)) {
            return responses[key];
        }
    }
    
    return '🤖 小乐正在为您查询相关信息...';
}

// 其他功能函数
function showLuogangDetail() {
    console.log('🏞️ 显示骆岗公园详情');
    showToast('🏞️ 正在加载骆岗公园详情页面...');

    setTimeout(() => {
        showToast('✅ 骆岗公园详情页面加载完成！');
    }, 1000);
}

function showSpotInfo(spot) {
    console.log('📍 显示景点信息:', spot);

    const spotInfo = {
        luogang: '骆岗公园 - 人流较少，适合游玩',
        baogong: '包公园 - 人流适中',
        binhu: '滨湖湿地 - 推荐游玩',
        kexuedao: '科学岛 - 科创之旅'
    };

    const info = spotInfo[spot] || `${spot} - 景点信息`;
    showToast(info);
}

function showAIAssistant() {
    console.log('🤖 显示AI助手');
    showToast('🤖 小乐：您好！有什么可以帮助您的吗？');

    setTimeout(() => {
        showToast('💬 您可以问我关于骆岗公园的任何问题');
    }, 1500);
}

function startAIImageRecognition() {
    console.log('📷 启动AI图像识别');
    showToast('📷 启动AI看图识景功能...');

    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices.getUserMedia({ video: true })
            .then(stream => {
                showToast('🎯 请将镜头对准景点拍摄');

                // 模拟AI识别过程
                setTimeout(() => {
                    stream.getTracks().forEach(track => track.stop());
                    showToast('✅ 识别成功：这是骆岗公园的航空馆！');
                }, 3000);
            })
            .catch(error => {
                showToast('❌ 无法访问相机，请检查权限设置');
            });
    } else {
        showToast('❌ 您的设备不支持相机功能');
    }
}

function showEmergencyHelp() {
    console.log('🚨 显示紧急求助');
    showToast('🚨 紧急求助功能已启动');

    setTimeout(() => {
        showToast('📍 您的位置已发送至服务中心');
    }, 1000);

    setTimeout(() => {
        showToast('🏥 最近急救点：骆岗公园医疗站 (200m)');
    }, 2000);
}

function showPlanningInterface() {
    console.log('📋 显示规划界面');
    showToast('🤖 AI智能规划界面启动中...');

    setTimeout(() => {
        showToast('📋 请选择您的游玩偏好和预算');
    }, 1000);

    setTimeout(() => {
        showToast('✅ AI正在为您生成专属行程...');
    }, 3000);

    setTimeout(() => {
        showToast('🎯 专属行程生成完成！');
        switchToPage('itinerary');
    }, 5000);
}

// 辅助函数
function getPageName(page) {
    const names = {
        'home': '首页',
        'explore': '探索',
        'itinerary': '行程',
        'discover': '发现',
        'profile': '我的'
    };
    return names[page] || page;
}

// Toast提示函数
function showToast(message) {
    console.log('💬 Toast:', message);

    // 移除现有的toast
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }

    // 创建新的toast
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 12px 20px;
        border-radius: 20px;
        font-size: 14px;
        z-index: 9999;
        max-width: 80%;
        text-align: center;
        animation: toastFadeIn 0.3s ease;
    `;

    document.body.appendChild(toast);

    // 自动移除
    setTimeout(() => {
        toast.style.animation = 'toastFadeOut 0.3s ease';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 2000);
}

// 添加必要的CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes toastFadeIn {
        from { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
        to { opacity: 1; transform: translate(-50%, -50%) scale(1); }
    }

    @keyframes toastFadeOut {
        from { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        to { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    }

    .page {
        transition: all 0.3s ease;
    }

    .nav-item, .access-item, .attraction-card, .map-marker, .floating-assistant {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .nav-item:active, .access-item:active, .attraction-card:active,
    .map-marker:active, .floating-assistant:active {
        transform: scale(0.95);
    }
`;
document.head.appendChild(style);

// 最终检查
console.log('=== 🔍 乐游合肥应用功能检查 ===');
console.log('导航项:', document.querySelectorAll('.nav-item').length);
console.log('页面数:', document.querySelectorAll('.page').length);
console.log('快捷入口:', document.querySelectorAll('.access-item').length);
console.log('景点卡片:', document.querySelectorAll('.attraction-card').length);
console.log('地图标记:', document.querySelectorAll('.map-marker').length);
console.log('智能规划按钮:', document.getElementById('startPlanning') ? '✅' : '❌');
console.log('悬浮助手:', document.getElementById('floatingAssistant') ? '✅' : '❌');
console.log('语音搜索:', document.getElementById('voiceSearch') ? '✅' : '❌');
console.log('=== ✅ 检查完成 ===');
