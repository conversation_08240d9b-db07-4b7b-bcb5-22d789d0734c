<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小骆助手 - 骆岗公园</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #2c3e50;
        }

        .app-container {
            max-width: 414px;
            margin: 0 auto;
            background: #f8fffe;
            min-height: 100vh;
            position: relative;
        }

        .header {
            background: linear-gradient(135deg, #3498db, #2ecc71);
            padding: 16px;
            display: flex;
            align-items: center;
            color: white;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .back-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            margin-right: 16px;
            color: white;
        }

        .header-content {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .bot-avatar {
            width: 40px;
            height: 40px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .bot-info {
            flex: 1;
        }

        .bot-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .bot-status {
            font-size: 12px;
            opacity: 0.9;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: calc(100vh - 140px);
        }

        .chat-messages {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .message {
            display: flex;
            gap: 8px;
            max-width: 85%;
        }

        .message.user {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }

        .message-avatar.bot {
            background: linear-gradient(135deg, #3498db, #2ecc71);
            color: white;
        }

        .message-avatar.user {
            background: #95a5a6;
            color: white;
        }

        .message-content {
            background: white;
            padding: 12px 16px;
            border-radius: 18px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
        }

        .message.user .message-content {
            background: #3498db;
            color: white;
        }

        .message-text {
            font-size: 14px;
            line-height: 1.4;
        }

        .message-time {
            font-size: 10px;
            color: #95a5a6;
            margin-top: 4px;
        }

        .message.user .message-time {
            color: rgba(255,255,255,0.8);
        }

        /* Quick Questions */
        .quick-questions {
            padding: 16px;
            background: white;
            border-top: 1px solid #ecf0f1;
        }

        .quick-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 12px;
            color: #2c3e50;
        }

        .question-buttons {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .question-btn {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 12px 16px;
            border-radius: 12px;
            text-align: left;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 13px;
            color: #2c3e50;
        }

        .question-btn:hover {
            background: #e3f2fd;
            border-color: #3498db;
            transform: translateY(-1px);
        }

        /* Input Area */
        .input-area {
            padding: 16px;
            background: white;
            border-top: 1px solid #ecf0f1;
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #e9ecef;
            border-radius: 24px;
            font-size: 14px;
            outline: none;
            background: #f8f9fa;
        }

        .message-input:focus {
            border-color: #3498db;
            background: white;
        }

        .send-btn {
            width: 40px;
            height: 40px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.3s;
        }

        .send-btn:hover {
            background: #2980b9;
            transform: scale(1.05);
        }

        .send-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }

        /* Typing Indicator */
        .typing-indicator {
            display: none;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            background: white;
            border-radius: 18px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 85%;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background: #95a5a6;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.5;
            }
            30% {
                transform: translateY(-10px);
                opacity: 1;
            }
        }

        /* Welcome Message */
        .welcome-message {
            text-align: center;
            padding: 40px 20px;
            color: #7f8c8d;
        }

        .welcome-avatar {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #3498db, #2ecc71);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: white;
            margin: 0 auto 16px;
            animation: bounce 2s infinite;
        }

        .welcome-text {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .welcome-desc {
            font-size: 14px;
            line-height: 1.4;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        /* Notifications */
        .notification {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 12px 24px;
            border-radius: 8px;
            background: #3498db;
            color: white;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            animation: fadeIn 0.3s, fadeOut 0.3s 2.7s;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translate(-50%, 20px); }
            to { opacity: 1; transform: translate(-50%, 0); }
        }

        @keyframes fadeOut {
            from { opacity: 1; transform: translate(-50%, 0); }
            to { opacity: 0; transform: translate(-50%, 20px); }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">←</button>
            <div class="header-content">
                <div class="bot-avatar">🦌</div>
                <div class="bot-info">
                    <div class="bot-name">小骆助手</div>
                    <div class="bot-status">在线 · 随时为您服务</div>
                </div>
            </div>
        </div>

        <div class="chat-container">
            <div class="chat-messages" id="chatMessages">
                <div class="welcome-message">
                    <div class="welcome-avatar">🦌</div>
                    <div class="welcome-text">你好！我是小骆助手</div>
                    <div class="welcome-desc">我可以帮您了解骆岗公园的各种信息<br>有什么问题尽管问我吧！</div>
                </div>
            </div>

            <!-- Quick Questions -->
            <div class="quick-questions" id="quickQuestions">
                <div class="quick-title">💡 常见问题</div>
                <div class="question-buttons">
                    <button class="question-btn" onclick="askQuestion('骆岗公园的开放时间是什么时候？')">
                        🕐 骆岗公园的开放时间是什么时候？
                    </button>
                    <button class="question-btn" onclick="askQuestion('有哪些必看的景点推荐？')">
                        🏛️ 有哪些必看的景点推荐？
                    </button>
                    <button class="question-btn" onclick="askQuestion('园区内有哪些交通方式？')">
                        🚌 园区内有哪些交通方式？
                    </button>
                    <button class="question-btn" onclick="askQuestion('适合带小朋友游玩吗？')">
                        👶 适合带小朋友游玩吗？
                    </button>
                    <button class="question-btn" onclick="askQuestion('园区内有餐饮服务吗？')">
                        🍽️ 园区内有餐饮服务吗？
                    </button>
                    <button class="question-btn" onclick="askQuestion('如何到达骆岗公园？')">
                        🚗 如何到达骆岗公园？
                    </button>
                </div>
            </div>

            <!-- Typing Indicator -->
            <div class="typing-indicator" id="typingIndicator">
                <div class="message-avatar bot">🦌</div>
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>

            <!-- Input Area -->
            <div class="input-area">
                <input type="text" class="message-input" id="messageInput" placeholder="输入您的问题..." onkeypress="handleKeyPress(event)">
                <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                    ➤
                </button>
            </div>
        </div>
    </div>

    <script>
        let messageHistory = [];

        function goBack() {
            window.history.back();
        }

        function askQuestion(question) {
            document.getElementById('messageInput').value = question;
            sendMessage();
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;

            // Add user message
            addMessage(message, 'user');
            input.value = '';

            // Hide quick questions after first message
            if (messageHistory.length === 1) {
                document.getElementById('quickQuestions').style.display = 'none';
            }

            // Show typing indicator
            showTypingIndicator();

            // Simulate bot response
            setTimeout(() => {
                hideTypingIndicator();
                const response = getBotResponse(message);
                addMessage(response, 'bot');
            }, 1500 + Math.random() * 1000);
        }

        function addMessage(text, sender) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageElement = document.createElement('div');
            messageElement.className = `message ${sender}`;
            
            const now = new Date();
            const timeString = now.getHours().toString().padStart(2, '0') + ':' + 
                             now.getMinutes().toString().padStart(2, '0');

            messageElement.innerHTML = `
                <div class="message-avatar ${sender}">
                    ${sender === 'bot' ? '🦌' : '👤'}
                </div>
                <div class="message-content">
                    <div class="message-text">${text}</div>
                    <div class="message-time">${timeString}</div>
                </div>
            `;

            messagesContainer.appendChild(messageElement);
            messageHistory.push({ text, sender, time: timeString });
            
            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function showTypingIndicator() {
            const indicator = document.getElementById('typingIndicator');
            const messagesContainer = document.getElementById('chatMessages');
            indicator.style.display = 'flex';
            messagesContainer.appendChild(indicator);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function hideTypingIndicator() {
            const indicator = document.getElementById('typingIndicator');
            indicator.style.display = 'none';
        }

        function getBotResponse(message) {
            const responses = {
                '开放时间': '骆岗公园全天24小时开放，但建议您在8:00-18:00期间游览，这时各个场馆和设施都正常开放。夜间虽然可以进入，但部分设施会关闭。',
                '景点推荐': '为您推荐几个必看景点：\n🏛️ 信标台 - 园区地标，可俯瞰全景\n✈️ 航空科普馆 - 了解航空历史\n🌿 梦想大草坪 - 适合休闲野餐\n🏢 城市馆 - 合肥城市发展展示\n🦋 昆虫博物馆 - 自然科普教育',
                '交通方式': '园区内提供多种交通方式：\n🚌 无人巴士 - 免费，15分钟一班\n🚐 接驳车 - 5-8元，覆盖主要景点\n🚶 步行 - 推荐，可以慢慢欣赏风景\n🚲 共享单车 - 部分区域可骑行',
                '小朋友': '非常适合带小朋友！我们有：\n👶 儿童游乐区 - 安全有趣的游乐设施\n🦋 昆虫博物馆 - 寓教于乐的科普体验\n✈️ 航空科普馆 - 飞行模拟器体验\n🌿 大草坪 - 可以奔跑玩耍\n还有专门的亲子路线推荐哦！',
                '餐饮服务': '园区内有多种餐饮选择：\n☕ 骆岗咖啡厅 - 精品咖啡和轻食\n🥪 便利店 - 饮料零食\n🧺 野餐区 - 可自带食物\n🍱 临时餐车 - 节假日会有特色小食\n建议您也可以自带一些食物在草坪上野餐！',
                '如何到达': '到达骆岗公园的方式：\n🚇 地铁：乘坐地铁到骆岗站，步行约10分钟\n🚌 公交：多路公交车可达，在骆岗公园站下车\n🚗 自驾：园区周边有停车场\n🚕 打车：直接导航"骆岗公园"即可\n建议使用公共交通，更加环保便捷！'
            };

            // Simple keyword matching
            for (const [keyword, response] of Object.entries(responses)) {
                if (message.includes(keyword)) {
                    return response;
                }
            }

            // Default responses
            const defaultResponses = [
                '感谢您的提问！我正在努力学习更多关于骆岗公园的知识。您可以尝试问我关于开放时间、景点推荐、交通方式等问题。',
                '这是个很好的问题！建议您可以查看园区地图或咨询现场工作人员获取更详细的信息。',
                '我会把您的问题记录下来，持续改进我的服务。您还可以尝试问我其他关于骆岗公园的问题。',
                '抱歉我暂时无法准确回答这个问题。您可以拨打园区服务热线或查看官方信息获取帮助。'
            ];

            return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('messageInput').focus();
        });
    </script>
</body>
</html>
