<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库源配置管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            text-align: center;
            border: 1px solid #e5e7eb;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #4f46e5;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #6b7280;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 500;
        }

        .controls {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
            border: 1px solid #e5e7eb;
        }

        .search-box {
            flex: 1;
            min-width: 300px;
        }

        .search-box input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .search-box input:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .filter-group {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .filter-select {
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-select:focus {
            outline: none;
            border-color: #4f46e5;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-align: center;
        }

        .btn-primary {
            background: #4f46e5;
            color: white;
        }

        .btn-primary:hover {
            background: #4338ca;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.25);
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.25);
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.25);
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.25);
        }

        .btn-info {
            background: #06b6d4;
            color: white;
        }

        .btn-info:hover {
            background: #0891b2;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(6, 182, 212, 0.25);
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(107, 114, 128, 0.25);
        }

        .table-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: 1px solid #e5e7eb;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #f9fafb;
            padding: 18px 16px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
            font-size: 14px;
        }

        .table td {
            padding: 16px;
            border-bottom: 1px solid #f3f4f6;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background-color: #f9fafb;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-connected {
            background: #d1fae5;
            color: #065f46;
        }

        .status-disconnected {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-testing {
            background: #fef3c7;
            color: #92400e;
        }

        .db-type-badge {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .db-mysql {
            background: #dbeafe;
            color: #1e40af;
        }

        .db-postgresql {
            background: #e0e7ff;
            color: #3730a3;
        }

        .db-oracle {
            background: #fef3c7;
            color: #92400e;
        }

        .db-sqlserver {
            background: #f3e8ff;
            color: #7c2d12;
        }

        .db-mongodb {
            background: #d1fae5;
            color: #065f46;
        }

        .actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .actions .btn {
            padding: 8px 12px;
            font-size: 12px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(4px);
        }

        .modal-content {
            background-color: white;
            margin: 3% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 700px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.25);
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 25px 30px;
            border-bottom: 2px solid #f3f4f6;
            background: #f9fafb;
            border-radius: 12px 12px 0 0;
        }

        .modal-header h2 {
            color: #1f2937;
            font-size: 1.5em;
            font-weight: 700;
        }

        .modal-body {
            padding: 30px;
        }

        .close {
            color: #9ca3af;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #374151;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .modal-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            padding: 25px 30px;
            border-top: 2px solid #f3f4f6;
            background: #f9fafb;
            border-radius: 0 0 12px 12px;
        }

        .test-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }

        .test-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .test-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .test-loading {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fcd34d;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-group {
                justify-content: space-between;
                width: 100%;
            }

            .table-container {
                overflow-x: auto;
            }

            .actions {
                flex-direction: column;
            }

            .modal-content {
                margin: 5% auto;
                width: 95%;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>数据库源配置管理</h1>
            <p>管理和配置数据库连接源 | 支持多种数据库类型 | 实时连接测试</p>
        </div>

        <!-- 统计卡片 -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalSources">8</div>
                <div class="stat-label">总数据源</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="connectedSources">6</div>
                <div class="stat-label">已连接</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="disconnectedSources">2</div>
                <div class="stat-label">连接异常</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="dbTypes">4</div>
                <div class="stat-label">数据库类型</div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="controls">
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="搜索数据源名称、主机地址或描述...">
            </div>
            <div class="filter-group">
                <select id="dbTypeFilter" class="filter-select">
                    <option value="">全部类型</option>
                    <option value="mysql">MySQL</option>
                    <option value="postgresql">PostgreSQL</option>
                    <option value="oracle">Oracle</option>
                    <option value="sqlserver">SQL Server</option>
                    <option value="mongodb">MongoDB</option>
                </select>
                <select id="statusFilter" class="filter-select">
                    <option value="">全部状态</option>
                    <option value="connected">已连接</option>
                    <option value="disconnected">连接异常</option>
                </select>
                <button class="btn btn-success" onclick="showAddModal()">
                    <span>+</span> 新增数据源
                </button>
                <button class="btn btn-primary" onclick="refreshData()">
                    <span>🔄</span> 刷新
                </button>
            </div>
        </div>

        <!-- 数据源表格 -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>数据源名称</th>
                        <th>数据库类型</th>
                        <th>主机地址</th>
                        <th>端口</th>
                        <th>数据库名</th>
                        <th>连接状态</th>
                        <th>最后测试时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="dataSourceTableBody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- 新增/编辑数据源模态框 -->
    <div id="dataSourceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">新增数据源</h2>
                <span class="close" onclick="closeModal('dataSourceModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="dataSourceForm">
                    <div class="form-group">
                        <label for="sourceName">数据源名称 *</label>
                        <input type="text" id="sourceName" required placeholder="请输入数据源名称">
                    </div>

                    <div class="form-group">
                        <label for="description">描述</label>
                        <textarea id="description" rows="3" placeholder="请输入数据源描述（可选）"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="dbType">数据库类型 *</label>
                            <select id="dbType" required onchange="updateDefaultPort()">
                                <option value="">请选择数据库类型</option>
                                <option value="mysql">MySQL</option>
                                <option value="postgresql">PostgreSQL</option>
                                <option value="oracle">Oracle</option>
                                <option value="sqlserver">SQL Server</option>
                                <option value="mongodb">MongoDB</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="host">主机地址 *</label>
                            <input type="text" id="host" required placeholder="localhost">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="port">端口 *</label>
                            <input type="number" id="port" required placeholder="3306">
                        </div>
                        <div class="form-group">
                            <label for="database">数据库名 *</label>
                            <input type="text" id="database" required placeholder="database_name">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="username">用户名 *</label>
                            <input type="text" id="username" required placeholder="username">
                        </div>
                        <div class="form-group">
                            <label for="password">密码 *</label>
                            <input type="password" id="password" required placeholder="password">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="connectionString">连接字符串（高级）</label>
                        <textarea id="connectionString" rows="2" placeholder="可选：自定义连接字符串，留空则自动生成"></textarea>
                    </div>

                    <div class="form-group">
                        <button type="button" class="btn btn-info" onclick="testConnection()">
                            <span>🔗</span> 测试连接
                        </button>
                        <div id="testResult"></div>
                    </div>
                </form>
            </div>
            <div class="modal-actions">
                <button class="btn btn-secondary" onclick="closeModal('dataSourceModal')">取消</button>
                <button class="btn btn-primary" onclick="saveDataSource()">保存</button>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div id="confirmModal" class="modal">
        <div class="modal-content" style="max-width: 500px;">
            <div class="modal-header">
                <h2>确认删除</h2>
                <span class="close" onclick="closeModal('confirmModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div id="confirmMessage">
                    <!-- 确认消息将动态设置 -->
                </div>
            </div>
            <div class="modal-actions">
                <button class="btn btn-secondary" onclick="closeModal('confirmModal')">取消</button>
                <button class="btn btn-danger" id="confirmButton" onclick="executeDelete()">确认删除</button>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据源数据
        let dataSources = [
            {
                id: 1,
                name: "生产环境MySQL",
                description: "生产环境主数据库",
                type: "mysql",
                host: "prod-mysql.company.com",
                port: 3306,
                database: "production_db",
                username: "prod_user",
                password: "****",
                status: "connected",
                lastTestTime: "2024-01-15 14:30:25",
                connectionString: ""
            },
            {
                id: 2,
                name: "测试环境PostgreSQL",
                description: "测试环境数据库",
                type: "postgresql",
                host: "test-pg.company.com",
                port: 5432,
                database: "test_db",
                username: "test_user",
                password: "****",
                status: "connected",
                lastTestTime: "2024-01-15 13:45:12",
                connectionString: ""
            },
            {
                id: 3,
                name: "数据仓库Oracle",
                description: "数据仓库和分析系统",
                type: "oracle",
                host: "dw-oracle.company.com",
                port: 1521,
                database: "DWDB",
                username: "dw_user",
                password: "****",
                status: "connected",
                lastTestTime: "2024-01-15 12:20:33",
                connectionString: ""
            },
            {
                id: 4,
                name: "报表系统SQL Server",
                description: "报表和BI系统数据库",
                type: "sqlserver",
                host: "report-sql.company.com",
                port: 1433,
                database: "ReportDB",
                username: "report_user",
                password: "****",
                status: "disconnected",
                lastTestTime: "2024-01-14 16:15:45",
                connectionString: ""
            },
            {
                id: 5,
                name: "日志系统MongoDB",
                description: "应用日志和监控数据",
                type: "mongodb",
                host: "log-mongo.company.com",
                port: 27017,
                database: "logs",
                username: "log_user",
                password: "****",
                status: "connected",
                lastTestTime: "2024-01-15 11:30:18",
                connectionString: ""
            },
            {
                id: 6,
                name: "开发环境MySQL",
                description: "开发团队本地数据库",
                type: "mysql",
                host: "dev-mysql.company.com",
                port: 3306,
                database: "dev_db",
                username: "dev_user",
                password: "****",
                status: "connected",
                lastTestTime: "2024-01-15 10:45:22",
                connectionString: ""
            },
            {
                id: 7,
                name: "备份PostgreSQL",
                description: "数据备份和恢复系统",
                type: "postgresql",
                host: "backup-pg.company.com",
                port: 5432,
                database: "backup_db",
                username: "backup_user",
                password: "****",
                status: "connected",
                lastTestTime: "2024-01-15 09:20:15",
                connectionString: ""
            },
            {
                id: 8,
                name: "缓存Redis集群",
                description: "Redis缓存集群",
                type: "redis",
                host: "redis-cluster.company.com",
                port: 6379,
                database: "0",
                username: "",
                password: "****",
                status: "disconnected",
                lastTestTime: "2024-01-14 18:30:45",
                connectionString: ""
            }
        ];

        let currentEditId = null;
        let currentDeleteId = null;

        // 默认端口映射
        const defaultPorts = {
            mysql: 3306,
            postgresql: 5432,
            oracle: 1521,
            sqlserver: 1433,
            mongodb: 27017,
            redis: 6379
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadDataSources();
            setupEventListeners();
        });

        // 设置事件监听器
        function setupEventListeners() {
            // 搜索功能
            document.getElementById('searchInput').addEventListener('input', filterData);
            document.getElementById('dbTypeFilter').addEventListener('change', filterData);
            document.getElementById('statusFilter').addEventListener('change', filterData);

            // 模态框点击外部关闭
            window.addEventListener('click', function(event) {
                if (event.target.classList.contains('modal')) {
                    event.target.style.display = 'none';
                }
            });

            // 表单提交处理
            document.getElementById('dataSourceForm').addEventListener('submit', function(event) {
                event.preventDefault();
                saveDataSource();
            });
        }

        // 加载数据源到表格
        function loadDataSources(data = dataSources) {
            const tbody = document.getElementById('dataSourceTableBody');
            tbody.innerHTML = '';

            data.forEach(source => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <strong>${source.name}</strong>
                        ${source.description ? `<br><small style="color: #6b7280;">${source.description}</small>` : ''}
                    </td>
                    <td><span class="db-type-badge db-${source.type}">${source.type.toUpperCase()}</span></td>
                    <td>${source.host}</td>
                    <td>${source.port}</td>
                    <td>${source.database}</td>
                    <td><span class="status-badge status-${source.status}">${getStatusText(source.status)}</span></td>
                    <td>${source.lastTestTime}</td>
                    <td>
                        <div class="actions">
                            <button class="btn btn-info" onclick="testSingleConnection(${source.id})" title="测试连接">
                                <span>🔗</span>
                            </button>
                            <button class="btn btn-warning" onclick="editDataSource(${source.id})" title="编辑">
                                <span>✏️</span>
                            </button>
                            <button class="btn btn-danger" onclick="confirmDelete(${source.id})" title="删除">
                                <span>🗑️</span>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });

            updateStats(data);
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'connected': '已连接',
                'disconnected': '连接异常',
                'testing': '测试中'
            };
            return statusMap[status] || status;
        }

        // 更新统计数据
        function updateStats(data = dataSources) {
            const total = data.length;
            const connected = data.filter(item => item.status === 'connected').length;
            const disconnected = data.filter(item => item.status === 'disconnected').length;
            const types = new Set(data.map(item => item.type)).size;

            document.getElementById('totalSources').textContent = total;
            document.getElementById('connectedSources').textContent = connected;
            document.getElementById('disconnectedSources').textContent = disconnected;
            document.getElementById('dbTypes').textContent = types;
        }

        // 筛选数据
        function filterData() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const typeFilter = document.getElementById('dbTypeFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            let filteredData = dataSources.filter(source => {
                const matchesSearch = source.name.toLowerCase().includes(searchTerm) ||
                                    source.host.toLowerCase().includes(searchTerm) ||
                                    (source.description && source.description.toLowerCase().includes(searchTerm));
                const matchesType = !typeFilter || source.type === typeFilter;
                const matchesStatus = !statusFilter || source.status === statusFilter;

                return matchesSearch && matchesType && matchesStatus;
            });

            loadDataSources(filteredData);
        }

        // 显示新增模态框
        function showAddModal() {
            currentEditId = null;
            document.getElementById('modalTitle').textContent = '新增数据源';
            resetForm();
            document.getElementById('dataSourceModal').style.display = 'block';
        }

        // 编辑数据源
        function editDataSource(id) {
            const source = dataSources.find(item => item.id === id);
            if (!source) return;

            currentEditId = id;
            document.getElementById('modalTitle').textContent = '编辑数据源';

            // 填充表单
            document.getElementById('sourceName').value = source.name;
            document.getElementById('description').value = source.description || '';
            document.getElementById('dbType').value = source.type;
            document.getElementById('host').value = source.host;
            document.getElementById('port').value = source.port;
            document.getElementById('database').value = source.database;
            document.getElementById('username').value = source.username;
            document.getElementById('password').value = source.password;
            document.getElementById('connectionString').value = source.connectionString || '';

            document.getElementById('dataSourceModal').style.display = 'block';
        }

        // 重置表单
        function resetForm() {
            document.getElementById('dataSourceForm').reset();
            document.getElementById('testResult').innerHTML = '';
        }

        // 更新默认端口
        function updateDefaultPort() {
            const dbType = document.getElementById('dbType').value;
            const portInput = document.getElementById('port');

            if (dbType && defaultPorts[dbType] && !portInput.value) {
                portInput.value = defaultPorts[dbType];
            }
        }

        // 测试连接
        function testConnection() {
            const host = document.getElementById('host').value.trim();
            const port = document.getElementById('port').value.trim();
            const database = document.getElementById('database').value.trim();
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();

            if (!host || !port || !database || !username || !password) {
                showTestResult('请填写完整的连接信息！', 'error');
                return;
            }

            showTestResult('正在测试连接...', 'loading');

            // 模拟连接测试
            setTimeout(() => {
                const success = Math.random() > 0.3; // 70%成功率
                if (success) {
                    showTestResult('✅ 连接测试成功！数据库连接正常。', 'success');
                } else {
                    showTestResult('❌ 连接测试失败！请检查连接参数或网络状态。', 'error');
                }
            }, 2000);
        }

        // 显示测试结果
        function showTestResult(message, type) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = `<div class="test-result test-${type}">${message}</div>`;
        }

        // 测试单个数据源连接
        function testSingleConnection(id) {
            const source = dataSources.find(item => item.id === id);
            if (!source) return;

            // 更新状态为测试中
            source.status = 'testing';
            loadDataSources();

            // 模拟连接测试
            setTimeout(() => {
                const success = Math.random() > 0.2; // 80%成功率
                source.status = success ? 'connected' : 'disconnected';
                source.lastTestTime = new Date().toLocaleString('zh-CN');
                loadDataSources();

                if (success) {
                    alert(`✅ ${source.name} 连接测试成功！`);
                } else {
                    alert(`❌ ${source.name} 连接测试失败！`);
                }
            }, 2000);
        }

        // 保存数据源
        function saveDataSource() {
            const name = document.getElementById('sourceName').value.trim();
            const description = document.getElementById('description').value.trim();
            const type = document.getElementById('dbType').value;
            const host = document.getElementById('host').value.trim();
            const port = parseInt(document.getElementById('port').value);
            const database = document.getElementById('database').value.trim();
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const connectionString = document.getElementById('connectionString').value.trim();

            if (!name || !type || !host || !port || !database || !username || !password) {
                alert('请填写所有必填字段！');
                return;
            }

            const sourceData = {
                name,
                description,
                type,
                host,
                port,
                database,
                username,
                password: password === '****' ? password : password, // 保持原密码或更新
                connectionString,
                status: 'disconnected',
                lastTestTime: new Date().toLocaleString('zh-CN')
            };

            if (currentEditId) {
                // 编辑现有数据源
                const index = dataSources.findIndex(item => item.id === currentEditId);
                if (index !== -1) {
                    dataSources[index] = { ...dataSources[index], ...sourceData };
                    alert('数据源已成功更新！');
                }
            } else {
                // 新增数据源
                sourceData.id = Math.max(...dataSources.map(s => s.id)) + 1;
                dataSources.push(sourceData);
                alert('数据源已成功添加！');
            }

            loadDataSources();
            closeModal('dataSourceModal');
        }

        // 确认删除
        function confirmDelete(id) {
            const source = dataSources.find(item => item.id === id);
            if (!source) return;

            currentDeleteId = id;
            document.getElementById('confirmMessage').innerHTML = `
                <p style="margin: 20px 0; font-size: 16px;">
                    确定要删除数据源 <strong>"${source.name}"</strong> 吗？
                </p>
                <p style="color: #ef4444; font-size: 14px;">
                    ⚠️ 此操作不可恢复，删除后相关的数据同步任务可能会受到影响。
                </p>
            `;
            document.getElementById('confirmModal').style.display = 'block';
        }

        // 执行删除
        function executeDelete() {
            if (!currentDeleteId) return;

            const index = dataSources.findIndex(item => item.id === currentDeleteId);
            if (index !== -1) {
                const deletedSource = dataSources[index];
                dataSources.splice(index, 1);
                loadDataSources();
                alert(`数据源 "${deletedSource.name}" 已成功删除！`);
            }

            closeModal('confirmModal');
            currentDeleteId = null;
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';

            if (modalId === 'dataSourceModal') {
                currentEditId = null;
                resetForm();
            }
        }

        // 刷新数据
        function refreshData() {
            // 重置筛选条件
            document.getElementById('searchInput').value = '';
            document.getElementById('dbTypeFilter').value = '';
            document.getElementById('statusFilter').value = '';

            // 重新加载数据
            loadDataSources();
            alert('数据已刷新！');
        }

        // 键盘事件处理
        document.addEventListener('keydown', function(event) {
            // ESC键关闭模态框
            if (event.key === 'Escape') {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    if (modal.style.display === 'block') {
                        modal.style.display = 'none';
                    }
                });
            }
        });

        // 批量测试连接（可选功能）
        function testAllConnections() {
            if (confirm('确定要测试所有数据源的连接吗？这可能需要一些时间。')) {
                dataSources.forEach((source, index) => {
                    setTimeout(() => {
                        testSingleConnection(source.id);
                    }, index * 1000); // 每秒测试一个
                });
            }
        }
    </script>

</body>
</html>
