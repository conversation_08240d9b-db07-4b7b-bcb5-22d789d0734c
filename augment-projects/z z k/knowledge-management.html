<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>结构化知识管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-box {
            flex: 1;
            min-width: 250px;
        }

        .search-box input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .search-box input:focus {
            outline: none;
            border-color: #667eea;
        }

        .filter-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .filter-select {
            padding: 10px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            background: white;
            font-size: 14px;
            cursor: pointer;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background: #f8f9fa;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
        }

        .table td {
            padding: 15px 12px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-draft {
            background: #fff3cd;
            color: #856404;
        }

        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }

        .actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .actions .btn {
            padding: 6px 12px;
            font-size: 12px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .modal-header h2 {
            color: #333;
            font-size: 1.5em;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            color: #333;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .modal-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .add-method {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
            background: #f8f9fa;
        }

        .field-definition {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            align-items: center;
        }

        .field-definition input,
        .field-definition select {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .field-definition .btn {
            padding: 8px 12px;
            font-size: 12px;
        }

        .template-preview,
        .import-preview {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
        }

        .sync-config {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .sync-config h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .calculation-rule {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
        }

        .calculation-rule select,
        .calculation-rule input {
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.success {
            background: #28a745;
        }

        .status-indicator.error {
            background: #dc3545;
        }

        .status-indicator.warning {
            background: #ffc107;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-group {
                justify-content: space-between;
            }

            .table-container {
                overflow-x: auto;
            }

            .actions {
                flex-direction: column;
            }

            .modal-content {
                margin: 10% auto;
                width: 95%;
            }

            .field-definition {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>结构化知识管理系统</h1>
            <p>标准化知识管理 | 数据同步 | 智能转换</p>
        </div>

        <!-- 统计卡片 -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalKnowledge">12</div>
                <div class="stat-label">结构化知识库</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeKnowledge">8</div>
                <div class="stat-label">已发布</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="draftKnowledge">3</div>
                <div class="stat-label">草稿</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalSize">1.2GB</div>
                <div class="stat-label">总体量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="syncCount">5</div>
                <div class="stat-label">同步任务</div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="controls">
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="搜索知识名称或描述...">
            </div>
            <div class="filter-group">
                <select id="statusFilter" class="filter-select">
                    <option value="">全部状态</option>
                    <option value="active">已发布</option>
                    <option value="draft">草稿</option>
                    <option value="offline">已下架</option>
                </select>
                <select id="sortBy" class="filter-select">
                    <option value="updateTime">按更新时间</option>
                    <option value="name">按名称</option>
                    <option value="size">按体量</option>
                    <option value="items">按条目数</option>
                </select>
                <button class="btn btn-success" onclick="showAddModal()">+ 新增知识</button>
                <button class="btn btn-primary" onclick="refreshData()">刷新</button>
            </div>
        </div>

        <!-- 知识表格 -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>知识名称</th>
                        <th>说明</th>
                        <th>状态</th>
                        <th>条目数</th>
                        <th>体量</th>
                        <th>更新时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="knowledgeTableBody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- 查看知识详情模态框 -->
    <div id="viewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>查看知识详情</h2>
                <span class="close" onclick="closeModal('viewModal')">&times;</span>
            </div>
            <div id="viewContent">
                <!-- 详情内容将动态加载 -->
            </div>
            <div class="modal-actions">
                <button class="btn btn-secondary" onclick="closeModal('viewModal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 编辑知识模态框 -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>编辑知识</h2>
                <span class="close" onclick="closeModal('editModal')">&times;</span>
            </div>
            <form id="editForm">
                <div class="form-group">
                    <label for="editName">知识名称</label>
                    <input type="text" id="editName" required>
                </div>
                <div class="form-group">
                    <label for="editDescription">知识描述</label>
                    <textarea id="editDescription" required></textarea>
                </div>
                <div class="form-group">
                    <label for="editStatus">状态</label>
                    <select id="editStatus" required>
                        <option value="draft">草稿</option>
                        <option value="active">已发布</option>
                        <option value="offline">已下架</option>
                    </select>
                </div>
            </form>
            <div class="modal-actions">
                <button class="btn btn-secondary" onclick="closeModal('editModal')">取消</button>
                <button class="btn btn-primary" onclick="saveEdit()">保存</button>
            </div>
        </div>
    </div>

    <!-- 确认操作模态框 -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>确认操作</h2>
                <span class="close" onclick="closeModal('confirmModal')">&times;</span>
            </div>
            <div id="confirmMessage">
                <!-- 确认消息将动态设置 -->
            </div>
            <div class="modal-actions">
                <button class="btn btn-secondary" onclick="closeModal('confirmModal')">取消</button>
                <button class="btn btn-danger" id="confirmButton" onclick="executeAction()">确认</button>
            </div>
        </div>
    </div>

    <!-- 知识详情展示模态框 -->
    <div id="filterModal" class="modal">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h2 id="knowledgeDetailTitle">知识详情</h2>
                <span class="close" onclick="closeModal('filterModal')">&times;</span>
            </div>
            <div class="controls" style="margin-bottom: 20px;">
                <div class="search-box">
                    <input type="text" id="itemSearchInput" placeholder="搜索条目内容...">
                </div>
                <div class="filter-group">
                    <select id="itemTypeFilter" class="filter-select">
                        <option value="">全部字段</option>
                        <option value="string">文本</option>
                        <option value="number">数值</option>
                        <option value="date">日期</option>
                        <option value="boolean">布尔</option>
                    </select>
                    <button class="btn btn-info" onclick="exportToUnstructured()">转换为非结构化知识</button>
                </div>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead id="itemTableHead">
                        <!-- 表头将动态生成 -->
                    </thead>
                    <tbody id="itemTableBody">
                        <!-- 条目数据将动态加载 -->
                    </tbody>
                </table>
            </div>
            <div class="modal-actions">
                <button class="btn btn-secondary" onclick="closeModal('filterModal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 新增知识模态框 -->
    <div id="addModal" class="modal">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h2>新增结构化知识</h2>
                <span class="close" onclick="closeModal('addModal')">&times;</span>
            </div>

            <!-- 添加方式选择 -->
            <div class="form-group">
                <label>添加方式</label>
                <div style="display: flex; gap: 10px; margin-top: 10px;">
                    <button class="btn btn-primary" onclick="showAddMethod('manual')" id="manualBtn">手动填写</button>
                    <button class="btn btn-secondary" onclick="showAddMethod('template')" id="templateBtn">标准模板</button>
                    <button class="btn btn-secondary" onclick="showAddMethod('import')" id="importBtn">批量导入</button>
                    <button class="btn btn-secondary" onclick="showAddMethod('database')" id="databaseBtn">数据库同步</button>
                    <button class="btn btn-secondary" onclick="showAddMethod('api')" id="apiBtn">API接口</button>
                </div>
            </div>

            <!-- 基础信息 -->
            <div id="basicInfo">
                <div class="form-group">
                    <label for="addName">知识名称 *</label>
                    <input type="text" id="addName" required placeholder="请输入知识名称">
                </div>
                <div class="form-group">
                    <label for="addDescription">说明 *</label>
                    <textarea id="addDescription" required placeholder="请输入知识说明"></textarea>
                </div>
            </div>

            <!-- 手动填写 -->
            <div id="manualMethod" class="add-method">
                <div class="form-group">
                    <label>字段定义</label>
                    <div id="fieldDefinitions">
                        <div class="field-definition">
                            <input type="text" placeholder="字段名" class="field-name">
                            <select class="field-type">
                                <option value="string">文本</option>
                                <option value="number">数值</option>
                                <option value="date">日期</option>
                                <option value="boolean">布尔</option>
                            </select>
                            <input type="text" placeholder="默认值" class="field-default">
                            <button type="button" onclick="removeField(this)" class="btn btn-danger">删除</button>
                        </div>
                    </div>
                    <button type="button" onclick="addField()" class="btn btn-info">+ 添加字段</button>
                </div>
            </div>

            <!-- 标准模板 -->
            <div id="templateMethod" class="add-method" style="display: none;">
                <div class="form-group">
                    <label for="templateSelect">选择模板</label>
                    <select id="templateSelect" onchange="loadTemplate()">
                        <option value="">请选择模板</option>
                        <option value="user">用户信息模板</option>
                        <option value="product">产品信息模板</option>
                        <option value="order">订单信息模板</option>
                        <option value="financial">财务指标模板</option>
                    </select>
                </div>
                <div id="templatePreview"></div>
            </div>

            <!-- 批量导入 -->
            <div id="importMethod" class="add-method" style="display: none;">
                <div class="form-group">
                    <label for="importFile">选择文件</label>
                    <input type="file" id="importFile" accept=".csv,.xlsx,.json" onchange="previewImport()">
                    <small>支持 CSV、Excel、JSON 格式</small>
                </div>
                <div id="importPreview"></div>
            </div>

            <!-- 数据库同步 -->
            <div id="databaseMethod" class="add-method" style="display: none;">
                <div class="form-group">
                    <label for="dbType">数据库类型</label>
                    <select id="dbType">
                        <option value="mysql">MySQL</option>
                        <option value="postgresql">PostgreSQL</option>
                        <option value="oracle">Oracle</option>
                        <option value="sqlserver">SQL Server</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="dbHost">主机地址</label>
                    <input type="text" id="dbHost" placeholder="localhost">
                </div>
                <div class="form-group">
                    <label for="dbPort">端口</label>
                    <input type="number" id="dbPort" placeholder="3306">
                </div>
                <div class="form-group">
                    <label for="dbName">数据库名</label>
                    <input type="text" id="dbName" placeholder="database_name">
                </div>
                <div class="form-group">
                    <label for="dbTable">表名</label>
                    <input type="text" id="dbTable" placeholder="table_name">
                </div>
                <div class="form-group">
                    <label for="dbUser">用户名</label>
                    <input type="text" id="dbUser" placeholder="username">
                </div>
                <div class="form-group">
                    <label for="dbPassword">密码</label>
                    <input type="password" id="dbPassword" placeholder="password">
                </div>
                <button type="button" onclick="testConnection()" class="btn btn-info">测试连接</button>
            </div>

            <!-- API接口 -->
            <div id="apiMethod" class="add-method" style="display: none;">
                <div class="form-group">
                    <label for="apiUrl">API地址</label>
                    <input type="url" id="apiUrl" placeholder="https://api.example.com/data">
                </div>
                <div class="form-group">
                    <label for="apiMethod">请求方法</label>
                    <select id="apiMethodSelect">
                        <option value="GET">GET</option>
                        <option value="POST">POST</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="apiHeaders">请求头 (JSON格式)</label>
                    <textarea id="apiHeaders" placeholder='{"Authorization": "Bearer token"}'></textarea>
                </div>
                <div class="form-group">
                    <label for="apiParams">请求参数 (JSON格式)</label>
                    <textarea id="apiParams" placeholder='{"page": 1, "limit": 100}'></textarea>
                </div>
                <button type="button" onclick="testApi()" class="btn btn-info">测试API</button>
            </div>

            <div class="modal-actions">
                <button class="btn btn-secondary" onclick="closeModal('addModal')">取消</button>
                <button class="btn btn-primary" onclick="saveNewKnowledge()">保存</button>
            </div>
        </div>
    </div>

    <script>
        // 模拟结构化知识数据
        let knowledgeData = [
            {
                id: 1,
                name: "用户基础信息表",
                description: "用户注册、登录、个人资料等标准化信息",
                status: "active",
                items: 15420,
                size: "45.2 MB",
                updateTime: "2024-01-15 14:30:25",
                type: "database",
                syncStatus: "success"
            },
            {
                id: 2,
                name: "产品销售数据",
                description: "产品销售记录、价格、库存等结构化数据",
                status: "active",
                items: 8930,
                size: "128.7 MB",
                updateTime: "2024-01-14 09:15:42",
                type: "api",
                syncStatus: "success"
            },
            {
                id: 3,
                name: "财务指标统计",
                description: "月度、季度财务指标，支持自动计算和汇总",
                status: "draft",
                items: 2340,
                size: "23.8 MB",
                updateTime: "2024-01-13 16:45:18",
                type: "manual",
                syncStatus: "warning"
            },
            {
                id: 4,
                name: "客户服务记录",
                description: "客户咨询、投诉、处理结果等服务数据",
                status: "active",
                items: 12650,
                size: "67.4 MB",
                updateTime: "2024-01-12 11:20:33",
                type: "import",
                syncStatus: "success"
            },
            {
                id: 5,
                name: "设备监控数据",
                description: "IoT设备状态、性能指标、告警信息",
                status: "offline",
                items: 45600,
                size: "234.1 MB",
                updateTime: "2024-01-11 13:55:07",
                type: "api",
                syncStatus: "error"
            },
            {
                id: 6,
                name: "员工绩效评估",
                description: "员工KPI、考核结果、薪资调整等HR数据",
                status: "active",
                items: 890,
                size: "12.3 MB",
                updateTime: "2024-01-10 08:40:15",
                type: "template",
                syncStatus: "success"
            }
        ];

        // 模拟结构化数据
        let structuredData = {
            1: {
                fields: ["用户ID", "用户名", "邮箱", "注册时间", "状态", "积分"],
                data: [
                    ["U001", "张三", "<EMAIL>", "2024-01-01", "活跃", 1250],
                    ["U002", "李四", "<EMAIL>", "2024-01-02", "活跃", 890],
                    ["U003", "王五", "<EMAIL>", "2024-01-03", "暂停", 450],
                    ["U004", "赵六", "<EMAIL>", "2024-01-04", "活跃", 2100],
                    ["U005", "钱七", "<EMAIL>", "2024-01-05", "活跃", 780]
                ]
            },
            2: {
                fields: ["产品ID", "产品名称", "价格", "库存", "销量", "评分"],
                data: [
                    ["P001", "智能手机", 2999.00, 150, 89, 4.5],
                    ["P002", "笔记本电脑", 5999.00, 45, 23, 4.8],
                    ["P003", "平板电脑", 1999.00, 200, 156, 4.2],
                    ["P004", "智能手表", 1299.00, 300, 234, 4.6],
                    ["P005", "无线耳机", 399.00, 500, 445, 4.3]
                ]
            },
            3: {
                fields: ["月份", "收入", "支出", "利润", "利润率"],
                data: [
                    ["2024-01", 1500000, 1200000, 300000, "20%"],
                    ["2024-02", 1800000, 1350000, 450000, "25%"],
                    ["2024-03", 2100000, 1500000, 600000, "28.6%"],
                    ["2024-04", 1950000, 1400000, 550000, "28.2%"]
                ]
            }
        };

        // 标准模板定义
        let templates = {
            user: {
                name: "用户信息模板",
                fields: [
                    { name: "用户ID", type: "string", default: "" },
                    { name: "用户名", type: "string", default: "" },
                    { name: "邮箱", type: "string", default: "" },
                    { name: "手机号", type: "string", default: "" },
                    { name: "注册时间", type: "date", default: "" },
                    { name: "状态", type: "string", default: "活跃" },
                    { name: "积分", type: "number", default: 0 }
                ]
            },
            product: {
                name: "产品信息模板",
                fields: [
                    { name: "产品ID", type: "string", default: "" },
                    { name: "产品名称", type: "string", default: "" },
                    { name: "价格", type: "number", default: 0 },
                    { name: "库存", type: "number", default: 0 },
                    { name: "分类", type: "string", default: "" },
                    { name: "状态", type: "string", default: "上架" }
                ]
            },
            order: {
                name: "订单信息模板",
                fields: [
                    { name: "订单ID", type: "string", default: "" },
                    { name: "用户ID", type: "string", default: "" },
                    { name: "产品ID", type: "string", default: "" },
                    { name: "数量", type: "number", default: 1 },
                    { name: "金额", type: "number", default: 0 },
                    { name: "订单时间", type: "date", default: "" },
                    { name: "状态", type: "string", default: "待处理" }
                ]
            },
            financial: {
                name: "财务指标模板",
                fields: [
                    { name: "期间", type: "string", default: "" },
                    { name: "收入", type: "number", default: 0 },
                    { name: "支出", type: "number", default: 0 },
                    { name: "利润", type: "number", default: 0 },
                    { name: "利润率", type: "string", default: "0%" }
                ]
            }
        };

        let currentEditId = null;
        let currentAction = null;
        let currentActionId = null;
        let currentAddMethod = 'manual';
        let currentViewingKnowledge = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadKnowledgeData();
            setupEventListeners();
        });

        // 设置事件监听器
        function setupEventListeners() {
            // 搜索功能
            document.getElementById('searchInput').addEventListener('input', filterData);
            document.getElementById('statusFilter').addEventListener('change', filterData);
            document.getElementById('sortBy').addEventListener('change', sortData);

            // 条目搜索
            document.getElementById('itemSearchInput').addEventListener('input', filterItems);
            document.getElementById('itemTypeFilter').addEventListener('change', filterItems);

            // 模态框点击外部关闭
            window.addEventListener('click', function(event) {
                if (event.target.classList.contains('modal')) {
                    event.target.style.display = 'none';
                }
            });
        }

        // 加载知识数据到表格
        function loadKnowledgeData(data = knowledgeData) {
            const tbody = document.getElementById('knowledgeTableBody');
            tbody.innerHTML = '';

            data.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <strong>${item.name}</strong>
                        <br><small><span class="status-indicator ${item.syncStatus}"></span>${getTypeText(item.type)}</small>
                    </td>
                    <td>${item.description}</td>
                    <td><span class="status-badge status-${item.status}">${getStatusText(item.status)}</span></td>
                    <td>${item.items.toLocaleString()}</td>
                    <td>${item.size}</td>
                    <td>${item.updateTime}</td>
                    <td>
                        <div class="actions">
                            <button class="btn btn-info" onclick="viewKnowledgeDetail(${item.id})">查看</button>
                            <button class="btn btn-warning" onclick="editKnowledge(${item.id})">编辑</button>
                            ${item.status === 'draft' ?
                                `<button class="btn btn-success" onclick="confirmAction('publish', ${item.id})">发布</button>` :
                                item.status === 'active' ?
                                `<button class="btn btn-secondary" onclick="confirmAction('offline', ${item.id})">下架</button>` :
                                `<button class="btn btn-success" onclick="confirmAction('publish', ${item.id})">发布</button>`
                            }
                            <button class="btn btn-danger" onclick="confirmAction('delete', ${item.id})">删除</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });

            updateStats(data);
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'active': '已发布',
                'draft': '草稿',
                'offline': '已下架'
            };
            return statusMap[status] || status;
        }

        // 获取类型文本
        function getTypeText(type) {
            const typeMap = {
                'database': '数据库同步',
                'api': 'API接口',
                'manual': '手动录入',
                'import': '批量导入',
                'template': '标准模板'
            };
            return typeMap[type] || type;
        }

        // 更新统计数据
        function updateStats(data = knowledgeData) {
            const total = data.length;
            const active = data.filter(item => item.status === 'active').length;
            const draft = data.filter(item => item.status === 'draft').length;

            // 计算总大小（简化计算）
            let totalSizeNum = 0;
            data.forEach(item => {
                const sizeStr = item.size.replace(/[^\d.]/g, '');
                totalSizeNum += parseFloat(sizeStr);
            });

            document.getElementById('totalKnowledge').textContent = total;
            document.getElementById('activeKnowledge').textContent = active;
            document.getElementById('draftKnowledge').textContent = draft;
            document.getElementById('totalSize').textContent = totalSizeNum.toFixed(1) + 'MB';
            document.getElementById('syncCount').textContent = knowledgeData.filter(item => item.type === 'api' || item.type === 'database').length;
        }

        // 筛选数据
        function filterData() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;

            let filteredData = knowledgeData.filter(item => {
                const matchesSearch = item.name.toLowerCase().includes(searchTerm) ||
                                    item.description.toLowerCase().includes(searchTerm);
                const matchesStatus = !statusFilter || item.status === statusFilter;

                return matchesSearch && matchesStatus;
            });

            loadKnowledgeData(filteredData);
        }

        // 排序数据
        function sortData() {
            const sortBy = document.getElementById('sortBy').value;

            let sortedData = [...knowledgeData].sort((a, b) => {
                switch(sortBy) {
                    case 'name':
                        return a.name.localeCompare(b.name);
                    case 'size':
                        const sizeA = parseFloat(a.size.replace(/[^\d.]/g, ''));
                        const sizeB = parseFloat(b.size.replace(/[^\d.]/g, ''));
                        return sizeB - sizeA;
                    case 'items':
                        return b.items - a.items;
                    case 'updateTime':
                    default:
                        return new Date(b.updateTime) - new Date(a.updateTime);
                }
            });

            loadKnowledgeData(sortedData);
        }

        // 查看知识详情（列表形式展示结构化数据）
        function viewKnowledgeDetail(id) {
            const knowledge = knowledgeData.find(item => item.id === id);
            if (!knowledge) return;

            currentViewingKnowledge = id;
            const data = structuredData[id];

            if (data) {
                document.getElementById('knowledgeDetailTitle').textContent = knowledge.name;
                loadStructuredData(data);
                document.getElementById('filterModal').style.display = 'block';
            } else {
                alert('暂无结构化数据');
            }
        }

        // 加载结构化数据到表格
        function loadStructuredData(data) {
            const thead = document.getElementById('itemTableHead');
            const tbody = document.getElementById('itemTableBody');

            // 生成表头
            thead.innerHTML = `
                <tr>
                    ${data.fields.map(field => `<th>${field}</th>`).join('')}
                </tr>
            `;

            // 生成数据行
            tbody.innerHTML = '';
            data.data.forEach(row => {
                const tr = document.createElement('tr');
                tr.innerHTML = row.map(cell => `<td>${cell}</td>`).join('');
                tbody.appendChild(tr);
            });
        }

        // 转换为非结构化知识
        function exportToUnstructured() {
            if (!currentViewingKnowledge) return;

            const knowledge = knowledgeData.find(item => item.id === currentViewingKnowledge);
            const data = structuredData[currentViewingKnowledge];

            if (!knowledge || !data) return;

            // 生成HTML格式的非结构化知识
            let html = `
                <h1>${knowledge.name}</h1>
                <p><strong>说明：</strong>${knowledge.description}</p>
                <p><strong>数据更新时间：</strong>${knowledge.updateTime}</p>
                <h2>数据内容</h2>
                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <thead>
                        <tr style="background-color: #f0f0f0;">
                            ${data.fields.map(field => `<th style="padding: 8px; border: 1px solid #ddd;">${field}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody>
                        ${data.data.map(row => `
                            <tr>
                                ${row.map(cell => `<td style="padding: 8px; border: 1px solid #ddd;">${cell}</td>`).join('')}
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
                <p><strong>总计：</strong>${data.data.length} 条记录</p>
            `;

            // 创建新窗口显示转换结果
            const newWindow = window.open('', '_blank');
            newWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>${knowledge.name} - 非结构化知识</title>
                    <meta charset="UTF-8">
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        table { margin: 20px 0; }
                        h1 { color: #333; }
                        h2 { color: #666; }
                    </style>
                </head>
                <body>
                    ${html}
                </body>
                </html>
            `);
            newWindow.document.close();

            alert('已成功转换为非结构化知识并在新窗口中打开！');
        }

        // 显示新增知识模态框
        function showAddModal() {
            // 重置表单
            document.getElementById('addName').value = '';
            document.getElementById('addDescription').value = '';

            // 重置添加方式
            showAddMethod('manual');

            document.getElementById('addModal').style.display = 'block';
        }

        // 切换添加方式
        function showAddMethod(method) {
            currentAddMethod = method;

            // 重置按钮样式
            document.querySelectorAll('#addModal .btn').forEach(btn => {
                if (btn.id.includes('Btn')) {
                    btn.className = 'btn btn-secondary';
                }
            });

            // 高亮当前选中的方式
            document.getElementById(method + 'Btn').className = 'btn btn-primary';

            // 隐藏所有方法面板
            document.querySelectorAll('.add-method').forEach(panel => {
                panel.style.display = 'none';
            });

            // 显示当前选中的方法面板
            document.getElementById(method + 'Method').style.display = 'block';
        }

        // 添加字段
        function addField() {
            const container = document.getElementById('fieldDefinitions');
            const fieldDiv = document.createElement('div');
            fieldDiv.className = 'field-definition';
            fieldDiv.innerHTML = `
                <input type="text" placeholder="字段名" class="field-name">
                <select class="field-type">
                    <option value="string">文本</option>
                    <option value="number">数值</option>
                    <option value="date">日期</option>
                    <option value="boolean">布尔</option>
                </select>
                <input type="text" placeholder="默认值" class="field-default">
                <button type="button" onclick="removeField(this)" class="btn btn-danger">删除</button>
            `;
            container.appendChild(fieldDiv);
        }

        // 删除字段
        function removeField(button) {
            button.parentElement.remove();
        }

        // 加载模板
        function loadTemplate() {
            const templateKey = document.getElementById('templateSelect').value;
            if (!templateKey || !templates[templateKey]) return;

            const template = templates[templateKey];
            const preview = document.getElementById('templatePreview');

            preview.innerHTML = `
                <h4>${template.name}</h4>
                <table class="table">
                    <thead>
                        <tr>
                            <th>字段名</th>
                            <th>类型</th>
                            <th>默认值</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${template.fields.map(field => `
                            <tr>
                                <td>${field.name}</td>
                                <td>${field.type}</td>
                                <td>${field.default}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
        }

        // 预览导入文件
        function previewImport() {
            const file = document.getElementById('importFile').files[0];
            if (!file) return;

            const preview = document.getElementById('importPreview');
            preview.innerHTML = `
                <h4>文件信息</h4>
                <p><strong>文件名：</strong>${file.name}</p>
                <p><strong>文件大小：</strong>${(file.size / 1024).toFixed(2)} KB</p>
                <p><strong>文件类型：</strong>${file.type}</p>
                <p><em>注：实际项目中这里会解析文件内容并显示预览</em></p>
            `;
        }

        // 测试数据库连接
        function testConnection() {
            const dbType = document.getElementById('dbType').value;
            const dbHost = document.getElementById('dbHost').value;
            const dbPort = document.getElementById('dbPort').value;
            const dbName = document.getElementById('dbName').value;

            if (!dbHost || !dbPort || !dbName) {
                alert('请填写完整的数据库连接信息！');
                return;
            }

            // 模拟连接测试
            setTimeout(() => {
                alert('数据库连接测试成功！');
            }, 1000);
        }

        // 测试API
        function testApi() {
            const apiUrl = document.getElementById('apiUrl').value;
            const apiMethod = document.getElementById('apiMethodSelect').value;

            if (!apiUrl) {
                alert('请填写API地址！');
                return;
            }

            // 模拟API测试
            setTimeout(() => {
                alert('API连接测试成功！');
            }, 1000);
        }

        // 保存新知识
        function saveNewKnowledge() {
            const name = document.getElementById('addName').value.trim();
            const description = document.getElementById('addDescription').value.trim();

            if (!name || !description) {
                alert('请填写知识名称和说明！');
                return;
            }

            // 生成新的知识对象
            const newKnowledge = {
                id: knowledgeData.length + 1,
                name: name,
                description: description,
                status: 'draft',
                items: 0,
                size: '0 MB',
                updateTime: new Date().toLocaleString('zh-CN'),
                type: currentAddMethod,
                syncStatus: 'success'
            };

            // 根据添加方式设置不同的属性
            switch(currentAddMethod) {
                case 'manual':
                    const fields = [];
                    document.querySelectorAll('.field-definition').forEach(fieldDiv => {
                        const name = fieldDiv.querySelector('.field-name').value.trim();
                        const type = fieldDiv.querySelector('.field-type').value;
                        const defaultValue = fieldDiv.querySelector('.field-default').value.trim();
                        if (name) {
                            fields.push({ name, type, default: defaultValue });
                        }
                    });
                    newKnowledge.items = fields.length;
                    break;
                case 'template':
                    const templateKey = document.getElementById('templateSelect').value;
                    if (templateKey && templates[templateKey]) {
                        newKnowledge.items = templates[templateKey].fields.length;
                    }
                    break;
                case 'import':
                    const file = document.getElementById('importFile').files[0];
                    if (file) {
                        newKnowledge.size = (file.size / 1024 / 1024).toFixed(2) + ' MB';
                        newKnowledge.items = Math.floor(Math.random() * 1000) + 100; // 模拟导入条目数
                    }
                    break;
            }

            // 添加到数据中
            knowledgeData.push(newKnowledge);

            // 刷新表格
            loadKnowledgeData();

            // 关闭模态框
            closeModal('addModal');

            alert('新知识已成功创建！');
        }

        // 编辑知识
        function editKnowledge(id) {
            const knowledge = knowledgeData.find(item => item.id === id);
            if (!knowledge) return;

            currentEditId = id;
            document.getElementById('editName').value = knowledge.name;
            document.getElementById('editDescription').value = knowledge.description;
            document.getElementById('editStatus').value = knowledge.status;

            document.getElementById('editModal').style.display = 'block';
        }

        // 保存编辑
        function saveEdit() {
            if (!currentEditId) return;

            const name = document.getElementById('editName').value.trim();
            const description = document.getElementById('editDescription').value.trim();
            const status = document.getElementById('editStatus').value;

            if (!name || !description) {
                alert('请填写完整的知识名称和描述！');
                return;
            }

            const knowledgeIndex = knowledgeData.findIndex(item => item.id === currentEditId);
            if (knowledgeIndex !== -1) {
                knowledgeData[knowledgeIndex].name = name;
                knowledgeData[knowledgeIndex].description = description;
                knowledgeData[knowledgeIndex].status = status;
                knowledgeData[knowledgeIndex].updateTime = new Date().toLocaleString('zh-CN');

                loadKnowledgeData();
                closeModal('editModal');
                alert('知识信息已成功更新！');
            }
        }

        // 确认操作
        function confirmAction(action, id) {
            currentAction = action;
            currentActionId = id;

            const knowledge = knowledgeData.find(item => item.id === id);
            if (!knowledge) return;

            let message = '';
            let buttonText = '确认';

            switch(action) {
                case 'publish':
                    message = `确定要发布知识库 "${knowledge.name}" 吗？发布后用户将可以访问此知识库。`;
                    buttonText = '发布';
                    break;
                case 'offline':
                    message = `确定要下架知识库 "${knowledge.name}" 吗？下架后用户将无法访问此知识库。`;
                    buttonText = '下架';
                    break;
                case 'delete':
                    message = `确定要删除知识库 "${knowledge.name}" 吗？此操作不可恢复，将永久删除所有相关数据。`;
                    buttonText = '删除';
                    break;
            }

            document.getElementById('confirmMessage').innerHTML = `<p style="margin: 20px 0; font-size: 16px;">${message}</p>`;
            document.getElementById('confirmButton').textContent = buttonText;
            document.getElementById('confirmModal').style.display = 'block';
        }

        // 执行操作
        function executeAction() {
            if (!currentAction || !currentActionId) return;

            const knowledgeIndex = knowledgeData.findIndex(item => item.id === currentActionId);
            if (knowledgeIndex === -1) return;

            switch(currentAction) {
                case 'publish':
                    knowledgeData[knowledgeIndex].status = 'active';
                    knowledgeData[knowledgeIndex].updateTime = new Date().toLocaleString('zh-CN');
                    alert('知识库已成功发布！');
                    break;
                case 'offline':
                    knowledgeData[knowledgeIndex].status = 'offline';
                    knowledgeData[knowledgeIndex].updateTime = new Date().toLocaleString('zh-CN');
                    alert('知识库已成功下架！');
                    break;
                case 'delete':
                    knowledgeData.splice(knowledgeIndex, 1);
                    alert('知识库已成功删除！');
                    break;
            }

            loadKnowledgeData();
            closeModal('confirmModal');

            // 重置操作状态
            currentAction = null;
            currentActionId = null;
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';

            // 重置编辑状态
            if (modalId === 'editModal') {
                currentEditId = null;
            }
        }

        // 刷新数据
        function refreshData() {
            // 重置筛选条件
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('sortBy').value = 'updateTime';

            // 重新加载数据
            loadKnowledgeData();
            alert('数据已刷新！');
        }

        // 键盘事件处理
        document.addEventListener('keydown', function(event) {
            // ESC键关闭模态框
            if (event.key === 'Escape') {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    if (modal.style.display === 'block') {
                        modal.style.display = 'none';
                    }
                });
            }
        });

        // 表单提交处理
        document.getElementById('editForm').addEventListener('submit', function(event) {
            event.preventDefault();
            saveEdit();
        });
    </script>

</body>
</html>
