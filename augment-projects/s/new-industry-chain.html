<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产业链图谱展示平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: white;
            margin: 10px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 15px 20px;
            text-align: center;
            border-radius: 10px 10px 0 0;
        }

        .header h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
        }

        .header p {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            font-weight: 500;
        }

        .legend-color {
            width: 14px;
            height: 14px;
            border-radius: 3px;
        }

        .advantage { background: #28a745; }
        .strong { background: #007bff; }
        .weak { background: #ffc107; }
        .missing { background: #dc3545; }

        .main-content {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        .chain-section {
            flex: 0 0 70%;
            padding: 15px;
            overflow-y: auto;
            border-right: 3px solid #007bff;
        }

        .company-section {
            flex: 0 0 30%;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
        }

        .company-header {
            padding: 15px;
            background: white;
            border-bottom: 2px solid #007bff;
        }

        .company-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .company-subtitle {
            font-size: 0.9em;
            color: #6c757d;
        }

        .info-tabs {
            display: flex;
            background: white;
            border-bottom: 1px solid #e9ecef;
        }

        .tab-btn {
            flex: 1;
            padding: 10px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 0.85em;
            font-weight: 500;
            color: #6c757d;
            transition: all 0.2s ease;
        }

        .tab-btn.active {
            color: #007bff;
            background: #f8f9fa;
            border-bottom: 2px solid #007bff;
        }

        .tab-btn:hover {
            background: #f8f9fa;
        }

        .info-content {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
        }

        .chain-level {
            margin-bottom: 20px;
        }

        .level-title {
            text-align: center;
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 12px;
            color: #2c3e50;
            padding: 8px;
            background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }

        .level-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 12px;
        }

        .chain-node {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .chain-node:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .chain-node.no-subnodes {
            border-color: #007bff;
        }

        .chain-node.no-subnodes:hover {
            border-color: #0056b3;
            background: #f8f9fa;
        }

        .node-title {
            font-size: 0.9em;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
            text-align: center;
            padding-bottom: 6px;
            border-bottom: 1px solid #e9ecef;
        }

        .sub-nodes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(75px, 1fr));
            gap: 5px;
            margin-top: 8px;
        }

        .sub-node {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 5px;
            padding: 6px 4px;
            text-align: center;
            font-size: 0.7em;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            min-height: 50px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .sub-node:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
        }

        .sub-node.selected {
            background: #007bff;
            color: white;
            border-color: #0056b3;
        }

        .sub-node.advantage { border-color: #28a745; background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); }
        .sub-node.strong { border-color: #007bff; background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); }
        .sub-node.weak { border-color: #ffc107; background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%); }
        .sub-node.missing { border-color: #dc3545; background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); }

        .sub-node-name {
            font-weight: 600;
            margin-bottom: 2px;
        }

        .sub-node-count {
            font-size: 0.8em;
            opacity: 0.8;
        }

        .status-badge {
            position: absolute;
            top: -3px;
            right: -3px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid white;
        }

        .info-card {
            background: white;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .info-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .info-card.company {
            border-left: 4px solid #007bff;
        }

        .info-card.investment {
            border-left: 4px solid #28a745;
        }

        .info-card.talent {
            border-left: 4px solid #17a2b8;
        }

        .info-card.research {
            border-left: 4px solid #ffc107;
        }

        .card-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 0.95em;
        }

        .card-category {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.75em;
            display: inline-block;
            margin-bottom: 6px;
        }

        .card-info {
            font-size: 0.8em;
            color: #6c757d;
            line-height: 1.4;
            margin-bottom: 6px;
        }

        .card-meta {
            font-size: 0.75em;
            color: #6c757d;
            line-height: 1.3;
        }

        .meta-item {
            margin-bottom: 3px;
            display: flex;
            align-items: flex-start;
            gap: 4px;
        }

        .meta-icon {
            flex-shrink: 0;
            margin-top: 1px;
        }

        .reset-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 0.8em;
            margin-top: 8px;
            transition: all 0.2s ease;
        }

        .reset-btn:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 3% auto;
            padding: 25px;
            border-radius: 12px;
            width: 85%;
            max-width: 900px;
            max-height: 85vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            color: #000;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>产业链图谱展示平台</h1>
            <p>三级产业链结构 | 企业招商对接 | 人才科研合作</p>
        </div>

        <div class="legend">
            <div class="legend-item">
                <div class="legend-color advantage"></div>
                <span>优势链</span>
            </div>
            <div class="legend-item">
                <div class="legend-color strong"></div>
                <span>强链</span>
            </div>
            <div class="legend-item">
                <div class="legend-color weak"></div>
                <span>弱链</span>
            </div>
            <div class="legend-item">
                <div class="legend-color missing"></div>
                <span>缺链</span>
            </div>
        </div>

        <div class="main-content">
            <div class="chain-section">
                <!-- 产业链内容将通过JavaScript生成 -->
            </div>

            <div class="company-section">
                <div class="company-header">
                    <div class="company-title" id="infoTitle">产业链信息</div>
                    <div class="company-subtitle" id="infoSubtitle">点击左侧环节查看详细信息</div>
                    <button class="reset-btn" onclick="resetView()">重置视图</button>
                </div>
                <div class="info-tabs">
                    <button class="tab-btn active" onclick="switchTab('companies')">🏢 企业</button>
                    <button class="tab-btn" onclick="switchTab('investment')">💼 招商</button>
                    <button class="tab-btn" onclick="switchTab('talent')">👥 人才</button>
                    <button class="tab-btn" onclick="switchTab('research')">🏛️ 科研</button>
                </div>
                <div class="info-content" id="infoContent">
                    <!-- 信息内容将通过JavaScript生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="companyModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modalContent"></div>
        </div>
    </div>

    <script>
        // 产业链数据结构
        const industryChainData = [
            {
                title: "上游 - 原材料与能源",
                nodes: [
                    {
                        title: "原材料供应 (2,150家)",
                        subNodes: [
                            { name: "钢铁冶炼", count: "456家", status: "advantage" },
                            { name: "化工原料", count: "389家", status: "strong" },
                            { name: "有色金属", count: "400家", status: "advantage" },
                            { name: "稀土材料", count: "120家", status: "weak" },
                            { name: "建筑材料", count: "280家", status: "strong" },
                            { name: "新材料", count: "150家", status: "weak" },
                            { name: "复合材料", count: "355家", status: "missing" }
                        ]
                    },
                    {
                        title: "能源供应 (1,890家)",
                        subNodes: [
                            { name: "电力供应", count: "234家", status: "strong" },
                            { name: "石油开采", count: "345家", status: "advantage" },
                            { name: "天然气", count: "313家", status: "strong" },
                            { name: "太阳能", count: "480家", status: "advantage" },
                            { name: "风能", count: "350家", status: "strong" },
                            { name: "核能", count: "45家", status: "weak" },
                            { name: "氢能", count: "123家", status: "missing" }
                        ]
                    },
                    {
                        title: "基础设备 (1,567家)",
                        subNodes: [
                            { name: "机械制造", count: "434家", status: "weak" },
                            { name: "工具制造", count: "289家", status: "weak" },
                            { name: "自动化设备", count: "244家", status: "missing" },
                            { name: "精密仪器", count: "180家", status: "weak" },
                            { name: "模具制造", count: "220家", status: "strong" },
                            { name: "检测设备", count: "120家", status: "missing" },
                            { name: "工业软件", count: "80家", status: "missing" }
                        ]
                    },
                    {
                        title: "原料加工 (890家)",
                        subNodes: [
                            { name: "金属加工", count: "345家", status: "strong" },
                            { name: "塑料加工", count: "267家", status: "weak" },
                            { name: "纤维加工", count: "178家", status: "weak" },
                            { name: "陶瓷加工", count: "100家", status: "missing" }
                        ]
                    },
                    {
                        title: "能源存储 (456家)",
                        // 没有三级节点的情况
                        subNodes: null
                    }
                ]
            },
            {
                title: "中游 - 生产制造",
                nodes: [
                    {
                        title: "核心制造 (3,200家)",
                        subNodes: [
                            { name: "产品组装", count: "856家", status: "strong" },
                            { name: "精密加工", count: "678家", status: "advantage" },
                            { name: "质量检测", count: "622家", status: "strong" },
                            { name: "表面处理", count: "445家", status: "weak" },
                            { name: "热处理", count: "334家", status: "weak" },
                            { name: "包装加工", count: "265家", status: "strong" }
                        ]
                    },
                    {
                        title: "技术研发 (2,100家)",
                        subNodes: [
                            { name: "研发中心", count: "567家", status: "advantage" },
                            { name: "产品设计", count: "489家", status: "advantage" },
                            { name: "技术创新", count: "733家", status: "advantage" },
                            { name: "工艺优化", count: "311家", status: "strong" },
                            { name: "标准制定", count: "89家", status: "missing" },
                            { name: "知识产权", count: "211家", status: "weak" }
                        ]
                    },
                    {
                        title: "智能制造 (1,200家)",
                        // 没有三级节点
                        subNodes: null
                    }
                ]
            },
            {
                title: "下游 - 销售与服务",
                nodes: [
                    {
                        title: "销售渠道 (4,500家)",
                        subNodes: [
                            { name: "批发商", count: "1234家", status: "strong" },
                            { name: "零售商", count: "1567家", status: "advantage" },
                            { name: "电商平台", count: "655家", status: "advantage" },
                            { name: "代理商", count: "445家", status: "strong" },
                            { name: "经销商", count: "399家", status: "strong" },
                            { name: "直销", count: "200家", status: "weak" }
                        ]
                    },
                    {
                        title: "售后服务 (2,800家)",
                        subNodes: [
                            { name: "维修服务", count: "789家", status: "advantage" },
                            { name: "技术支持", count: "654家", status: "strong" },
                            { name: "培训服务", count: "544家", status: "strong" },
                            { name: "咨询服务", count: "433家", status: "weak" },
                            { name: "升级服务", count: "234家", status: "weak" },
                            { name: "回收服务", count: "146家", status: "missing" }
                        ]
                    },
                    {
                        title: "物流配送 (1,900家)",
                        subNodes: [
                            { name: "仓储服务", count: "445家", status: "weak" },
                            { name: "运输服务", count: "589家", status: "weak" },
                            { name: "配送服务", count: "389家", status: "strong" },
                            { name: "供应链", count: "267家", status: "weak" },
                            { name: "冷链物流", count: "134家", status: "missing" },
                            { name: "跨境物流", count: "75家", status: "missing" }
                        ]
                    },
                    {
                        title: "金融服务 (890家)",
                        subNodes: [
                            { name: "供应链金融", count: "234家", status: "strong" },
                            { name: "产业基金", count: "156家", status: "weak" },
                            { name: "保险服务", count: "345家", status: "strong" },
                            { name: "融资租赁", count: "155家", status: "weak" }
                        ]
                    },
                    {
                        title: "数据服务 (567家)",
                        subNodes: [
                            { name: "数据分析", count: "234家", status: "advantage" },
                            { name: "云计算", count: "189家", status: "strong" },
                            { name: "物联网", count: "144家", status: "weak" }
                        ]
                    },
                    {
                        title: "平台服务 (1,200家)",
                        // 没有三级节点
                        subNodes: null
                    },
                    {
                        title: "国际贸易 (678家)",
                        subNodes: [
                            { name: "进出口贸易", count: "345家", status: "strong" },
                            { name: "跨境电商", count: "233家", status: "advantage" },
                            { name: "贸易融资", count: "100家", status: "weak" }
                        ]
                    }
                ]
            }
        ];

        // 企业数据
        const companyData = {
            "钢铁冶炼": [
                {
                    name: "中国宝武钢铁集团",
                    category: "钢铁冶炼",
                    info: "全球第二大钢铁企业，年产量超过1亿吨",
                    revenue: "5200亿元",
                    status: "行业龙头"
                },
                {
                    name: "河钢集团",
                    category: "钢铁冶炼",
                    info: "中国最大的钢铁材料制造和综合服务商",
                    revenue: "3800亿元",
                    status: "大型企业"
                }
            ],
            "化工原料": [
                {
                    name: "中国石化",
                    category: "化工原料",
                    info: "亚洲最大的炼油化工一体化企业",
                    revenue: "2.9万亿元",
                    status: "央企巨头"
                },
                {
                    name: "万华化学",
                    category: "化工原料",
                    info: "全球MDI行业领导者",
                    revenue: "1800亿元",
                    status: "创新企业"
                }
            ],
            "太阳能": [
                {
                    name: "隆基绿能",
                    category: "太阳能",
                    info: "全球领先的太阳能科技公司",
                    revenue: "1200亿元",
                    status: "光伏龙头",
                    investment: "硅片电池组件一体化基地",
                    talent: "光伏工程师、材料专家",
                    research: "西安交通大学、中科院光电院"
                },
                {
                    name: "晶科能源",
                    category: "太阳能",
                    info: "全球光伏组件出货量领先企业",
                    revenue: "600亿元",
                    status: "光伏企业",
                    investment: "高效太阳能电池项目",
                    talent: "电池工程师、光伏技术专家",
                    research: "上海交通大学、中科院上海微系统所"
                }
            ],
            "产品组装": [
                {
                    name: "富士康",
                    category: "产品组装",
                    info: "全球最大的电子产品代工制造商",
                    revenue: "4500亿元",
                    status: "代工龙头",
                    investment: "工业互联网平台建设",
                    talent: "工业工程师、AI算法工程师",
                    research: "清华大学、中科院自动化所"
                },
                {
                    name: "比亚迪",
                    category: "产品组装",
                    info: "新能源汽车及电池制造商",
                    revenue: "4200亿元",
                    status: "新能源龙头",
                    investment: "动力电池产业基地",
                    talent: "电池技术专家、汽车工程师",
                    research: "中科院物理所、西安交通大学"
                }
            ],
            "研发中心": [
                {
                    name: "华为技术",
                    category: "研发中心",
                    info: "通信技术全球领先企业",
                    revenue: "6400亿元",
                    status: "技术巨头",
                    investment: "全球研发中心网络建设",
                    talent: "通信工程师、算法专家",
                    research: "清华大学、北京邮电大学"
                },
                {
                    name: "腾讯",
                    category: "研发中心",
                    info: "互联网技术创新企业",
                    revenue: "5600亿元",
                    status: "科技企业",
                    investment: "人工智能研究院",
                    talent: "AI工程师、产品经理",
                    research: "清华大学、中科院计算所"
                }
            ],
            "电商平台": [
                {
                    name: "阿里巴巴",
                    category: "电商平台",
                    info: "中国最大的电商平台",
                    revenue: "8700亿元",
                    status: "电商巨头",
                    investment: "数字经济产业园",
                    talent: "算法工程师、产品经理",
                    research: "浙江大学、清华大学"
                },
                {
                    name: "京东",
                    category: "电商平台",
                    info: "自营电商平台领导者",
                    revenue: "9500亿元",
                    status: "自营电商",
                    investment: "智慧供应链技术中心",
                    talent: "供应链专家、数据科学家",
                    research: "清华大学、中国人民大学"
                }
            ],
            "维修服务": [
                {
                    name: "海尔服务",
                    category: "维修服务",
                    info: "家电售后服务领导者",
                    revenue: "300亿元",
                    status: "服务龙头"
                }
            ],
            // 没有三级节点的二级节点数据
            "能源存储 (456家)": [
                {
                    name: "宁德时代",
                    category: "能源存储",
                    info: "全球领先的动力电池系统提供商",
                    revenue: "3200亿元",
                    status: "电池龙头"
                },
                {
                    name: "比亚迪电池",
                    category: "能源存储",
                    info: "新能源汽车电池制造商",
                    revenue: "1800亿元",
                    status: "电池企业"
                }
            ],
            "智能制造 (1,200家)": [
                {
                    name: "海康威视",
                    category: "智能制造",
                    info: "全球视频监控设备领导者",
                    revenue: "800亿元",
                    status: "智能制造龙头"
                },
                {
                    name: "大华股份",
                    category: "智能制造",
                    info: "视频监控解决方案提供商",
                    revenue: "300亿元",
                    status: "智能制造企业"
                }
            ],
            "平台服务 (1,200家)": [
                {
                    name: "阿里云",
                    category: "平台服务",
                    info: "云计算和数字化转型服务商",
                    revenue: "1000亿元",
                    status: "云服务龙头"
                },
                {
                    name: "腾讯云",
                    category: "平台服务",
                    info: "云计算基础设施服务商",
                    revenue: "500亿元",
                    status: "云服务企业"
                }
            ]
        };

        // 招商线索数据
        const investmentData = {
            "钢铁冶炼": [
                {
                    name: "绿色钢铁技术产业园",
                    category: "钢铁冶炼",
                    info: "建设年产500万吨绿色钢铁生产基地，采用氢能炼钢技术",
                    investment: "200亿元",
                    location: "河北唐山",
                    contact: "张经理 138****1234"
                },
                {
                    name: "智能钢铁制造项目",
                    category: "钢铁冶炼",
                    info: "打造全流程智能化钢铁生产线，实现无人化操作",
                    investment: "150亿元",
                    location: "江苏南京",
                    contact: "李总监 139****5678"
                }
            ],
            "太阳能": [
                {
                    name: "光伏产业园区",
                    category: "太阳能",
                    info: "建设集硅料、硅片、电池、组件于一体的光伏全产业链基地",
                    investment: "300亿元",
                    location: "安徽合肥",
                    contact: "王主任 137****9012"
                }
            ],
            "产品组装": [
                {
                    name: "智能制造产业基地",
                    category: "产品组装",
                    info: "建设新一代信息技术产品智能制造基地",
                    investment: "180亿元",
                    location: "广东深圳",
                    contact: "陈总 136****3456"
                }
            ]
        };

        // 人才需求数据
        const talentData = {
            "钢铁冶炼": [
                {
                    name: "高级冶金工程师",
                    category: "钢铁冶炼",
                    info: "负责绿色钢铁冶炼工艺研发和技术改进",
                    requirement: "博士学历，5年以上相关经验",
                    salary: "年薪50-80万",
                    benefits: "提供住房补贴、子女教育"
                },
                {
                    name: "环保技术专家",
                    category: "钢铁冶炼",
                    info: "负责钢铁生产环保技术研发和污染治理",
                    requirement: "硕士以上学历，环保工程专业",
                    salary: "年薪40-60万",
                    benefits: "五险一金、带薪年假"
                }
            ],
            "太阳能": [
                {
                    name: "光伏技术工程师",
                    category: "太阳能",
                    info: "负责太阳能电池技术研发和产业化",
                    requirement: "硕士学历，材料或电子专业",
                    salary: "年薪30-50万",
                    benefits: "股权激励、培训机会"
                }
            ],
            "研发中心": [
                {
                    name: "AI算法工程师",
                    category: "研发中心",
                    info: "负责人工智能算法研发和产品应用",
                    requirement: "硕士以上学历，计算机相关专业",
                    salary: "年薪60-100万",
                    benefits: "期权激励、弹性工作"
                }
            ]
        };

        // 科研院所数据
        const researchData = {
            "钢铁冶炼": [
                {
                    name: "钢铁研究总院",
                    category: "钢铁冶炼",
                    info: "国家级钢铁行业综合性科研院所",
                    expertise: "钢铁冶金、材料科学、环保技术",
                    cooperation: "技术转让、联合研发、人才培养",
                    contact: "科技处 010-****1234"
                },
                {
                    name: "东北大学材料学院",
                    category: "钢铁冶炼",
                    info: "国内顶尖的材料科学研究机构",
                    expertise: "金属材料、冶金工程、材料加工",
                    cooperation: "产学研合作、技术咨询、学生实习",
                    contact: "合作办 024-****5678"
                }
            ],
            "太阳能": [
                {
                    name: "中科院光电技术研究所",
                    category: "太阳能",
                    info: "光电技术领域国家级科研院所",
                    expertise: "太阳能电池、光伏材料、光电器件",
                    cooperation: "技术开发、成果转化、平台共享",
                    contact: "产业化部 028-****9012"
                }
            ],
            "研发中心": [
                {
                    name: "清华大学人工智能研究院",
                    category: "研发中心",
                    info: "国内领先的人工智能研究机构",
                    expertise: "机器学习、深度学习、智能系统",
                    cooperation: "联合实验室、技术攻关、人才交流",
                    contact: "合作发展部 010-****3456"
                }
            ]
        };

        let currentCategory = null;
        let currentTab = 'companies';

        // 初始化页面
        function initPage() {
            renderIndustryChain();
            resetView();
        }

        // 渲染产业链
        function renderIndustryChain() {
            const chainSection = document.querySelector('.chain-section');
            chainSection.innerHTML = '';

            industryChainData.forEach(level => {
                const levelDiv = document.createElement('div');
                levelDiv.className = 'chain-level';
                levelDiv.innerHTML = `
                    <div class="level-title">${level.title}</div>
                    <div class="level-content">
                        ${level.nodes.map(node => {
                            if (!node.subNodes || node.subNodes.length === 0) {
                                // 没有三级节点的情况
                                return `
                                    <div class="chain-node no-subnodes" onclick="selectCategory('${node.title}')">
                                        <div class="node-title">${node.title}</div>
                                        <div style="text-align: center; padding: 20px; color: #6c757d; font-size: 0.9em;">
                                            点击查看详细信息
                                        </div>
                                    </div>
                                `;
                            } else {
                                // 有三级节点的情况
                                return `
                                    <div class="chain-node">
                                        <div class="node-title">${node.title}</div>
                                        <div class="sub-nodes-grid">
                                            ${node.subNodes.map(subNode => `
                                                <div class="sub-node ${subNode.status}" onclick="selectCategory('${subNode.name}')">
                                                    <div class="status-badge ${subNode.status}"></div>
                                                    <div class="sub-node-name">${subNode.name}</div>
                                                    <div class="sub-node-count">${subNode.count}</div>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                `;
                            }
                        }).join('')}
                    </div>
                `;
                chainSection.appendChild(levelDiv);
            });
        }

        // 选择产业链环节
        function selectCategory(category) {
            // 清除之前的选中状态
            document.querySelectorAll('.sub-node').forEach(node => {
                node.classList.remove('selected');
            });

            // 添加当前选中状态
            event.target.closest('.sub-node').classList.add('selected');

            currentCategory = category;
            document.getElementById('infoTitle').textContent = `${category} - 详细信息`;
            document.getElementById('infoSubtitle').textContent = `查看 ${category} 环节的相关信息`;

            // 显示当前标签页的内容
            showTabContent(currentTab);
        }

        // 切换标签页
        function switchTab(tab) {
            // 更新标签页状态
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            currentTab = tab;
            showTabContent(tab);
        }

        // 显示标签页内容
        function showTabContent(tab) {
            const infoContent = document.getElementById('infoContent');

            if (!currentCategory) {
                infoContent.innerHTML = '<div style="text-align: center; padding: 20px; color: #6c757d;">请先选择左侧的产业链环节</div>';
                return;
            }

            switch(tab) {
                case 'companies':
                    renderCompanies();
                    break;
                case 'investment':
                    renderInvestment();
                    break;
                case 'talent':
                    renderTalent();
                    break;
                case 'research':
                    renderResearch();
                    break;
            }
        }

        // 渲染企业信息
        function renderCompanies() {
            const companies = companyData[currentCategory] || [];
            const infoContent = document.getElementById('infoContent');

            if (companies.length === 0) {
                infoContent.innerHTML = '<div style="text-align: center; padding: 20px; color: #6c757d;">该环节暂无企业数据</div>';
                return;
            }

            infoContent.innerHTML = companies.map(company => `
                <div class="info-card company" onclick="showCompanyDetail('${company.name}')">
                    <div class="card-category">${company.category}</div>
                    <div class="card-title">${company.name}</div>
                    <div class="card-info">${company.info}</div>
                    <div class="card-meta">
                        <div class="meta-item">
                            <span class="meta-icon">💰</span>
                            <span>营收: ${company.revenue}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-icon">🏆</span>
                            <span>地位: ${company.status}</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 渲染招商信息
        function renderInvestment() {
            const investments = investmentData[currentCategory] || [];
            const infoContent = document.getElementById('infoContent');

            if (investments.length === 0) {
                infoContent.innerHTML = '<div style="text-align: center; padding: 20px; color: #6c757d;">该环节暂无招商项目</div>';
                return;
            }

            infoContent.innerHTML = investments.map(investment => `
                <div class="info-card investment">
                    <div class="card-category">${investment.category}</div>
                    <div class="card-title">${investment.name}</div>
                    <div class="card-info">${investment.info}</div>
                    <div class="card-meta">
                        <div class="meta-item">
                            <span class="meta-icon">💰</span>
                            <span>投资额: ${investment.investment}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-icon">📍</span>
                            <span>地点: ${investment.location}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-icon">📞</span>
                            <span>联系: ${investment.contact}</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 渲染人才信息
        function renderTalent() {
            const talents = talentData[currentCategory] || [];
            const infoContent = document.getElementById('infoContent');

            if (talents.length === 0) {
                infoContent.innerHTML = '<div style="text-align: center; padding: 20px; color: #6c757d;">该环节暂无人才需求</div>';
                return;
            }

            infoContent.innerHTML = talents.map(talent => `
                <div class="info-card talent">
                    <div class="card-category">${talent.category}</div>
                    <div class="card-title">${talent.name}</div>
                    <div class="card-info">${talent.info}</div>
                    <div class="card-meta">
                        <div class="meta-item">
                            <span class="meta-icon">🎓</span>
                            <span>要求: ${talent.requirement}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-icon">💰</span>
                            <span>薪资: ${talent.salary}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-icon">🎁</span>
                            <span>福利: ${talent.benefits}</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 渲染科研信息
        function renderResearch() {
            const researches = researchData[currentCategory] || [];
            const infoContent = document.getElementById('infoContent');

            if (researches.length === 0) {
                infoContent.innerHTML = '<div style="text-align: center; padding: 20px; color: #6c757d;">该环节暂无科研院所</div>';
                return;
            }

            infoContent.innerHTML = researches.map(research => `
                <div class="info-card research">
                    <div class="card-category">${research.category}</div>
                    <div class="card-title">${research.name}</div>
                    <div class="card-info">${research.info}</div>
                    <div class="card-meta">
                        <div class="meta-item">
                            <span class="meta-icon">🔬</span>
                            <span>专长: ${research.expertise}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-icon">🤝</span>
                            <span>合作: ${research.cooperation}</span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-icon">📞</span>
                            <span>联系: ${research.contact}</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 重置视图
        function resetView() {
            // 清除选中状态
            document.querySelectorAll('.sub-node').forEach(node => {
                node.classList.remove('selected');
            });

            currentCategory = null;
            document.getElementById('infoTitle').textContent = '产业链信息';
            document.getElementById('infoSubtitle').textContent = '点击左侧环节查看详细信息';

            const infoContent = document.getElementById('infoContent');
            infoContent.innerHTML = '<div style="text-align: center; padding: 20px; color: #6c757d;">点击左侧产业链环节查看对应信息</div>';
        }

        // 显示企业详情
        function showCompanyDetail(companyName) {
            // 查找企业数据
            let company = null;
            for (const category in companyData) {
                const found = companyData[category].find(c => c.name === companyName);
                if (found) {
                    company = found;
                    break;
                }
            }

            if (!company) return;

            const modal = document.getElementById('companyModal');
            const modalContent = document.getElementById('modalContent');

            modalContent.innerHTML = `
                <h2 style="color: #2c3e50; margin-bottom: 20px;">${company.name} - 企业详情</h2>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h3 style="color: #2c3e50; margin: 0;">${company.name}</h3>
                        <span style="background: #007bff; color: white; padding: 6px 12px; border-radius: 15px; font-size: 0.9em;">
                            ${company.category}
                        </span>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div style="text-align: center; background: white; padding: 15px; border-radius: 8px;">
                            <div style="font-size: 1.8em; font-weight: bold; color: #28a745;">${company.revenue}</div>
                            <div style="color: #6c757d;">营收规模</div>
                        </div>
                        <div style="text-align: center; background: white; padding: 15px; border-radius: 8px;">
                            <div style="font-size: 1.2em; font-weight: bold; color: #ffc107;">${company.status}</div>
                            <div style="color: #6c757d;">企业地位</div>
                        </div>
                    </div>
                </div>

                <div style="background: #e9ecef; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">企业简介</h4>
                    <p style="color: #6c757d; line-height: 1.6;">
                        ${company.info}
                    </p>
                </div>

                <div style="background: white; padding: 20px; border-radius: 10px; border: 1px solid #dee2e6; margin-bottom: 20px;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">🤝 合作机会</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div style="background: #d4edda; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;">
                            <h5 style="color: #155724; margin-bottom: 10px;">💼 招商线索</h5>
                            <p style="color: #155724; margin: 0; line-height: 1.5;">${company.investment}</p>
                        </div>
                        <div style="background: #d1ecf1; padding: 15px; border-radius: 8px; border-left: 4px solid #17a2b8;">
                            <h5 style="color: #0c5460; margin-bottom: 10px;">👥 人才需求</h5>
                            <p style="color: #0c5460; margin: 0; line-height: 1.5;">${company.talent}</p>
                        </div>
                        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;">
                            <h5 style="color: #856404; margin-bottom: 10px;">🏛️ 科研合作</h5>
                            <p style="color: #856404; margin: 0; line-height: 1.5;">${company.research}</p>
                        </div>
                    </div>
                </div>

                <div style="background: white; padding: 20px; border-radius: 10px; border: 1px solid #dee2e6;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">📊 企业分析</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <h5 style="color: #28a745; margin-bottom: 10px;">🔥 核心优势</h5>
                            <ul style="color: #6c757d; padding-left: 20px; line-height: 1.6;">
                                <li>行业地位领先，品牌影响力强</li>
                                <li>技术实力雄厚，创新能力突出</li>
                                <li>资金实力充足，抗风险能力强</li>
                            </ul>
                        </div>
                        <div>
                            <h5 style="color: #007bff; margin-bottom: 10px;">📈 发展机遇</h5>
                            <ul style="color: #6c757d; padding-left: 20px; line-height: 1.6;">
                                <li>政策支持力度持续加大</li>
                                <li>市场需求保持稳定增长</li>
                                <li>数字化转型带来新机遇</li>
                            </ul>
                        </div>
                    </div>
                </div>
            `;

            modal.style.display = 'block';
        }

        // 关闭模态框
        document.querySelector('.close').addEventListener('click', function() {
            document.getElementById('companyModal').style.display = 'none';
        });

        window.addEventListener('click', function(e) {
            const modal = document.getElementById('companyModal');
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initPage);
    </script>
</body>
</html>
