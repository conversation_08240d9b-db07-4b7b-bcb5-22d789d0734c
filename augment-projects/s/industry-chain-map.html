<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产业链图谱展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
            margin: 0;
        }

        .container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            height: calc(100vh - 20px);
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 15px 20px;
            text-align: center;
            flex-shrink: 0;
        }

        .header h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
        }

        .header p {
            font-size: 0.9em;
            opacity: 0.9;
            margin: 0;
        }

        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            flex-shrink: 0;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 3px;
        }

        .advantage { background: #28a745; }
        .strong { background: #007bff; }
        .weak { background: #ffc107; }
        .missing { background: #dc3545; }

        .main-content {
            flex: 1;
            display: flex;
            overflow: hidden;
            height: calc(100vh - 120px);
        }

        .chain-container {
            flex: 0 0 65%;
            padding: 10px;
            overflow-y: auto;
            border-right: 2px solid #007bff;
            background: white;
        }

        .company-panel {
            flex: 0 0 35%;
            background: #f8f9fa;
            overflow-y: auto;
            padding: 15px;
            display: flex;
            flex-direction: column;
        }

        .company-panel-header {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            flex-shrink: 0;
        }

        .company-panel-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .company-panel-subtitle {
            font-size: 0.9em;
            color: #6c757d;
        }

        .company-list-container {
            flex: 1;
            overflow-y: auto;
            padding-right: 5px;
        }

        .company-card {
            background: white;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .company-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
            border-color: #007bff;
        }

        .company-card.selected {
            border-color: #007bff;
            background: #f0f8ff;
            transform: translateY(-2px);
        }

        .company-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 0.95em;
        }

        .company-category {
            font-size: 0.8em;
            color: #007bff;
            background: #e3f2fd;
            padding: 2px 8px;
            border-radius: 12px;
            display: inline-block;
            margin-bottom: 5px;
        }

        .company-info {
            font-size: 0.85em;
            color: #6c757d;
            line-height: 1.4;
        }

        .company-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 8px;
            font-size: 0.8em;
        }

        .company-revenue {
            color: #28a745;
            font-weight: 500;
        }

        .company-status {
            color: #ffc107;
            font-weight: 500;
        }

        .company-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            margin-top: 6px;
        }

        .company-tag {
            background: #e9ecef;
            color: #495057;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 0.7em;
            font-weight: 500;
        }

        .company-tag.investment {
            background: #d4edda;
            color: #155724;
        }

        .company-tag.talent {
            background: #d1ecf1;
            color: #0c5460;
        }

        .company-tag.research {
            background: #fff3cd;
            color: #856404;
        }

        .company-details {
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid #e9ecef;
        }

        .company-detail-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 4px;
            font-size: 0.75em;
            line-height: 1.3;
        }

        .company-detail-icon {
            width: 14px;
            height: 14px;
            margin-right: 5px;
            flex-shrink: 0;
            margin-top: 1px;
        }

        .chain-level {
            margin-bottom: 15px;
        }

        .level-title {
            text-align: center;
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
            padding: 6px 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 6px;
        }

        .level-content {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
        }

        .chain-node {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            padding: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .node-title {
            font-size: 0.85em;
            font-weight: bold;
            margin-bottom: 5px;
            color: #2c3e50;
            text-align: center;
            padding-bottom: 5px;
            border-bottom: 1px solid #e9ecef;
        }

        .sub-nodes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 4px;
            margin-top: 6px;
        }

        .sub-node {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 4px;
            text-align: center;
            font-size: 0.7em;
            transition: all 0.2s ease;
            cursor: pointer;
            position: relative;
            min-height: 45px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .sub-node:hover {
            background: #e9ecef;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .sub-node.selected {
            background: #007bff !important;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }

        .sub-node.selected .sub-node-name,
        .sub-node.selected .sub-node-count {
            color: white;
        }

        .level-title:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        }

        .sub-node.advantage {
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }
        .sub-node.strong {
            border-color: #007bff;
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
        }
        .sub-node.weak {
            border-color: #ffc107;
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        }
        .sub-node.missing {
            border-color: #dc3545;
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        }

        .sub-node-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 2px;
            font-size: 0.9em;
        }

        .sub-node-count {
            font-size: 0.75em;
            color: #6c757d;
        }

        .chain-status-badge {
            position: absolute;
            top: -4px;
            right: -4px;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            border: 2px solid white;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .company-list {
            margin-top: 20px;
        }

        .company-item {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .company-name {
            font-weight: bold;
            color: #2c3e50;
        }

        .company-info {
            font-size: 0.9em;
            color: #6c757d;
            margin-top: 5px;
        }

        .connecting-lines {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>产业链图谱</h1>
            <p>三级产业链结构全景展示 | 优势链/强链/弱链/缺链标注</p>
        </div>

        <div class="legend">
            <div class="legend-item">
                <div class="legend-color advantage"></div>
                <span>优势链</span>
            </div>
            <div class="legend-item">
                <div class="legend-color strong"></div>
                <span>强链</span>
            </div>
            <div class="legend-item">
                <div class="legend-color weak"></div>
                <span>弱链</span>
            </div>
            <div class="legend-item">
                <div class="legend-color missing"></div>
                <span>缺链</span>
            </div>
        </div>

        <div class="main-content">
            <div class="chain-container">
            <!-- 上游 -->
            <div class="chain-level">
                <div class="level-title">上游 - 原材料与基础设施</div>
                <div class="level-content">
                    <div class="chain-node">
                        <div class="node-title">原材料供应 (1,245家)</div>
                        <div class="sub-nodes-grid">
                            <div class="sub-node advantage" data-info="钢铁冶炼,456家企业,技术成熟,产能充足">
                                <div class="chain-status-badge advantage"></div>
                                <div class="sub-node-name">钢铁冶炼</div>
                                <div class="sub-node-count">456家</div>
                            </div>
                            <div class="sub-node strong" data-info="化工原料,389家企业,产业链完整,竞争激烈">
                                <div class="chain-status-badge strong"></div>
                                <div class="sub-node-name">化工原料</div>
                                <div class="sub-node-count">389家</div>
                            </div>
                            <div class="sub-node advantage" data-info="有色金属,400家企业,资源丰富,技术先进">
                                <div class="chain-status-badge advantage"></div>
                                <div class="sub-node-name">有色金属</div>
                                <div class="sub-node-count">400家</div>
                            </div>
                            <div class="sub-node weak" data-info="稀土材料,120家企业,资源稀缺,技术待提升">
                                <div class="chain-status-badge weak"></div>
                                <div class="sub-node-name">稀土材料</div>
                                <div class="sub-node-count">120家</div>
                            </div>
                            <div class="sub-node strong" data-info="建筑材料,280家企业,需求稳定,产能充足">
                                <div class="chain-status-badge strong"></div>
                                <div class="sub-node-name">建筑材料</div>
                                <div class="sub-node-count">280家</div>
                            </div>
                            <div class="sub-node weak" data-info="新材料,150家企业,创新驱动,市场培育期">
                                <div class="chain-status-badge weak"></div>
                                <div class="sub-node-name">新材料</div>
                                <div class="sub-node-count">150家</div>
                            </div>
                        </div>
                    </div>

                    <div class="chain-node">
                        <div class="node-title">能源供应 (892家)</div>
                        <div class="sub-nodes-grid">
                            <div class="sub-node strong" data-info="电力供应,234家企业,基础设施完善,供应稳定">
                                <div class="chain-status-badge strong"></div>
                                <div class="sub-node-name">电力供应</div>
                                <div class="sub-node-count">234家</div>
                            </div>
                            <div class="sub-node advantage" data-info="石油开采,345家企业,技术领先,储量丰富">
                                <div class="chain-status-badge advantage"></div>
                                <div class="sub-node-name">石油开采</div>
                                <div class="sub-node-count">345家</div>
                            </div>
                            <div class="sub-node strong" data-info="天然气,313家企业,清洁能源,发展迅速">
                                <div class="chain-status-badge strong"></div>
                                <div class="sub-node-name">天然气</div>
                                <div class="sub-node-count">313家</div>
                            </div>
                            <div class="sub-node advantage" data-info="太阳能,180家企业,技术成熟,成本下降">
                                <div class="chain-status-badge advantage"></div>
                                <div class="sub-node-name">太阳能</div>
                                <div class="sub-node-count">180家</div>
                            </div>
                            <div class="sub-node strong" data-info="风能,150家企业,装机增长,技术进步">
                                <div class="chain-status-badge strong"></div>
                                <div class="sub-node-name">风能</div>
                                <div class="sub-node-count">150家</div>
                            </div>
                            <div class="sub-node weak" data-info="核能,45家企业,技术门槛高,安全要求严">
                                <div class="chain-status-badge weak"></div>
                                <div class="sub-node-name">核能</div>
                                <div class="sub-node-count">45家</div>
                            </div>
                            <div class="sub-node missing" data-info="氢能,25家企业,技术突破期,产业化初期">
                                <div class="chain-status-badge missing"></div>
                                <div class="sub-node-name">氢能</div>
                                <div class="sub-node-count">25家</div>
                            </div>
                        </div>
                    </div>

                    <div class="chain-node">
                        <div class="node-title">基础设备 (567家)</div>
                        <div class="sub-nodes-grid">
                            <div class="sub-node weak" data-info="机械制造,234家企业,技术相对落后,需要升级">
                                <div class="chain-status-badge weak"></div>
                                <div class="sub-node-name">机械制造</div>
                                <div class="sub-node-count">234家</div>
                            </div>
                            <div class="sub-node weak" data-info="工具制造,189家企业,精度有待提升,市场分散">
                                <div class="chain-status-badge weak"></div>
                                <div class="sub-node-name">工具制造</div>
                                <div class="sub-node-count">189家</div>
                            </div>
                            <div class="sub-node missing" data-info="自动化设备,144家企业,技术缺口明显,依赖进口">
                                <div class="chain-status-badge missing"></div>
                                <div class="sub-node-name">自动化设备</div>
                                <div class="sub-node-count">144家</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中游 -->
            <div class="chain-level">
                <div class="level-title">中游 - 生产制造</div>
                <div class="level-content">
                    <div class="chain-node">
                        <div class="node-title">核心制造 (2,156家)</div>
                        <div class="sub-nodes-grid">
                            <div class="sub-node strong" data-info="产品组装,856家企业,规模化生产,效率较高">
                                <div class="chain-status-badge strong"></div>
                                <div class="sub-node-name">产品组装</div>
                                <div class="sub-node-count">856家</div>
                            </div>
                            <div class="sub-node advantage" data-info="精密加工,678家企业,技术精湛,质量优秀">
                                <div class="chain-status-badge advantage"></div>
                                <div class="sub-node-name">精密加工</div>
                                <div class="sub-node-count">678家</div>
                            </div>
                            <div class="sub-node strong" data-info="质量检测,622家企业,标准完善,检测能力强">
                                <div class="chain-status-badge strong"></div>
                                <div class="sub-node-name">质量检测</div>
                                <div class="sub-node-count">622家</div>
                            </div>
                        </div>
                    </div>

                    <div class="chain-node">
                        <div class="node-title">技术研发 (1,789家)</div>
                        <div class="sub-nodes-grid">
                            <div class="sub-node advantage" data-info="研发中心,567家企业,创新能力强,投入充足">
                                <div class="chain-status-badge advantage"></div>
                                <div class="sub-node-name">研发中心</div>
                                <div class="sub-node-count">567家</div>
                            </div>
                            <div class="sub-node advantage" data-info="产品设计,489家企业,设计理念先进,用户体验佳">
                                <div class="chain-status-badge advantage"></div>
                                <div class="sub-node-name">产品设计</div>
                                <div class="sub-node-count">489家</div>
                            </div>
                            <div class="sub-node advantage" data-info="技术创新,733家企业,专利数量多,转化率高">
                                <div class="chain-status-badge advantage"></div>
                                <div class="sub-node-name">技术创新</div>
                                <div class="sub-node-count">733家</div>
                            </div>
                        </div>
                    </div>

                    <div class="chain-node">
                        <div class="node-title">质量控制 (234家)</div>
                        <div class="sub-nodes-grid">
                            <div class="sub-node missing" data-info="标准制定,89家企业,标准体系不完善,国际化程度低">
                                <div class="chain-status-badge missing"></div>
                                <div class="sub-node-name">标准制定</div>
                                <div class="sub-node-count">89家</div>
                            </div>
                            <div class="sub-node weak" data-info="认证服务,78家企业,认证机构较少,权威性有待提升">
                                <div class="chain-status-badge weak"></div>
                                <div class="sub-node-name">认证服务</div>
                                <div class="sub-node-count">78家</div>
                            </div>
                            <div class="sub-node missing" data-info="检验检测,67家企业,检测设备落后,覆盖面不足">
                                <div class="chain-status-badge missing"></div>
                                <div class="sub-node-name">检验检测</div>
                                <div class="sub-node-count">67家</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 下游 -->
            <div class="chain-level">
                <div class="level-title">下游 - 销售与服务</div>
                <div class="level-content">
                    <div class="chain-node">
                        <div class="node-title">销售渠道 (3,456家)</div>
                        <div class="sub-nodes-grid">
                            <div class="sub-node strong" data-info="批发商,1234家企业,网络覆盖广,分销能力强">
                                <div class="chain-status-badge strong"></div>
                                <div class="sub-node-name">批发商</div>
                                <div class="sub-node-count">1,234家</div>
                            </div>
                            <div class="sub-node advantage" data-info="零售商,1567家企业,品牌影响力大,用户粘性高">
                                <div class="chain-status-badge advantage"></div>
                                <div class="sub-node-name">零售商</div>
                                <div class="sub-node-count">1,567家</div>
                            </div>
                            <div class="sub-node advantage" data-info="电商平台,655家企业,数字化程度高,增长迅速">
                                <div class="chain-status-badge advantage"></div>
                                <div class="sub-node-name">电商平台</div>
                                <div class="sub-node-count">655家</div>
                            </div>
                        </div>
                    </div>

                    <div class="chain-node">
                        <div class="node-title">售后服务 (1,987家)</div>
                        <div class="sub-nodes-grid">
                            <div class="sub-node advantage" data-info="维修服务,789家企业,技术专业,响应及时">
                                <div class="chain-status-badge advantage"></div>
                                <div class="sub-node-name">维修服务</div>
                                <div class="sub-node-count">789家</div>
                            </div>
                            <div class="sub-node strong" data-info="技术支持,654家企业,专业团队,服务质量高">
                                <div class="chain-status-badge strong"></div>
                                <div class="sub-node-name">技术支持</div>
                                <div class="sub-node-count">654家</div>
                            </div>
                            <div class="sub-node strong" data-info="培训服务,544家企业,培训体系完善,效果显著">
                                <div class="chain-status-badge strong"></div>
                                <div class="sub-node-name">培训服务</div>
                                <div class="sub-node-count">544家</div>
                            </div>
                        </div>
                    </div>

                    <div class="chain-node">
                        <div class="node-title">物流配送 (1,123家)</div>
                        <div class="sub-nodes-grid">
                            <div class="sub-node weak" data-info="仓储服务,445家企业,自动化程度低,成本较高">
                                <div class="chain-status-badge weak"></div>
                                <div class="sub-node-name">仓储服务</div>
                                <div class="sub-node-count">445家</div>
                            </div>
                            <div class="sub-node weak" data-info="运输服务,389家企业,运力分散,效率有待提升">
                                <div class="chain-status-badge weak"></div>
                                <div class="sub-node-name">运输服务</div>
                                <div class="sub-node-count">389家</div>
                            </div>
                            <div class="sub-node strong" data-info="配送服务,289家企业,最后一公里优势,用户体验好">
                                <div class="chain-status-badge strong"></div>
                                <div class="sub-node-name">配送服务</div>
                                <div class="sub-node-count">289家</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 企业面板 -->
            <div class="company-panel">
                <div class="company-panel-header">
                    <div class="company-panel-title" id="companyPanelTitle">全产业链企业名单</div>
                    <div class="company-panel-subtitle" id="companyPanelSubtitle">共计 <span id="totalCompanyCount">0</span> 家企业</div>
                </div>
                <div class="company-list-container" id="companyListContainer">
                    <!-- 企业列表将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="companyModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 id="modalTitle">节点详情</h2>
            <div id="modalContent">
                <!-- 动态内容 -->
            </div>
        </div>
    </div>

    <script>
        // 企业数据
        const companyData = {
            // 上游企业
            '钢铁冶炼': [
                {
                    name: '中国宝武钢铁集团',
                    category: '钢铁冶炼',
                    info: '全球第二大钢铁企业，年产量超过1亿吨',
                    revenue: '5200亿元',
                    status: '行业龙头',
                    investment: '寻求绿色钢铁技术合作伙伴',
                    talent: '需求：冶金工程师、环保技术专家',
                    research: '合作院所：钢铁研究总院、东北大学'
                },
                {
                    name: '河钢集团',
                    category: '钢铁冶炼',
                    info: '中国最大的钢铁材料制造和综合服务商',
                    revenue: '3800亿元',
                    status: '大型企业',
                    investment: '投资新材料产业园项目',
                    talent: '需求：材料科学博士、工艺工程师',
                    research: '合作院所：北京科技大学、中科院金属所'
                },
                {
                    name: '沙钢集团',
                    category: '钢铁冶炼',
                    info: '中国最大的民营钢铁企业',
                    revenue: '2600亿元',
                    status: '民营龙头',
                    investment: '智能制造升级改造项目',
                    talent: '需求：自动化工程师、数据分析师',
                    research: '合作院所：清华大学、上海交通大学'
                },
                {
                    name: '鞍钢集团',
                    category: '钢铁冶炼',
                    info: '中国重要的钢铁生产基地',
                    revenue: '1800亿元',
                    status: '国有企业',
                    investment: '高端装备用钢研发中心',
                    talent: '需求：金属材料专家、质量工程师',
                    research: '合作院所：大连理工大学、中南大学'
                }
            ],
            '化工原料': [
                {
                    name: '中国石化',
                    category: '化工原料',
                    info: '亚洲最大的炼油化工一体化企业',
                    revenue: '2.9万亿元',
                    status: '央企巨头',
                    investment: '新能源化工产业基地建设',
                    talent: '需求：化工工程师、安全工程师',
                    research: '合作院所：中科院化学所、华东理工大学'
                },
                {
                    name: '万华化学',
                    category: '化工原料',
                    info: '全球MDI行业领导者',
                    revenue: '1800亿元',
                    status: '创新企业',
                    investment: '高性能材料研发中心',
                    talent: '需求：高分子材料博士、工艺研发工程师',
                    research: '合作院所：中科院化学所、北京化工大学'
                },
                {
                    name: '恒力石化',
                    category: '化工原料',
                    info: '全产业链布局的化工企业',
                    revenue: '1600亿元',
                    status: '民营企业',
                    investment: '绿色化工技术产业化项目',
                    talent: '需求：催化技术专家、环保工程师',
                    research: '合作院所：大连理工大学、天津大学'
                },
                {
                    name: '荣盛石化',
                    category: '化工原料',
                    info: 'PTA产能全球第一',
                    revenue: '1400亿元',
                    status: '专业企业',
                    investment: '循环经济示范园区',
                    talent: '需求：化学工程师、项目管理专家',
                    research: '合作院所：浙江大学、中科院宁波材料所'
                }
            ],
            '有色金属': [
                {
                    name: '紫金矿业',
                    category: '有色金属',
                    info: '中国最大的黄金企业之一',
                    revenue: '2800亿元',
                    status: '行业领先',
                    investment: '海外矿业投资项目',
                    talent: '需求：地质工程师、采矿工程师',
                    research: '合作院所：中南大学、中国地质大学'
                },
                {
                    name: '江西铜业',
                    category: '有色金属',
                    info: '中国最大的铜生产企业',
                    revenue: '4200亿元',
                    status: '铜业龙头',
                    investment: '新能源材料产业基地',
                    talent: '需求：冶金工程师、材料专家',
                    research: '合作院所：江西理工大学、北京有色金属研究总院'
                }
            ],
            '电力供应': [
                {
                    name: '国家电网',
                    category: '电力供应',
                    info: '世界最大的公用事业企业',
                    revenue: '2.6万亿元',
                    status: '国家电网',
                    investment: '智能电网建设项目',
                    talent: '需求：电气工程师、信息技术专家',
                    research: '合作院所：清华大学、华北电力大学'
                }
            ],
            '石油开采': [
                {
                    name: '中国石油',
                    category: '石油开采',
                    info: '中国最大的油气生产商',
                    revenue: '2.9万亿元',
                    status: '石油巨头',
                    investment: '页岩油气开发项目',
                    talent: '需求：石油工程师、地质专家',
                    research: '合作院所：中国石油大学、西南石油大学'
                }
            ],
            '天然气': [
                {
                    name: '中国燃气',
                    category: '天然气',
                    info: '中国最大的跨区域综合燃气运营商',
                    revenue: '800亿元',
                    status: '燃气龙头',
                    investment: 'LNG接收站建设',
                    talent: '需求：燃气工程师、安全管理专家',
                    research: '合作院所：西安交通大学、大连理工大学'
                }
            ],
            '稀土材料': [
                {
                    name: '北方稀土',
                    category: '稀土材料',
                    info: '全球最大的稀土产业集团',
                    revenue: '400亿元',
                    status: '稀土龙头',
                    investment: '稀土新材料产业园',
                    talent: '需求：材料工程师、稀土专家',
                    research: '合作院所：北京理工大学、中科院安泰科技'
                }
            ],
            '建筑材料': [
                {
                    name: '中国建材',
                    category: '建筑材料',
                    info: '全球最大的建材制造商',
                    revenue: '3600亿元',
                    status: '建材龙头',
                    investment: '绿色建材产业基地',
                    talent: '需求：材料工程师、工艺工程师',
                    research: '合作院所：清华大学、同济大学'
                }
            ],
            '新材料': [
                {
                    name: '中科三环',
                    category: '新材料',
                    info: '稀土永磁材料领导者',
                    revenue: '80亿元',
                    status: '新材料企业',
                    investment: '高性能磁性材料项目',
                    talent: '需求：材料科学博士、研发工程师',
                    research: '合作院所：中科院物理所、北京科技大学'
                }
            ],
            '太阳能': [
                {
                    name: '隆基绿能',
                    category: '太阳能',
                    info: '全球领先的太阳能科技公司',
                    revenue: '1200亿元',
                    status: '光伏龙头',
                    investment: '硅片电池组件一体化基地',
                    talent: '需求：光伏工程师、材料专家',
                    research: '合作院所：西安交通大学、中科院光电院'
                }
            ],
            '风能': [
                {
                    name: '金风科技',
                    category: '风能',
                    info: '中国领先的风电整体解决方案提供商',
                    revenue: '500亿元',
                    status: '风电龙头',
                    investment: '海上风电装备制造基地',
                    talent: '需求：风电工程师、机械设计师',
                    research: '合作院所：华北电力大学、新疆大学'
                }
            ],
            '核能': [
                {
                    name: '中国核电',
                    category: '核能',
                    info: '中国核电运营领军企业',
                    revenue: '700亿元',
                    status: '核电运营',
                    investment: '第四代核电技术研发',
                    talent: '需求：核工程师、安全工程师',
                    research: '合作院所：清华大学、中科院核能安全所'
                }
            ],
            '氢能': [
                {
                    name: '亿华通',
                    category: '氢能',
                    info: '氢燃料电池发动机领导者',
                    revenue: '15亿元',
                    status: '氢能企业',
                    investment: '氢燃料电池产业化项目',
                    talent: '需求：燃料电池工程师、化学工程师',
                    research: '合作院所：清华大学、中科院大连化物所'
                }
            ],
            // 中游企业
            '产品组装': [
                {
                    name: '富士康',
                    category: '产品组装',
                    info: '全球最大的电子产品代工制造商',
                    revenue: '4500亿元',
                    status: '代工龙头',
                    investment: '工业互联网平台建设',
                    talent: '需求：工业工程师、AI算法工程师',
                    research: '合作院所：清华大学、中科院自动化所'
                },
                {
                    name: '比亚迪',
                    category: '产品组装',
                    info: '新能源汽车及电池制造商',
                    revenue: '4200亿元',
                    status: '新能源',
                    investment: '动力电池产业基地',
                    talent: '需求：电池技术专家、汽车工程师',
                    research: '合作院所：中科院物理所、西安交通大学'
                },
                {
                    name: '立讯精密',
                    category: '产品组装',
                    info: '精密制造和智能制造服务商',
                    revenue: '1200亿元',
                    status: '精密制造',
                    investment: '5G通信模组生产线',
                    talent: '需求：通信工程师、精密制造工程师',
                    research: '合作院所：电子科技大学、华中科技大学'
                },
                {
                    name: '歌尔股份',
                    category: '产品组装',
                    info: '声学整机制造领导者',
                    revenue: '800亿元',
                    status: '声学龙头',
                    investment: 'VR/AR设备制造基地',
                    talent: '需求：声学工程师、光学工程师',
                    research: '合作院所：北京理工大学、哈尔滨工业大学'
                }
            ],
            '精密加工': [
                { name: '海康威视', category: '精密加工', info: '全球视频监控设备领导者', revenue: '800亿元', status: '安防龙头' },
                { name: '大族激光', category: '精密加工', info: '激光加工设备领导者', revenue: '150亿元', status: '激光龙头' },
                { name: '华工科技', category: '精密加工', info: '激光装备与光通信器件制造商', revenue: '120亿元', status: '光电企业' },
                { name: '亚威股份', category: '精密加工', info: '金属成形机床制造商', revenue: '45亿元', status: '机床企业' }
            ],
            '质量检测': [
                { name: '华测检测', category: '质量检测', info: '第三方检测服务领导者', revenue: '45亿元', status: '检测龙头' },
                { name: '苏试试验', category: '质量检测', info: '试验检测技术服务商', revenue: '15亿元', status: '试验检测' },
                { name: '钢研纳克', category: '质量检测', info: '分析检测仪器制造商', revenue: '25亿元', status: '仪器制造' },
                { name: '广电计量', category: '质量检测', info: '计量检测技术服务商', revenue: '30亿元', status: '计量检测' }
            ],
            // 添加缺失的中游企业
            '机械制造': [
                {
                    name: '三一重工',
                    category: '机械制造',
                    info: '工程机械制造龙头企业',
                    revenue: '1200亿元',
                    status: '行业龙头',
                    investment: '智能制造产业园',
                    talent: '需求：机械工程师、自动化专家',
                    research: '合作院所：湖南大学、中南大学'
                }
            ],
            '工具制造': [
                {
                    name: '株洲钻石',
                    category: '工具制造',
                    info: '硬质合金刀具制造商',
                    revenue: '80亿元',
                    status: '专业企业',
                    investment: '精密刀具生产线',
                    talent: '需求：材料工程师、工艺工程师',
                    research: '合作院所：中南大学、华中科技大学'
                }
            ],
            '自动化设备': [
                {
                    name: '汇川技术',
                    category: '自动化设备',
                    info: '工业自动化产品供应商',
                    revenue: '200亿元',
                    status: '技术企业',
                    investment: '工业机器人产业基地',
                    talent: '需求：控制工程师、软件工程师',
                    research: '合作院所：华中科技大学、西安交通大学'
                }
            ],
            // 下游企业
            '批发商': [
                {
                    name: '国药控股',
                    category: '批发商',
                    info: '中国最大的药品分销企业',
                    revenue: '5200亿元',
                    status: '医药分销',
                    investment: '医药物流中心建设',
                    talent: '需求：物流管理专家、医药专业人才',
                    research: '合作院所：中国药科大学、北京中医药大学'
                }
            ],
            '零售商': [
                {
                    name: '永辉超市',
                    category: '零售商',
                    info: '生鲜食品零售企业',
                    revenue: '1000亿元',
                    status: '生鲜零售',
                    investment: '智慧零售门店升级',
                    talent: '需求：零售管理专家、数据分析师',
                    research: '合作院所：厦门大学、福建农林大学'
                }
            ],
            '电商平台': [
                {
                    name: '阿里巴巴',
                    category: '电商平台',
                    info: '中国最大的电商平台',
                    revenue: '8700亿元',
                    status: '电商巨头',
                    investment: '数字经济产业园',
                    talent: '需求：算法工程师、产品经理',
                    research: '合作院所：浙江大学、清华大学'
                }
            ],
            '维修服务': [
                {
                    name: '海尔服务',
                    category: '维修服务',
                    info: '家电售后服务领导者',
                    revenue: '300亿元',
                    status: '服务龙头',
                    investment: '智能服务平台建设',
                    talent: '需求：服务工程师、客户管理专家',
                    research: '合作院所：青岛大学、中国海洋大学'
                }
            ],
            '技术支持': [
                {
                    name: '华为服务',
                    category: '技术支持',
                    info: '通信设备技术支持专家',
                    revenue: '400亿元',
                    status: '技术服务',
                    investment: '全球技术服务中心',
                    talent: '需求：通信工程师、技术支持专家',
                    research: '合作院所：清华大学、北京邮电大学'
                }
            ],
            '培训服务': [
                {
                    name: '新东方',
                    category: '培训服务',
                    info: '教育培训服务提供商',
                    revenue: '400亿元',
                    status: '教育龙头',
                    investment: '在线教育平台建设',
                    talent: '需求：教育专家、技术开发人员',
                    research: '合作院所：北京师范大学、华东师范大学'
                }
            ]
        };

        // 获取DOM元素
        const modal = document.getElementById('companyModal');
        const modalTitle = document.getElementById('modalTitle');
        const modalContent = document.getElementById('modalContent');
        const closeBtn = document.querySelector('.close');
        const companyPanelTitle = document.getElementById('companyPanelTitle');
        const companyPanelSubtitle = document.getElementById('companyPanelSubtitle');
        const totalCompanyCount = document.getElementById('totalCompanyCount');
        const companyListContainer = document.getElementById('companyListContainer');

        let selectedCategory = null;

        // 获取所有企业
        function getAllCompanies() {
            let allCompanies = [];
            Object.keys(companyData).forEach(category => {
                companyData[category].forEach(company => {
                    allCompanies.push({...company, category});
                });
            });
            return allCompanies;
        }

        // 渲染企业列表
        function renderCompanyList(companies, title = '全产业链企业名单') {
            companyPanelTitle.textContent = title;
            totalCompanyCount.textContent = companies.length;
            companyPanelSubtitle.innerHTML = `共计 <span id="totalCompanyCount">${companies.length}</span> 家企业`;

            companyListContainer.innerHTML = '';

            companies.forEach((company, index) => {
                const companyCard = document.createElement('div');
                companyCard.className = 'company-card';
                companyCard.innerHTML = `
                    <div class="company-category">${company.category}</div>
                    <div class="company-name">${company.name}</div>
                    <div class="company-info">${company.info}</div>
                    <div class="company-stats">
                        <span class="company-revenue">${company.revenue}</span>
                        <span class="company-status">${company.status}</span>
                    </div>
                    ${company.investment || company.talent || company.research ? `
                        <div class="company-tags">
                            ${company.investment ? '<span class="company-tag investment">招商</span>' : ''}
                            ${company.talent ? '<span class="company-tag talent">人才</span>' : ''}
                            ${company.research ? '<span class="company-tag research">科研</span>' : ''}
                        </div>
                        <div class="company-details">
                            ${company.investment ? `
                                <div class="company-detail-item">
                                    <span class="company-detail-icon">💼</span>
                                    <span>${company.investment}</span>
                                </div>
                            ` : ''}
                            ${company.talent ? `
                                <div class="company-detail-item">
                                    <span class="company-detail-icon">👥</span>
                                    <span>${company.talent}</span>
                                </div>
                            ` : ''}
                            ${company.research ? `
                                <div class="company-detail-item">
                                    <span class="company-detail-icon">🏛️</span>
                                    <span>${company.research}</span>
                                </div>
                            ` : ''}
                        </div>
                    ` : ''}
                `;

                companyCard.addEventListener('click', () => {
                    // 移除之前的选中状态
                    document.querySelectorAll('.company-card').forEach(card => {
                        card.classList.remove('selected');
                    });
                    // 添加选中状态
                    companyCard.classList.add('selected');
                    // 显示企业详情
                    showCompanyDetail(company);
                });

                companyListContainer.appendChild(companyCard);
            });
        }

        // 子节点点击事件 - 显示该环节的企业
        document.querySelectorAll('.sub-node').forEach(subNode => {
            subNode.addEventListener('click', function(e) {
                e.stopPropagation();
                const name = this.querySelector('.sub-node-name').textContent;

                // 移除之前的选中状态
                document.querySelectorAll('.sub-node').forEach(node => {
                    node.classList.remove('selected');
                });
                // 添加选中状态
                this.classList.add('selected');

                selectedCategory = name;

                if (companyData[name]) {
                    renderCompanyList(companyData[name], `${name} - 企业名单`);
                } else {
                    renderCompanyList([], `${name} - 企业名单`);
                }
            });
        });

        // 显示企业详情
        function showCompanyDetail(company) {
            modalTitle.textContent = `${company.name} - 企业详情`;
            modalContent.innerHTML = `
                <div style="padding: 20px;">
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <h3 style="color: #2c3e50; margin: 0;">${company.name}</h3>
                            <span style="background: #007bff; color: white; padding: 6px 12px; border-radius: 15px; font-size: 0.9em;">
                                ${company.category}
                            </span>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div style="text-align: center; background: white; padding: 15px; border-radius: 8px;">
                                <div style="font-size: 1.8em; font-weight: bold; color: #28a745;">${company.revenue}</div>
                                <div style="color: #6c757d;">营收规模</div>
                            </div>
                            <div style="text-align: center; background: white; padding: 15px; border-radius: 8px;">
                                <div style="font-size: 1.2em; font-weight: bold; color: #ffc107;">${company.status}</div>
                                <div style="color: #6c757d;">企业地位</div>
                            </div>
                        </div>
                    </div>

                    <div style="background: #e9ecef; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">企业简介</h4>
                        <p style="color: #6c757d; line-height: 1.6;">
                            ${company.info}
                        </p>
                    </div>

                    ${company.investment || company.talent || company.research ? `
                        <div style="background: white; padding: 20px; border-radius: 10px; border: 1px solid #dee2e6; margin-bottom: 20px;">
                            <h4 style="color: #2c3e50; margin-bottom: 15px;">🤝 合作机会</h4>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                                ${company.investment ? `
                                    <div style="background: #d4edda; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;">
                                        <h5 style="color: #155724; margin-bottom: 10px;">💼 招商线索</h5>
                                        <p style="color: #155724; margin: 0; line-height: 1.5;">${company.investment}</p>
                                    </div>
                                ` : ''}
                                ${company.talent ? `
                                    <div style="background: #d1ecf1; padding: 15px; border-radius: 8px; border-left: 4px solid #17a2b8;">
                                        <h5 style="color: #0c5460; margin-bottom: 10px;">👥 人才需求</h5>
                                        <p style="color: #0c5460; margin: 0; line-height: 1.5;">${company.talent}</p>
                                    </div>
                                ` : ''}
                                ${company.research ? `
                                    <div style="background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid #ffc107;">
                                        <h5 style="color: #856404; margin-bottom: 10px;">🏛️ 科研合作</h5>
                                        <p style="color: #856404; margin: 0; line-height: 1.5;">${company.research}</p>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    ` : ''}

                    <div style="background: white; padding: 20px; border-radius: 10px; border: 1px solid #dee2e6;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">📊 企业分析</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <h5 style="color: #28a745; margin-bottom: 10px;">🔥 核心优势</h5>
                                <ul style="color: #6c757d; padding-left: 20px; line-height: 1.6;">
                                    <li>行业地位领先，品牌影响力强</li>
                                    <li>技术实力雄厚，创新能力突出</li>
                                    <li>资金实力充足，抗风险能力强</li>
                                </ul>
                            </div>
                            <div>
                                <h5 style="color: #007bff; margin-bottom: 10px;">📈 发展机遇</h5>
                                <ul style="color: #6c757d; padding-left: 20px; line-height: 1.6;">
                                    <li>政策支持力度持续加大</li>
                                    <li>市场需求保持稳定增长</li>
                                    <li>数字化转型带来新机遇</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            modal.style.display = 'block';
        }

        // 重置到全部企业视图
        function resetToAllCompanies() {
            selectedCategory = null;
            // 移除所有选中状态
            document.querySelectorAll('.sub-node').forEach(node => {
                node.classList.remove('selected');
            });
            document.querySelectorAll('.company-card').forEach(card => {
                card.classList.remove('selected');
            });
            // 显示所有企业
            renderCompanyList(getAllCompanies());
        }

        // 添加重置按钮功能
        function addResetButton() {
            const resetButton = document.createElement('button');
            resetButton.textContent = '显示全部企业';
            resetButton.style.cssText = `
                background: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 20px;
                cursor: pointer;
                font-size: 0.9em;
                margin-top: 10px;
                width: 100%;
            `;
            resetButton.addEventListener('click', resetToAllCompanies);

            const header = document.querySelector('.company-panel-header');
            header.appendChild(resetButton);
        }

        // 关闭模态框
        if (closeBtn) {
            closeBtn.addEventListener('click', function() {
                modal.style.display = 'none';
            });
        }

        window.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('产业链图谱页面加载完成');

            // 初始化显示所有企业
            renderCompanyList(getAllCompanies());

            // 添加重置按钮
            addResetButton();

            // 为产业链标题添加点击事件，可以重置到全部企业
            document.querySelectorAll('.level-title').forEach(title => {
                title.addEventListener('click', resetToAllCompanies);
                title.style.cursor = 'pointer';
                title.title = '点击显示全部企业';
            });
        });

        // 搜索框回车事件
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        searchIndustry();
                    }
                });
            }
            console.log('产业链图谱页面加载完成');
        });
    </script>
</body>
</html>
