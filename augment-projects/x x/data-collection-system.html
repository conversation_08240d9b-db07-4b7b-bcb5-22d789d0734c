<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据采集系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
            font-weight: 500;
        }
        
        .nav-tab.active {
            background: #4facfe;
            color: white;
        }
        
        .nav-tab:hover {
            background: #3d8bfe;
            color: white;
        }
        
        .content-panel {
            display: none;
            background: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .content-panel.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 2px rgba(79, 172, 254, 0.2);
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            margin-right: 10px;
        }
        
        .btn-primary {
            background: #4facfe;
            color: white;
        }
        
        .btn-primary:hover {
            background: #3d8bfe;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
            font-size: 13px;
        }
        
        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            position: sticky;
            top: 0;
        }
        
        .data-table tr:hover {
            background: #f8f9ff;
        }
        
        .row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .col {
            flex: 1;
        }
        
        .col-2 {
            flex: 2;
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .database-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .database-card {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .database-card:hover {
            border-color: #4facfe;
            background: #f8f9ff;
        }
        
        .database-card.selected {
            border-color: #4facfe;
            background: #e3f2fd;
        }
        
        .database-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .task-type-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .task-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .task-card:hover {
            border-color: #4facfe;
            box-shadow: 0 2px 8px rgba(79, 172, 254, 0.2);
        }
        
        .task-card.selected {
            border-color: #4facfe;
            background: #e3f2fd;
        }
        
        .script-editor {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            background: #f8f9fa;
            font-family: 'Courier New', monospace;
            min-height: 200px;
        }
        
        .schedule-visual {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
            min-height: 300px;
        }
        
        .task-node {
            display: inline-block;
            padding: 10px 15px;
            background: #4facfe;
            color: white;
            border-radius: 6px;
            margin: 5px;
            cursor: pointer;
        }
        
        .task-node.parallel {
            background: #28a745;
        }
        
        .connection-line {
            display: inline-block;
            width: 30px;
            height: 2px;
            background: #666;
            margin: 0 10px;
            vertical-align: middle;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .status-running {
            background: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-completed {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        
        .responsibility-table {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .search-box {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .search-box input {
            flex: 1;
        }
        
        .cron-builder {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .cron-field {
            text-align: center;
        }
        
        .cron-field label {
            font-size: 12px;
            color: #666;
        }
        
        .cron-preview {
            background: #e9ecef;
            padding: 10px;
            border-radius: 6px;
            font-family: monospace;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>数据采集系统</h1>
        <p>支持主流数据库采集、数据处理、任务调度和数据治理的一体化平台</p>
    </div>

    <div class="container">
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showPanel('datasource')">数据源管理</button>
            <button class="nav-tab" onclick="showPanel('tasks')">任务管理</button>
            <button class="nav-tab" onclick="showPanel('scripts')">脚本管理</button>
            <button class="nav-tab" onclick="showPanel('schedule')">调度管理</button>
            <button class="nav-tab" onclick="showPanel('governance')">数据治理</button>
            <button class="nav-tab" onclick="showPanel('monitor')">监控中心</button>
        </div>

        <!-- 数据源管理面板 -->
        <div id="datasource" class="content-panel active">
            <h2>数据源管理</h2>
            <div class="alert alert-info">
                支持主流数据库采集：GaussDB、Greenplum、MariaDB、MySQL、Oracle、PostgreSQL、SQLServer、Sybase、达梦、海量、神通等
            </div>
            
            <h3>选择数据库类型</h3>
            <div class="database-grid">
                <div class="database-card" onclick="selectDatabase(this, 'mysql')">
                    <div class="database-icon">🐬</div>
                    <div>MySQL</div>
                </div>
                <div class="database-card" onclick="selectDatabase(this, 'oracle')">
                    <div class="database-icon">🔶</div>
                    <div>Oracle</div>
                </div>
                <div class="database-card" onclick="selectDatabase(this, 'postgresql')">
                    <div class="database-icon">🐘</div>
                    <div>PostgreSQL</div>
                </div>
                <div class="database-card" onclick="selectDatabase(this, 'sqlserver')">
                    <div class="database-icon">🏢</div>
                    <div>SQL Server</div>
                </div>
                <div class="database-card" onclick="selectDatabase(this, 'gaussdb')">
                    <div class="database-icon">🌟</div>
                    <div>GaussDB</div>
                </div>
                <div class="database-card" onclick="selectDatabase(this, 'greenplum')">
                    <div class="database-icon">🟢</div>
                    <div>Greenplum</div>
                </div>
                <div class="database-card" onclick="selectDatabase(this, 'mariadb')">
                    <div class="database-icon">🌊</div>
                    <div>MariaDB</div>
                </div>
                <div class="database-card" onclick="selectDatabase(this, 'sybase')">
                    <div class="database-icon">📊</div>
                    <div>Sybase</div>
                </div>
                <div class="database-card" onclick="selectDatabase(this, 'dameng')">
                    <div class="database-icon">🏔️</div>
                    <div>达梦</div>
                </div>
                <div class="database-card" onclick="selectDatabase(this, 'vastbase')">
                    <div class="database-icon">🌐</div>
                    <div>海量</div>
                </div>
                <div class="database-card" onclick="selectDatabase(this, 'shentong')">
                    <div class="database-icon">⚡</div>
                    <div>神通</div>
                </div>
            </div>
            
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>数据源名称</label>
                        <input type="text" class="form-control" placeholder="请输入数据源名称">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>主机地址</label>
                        <input type="text" class="form-control" placeholder="192.168.1.100">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>端口</label>
                        <input type="text" class="form-control" placeholder="3306">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>数据库名</label>
                        <input type="text" class="form-control" placeholder="请输入数据库名">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>用户名</label>
                        <input type="text" class="form-control" placeholder="请输入用户名">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>密码</label>
                        <input type="password" class="form-control" placeholder="请输入密码">
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <button class="btn btn-primary">测试连接</button>
                <button class="btn btn-success">保存数据源</button>
                <button class="btn btn-info">查看已配置数据源</button>
            </div>
        </div>

        <!-- 任务管理面板 -->
        <div id="tasks" class="content-panel">
            <h2>任务管理</h2>
            <div class="alert alert-info">
                支持数据采集、数据清洗转换、模型转换、共享交换、补采、上报以及其他自定义任务类型
            </div>

            <h3>选择任务类型</h3>
            <div class="task-type-grid">
                <div class="task-card" onclick="selectTask(this, 'collection')">
                    <div style="font-size: 24px; margin-bottom: 8px;">📥</div>
                    <div>数据采集</div>
                    <small>从数据源采集数据</small>
                </div>
                <div class="task-card" onclick="selectTask(this, 'cleaning')">
                    <div style="font-size: 24px; margin-bottom: 8px;">🧹</div>
                    <div>数据清洗转换</div>
                    <small>清洗和转换数据</small>
                </div>
                <div class="task-card" onclick="selectTask(this, 'model')">
                    <div style="font-size: 24px; margin-bottom: 8px;">🔄</div>
                    <div>模型转换</div>
                    <small>数据模型转换</small>
                </div>
                <div class="task-card" onclick="selectTask(this, 'exchange')">
                    <div style="font-size: 24px; margin-bottom: 8px;">🔀</div>
                    <div>共享交换</div>
                    <small>数据共享和交换</small>
                </div>
                <div class="task-card" onclick="selectTask(this, 'supplement')">
                    <div style="font-size: 24px; margin-bottom: 8px;">📋</div>
                    <div>补采</div>
                    <small>补充采集数据</small>
                </div>
                <div class="task-card" onclick="selectTask(this, 'report')">
                    <div style="font-size: 24px; margin-bottom: 8px;">📊</div>
                    <div>上报</div>
                    <small>数据上报任务</small>
                </div>
                <div class="task-card" onclick="selectTask(this, 'custom')">
                    <div style="font-size: 24px; margin-bottom: 8px;">⚙️</div>
                    <div>自定义任务</div>
                    <small>自定义处理逻辑</small>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>任务名称</label>
                        <input type="text" class="form-control" placeholder="请输入任务名称">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>数据源</label>
                        <select class="form-control">
                            <option>请选择数据源</option>
                            <option>MySQL-学生管理系统</option>
                            <option>Oracle-教务系统</option>
                            <option>PostgreSQL-科研系统</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>源表/查询</label>
                        <textarea class="form-control" rows="3" placeholder="请输入表名或SQL查询语句"></textarea>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>目标表</label>
                        <input type="text" class="form-control" placeholder="请输入目标表名">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>任务描述</label>
                <textarea class="form-control" rows="3" placeholder="请输入任务描述和开发说明"></textarea>
            </div>

            <div class="form-group">
                <button class="btn btn-primary">创建任务</button>
                <button class="btn btn-success">保存配置</button>
                <button class="btn btn-info">查看开发说明</button>
            </div>

            <h3>任务列表</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>任务名称</th>
                        <th>任务类型</th>
                        <th>数据源</th>
                        <th>状态</th>
                        <th>最后执行时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>学生信息采集</td>
                        <td>数据采集</td>
                        <td>MySQL-学生管理系统</td>
                        <td><span class="status-badge status-completed">已完成</span></td>
                        <td>2024-01-15 10:30:00</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">执行</button>
                        </td>
                    </tr>
                    <tr>
                        <td>教师数据清洗</td>
                        <td>数据清洗转换</td>
                        <td>Oracle-教务系统</td>
                        <td><span class="status-badge status-running">运行中</span></td>
                        <td>2024-01-15 11:00:00</td>
                        <td>
                            <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">停止</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">日志</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 脚本管理面板 -->
        <div id="scripts" class="content-panel">
            <h2>脚本管理</h2>
            <div class="alert alert-info">
                支持添加和配置Python或SQL脚本，用于数据清洗和转换，包括脚本的运行参数信息配置
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>脚本名称</label>
                        <input type="text" class="form-control" placeholder="请输入脚本名称">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>脚本类型</label>
                        <select class="form-control" onchange="changeScriptType(this.value)">
                            <option value="python">Python脚本</option>
                            <option value="sql">SQL脚本</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>脚本内容</label>
                <div class="script-editor" contenteditable="true" id="scriptEditor">
# Python数据清洗脚本示例
import pandas as pd
import numpy as np

def clean_data(df):
    # 去除重复数据
    df = df.drop_duplicates()

    # 处理缺失值
    df = df.fillna(method='forward')

    # 数据类型转换
    df['age'] = pd.to_numeric(df['age'], errors='coerce')

    return df

# 主函数
if __name__ == "__main__":
    # 读取数据
    data = pd.read_csv('input.csv')

    # 清洗数据
    cleaned_data = clean_data(data)

    # 保存结果
    cleaned_data.to_csv('output.csv', index=False)
                </div>
            </div>

            <div class="form-group">
                <label>运行参数配置</label>
                <textarea class="form-control" rows="4" placeholder="请输入运行参数，格式：参数名=参数值，每行一个参数">input_file=/data/student.csv
output_file=/data/cleaned_student.csv
batch_size=1000
timeout=300</textarea>
            </div>

            <div class="form-group">
                <button class="btn btn-primary">保存脚本</button>
                <button class="btn btn-success">测试运行</button>
                <button class="btn btn-info">查看日志</button>
                <button class="btn btn-warning">参数配置</button>
            </div>

            <h3>脚本列表</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>脚本名称</th>
                        <th>脚本类型</th>
                        <th>创建时间</th>
                        <th>最后修改</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>学生数据清洗</td>
                        <td>Python</td>
                        <td>2024-01-10</td>
                        <td>2024-01-15</td>
                        <td><span class="status-badge status-completed">正常</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">运行</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">复制</button>
                        </td>
                    </tr>
                    <tr>
                        <td>成绩统计查询</td>
                        <td>SQL</td>
                        <td>2024-01-12</td>
                        <td>2024-01-14</td>
                        <td><span class="status-badge status-completed">正常</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">运行</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">复制</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 调度管理面板 -->
        <div id="schedule" class="content-panel">
            <h2>调度管理</h2>
            <div class="alert alert-info">
                支持新建调度计划，通过可视化界面以图形化简易操作完成任务调度排布。支持串行、并行执行的任务调度形式
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>调度计划名称</label>
                        <input type="text" class="form-control" placeholder="请输入调度计划名称">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>执行方式</label>
                        <select class="form-control">
                            <option>串行执行</option>
                            <option>并行执行</option>
                            <option>混合执行</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>调度周期配置</label>
                <div class="row">
                    <div class="col">
                        <select class="form-control" onchange="changeScheduleType(this.value)">
                            <option value="cron">Cron表达式</option>
                            <option value="interval">按间隔执行</option>
                            <option value="once">指定时间执行</option>
                        </select>
                    </div>
                    <div class="col-2">
                        <div id="scheduleConfig">
                            <div class="cron-builder">
                                <div class="cron-field">
                                    <label>秒</label>
                                    <input type="text" class="form-control" value="0" onchange="updateCron()">
                                </div>
                                <div class="cron-field">
                                    <label>分</label>
                                    <input type="text" class="form-control" value="0" onchange="updateCron()">
                                </div>
                                <div class="cron-field">
                                    <label>时</label>
                                    <input type="text" class="form-control" value="*" onchange="updateCron()">
                                </div>
                                <div class="cron-field">
                                    <label>日</label>
                                    <input type="text" class="form-control" value="*" onchange="updateCron()">
                                </div>
                                <div class="cron-field">
                                    <label>月</label>
                                    <input type="text" class="form-control" value="*" onchange="updateCron()">
                                </div>
                                <div class="cron-field">
                                    <label>周</label>
                                    <input type="text" class="form-control" value="?" onchange="updateCron()">
                                </div>
                            </div>
                            <div class="cron-preview" id="cronPreview">
                                Cron表达式: 0 0 * * * ?
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>任务流程设计</label>
                <div class="schedule-visual">
                    <div style="margin-bottom: 15px;">
                        <button class="btn btn-primary" onclick="addTask('serial')">添加串行任务</button>
                        <button class="btn btn-success" onclick="addTask('parallel')">添加并行任务</button>
                        <button class="btn btn-info" onclick="clearFlow()">清空流程</button>
                    </div>
                    <div id="taskFlow">
                        <div class="task-node" onclick="editTask(this)">开始</div>
                        <div class="connection-line"></div>
                        <div class="task-node" onclick="editTask(this)">学生数据采集</div>
                        <div class="connection-line"></div>
                        <div style="display: inline-block; vertical-align: top;">
                            <div class="task-node parallel" onclick="editTask(this)">数据清洗</div>
                            <br><br>
                            <div class="task-node parallel" onclick="editTask(this)">数据验证</div>
                        </div>
                        <div class="connection-line"></div>
                        <div class="task-node" onclick="editTask(this)">数据入库</div>
                        <div class="connection-line"></div>
                        <div class="task-node" onclick="editTask(this)">结束</div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <button class="btn btn-primary">保存调度计划</button>
                <button class="btn btn-success">启动调度</button>
                <button class="btn btn-warning">暂停调度</button>
                <button class="btn btn-info">测试执行</button>
            </div>

            <h3>调度计划列表</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>计划名称</th>
                        <th>执行方式</th>
                        <th>调度表达式</th>
                        <th>状态</th>
                        <th>下次执行时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>学生数据日常采集</td>
                        <td>串行执行</td>
                        <td>0 0 2 * * ?</td>
                        <td><span class="status-badge status-running">运行中</span></td>
                        <td>2024-01-16 02:00:00</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">暂停</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">日志</button>
                        </td>
                    </tr>
                    <tr>
                        <td>教师信息同步</td>
                        <td>并行执行</td>
                        <td>0 0 */6 * * ?</td>
                        <td><span class="status-badge status-pending">等待中</span></td>
                        <td>2024-01-15 18:00:00</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">启动</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">日志</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 数据治理面板 -->
        <div id="governance" class="content-panel">
            <h2>数据治理责任清单</h2>
            <div class="alert alert-info">
                根据一数一源的工作原则完成《数据治理责任清单》，包括业务大类、业务子类、数据表单名称、责任单位、提供方式、共享方式等
            </div>

            <div class="search-box">
                <input type="text" class="form-control" placeholder="搜索数据表单...">
                <select class="form-control" style="flex: 0 0 150px;">
                    <option>全部业务大类</option>
                    <option>学生管理</option>
                    <option>教师管理</option>
                    <option>教学管理</option>
                    <option>科研管理</option>
                    <option>财务管理</option>
                </select>
                <select class="form-control" style="flex: 0 0 120px;">
                    <option>全部状态</option>
                    <option>主数据</option>
                    <option>非主数据</option>
                </select>
                <button class="btn btn-primary">搜索</button>
                <button class="btn btn-success">新增清单</button>
            </div>

            <div class="responsibility-table">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>业务大类</th>
                            <th>业务子类</th>
                            <th>数据表单名称</th>
                            <th>责任单位</th>
                            <th>提供方式</th>
                            <th>共享方式</th>
                            <th>是否主数据</th>
                            <th>是否进数仓</th>
                            <th>是否补采</th>
                            <th>补采策略</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>学生管理</td>
                            <td>学籍管理</td>
                            <td>学生基本信息表</td>
                            <td>学生处</td>
                            <td>API接口</td>
                            <td>实时共享</td>
                            <td><span style="color: green;">✓ 是</span></td>
                            <td><span style="color: green;">✓ 是</span></td>
                            <td><span style="color: red;">✗ 否</span></td>
                            <td>-</td>
                            <td>
                                <button class="btn btn-primary" style="padding: 4px 8px; font-size: 11px;">编辑</button>
                                <button class="btn btn-info" style="padding: 4px 8px; font-size: 11px;">查看</button>
                            </td>
                        </tr>
                        <tr>
                            <td>学生管理</td>
                            <td>成绩管理</td>
                            <td>学生成绩表</td>
                            <td>教务处</td>
                            <td>数据库同步</td>
                            <td>定时共享</td>
                            <td><span style="color: red;">✗ 否</span></td>
                            <td><span style="color: green;">✓ 是</span></td>
                            <td><span style="color: green;">✓ 是</span></td>
                            <td>每日增量补采</td>
                            <td>
                                <button class="btn btn-primary" style="padding: 4px 8px; font-size: 11px;">编辑</button>
                                <button class="btn btn-info" style="padding: 4px 8px; font-size: 11px;">查看</button>
                            </td>
                        </tr>
                        <tr>
                            <td>教师管理</td>
                            <td>人事档案</td>
                            <td>教师基本信息表</td>
                            <td>人事处</td>
                            <td>文件导入</td>
                            <td>按需共享</td>
                            <td><span style="color: green;">✓ 是</span></td>
                            <td><span style="color: green;">✓ 是</span></td>
                            <td><span style="color: red;">✗ 否</span></td>
                            <td>-</td>
                            <td>
                                <button class="btn btn-primary" style="padding: 4px 8px; font-size: 11px;">编辑</button>
                                <button class="btn btn-info" style="padding: 4px 8px; font-size: 11px;">查看</button>
                            </td>
                        </tr>
                        <tr>
                            <td>教学管理</td>
                            <td>课程管理</td>
                            <td>课程信息表</td>
                            <td>教务处</td>
                            <td>API接口</td>
                            <td>实时共享</td>
                            <td><span style="color: green;">✓ 是</span></td>
                            <td><span style="color: green;">✓ 是</span></td>
                            <td><span style="color: green;">✓ 是</span></td>
                            <td>每周全量补采</td>
                            <td>
                                <button class="btn btn-primary" style="padding: 4px 8px; font-size: 11px;">编辑</button>
                                <button class="btn btn-info" style="padding: 4px 8px; font-size: 11px;">查看</button>
                            </td>
                        </tr>
                        <tr>
                            <td>科研管理</td>
                            <td>项目管理</td>
                            <td>科研项目表</td>
                            <td>科研处</td>
                            <td>数据库同步</td>
                            <td>定时共享</td>
                            <td><span style="color: red;">✗ 否</span></td>
                            <td><span style="color: green;">✓ 是</span></td>
                            <td><span style="color: green;">✓ 是</span></td>
                            <td>每月增量补采</td>
                            <td>
                                <button class="btn btn-primary" style="padding: 4px 8px; font-size: 11px;">编辑</button>
                                <button class="btn btn-info" style="padding: 4px 8px; font-size: 11px;">查看</button>
                            </td>
                        </tr>
                        <tr>
                            <td>财务管理</td>
                            <td>收费管理</td>
                            <td>学费缴费记录表</td>
                            <td>财务处</td>
                            <td>文件导入</td>
                            <td>按需共享</td>
                            <td><span style="color: red;">✗ 否</span></td>
                            <td><span style="color: green;">✓ 是</span></td>
                            <td><span style="color: green;">✓ 是</span></td>
                            <td>每日增量补采</td>
                            <td>
                                <button class="btn btn-primary" style="padding: 4px 8px; font-size: 11px;">编辑</button>
                                <button class="btn btn-info" style="padding: 4px 8px; font-size: 11px;">查看</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div style="margin-top: 20px;">
                <button class="btn btn-success">导出清单</button>
                <button class="btn btn-info">生成报告</button>
                <button class="btn btn-warning">批量更新</button>
            </div>
        </div>

        <!-- 监控中心面板 -->
        <div id="monitor" class="content-panel">
            <h2>监控中心</h2>
            <div class="alert alert-info">
                实时监控数据采集任务执行状态、系统性能和数据质量
            </div>

            <div class="row">
                <div class="col">
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
                        <h3 style="color: #28a745; margin-bottom: 10px;">24</h3>
                        <p>今日成功任务</p>
                    </div>
                </div>
                <div class="col">
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
                        <h3 style="color: #dc3545; margin-bottom: 10px;">2</h3>
                        <p>今日失败任务</p>
                    </div>
                </div>
                <div class="col">
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
                        <h3 style="color: #ffc107; margin-bottom: 10px;">5</h3>
                        <p>运行中任务</p>
                    </div>
                </div>
                <div class="col">
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
                        <h3 style="color: #17a2b8; margin-bottom: 10px;">1.2TB</h3>
                        <p>今日采集数据量</p>
                    </div>
                </div>
            </div>

            <h3>实时任务监控</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>任务名称</th>
                        <th>任务类型</th>
                        <th>开始时间</th>
                        <th>运行时长</th>
                        <th>处理记录数</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>学生信息采集</td>
                        <td>数据采集</td>
                        <td>2024-01-15 14:30:00</td>
                        <td>00:05:23</td>
                        <td>15,234</td>
                        <td><span class="status-badge status-running">运行中</span></td>
                        <td>
                            <button class="btn btn-warning" style="padding: 4px 8px; font-size: 11px;">停止</button>
                            <button class="btn btn-info" style="padding: 4px 8px; font-size: 11px;">日志</button>
                        </td>
                    </tr>
                    <tr>
                        <td>教师数据清洗</td>
                        <td>数据清洗</td>
                        <td>2024-01-15 14:25:00</td>
                        <td>00:10:15</td>
                        <td>3,456</td>
                        <td><span class="status-badge status-completed">已完成</span></td>
                        <td>
                            <button class="btn btn-info" style="padding: 4px 8px; font-size: 11px;">查看</button>
                            <button class="btn btn-success" style="padding: 4px 8px; font-size: 11px;">重新执行</button>
                        </td>
                    </tr>
                    <tr>
                        <td>成绩数据同步</td>
                        <td>数据同步</td>
                        <td>2024-01-15 14:20:00</td>
                        <td>00:02:45</td>
                        <td>0</td>
                        <td><span class="status-badge status-failed">执行失败</span></td>
                        <td>
                            <button class="btn btn-danger" style="padding: 4px 8px; font-size: 11px;">查看错误</button>
                            <button class="btn btn-success" style="padding: 4px 8px; font-size: 11px;">重新执行</button>
                        </td>
                    </tr>
                </tbody>
            </table>

            <h3>系统资源监控</h3>
            <div class="row">
                <div class="col">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <h4>CPU使用率</h4>
                        <div style="background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden;">
                            <div style="background: #28a745; height: 100%; width: 45%; transition: width 0.3s;"></div>
                        </div>
                        <p style="margin-top: 5px; font-size: 12px;">45%</p>
                    </div>
                </div>
                <div class="col">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <h4>内存使用率</h4>
                        <div style="background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden;">
                            <div style="background: #ffc107; height: 100%; width: 72%; transition: width 0.3s;"></div>
                        </div>
                        <p style="margin-top: 5px; font-size: 12px;">72%</p>
                    </div>
                </div>
                <div class="col">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <h4>磁盘使用率</h4>
                        <div style="background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden;">
                            <div style="background: #17a2b8; height: 100%; width: 58%; transition: width 0.3s;"></div>
                        </div>
                        <p style="margin-top: 5px; font-size: 12px;">58%</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedDatabase = null;
        let selectedTask = null;

        // 切换面板显示
        function showPanel(panelId) {
            // 隐藏所有面板
            const panels = document.querySelectorAll('.content-panel');
            panels.forEach(panel => panel.classList.remove('active'));

            // 移除所有标签的active状态
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的面板
            document.getElementById(panelId).classList.add('active');

            // 激活对应的标签
            event.target.classList.add('active');
        }

        // 选择数据库
        function selectDatabase(element, dbType) {
            // 移除其他选中状态
            document.querySelectorAll('.database-card').forEach(card => {
                card.classList.remove('selected');
            });

            // 添加选中状态
            element.classList.add('selected');
            selectedDatabase = dbType;

            console.log('选择数据库:', dbType);
        }

        // 选择任务类型
        function selectTask(element, taskType) {
            // 移除其他选中状态
            document.querySelectorAll('.task-card').forEach(card => {
                card.classList.remove('selected');
            });

            // 添加选中状态
            element.classList.add('selected');
            selectedTask = taskType;

            console.log('选择任务类型:', taskType);
        }

        // 更改脚本类型
        function changeScriptType(type) {
            const editor = document.getElementById('scriptEditor');
            if (type === 'sql') {
                editor.innerHTML = `-- SQL数据查询脚本示例
SELECT
    s.student_id,
    s.student_name,
    s.class_id,
    c.class_name,
    s.enrollment_date
FROM students s
LEFT JOIN classes c ON s.class_id = c.class_id
WHERE s.status = 'active'
    AND s.enrollment_date >= '2023-01-01'
ORDER BY s.enrollment_date DESC;`;
            } else {
                editor.innerHTML = `# Python数据清洗脚本示例
import pandas as pd
import numpy as np

def clean_data(df):
    # 去除重复数据
    df = df.drop_duplicates()

    # 处理缺失值
    df = df.fillna(method='forward')

    # 数据类型转换
    df['age'] = pd.to_numeric(df['age'], errors='coerce')

    return df

# 主函数
if __name__ == "__main__":
    # 读取数据
    data = pd.read_csv('input.csv')

    # 清洗数据
    cleaned_data = clean_data(data)

    # 保存结果
    cleaned_data.to_csv('output.csv', index=False)`;
            }
        }

        // 更改调度类型
        function changeScheduleType(type) {
            const config = document.getElementById('scheduleConfig');
            if (type === 'interval') {
                config.innerHTML = `
                    <div class="row">
                        <div class="col">
                            <label>间隔时间</label>
                            <input type="number" class="form-control" placeholder="60" min="1">
                        </div>
                        <div class="col">
                            <label>时间单位</label>
                            <select class="form-control">
                                <option>秒</option>
                                <option>分钟</option>
                                <option>小时</option>
                                <option>天</option>
                            </select>
                        </div>
                    </div>
                `;
            } else if (type === 'once') {
                config.innerHTML = `
                    <div class="row">
                        <div class="col">
                            <label>执行日期</label>
                            <input type="date" class="form-control">
                        </div>
                        <div class="col">
                            <label>执行时间</label>
                            <input type="time" class="form-control">
                        </div>
                    </div>
                `;
            } else {
                config.innerHTML = `
                    <div class="cron-builder">
                        <div class="cron-field">
                            <label>秒</label>
                            <input type="text" class="form-control" value="0" onchange="updateCron()">
                        </div>
                        <div class="cron-field">
                            <label>分</label>
                            <input type="text" class="form-control" value="0" onchange="updateCron()">
                        </div>
                        <div class="cron-field">
                            <label>时</label>
                            <input type="text" class="form-control" value="*" onchange="updateCron()">
                        </div>
                        <div class="cron-field">
                            <label>日</label>
                            <input type="text" class="form-control" value="*" onchange="updateCron()">
                        </div>
                        <div class="cron-field">
                            <label>月</label>
                            <input type="text" class="form-control" value="*" onchange="updateCron()">
                        </div>
                        <div class="cron-field">
                            <label>周</label>
                            <input type="text" class="form-control" value="?" onchange="updateCron()">
                        </div>
                    </div>
                    <div class="cron-preview" id="cronPreview">
                        Cron表达式: 0 0 * * * ?
                    </div>
                `;
            }
        }

        // 更新Cron表达式
        function updateCron() {
            const fields = document.querySelectorAll('.cron-field input');
            const values = Array.from(fields).map(field => field.value);
            const cronExpression = values.join(' ');
            document.getElementById('cronPreview').textContent = `Cron表达式: ${cronExpression}`;
        }

        // 添加任务到流程
        function addTask(type) {
            const flow = document.getElementById('taskFlow');
            const taskName = prompt('请输入任务名称:');
            if (taskName) {
                const taskNode = document.createElement('div');
                taskNode.className = `task-node ${type === 'parallel' ? 'parallel' : ''}`;
                taskNode.textContent = taskName;
                taskNode.onclick = function() { editTask(this); };

                const connectionLine = document.createElement('div');
                connectionLine.className = 'connection-line';

                flow.appendChild(connectionLine);
                flow.appendChild(taskNode);
            }
        }

        // 编辑任务
        function editTask(element) {
            const newName = prompt('请输入新的任务名称:', element.textContent);
            if (newName) {
                element.textContent = newName;
            }
        }

        // 清空流程
        function clearFlow() {
            if (confirm('确定要清空所有任务流程吗？')) {
                document.getElementById('taskFlow').innerHTML = `
                    <div class="task-node" onclick="editTask(this)">开始</div>
                    <div class="connection-line"></div>
                    <div class="task-node" onclick="editTask(this)">结束</div>
                `;
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('数据采集系统已加载');

            // 模拟实时数据更新
            setInterval(function() {
                // 这里可以添加实时数据更新逻辑
                console.log('更新监控数据...');
            }, 30000); // 每30秒更新一次
        });
    </script>
</body>
</html>
