<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>业务驾驶舱系统</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            color: white;
            padding: 15px 0;
            text-align: center;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .nav-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
            flex-wrap: wrap;
        }
        
        .nav-tab {
            flex: 1;
            min-width: 120px;
            padding: 15px 10px;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            color: #1e3c72;
        }
        
        .nav-tab.active {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
        }
        
        .content-panel {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .content-panel.active {
            display: block;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .dashboard-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border-left: 4px solid #1e3c72;
        }
        
        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        
        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        }
        
        .card-value {
            font-size: 32px;
            font-weight: bold;
            color: #1e3c72;
            margin-bottom: 8px;
        }
        
        .card-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .card-trend {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #28a745;
        }
        
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }
        
        .chart-wrapper {
            position: relative;
            height: 300px;
            width: 100%;
        }
        
        .row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .col {
            flex: 1;
        }
        
        .alert {
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            background: #d1ecf1;
            color: #0c5460;
            border-left: 4px solid #17a2b8;
        }
        
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            margin: 8px 0;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 10px;
            background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 业务驾驶舱系统</h1>
        <p>实时监控各部门业务运行状况，支持可定制化数据展示</p>
    </div>

    <div class="container">
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showPanel('office')">📋 办公室</button>
            <button class="nav-tab" onclick="showPanel('academic')">📚 教务处</button>
            <button class="nav-tab" onclick="showPanel('support')">🔧 教保处</button>
            <button class="nav-tab" onclick="showPanel('research')">🔬 科研处</button>
            <button class="nav-tab" onclick="showPanel('personnel')">👥 政工处</button>
            <button class="nav-tab" onclick="showPanel('security')">🛡️ 安管处</button>
            <button class="nav-tab" onclick="showPanel('logistics')">🏢 供保处</button>
        </div>

        <!-- 办公室业务看板 -->
        <div id="office" class="content-panel active">
            <h2>📋 办公室业务看板</h2>
            <div class="alert">
                重点事项督办、信息服务报送情况、人员在位情况、用印情况等业务监控
            </div>
            
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">重点事项督办</div>
                        <div class="card-icon">📌</div>
                    </div>
                    <div class="card-value">23</div>
                    <div class="card-label">待办事项</div>
                    <div class="card-trend">↗ 较昨日 +3</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 76%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">完成率: 76%</div>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">信息服务报送</div>
                        <div class="card-icon">📊</div>
                    </div>
                    <div class="card-value">156</div>
                    <div class="card-label">本月报送数量</div>
                    <div class="card-trend">↗ 较上月 +12</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 89%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">及时率: 89%</div>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">人员在位情况</div>
                        <div class="card-icon">👤</div>
                    </div>
                    <div class="card-value">186</div>
                    <div class="card-label">当前在位人数</div>
                    <div class="card-trend">→ 出勤率 95.4%</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 95.4%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">总人数: 195人</div>
                </div>
                
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">用印情况</div>
                        <div class="card-icon">🔖</div>
                    </div>
                    <div class="card-value">45</div>
                    <div class="card-label">本周用印次数</div>
                    <div class="card-trend">↘ 较上周 -8</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 68%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">审批通过率: 98%</div>
                </div>
            </div>
            
            <div class="row">
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">重点事项督办完成趋势</div>
                        <div class="chart-wrapper">
                            <canvas id="officeTaskChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">信息报送类型分布</div>
                        <div class="chart-wrapper">
                            <canvas id="officeReportChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 教务处业务看板 -->
        <div id="academic" class="content-panel">
            <h2>📚 教务处业务看板</h2>
            <div class="alert">开课情况、教材使用、课程教学计划、教学成果及奖励等业务监控</div>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">开课情况</div>
                        <div class="card-icon">📖</div>
                    </div>
                    <div class="card-value">342</div>
                    <div class="card-label">本学期开课门数</div>
                    <div class="card-trend">↗ 较上学期 +15</div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">教学成果及奖励统计</div>
                        <div class="chart-wrapper">
                            <canvas id="academicAwardChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">领导听查课情况</div>
                        <div class="chart-wrapper">
                            <canvas id="academicInspectionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 教保处业务看板 -->
        <div id="support" class="content-panel">
            <h2>🔧 教保处业务看板</h2>
            <div class="alert">重点项目、场地、信息化、实验室等板块业务监控</div>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">重点项目</div>
                        <div class="card-icon">🎯</div>
                    </div>
                    <div class="card-value">15</div>
                    <div class="card-label">在建项目数量</div>
                    <div class="card-trend">↗ 新增 3个</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 73%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">平均进度: 73%</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">场地管理</div>
                        <div class="card-icon">🏢</div>
                    </div>
                    <div class="card-value">85.6%</div>
                    <div class="card-label">场地使用率</div>
                    <div class="card-trend">↗ 较上月 +3.2%</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 85.6%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">可用场地: 156个</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">信息化建设</div>
                        <div class="card-icon">💻</div>
                    </div>
                    <div class="card-value">12</div>
                    <div class="card-label">信息化项目</div>
                    <div class="card-trend">→ 运行正常</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 92%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">系统可用率: 99.2%</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">实验室管理</div>
                        <div class="card-icon">🔬</div>
                    </div>
                    <div class="card-value">45</div>
                    <div class="card-label">实验室数量</div>
                    <div class="card-trend">↗ 新建 2个</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 88%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">设备完好率: 96%</div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">重点项目进度统计</div>
                        <div class="chart-wrapper">
                            <canvas id="supportProjectChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">实验室使用情况</div>
                        <div class="chart-wrapper">
                            <canvas id="supportLabChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="research" class="content-panel">
            <h2>🔬 科研处业务看板</h2>
            <div class="alert">科研项目、学术活动、学术成果、创新平台等板块业务监控</div>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">科研项目</div>
                        <div class="card-icon">📋</div>
                    </div>
                    <div class="card-value">107</div>
                    <div class="card-label">在研项目数量</div>
                    <div class="card-trend">↗ 新增 8个</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 78%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">总经费: 2856万</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">学术活动</div>
                        <div class="card-icon">🎓</div>
                    </div>
                    <div class="card-value">23</div>
                    <div class="card-label">本月学术活动</div>
                    <div class="card-trend">↗ 较上月 +5</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 85%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">参与人次: 1,234</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">学术成果</div>
                        <div class="card-icon">📄</div>
                    </div>
                    <div class="card-value">245</div>
                    <div class="card-label">本年度发表论文</div>
                    <div class="card-trend">↗ 较去年 +23</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 92%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">SCI论文: 89篇</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">创新平台</div>
                        <div class="card-icon">🏛️</div>
                    </div>
                    <div class="card-value">6</div>
                    <div class="card-label">科研平台数量</div>
                    <div class="card-trend">→ 运行良好</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 95%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">国家级: 1个</div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">科研项目类型分布</div>
                        <div class="chart-wrapper">
                            <canvas id="researchProjectChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">学术成果趋势</div>
                        <div class="chart-wrapper">
                            <canvas id="researchOutputChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="personnel" class="content-panel">
            <h2>👥 政工处业务看板</h2>
            <div class="alert">人员基础数据整体情况、教师信息、学生信息、职工信息、社聘人员信息等板块业务监控</div>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">教师信息</div>
                        <div class="card-icon">👨‍🏫</div>
                    </div>
                    <div class="card-value">186</div>
                    <div class="card-label">在职教师总数</div>
                    <div class="card-trend">↗ 新增 5人</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 93%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">博士比例: 83.9%</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">学生信息</div>
                        <div class="card-icon">👨‍🎓</div>
                    </div>
                    <div class="card-value">3,256</div>
                    <div class="card-label">在校学生总数</div>
                    <div class="card-trend">↗ 较上年 +78</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 98%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">本科生: 2,456人</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">职工信息</div>
                        <div class="card-icon">👷</div>
                    </div>
                    <div class="card-value">89</div>
                    <div class="card-label">管理职工总数</div>
                    <div class="card-trend">→ 人员稳定</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 95%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">在岗率: 95%</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">社聘人员</div>
                        <div class="card-icon">🤝</div>
                    </div>
                    <div class="card-value">45</div>
                    <div class="card-label">社聘人员总数</div>
                    <div class="card-trend">↗ 新增 3人</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 88%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">合同续签率: 92%</div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">人员结构分布</div>
                        <div class="chart-wrapper">
                            <canvas id="personnelStructureChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">人员变动趋势</div>
                        <div class="chart-wrapper">
                            <canvas id="personnelTrendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="security" class="content-panel">
            <h2>🛡️ 安管处业务看板</h2>
            <div class="alert">管理、安全、保密、保卫、运投、油料等板块业务监控</div>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">安全管理</div>
                        <div class="card-icon">🔒</div>
                    </div>
                    <div class="card-value">0</div>
                    <div class="card-label">本月安全事故</div>
                    <div class="card-trend">→ 安全稳定</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 100%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">安全检查: 156次</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">保密工作</div>
                        <div class="card-icon">🔐</div>
                    </div>
                    <div class="card-value">98.5%</div>
                    <div class="card-label">保密检查合格率</div>
                    <div class="card-trend">↗ 较上月 +1.2%</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 98.5%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">培训人次: 234</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">保卫工作</div>
                        <div class="card-icon">👮</div>
                    </div>
                    <div class="card-value">24/7</div>
                    <div class="card-label">全天候值守</div>
                    <div class="card-trend">→ 运行正常</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 100%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">巡逻次数: 48次/日</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">油料管理</div>
                        <div class="card-icon">⛽</div>
                    </div>
                    <div class="card-value">85.6%</div>
                    <div class="card-label">油料库存率</div>
                    <div class="card-trend">↘ 需要补充</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 85.6%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">月消耗: 1,234升</div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">安全检查统计</div>
                        <div class="chart-wrapper">
                            <canvas id="securityCheckChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">保密培训情况</div>
                        <div class="chart-wrapper">
                            <canvas id="securityTrainingChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="logistics" class="content-panel">
            <h2>🏢 供保处业务看板</h2>
            <div class="alert">采购和公共用房等板块业务监控</div>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">采购管理</div>
                        <div class="card-icon">🛒</div>
                    </div>
                    <div class="card-value">156</div>
                    <div class="card-label">本月采购项目</div>
                    <div class="card-trend">↗ 较上月 +12</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 89%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">采购金额: 234万</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">公共用房</div>
                        <div class="card-icon">🏠</div>
                    </div>
                    <div class="card-value">92.3%</div>
                    <div class="card-label">用房使用率</div>
                    <div class="card-trend">↗ 较上月 +2.1%</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 92.3%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">总面积: 15.6万㎡</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">维修保养</div>
                        <div class="card-icon">🔧</div>
                    </div>
                    <div class="card-value">45</div>
                    <div class="card-label">本月维修工单</div>
                    <div class="card-trend">↘ 较上月 -8</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 95%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">完成率: 95%</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">能耗管理</div>
                        <div class="card-icon">⚡</div>
                    </div>
                    <div class="card-value">-5.2%</div>
                    <div class="card-label">能耗同比变化</div>
                    <div class="card-trend">↗ 节能效果显著</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 78%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">月用电: 45万度</div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">采购类型分布</div>
                        <div class="chart-wrapper">
                            <canvas id="logisticsProcurementChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">能耗趋势分析</div>
                        <div class="chart-wrapper">
                            <canvas id="logisticsEnergyChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 切换面板显示
        function showPanel(panelId) {
            // 隐藏所有面板
            const panels = document.querySelectorAll('.content-panel');
            panels.forEach(panel => panel.classList.remove('active'));

            // 移除所有标签的active状态
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的面板
            document.getElementById(panelId).classList.add('active');

            // 激活对应的标签
            event.target.classList.add('active');

            // 初始化对应面板的图表
            setTimeout(() => {
                initPanelCharts(panelId);
            }, 100);
        }

        // 初始化特定面板的图表
        function initPanelCharts(panelId) {
            switch(panelId) {
                case 'office':
                    initOfficeCharts();
                    break;
                case 'academic':
                    initAcademicCharts();
                    break;
                case 'support':
                    initSupportCharts();
                    break;
                case 'research':
                    initResearchCharts();
                    break;
                case 'personnel':
                    initPersonnelCharts();
                    break;
                case 'security':
                    initSecurityCharts();
                    break;
                case 'logistics':
                    initLogisticsCharts();
                    break;
            }
        }

        // 办公室图表
        function initOfficeCharts() {
            console.log('初始化办公室图表...');

            // 重点事项督办完成趋势
            const taskCtx = document.getElementById('officeTaskChart');
            if (taskCtx) {
                new Chart(taskCtx, {
                    type: 'line',
                    data: {
                        labels: ['周一', '周二', '周三', '周四', '周五'],
                        datasets: [{
                            label: '完成事项',
                            data: [8, 12, 15, 18, 23],
                            borderColor: '#1e3c72',
                            backgroundColor: 'rgba(30, 60, 114, 0.1)',
                            tension: 0.4
                        }, {
                            label: '新增事项',
                            data: [5, 8, 6, 9, 7],
                            borderColor: '#2a5298',
                            backgroundColor: 'rgba(42, 82, 152, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
                console.log('重点事项督办图表创建成功');
            }

            // 信息报送类型分布
            const reportCtx = document.getElementById('officeReportChart');
            if (reportCtx) {
                new Chart(reportCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['工作简报', '统计报表', '专项报告', '会议纪要', '其他'],
                        datasets: [{
                            data: [45, 32, 28, 25, 26],
                            backgroundColor: [
                                '#1e3c72',
                                '#2a5298',
                                '#4a90e2',
                                '#7bb3f0',
                                '#a8d0f7'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
                console.log('信息报送分布图表创建成功');
            }
        }

        // 教务处图表
        function initAcademicCharts() {
            console.log('初始化教务处图表...');

            // 教学成果及奖励统计
            const awardCtx = document.getElementById('academicAwardChart');
            if (awardCtx) {
                new Chart(awardCtx, {
                    type: 'bar',
                    data: {
                        labels: ['国家级', '省部级', '校级', '院级'],
                        datasets: [{
                            label: '教学成果奖',
                            data: [3, 8, 15, 23],
                            backgroundColor: '#1e3c72'
                        }, {
                            label: '教学竞赛奖',
                            data: [2, 12, 18, 28],
                            backgroundColor: '#2a5298'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }

            // 领导听查课情况
            const inspectionCtx = document.getElementById('academicInspectionChart');
            if (inspectionCtx) {
                new Chart(inspectionCtx, {
                    type: 'line',
                    data: {
                        labels: ['第1周', '第2周', '第3周', '第4周'],
                        datasets: [{
                            label: '听课次数',
                            data: [12, 15, 18, 20],
                            borderColor: '#1e3c72',
                            tension: 0.4
                        }, {
                            label: '查课次数',
                            data: [8, 10, 12, 14],
                            borderColor: '#2a5298',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        }

        // 教保处图表
        function initSupportCharts() {
            console.log('初始化教保处图表...');

            // 重点项目进度统计
            const projectCtx = document.getElementById('supportProjectChart');
            if (projectCtx) {
                new Chart(projectCtx, {
                    type: 'bar',
                    data: {
                        labels: ['智慧教室', '实验室改造', '网络升级', '设备采购', '基础设施'],
                        datasets: [{
                            label: '完成进度(%)',
                            data: [85, 73, 92, 68, 76],
                            backgroundColor: [
                                '#1e3c72',
                                '#2a5298',
                                '#4a90e2',
                                '#7bb3f0',
                                '#a8d0f7'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100
                            }
                        }
                    }
                });
            }

            // 实验室使用情况
            const labCtx = document.getElementById('supportLabChart');
            if (labCtx) {
                new Chart(labCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['计算机实验室', '物理实验室', '化学实验室', '工程实验室', '其他'],
                        datasets: [{
                            data: [25, 18, 15, 20, 12],
                            backgroundColor: [
                                '#1e3c72',
                                '#2a5298',
                                '#4a90e2',
                                '#7bb3f0',
                                '#a8d0f7'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        }

        // 科研处图表
        function initResearchCharts() {
            console.log('初始化科研处图表...');

            // 科研项目类型分布
            const projectCtx = document.getElementById('researchProjectChart');
            if (projectCtx) {
                new Chart(projectCtx, {
                    type: 'pie',
                    data: {
                        labels: ['国家级', '省部级', '军队级', '企业合作', '其他'],
                        datasets: [{
                            data: [26, 35, 28, 18, 8],
                            backgroundColor: [
                                '#1e3c72',
                                '#2a5298',
                                '#4a90e2',
                                '#7bb3f0',
                                '#a8d0f7'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            // 学术成果趋势
            const outputCtx = document.getElementById('researchOutputChart');
            if (outputCtx) {
                new Chart(outputCtx, {
                    type: 'line',
                    data: {
                        labels: ['2022年', '2023年', '2024年'],
                        datasets: [{
                            label: '发表论文',
                            data: [198, 213, 245],
                            borderColor: '#1e3c72',
                            tension: 0.4
                        }, {
                            label: '授权专利',
                            data: [56, 69, 89],
                            borderColor: '#2a5298',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        }

        // 政工处图表
        function initPersonnelCharts() {
            console.log('初始化政工处图表...');

            // 人员结构分布
            const structureCtx = document.getElementById('personnelStructureChart');
            if (structureCtx) {
                new Chart(structureCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['教师', '学生', '职工', '社聘人员'],
                        datasets: [{
                            data: [186, 3256, 89, 45],
                            backgroundColor: [
                                '#1e3c72',
                                '#2a5298',
                                '#4a90e2',
                                '#7bb3f0'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            // 人员变动趋势
            const trendCtx = document.getElementById('personnelTrendChart');
            if (trendCtx) {
                new Chart(trendCtx, {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                        datasets: [{
                            label: '新入职',
                            data: [5, 3, 8, 6, 4, 7],
                            borderColor: '#1e3c72',
                            tension: 0.4
                        }, {
                            label: '离职',
                            data: [2, 1, 3, 2, 1, 2],
                            borderColor: '#2a5298',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        }

        // 安管处图表
        function initSecurityCharts() {
            console.log('初始化安管处图表...');

            // 安全检查统计
            const checkCtx = document.getElementById('securityCheckChart');
            if (checkCtx) {
                new Chart(checkCtx, {
                    type: 'bar',
                    data: {
                        labels: ['消防检查', '设备检查', '人员检查', '环境检查', '其他检查'],
                        datasets: [{
                            label: '检查次数',
                            data: [45, 38, 42, 31, 28],
                            backgroundColor: '#1e3c72'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }

            // 保密培训情况
            const trainingCtx = document.getElementById('securityTrainingChart');
            if (trainingCtx) {
                new Chart(trainingCtx, {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                        datasets: [{
                            label: '培训人次',
                            data: [45, 52, 38, 61, 42, 58],
                            borderColor: '#1e3c72',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        }

        // 供保处图表
        function initLogisticsCharts() {
            console.log('初始化供保处图表...');

            // 采购类型分布
            const procurementCtx = document.getElementById('logisticsProcurementChart');
            if (procurementCtx) {
                new Chart(procurementCtx, {
                    type: 'bar',
                    data: {
                        labels: ['设备采购', '办公用品', '维修材料', '服务采购', '其他'],
                        datasets: [{
                            label: '采购金额(万元)',
                            data: [89, 45, 32, 28, 40],
                            backgroundColor: [
                                '#1e3c72',
                                '#2a5298',
                                '#4a90e2',
                                '#7bb3f0',
                                '#a8d0f7'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }

            // 能耗趋势分析
            const energyCtx = document.getElementById('logisticsEnergyChart');
            if (energyCtx) {
                new Chart(energyCtx, {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                        datasets: [{
                            label: '用电量(万度)',
                            data: [52, 48, 45, 43, 41, 45],
                            borderColor: '#1e3c72',
                            tension: 0.4
                        }, {
                            label: '用水量(万吨)',
                            data: [8.5, 7.8, 7.2, 6.9, 6.5, 7.1],
                            borderColor: '#2a5298',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('业务驾驶舱系统已加载');

            // 检查Chart.js是否加载
            if (typeof Chart === 'undefined') {
                console.error('Chart.js未加载');
                return;
            }

            // 延迟初始化办公室图表
            setTimeout(() => {
                initOfficeCharts();
            }, 1000);
        });
    </script>
</body>
</html>
