<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>司机工作台 - 智能派单系统</title>
    <link rel="stylesheet" href="common-styles.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding-bottom: 80px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .driver-status-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        
        .driver-info {
            display: flex;
            align-items: center;
        }
        
        .avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-right: 15px;
        }
        
        .driver-details h3 {
            font-size: 18px;
            margin-bottom: 3px;
        }
        
        .driver-details p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .status-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-toggle {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .status-toggle.online {
            background: #2ed573;
            border-color: #2ed573;
        }
        
        .status-toggle.offline {
            background: #ff4757;
            border-color: #ff4757;
        }
        
        .heartbeat-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #2ed573;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(46, 213, 115, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(46, 213, 115, 0); }
            100% { box-shadow: 0 0 0 0 rgba(46, 213, 115, 0); }
        }
        
        .real-time-stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
        }
        
        .stat-item {
            text-align: center;
            background: rgba(255,255,255,0.1);
            padding: 12px 8px;
            border-radius: 10px;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 3px;
        }
        
        .stat-label {
            font-size: 11px;
            opacity: 0.8;
        }
        
        .earnings-card {
            background: linear-gradient(135deg, #2ed573 0%, #00b894 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .earnings-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .earnings-title {
            font-size: 16px;
            margin-bottom: 10px;
            opacity: 0.9;
        }
        
        .earnings-amount {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .earnings-subtitle {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .order-management {
            margin: 20px;
        }
        
        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            display: flex;
            align-items: center;
        }
        
        .section-icon {
            margin-right: 8px;
            font-size: 20px;
        }
        
        .refresh-btn {
            background: none;
            border: none;
            color: #667eea;
            font-size: 16px;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            background: #f0f0f0;
            transform: rotate(180deg);
        }
        
        .order-tabs {
            display: flex;
            background: white;
            border-radius: 15px;
            padding: 5px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .tab-btn {
            flex: 1;
            background: none;
            border: none;
            padding: 12px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            position: relative;
        }
        
        .tab-btn.active {
            background: #667eea;
            color: white;
        }
        
        .tab-badge {
            position: absolute;
            top: 2px;
            right: 5px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .urgent-order {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 4px 20px rgba(255, 71, 87, 0.2);
            border-left: 4px solid #ff4757;
            animation: urgentPulse 2s infinite;
            position: relative;
        }
        
        @keyframes urgentPulse {
            0%, 100% { box-shadow: 0 4px 20px rgba(255, 71, 87, 0.2); }
            50% { box-shadow: 0 6px 25px rgba(255, 71, 87, 0.4); }
        }
        
        .urgent-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #ff4757;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .countdown-timer {
            position: absolute;
            top: 15px;
            right: 80px;
            background: #ffa502;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            animation: countdownBlink 1s infinite;
        }
        
        @keyframes countdownBlink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.7; }
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .order-id {
            font-weight: bold;
            color: #333;
            font-size: 16px;
        }
        
        .order-price {
            font-size: 20px;
            font-weight: bold;
            color: #667eea;
        }
        
        .passenger-info {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 12px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .passenger-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #2ed573;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 12px;
        }
        
        .passenger-details {
            flex: 1;
        }
        
        .passenger-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 2px;
        }
        
        .passenger-rating {
            font-size: 12px;
            color: #666;
        }
        
        .contact-btns {
            display: flex;
            gap: 8px;
        }
        
        .contact-btn {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .call-btn {
            background: #2ed573;
            color: white;
        }
        
        .message-btn {
            background: #667eea;
            color: white;
        }
        
        .route-display {
            margin-bottom: 15px;
        }
        
        .location-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .location-icon {
            width: 20px;
            text-align: center;
            margin-right: 10px;
            font-size: 16px;
        }
        
        .location-text {
            flex: 1;
            font-size: 14px;
        }
        
        .distance-info {
            font-size: 12px;
            color: #666;
            background: white;
            padding: 2px 6px;
            border-radius: 10px;
        }
        
        .order-actions {
            display: flex;
            gap: 10px;
        }
        
        .action-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        
        .btn-accept {
            background: #2ed573;
            color: white;
        }
        
        .btn-reject {
            background: #ff4757;
            color: white;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            display: flex;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }
        
        .nav-item {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            background: none;
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 5px;
        }
        
        .nav-label {
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="driver-status-bar">
            <div class="driver-info">
                <div class="avatar">👨‍💼</div>
                <div class="driver-details">
                    <h3>张师傅</h3>
                    <p>京A12345 | 在线时长: 3小时28分</p>
                </div>
            </div>
            <div class="status-controls">
                <div class="heartbeat-indicator" title="心跳正常"></div>
                <button class="status-toggle online" onclick="toggleStatus()">在线接单</button>
            </div>
        </div>
        
        <div class="real-time-stats">
            <div class="stat-item">
                <div class="stat-value">8</div>
                <div class="stat-label">今日订单</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">4.9</div>
                <div class="stat-label">服务评分</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">156</div>
                <div class="stat-label">总里程</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">98%</div>
                <div class="stat-label">接单率</div>
            </div>
        </div>
    </div>
    
    <div class="earnings-card">
        <div class="earnings-title">今日收入</div>
        <div class="earnings-amount">¥428.50</div>
        <div class="earnings-subtitle">昨日同期: ¥385.20 ↗</div>
    </div>
    
    <div class="order-management">
        <div class="section-header">
            <h2 class="section-title">
                <span class="section-icon">📋</span>
                订单管理
            </h2>
            <button class="refresh-btn" onclick="refreshOrders()" title="刷新订单">🔄</button>
        </div>
        
        <div class="order-tabs">
            <button class="tab-btn active" onclick="switchTab('pending')">
                待接单
                <span class="tab-badge">2</span>
            </button>
            <button class="tab-btn" onclick="switchTab('ongoing')">进行中</button>
            <button class="tab-btn" onclick="switchTab('completed')">已完成</button>
        </div>
        
        <!-- 紧急订单 -->
        <div class="urgent-order">
            <div class="urgent-badge">紧急</div>
            <div class="countdown-timer" id="countdown">15s</div>
            
            <div class="order-header">
                <span class="order-id">订单 #20241204001</span>
                <span class="order-price">¥45.00</span>
            </div>
            
            <div class="passenger-info">
                <div class="passenger-avatar">👤</div>
                <div class="passenger-details">
                    <div class="passenger-name">李女士</div>
                    <div class="passenger-rating">⭐ 4.8分 | 常客</div>
                </div>
                <div class="contact-btns">
                    <button class="contact-btn call-btn" onclick="callPassenger()">📞</button>
                    <button class="contact-btn message-btn" onclick="messagePassenger()">💬</button>
                </div>
            </div>
            
            <div class="route-display">
                <div class="location-item">
                    <span class="location-icon">🟢</span>
                    <span class="location-text">北京站东广场</span>
                    <span class="distance-info">距离 2.3km</span>
                </div>
                <div class="location-item">
                    <span class="location-icon">🔴</span>
                    <span class="location-text">首都国际机场T3航站楼</span>
                    <span class="distance-info">预计 45分钟</span>
                </div>
            </div>
            
            <div class="order-actions">
                <button class="action-btn btn-reject" onclick="rejectOrder('20241204001')">拒绝</button>
                <button class="action-btn btn-accept" onclick="acceptOrder('20241204001')">接单</button>
            </div>
        </div>
    </div>
    
    <div class="bottom-nav">
        <button class="nav-item active">
            <div class="nav-icon">🏠</div>
            <div class="nav-label">工作台</div>
        </button>
        <button class="nav-item" onclick="goToOrders()">
            <div class="nav-icon">📋</div>
            <div class="nav-label">订单</div>
        </button>
        <button class="nav-item" onclick="goToEarnings()">
            <div class="nav-icon">💰</div>
            <div class="nav-label">收入</div>
        </button>
        <button class="nav-item" onclick="goToProfile()">
            <div class="nav-icon">👤</div>
            <div class="nav-label">我的</div>
        </button>
    </div>

    <script>
        let isOnline = true;
        let countdownTimer = 15;
        let countdownInterval;
        
        // 心跳上传模拟
        setInterval(() => {
            uploadHeartbeat();
        }, 30000); // 30秒间隔
        
        function uploadHeartbeat() {
            console.log('上传心跳数据:', {
                timestamp: new Date().toISOString(),
                location: { lat: 39.9042, lng: 116.4074 },
                status: isOnline ? 'online' : 'offline',
                ordersToday: 8,
                earnings: 428.50
            });
        }
        
        function toggleStatus() {
            isOnline = !isOnline;
            const toggle = document.querySelector('.status-toggle');
            const heartbeat = document.querySelector('.heartbeat-indicator');
            
            if (isOnline) {
                toggle.textContent = '在线接单';
                toggle.className = 'status-toggle online';
                heartbeat.style.background = '#2ed573';
            } else {
                toggle.textContent = '离线休息';
                toggle.className = 'status-toggle offline';
                heartbeat.style.background = '#ff4757';
            }
        }
        
        function startCountdown() {
            countdownInterval = setInterval(() => {
                countdownTimer--;
                document.getElementById('countdown').textContent = countdownTimer + 's';
                
                if (countdownTimer <= 0) {
                    clearInterval(countdownInterval);
                    autoRejectOrder();
                }
            }, 1000);
        }
        
        function autoRejectOrder() {
            alert('订单超时，自动拒绝');
            document.querySelector('.urgent-order').style.display = 'none';
        }
        
        function acceptOrder(orderId) {
            clearInterval(countdownInterval);
            alert(`已接受订单 ${orderId}，正在导航到上车点...`);
            // 这里可以调用地图导航API
        }
        
        function rejectOrder(orderId) {
            clearInterval(countdownInterval);
            if (confirm('确认拒绝此订单？')) {
                alert(`已拒绝订单 ${orderId}`);
                document.querySelector('.urgent-order').style.display = 'none';
            }
        }
        
        function callPassenger() {
            alert('正在拨打乘客电话...');
        }
        
        function messagePassenger() {
            alert('正在发送消息给乘客...');
        }
        
        function refreshOrders() {
            const btn = document.querySelector('.refresh-btn');
            btn.style.transform = 'rotate(360deg)';
            setTimeout(() => {
                btn.style.transform = 'rotate(0deg)';
                alert('订单列表已刷新');
            }, 500);
        }
        
        function switchTab(tab) {
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // 这里可以加载对应标签的订单数据
            console.log('切换到标签:', tab);
        }
        
        function goToOrders() {
            window.location.href = 'driver-orders.html';
        }
        
        function goToEarnings() {
            window.location.href = 'driver-earnings.html';
        }
        
        function goToProfile() {
            window.location.href = 'driver-profile.html';
        }
        
        // 页面加载时启动倒计时
        window.addEventListener('load', () => {
            startCountdown();
            uploadHeartbeat();
        });
    </script>

    <!-- 引入通用导航组件 -->
    <script src="common-navigation.js"></script>
</body>
</html>
