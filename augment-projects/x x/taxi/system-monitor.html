<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统监控中心</title>
    <link rel="stylesheet" href="common-styles.css">
    <style>
        body {
            background: #0f1419;
            color: #ffffff;
            font-family: 'Courier New', monospace;
        }
        
        .monitor-header {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            padding: 20px;
            border-bottom: 2px solid #00ff41;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .system-title {
            font-size: 24px;
            font-weight: bold;
            color: #00ff41;
            text-shadow: 0 0 10px #00ff41;
        }
        
        .system-status {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            background: rgba(0, 255, 65, 0.1);
            border: 1px solid #00ff41;
            border-radius: 20px;
            font-size: 14px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #00ff41;
            animation: pulse 2s infinite;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .monitor-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .monitor-card {
            background: rgba(26, 26, 46, 0.8);
            border: 1px solid #00ff41;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 0 20px rgba(0, 255, 65, 0.1);
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(0, 255, 65, 0.3);
        }
        
        .card-title {
            font-size: 16px;
            font-weight: bold;
            color: #00ff41;
        }
        
        .card-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-online {
            background: rgba(0, 255, 65, 0.2);
            color: #00ff41;
            border: 1px solid #00ff41;
        }
        
        .status-warning {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
            border: 1px solid #ffc107;
        }
        
        .status-error {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
            border: 1px solid #dc3545;
        }
        
        .metric-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .metric-label {
            color: #cccccc;
        }
        
        .metric-value {
            color: #00ff41;
            font-weight: bold;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00ff41 0%, #00cc33 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .real-time-log {
            background: rgba(15, 20, 25, 0.9);
            border: 1px solid #00ff41;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            height: 300px;
            overflow-y: auto;
        }
        
        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(0, 255, 65, 0.3);
        }
        
        .log-title {
            color: #00ff41;
            font-weight: bold;
        }
        
        .log-controls {
            display: flex;
            gap: 10px;
        }
        
        .log-btn {
            background: rgba(0, 255, 65, 0.1);
            border: 1px solid #00ff41;
            color: #00ff41;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .log-btn:hover {
            background: rgba(0, 255, 65, 0.2);
        }
        
        .log-entry {
            margin-bottom: 8px;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .log-timestamp {
            color: #666;
            margin-right: 10px;
        }
        
        .log-level {
            margin-right: 10px;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        
        .log-info {
            background: rgba(0, 123, 255, 0.2);
            color: #007bff;
        }
        
        .log-success {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
        }
        
        .log-warning {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
        }
        
        .log-error {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }
        
        .log-message {
            color: #cccccc;
        }
        
        .system-topology {
            background: rgba(26, 26, 46, 0.8);
            border: 1px solid #00ff41;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .topology-header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .topology-title {
            color: #00ff41;
            font-size: 18px;
            font-weight: bold;
        }
        
        .topology-diagram {
            display: flex;
            justify-content: space-around;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
            min-height: 200px;
        }
        
        .topology-node {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px;
            background: rgba(0, 255, 65, 0.1);
            border: 2px solid #00ff41;
            border-radius: 10px;
            min-width: 120px;
            position: relative;
        }
        
        .node-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .node-name {
            font-size: 12px;
            color: #00ff41;
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .node-status {
            font-size: 10px;
            color: #cccccc;
        }
        
        .connection-line {
            position: absolute;
            height: 2px;
            background: #00ff41;
            opacity: 0.6;
        }
        
        .alert-panel {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid #dc3545;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .alert-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .alert-icon {
            font-size: 20px;
            color: #dc3545;
        }
        
        .alert-title {
            color: #dc3545;
            font-weight: bold;
        }
        
        .alert-item {
            padding: 10px;
            background: rgba(220, 53, 69, 0.05);
            border-left: 3px solid #dc3545;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        
        .alert-time {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .alert-message {
            color: #cccccc;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="monitor-header">
        <div class="header-content">
            <h1 class="system-title">🚕 出租车管理系统 - 监控中心</h1>
            <div class="system-status">
                <div class="status-item">
                    <div class="status-dot"></div>
                    <span>系统运行正常</span>
                </div>
                <div class="status-item">
                    <span>在线车辆: 156</span>
                </div>
                <div class="status-item">
                    <span>活跃用户: 2,847</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="monitor-grid">
            <div class="monitor-card">
                <div class="card-header">
                    <h3 class="card-title">司机端系统</h3>
                    <span class="card-status status-online">在线</span>
                </div>
                <div class="metric-row">
                    <span class="metric-label">在线司机:</span>
                    <span class="metric-value">156人</span>
                </div>
                <div class="metric-row">
                    <span class="metric-label">活跃订单:</span>
                    <span class="metric-value">89单</span>
                </div>
                <div class="metric-row">
                    <span class="metric-label">系统负载:</span>
                    <span class="metric-value">68%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 68%"></div>
                </div>
            </div>
            
            <div class="monitor-card">
                <div class="card-header">
                    <h3 class="card-title">乘客端系统</h3>
                    <span class="card-status status-online">在线</span>
                </div>
                <div class="metric-row">
                    <span class="metric-label">在线用户:</span>
                    <span class="metric-value">2,847人</span>
                </div>
                <div class="metric-row">
                    <span class="metric-label">今日订单:</span>
                    <span class="metric-value">1,256单</span>
                </div>
                <div class="metric-row">
                    <span class="metric-label">响应时间:</span>
                    <span class="metric-value">1.2s</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 85%"></div>
                </div>
            </div>
            
            <div class="monitor-card">
                <div class="card-header">
                    <h3 class="card-title">派单系统</h3>
                    <span class="card-status status-online">在线</span>
                </div>
                <div class="metric-row">
                    <span class="metric-label">派单成功率:</span>
                    <span class="metric-value">96%</span>
                </div>
                <div class="metric-row">
                    <span class="metric-label">平均响应:</span>
                    <span class="metric-value">2.3分钟</span>
                </div>
                <div class="metric-row">
                    <span class="metric-label">队列长度:</span>
                    <span class="metric-value">3单</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 96%"></div>
                </div>
            </div>
            
            <div class="monitor-card">
                <div class="card-header">
                    <h3 class="card-title">支付系统</h3>
                    <span class="card-status status-warning">警告</span>
                </div>
                <div class="metric-row">
                    <span class="metric-label">交易成功率:</span>
                    <span class="metric-value">98.5%</span>
                </div>
                <div class="metric-row">
                    <span class="metric-label">今日交易:</span>
                    <span class="metric-value">¥45,678</span>
                </div>
                <div class="metric-row">
                    <span class="metric-label">系统延迟:</span>
                    <span class="metric-value">3.2s</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 78%"></div>
                </div>
            </div>
        </div>
        
        <div class="system-topology">
            <div class="topology-header">
                <h3 class="topology-title">系统架构拓扑</h3>
            </div>
            <div class="topology-diagram">
                <div class="topology-node">
                    <div class="node-icon">👨‍💼</div>
                    <div class="node-name">司机端</div>
                    <div class="node-status">156 在线</div>
                </div>
                
                <div class="topology-node">
                    <div class="node-icon">🚖</div>
                    <div class="node-name">乘客端</div>
                    <div class="node-status">2,847 在线</div>
                </div>
                
                <div class="topology-node">
                    <div class="node-icon">📡</div>
                    <div class="node-name">派单中心</div>
                    <div class="node-status">正常运行</div>
                </div>
                
                <div class="topology-node">
                    <div class="node-icon">💰</div>
                    <div class="node-name">支付系统</div>
                    <div class="node-status">轻微延迟</div>
                </div>
                
                <div class="topology-node">
                    <div class="node-icon">📱</div>
                    <div class="node-name">二维码系统</div>
                    <div class="node-status">正常运行</div>
                </div>
                
                <div class="topology-node">
                    <div class="node-icon">🗄️</div>
                    <div class="node-name">数据库</div>
                    <div class="node-status">正常运行</div>
                </div>
            </div>
        </div>
        
        <div class="alert-panel">
            <div class="alert-header">
                <span class="alert-icon">⚠️</span>
                <h3 class="alert-title">系统告警</h3>
            </div>
            <div class="alert-item">
                <div class="alert-time">2024-12-04 15:23:45</div>
                <div class="alert-message">支付系统响应时间超过阈值 (3.2s > 3.0s)</div>
            </div>
            <div class="alert-item">
                <div class="alert-time">2024-12-04 14:56:12</div>
                <div class="alert-message">数据库连接池使用率达到85%</div>
            </div>
        </div>
        
        <div class="real-time-log">
            <div class="log-header">
                <h3 class="log-title">实时系统日志</h3>
                <div class="log-controls">
                    <button class="log-btn" onclick="clearLogs()">清空</button>
                    <button class="log-btn" onclick="pauseLogs()">暂停</button>
                    <button class="log-btn" onclick="exportLogs()">导出</button>
                </div>
            </div>
            <div id="logContainer">
                <div class="log-entry">
                    <span class="log-timestamp">15:24:32</span>
                    <span class="log-level log-info">INFO</span>
                    <span class="log-message">司机 京A12345 接受订单 #20241204001</span>
                </div>
                <div class="log-entry">
                    <span class="log-timestamp">15:24:28</span>
                    <span class="log-level log-success">SUCCESS</span>
                    <span class="log-message">订单 #20241204002 支付成功，金额: ¥32.50</span>
                </div>
                <div class="log-entry">
                    <span class="log-timestamp">15:24:15</span>
                    <span class="log-level log-warning">WARN</span>
                    <span class="log-message">支付系统响应时间: 3.2s，超过预警阈值</span>
                </div>
                <div class="log-entry">
                    <span class="log-timestamp">15:24:02</span>
                    <span class="log-level log-info">INFO</span>
                    <span class="log-message">新用户注册: 手机号 138****5678</span>
                </div>
                <div class="log-entry">
                    <span class="log-timestamp">15:23:45</span>
                    <span class="log-level log-error">ERROR</span>
                    <span class="log-message">支付接口调用失败，订单 #20241204003</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        let logsPaused = false;
        
        // 模拟实时日志更新
        function addLogEntry(level, message) {
            if (logsPaused) return;
            
            const container = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-timestamp">${timestamp}</span>
                <span class="log-level log-${level}">${level.toUpperCase()}</span>
                <span class="log-message">${message}</span>
            `;
            
            container.insertBefore(logEntry, container.firstChild);
            
            // 限制日志条数
            const entries = container.querySelectorAll('.log-entry');
            if (entries.length > 50) {
                container.removeChild(entries[entries.length - 1]);
            }
        }
        
        // 模拟系统事件
        function simulateSystemEvents() {
            const events = [
                { level: 'info', message: '司机 京B67890 上线' },
                { level: 'success', message: '订单派发成功，响应时间: 1.8s' },
                { level: 'info', message: '乘客发起叫车请求' },
                { level: 'warning', message: '数据库连接池使用率: 87%' },
                { level: 'success', message: '支付完成，订单金额: ¥45.80' },
                { level: 'info', message: '二维码扫描成功，位置: 王府井大街' },
                { level: 'error', message: '网络连接超时，重试中...' }
            ];
            
            const randomEvent = events[Math.floor(Math.random() * events.length)];
            addLogEntry(randomEvent.level, randomEvent.message);
        }
        
        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
        }
        
        function pauseLogs() {
            logsPaused = !logsPaused;
            const btn = event.target;
            btn.textContent = logsPaused ? '继续' : '暂停';
        }
        
        function exportLogs() {
            alert('日志导出功能');
        }
        
        // 更新系统指标
        function updateMetrics() {
            // 模拟数据更新
            const metrics = document.querySelectorAll('.metric-value');
            metrics.forEach(metric => {
                // 随机更新一些数值
                if (Math.random() < 0.1) {
                    const currentValue = metric.textContent;
                    if (currentValue.includes('人') || currentValue.includes('单')) {
                        const num = parseInt(currentValue);
                        if (!isNaN(num)) {
                            const change = Math.floor(Math.random() * 10) - 5;
                            metric.textContent = currentValue.replace(num, Math.max(0, num + change));
                        }
                    }
                }
            });
        }
        
        // 页面加载时启动监控
        window.addEventListener('load', () => {
            // 每3秒添加一条日志
            setInterval(simulateSystemEvents, 3000);
            
            // 每10秒更新一次指标
            setInterval(updateMetrics, 10000);
            
            console.log('系统监控中心已启动');
        });
    </script>
    
    <!-- 引入通用导航组件 -->
    <script src="common-navigation.js"></script>
</body>
</html>
