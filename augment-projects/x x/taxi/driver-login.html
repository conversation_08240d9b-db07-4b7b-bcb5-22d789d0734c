<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>司机登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .login-container {
            max-width: 400px;
            margin: 60px auto 0;
            background: white;
            border-radius: 20px;
            padding: 40px 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .logo-section {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: white;
        }
        
        .app-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        
        .app-desc {
            color: #666;
            font-size: 14px;
        }
        
        .language-switch {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .lang-btn {
            background: none;
            border: 1px solid #ddd;
            padding: 8px 20px;
            margin: 0 5px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .lang-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .login-tabs {
            display: flex;
            margin-bottom: 30px;
            background: #f5f5f5;
            border-radius: 10px;
            padding: 4px;
        }
        
        .tab-btn {
            flex: 1;
            background: none;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }
        
        .tab-btn.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .sms-input-group {
            display: flex;
            gap: 10px;
        }
        
        .sms-input {
            flex: 1;
        }
        
        .sms-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            cursor: pointer;
            white-space: nowrap;
            transition: background 0.3s ease;
        }
        
        .sms-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 18px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s ease;
            margin-top: 20px;
        }
        
        .login-btn:active {
            transform: scale(0.98);
        }
        
        .forgot-password {
            text-align: center;
            margin-top: 20px;
        }
        
        .forgot-password a {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
        }
        
        .hidden {
            display: none;
        }
        
        .error-message {
            color: #ff4757;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .success-message {
            color: #2ed573;
            font-size: 14px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo-section">
            <div class="logo">🚕</div>
            <h1 class="app-name">司机端登录</h1>
            <p class="app-desc">安全便捷的出行服务</p>
        </div>
        
        <div class="language-switch">
            <button class="lang-btn active" onclick="switchLanguage('zh')">中文</button>
            <button class="lang-btn" onclick="switchLanguage('en')">English</button>
        </div>
        
        <div class="login-tabs">
            <button class="tab-btn active" onclick="switchTab('password')">密码登录</button>
            <button class="tab-btn" onclick="switchTab('sms')">短信登录</button>
        </div>
        
        <form id="loginForm">
            <!-- 密码登录表单 -->
            <div id="passwordLogin">
                <div class="form-group">
                    <label class="form-label">手机号</label>
                    <input type="tel" class="form-input" id="phonePassword" placeholder="请输入手机号" maxlength="11">
                    <div class="error-message" id="phonePasswordError"></div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">密码</label>
                    <input type="password" class="form-input" id="password" placeholder="请输入密码">
                    <div class="error-message" id="passwordError"></div>
                </div>
            </div>
            
            <!-- 短信登录表单 -->
            <div id="smsLogin" class="hidden">
                <div class="form-group">
                    <label class="form-label">手机号</label>
                    <input type="tel" class="form-input" id="phoneSms" placeholder="请输入手机号" maxlength="11">
                    <div class="error-message" id="phoneSmsError"></div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">验证码</label>
                    <div class="sms-input-group">
                        <input type="text" class="form-input sms-input" id="smsCode" placeholder="请输入验证码" maxlength="6">
                        <button type="button" class="sms-btn" id="sendSmsBtn" onclick="sendSms()">发送验证码</button>
                    </div>
                    <div class="error-message" id="smsCodeError"></div>
                </div>
            </div>
            
            <button type="submit" class="login-btn">登录</button>
        </form>
        
        <div class="forgot-password">
            <a href="#" onclick="forgotPassword()">忘记密码？</a>
        </div>
    </div>

    <script>
        let currentTab = 'password';
        let smsCountdown = 0;
        
        function switchLanguage(lang) {
            document.querySelectorAll('.lang-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // 这里可以添加多语言切换逻辑
            if (lang === 'en') {
                // 切换到英文
                console.log('Switch to English');
            } else {
                // 切换到中文
                console.log('Switch to Chinese');
            }
        }
        
        function switchTab(tab) {
            currentTab = tab;
            
            // 更新标签样式
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // 切换表单显示
            if (tab === 'password') {
                document.getElementById('passwordLogin').classList.remove('hidden');
                document.getElementById('smsLogin').classList.add('hidden');
            } else {
                document.getElementById('passwordLogin').classList.add('hidden');
                document.getElementById('smsLogin').classList.remove('hidden');
            }
            
            // 清除错误信息
            clearErrors();
        }
        
        function sendSms() {
            const phone = document.getElementById('phoneSms').value;
            const phoneError = document.getElementById('phoneSmsError');
            
            if (!validatePhone(phone)) {
                phoneError.textContent = '请输入正确的手机号';
                return;
            }
            
            phoneError.textContent = '';
            
            // 开始倒计时
            smsCountdown = 60;
            const sendBtn = document.getElementById('sendSmsBtn');
            sendBtn.disabled = true;
            
            const timer = setInterval(() => {
                sendBtn.textContent = `${smsCountdown}秒后重发`;
                smsCountdown--;
                
                if (smsCountdown < 0) {
                    clearInterval(timer);
                    sendBtn.disabled = false;
                    sendBtn.textContent = '发送验证码';
                }
            }, 1000);
            
            // 模拟发送短信
            console.log('发送验证码到:', phone);
            alert('验证码已发送，请注意查收');
        }
        
        function validatePhone(phone) {
            return /^1[3-9]\d{9}$/.test(phone);
        }
        
        function clearErrors() {
            document.querySelectorAll('.error-message').forEach(el => el.textContent = '');
        }
        
        function forgotPassword() {
            alert('请联系管理员重置密码');
        }
        
        // 表单提交
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            clearErrors();
            let isValid = true;
            
            if (currentTab === 'password') {
                const phone = document.getElementById('phonePassword').value;
                const password = document.getElementById('password').value;
                
                if (!validatePhone(phone)) {
                    document.getElementById('phonePasswordError').textContent = '请输入正确的手机号';
                    isValid = false;
                }
                
                if (password.length < 6) {
                    document.getElementById('passwordError').textContent = '密码至少6位';
                    isValid = false;
                }
                
                if (isValid) {
                    // 模拟登录
                    console.log('密码登录:', { phone, password });
                    login();
                }
            } else {
                const phone = document.getElementById('phoneSms').value;
                const smsCode = document.getElementById('smsCode').value;
                
                if (!validatePhone(phone)) {
                    document.getElementById('phoneSmsError').textContent = '请输入正确的手机号';
                    isValid = false;
                }
                
                if (smsCode.length !== 6) {
                    document.getElementById('smsCodeError').textContent = '请输入6位验证码';
                    isValid = false;
                }
                
                if (isValid) {
                    // 模拟登录
                    console.log('短信登录:', { phone, smsCode });
                    login();
                }
            }
        });
        
        function login() {
            // 显示加载状态
            const loginBtn = document.querySelector('.login-btn');
            loginBtn.textContent = '登录中...';
            loginBtn.disabled = true;
            
            // 模拟登录请求
            setTimeout(() => {
                // 登录成功，跳转到工作台
                window.location.href = 'driver-dashboard.html';
            }, 1500);
        }
    </script>
</body>
</html>
