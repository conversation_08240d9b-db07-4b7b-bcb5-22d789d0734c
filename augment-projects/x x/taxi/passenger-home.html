<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>预约用车</title>
    <link rel="stylesheet" href="common-styles.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding-bottom: 80px;
        }
        
        .header {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-size: 18px;
        }
        
        .greeting {
            font-size: 16px;
        }
        
        .weather-info {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .map-container {
            height: 300px;
            background: #e8e8e8;
            position: relative;
            margin: 20px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .map-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        
        .location-panel {
            background: white;
            margin: 0 20px 20px;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .location-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
        }
        
        .location-item:last-child {
            border-bottom: none;
        }
        
        .location-icon {
            width: 20px;
            text-align: center;
            margin-right: 15px;
            font-size: 16px;
        }
        
        .location-content {
            flex: 1;
        }
        
        .location-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .location-address {
            font-size: 16px;
            color: #333;
        }
        
        .location-placeholder {
            color: #999;
        }
        
        .swap-btn {
            background: #74b9ff;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin-left: 10px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .time-panel {
            background: white;
            margin: 0 20px 20px;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .time-options {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .time-btn {
            flex: 1;
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 12px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .time-btn.active {
            background: #74b9ff;
            color: white;
            border-color: #74b9ff;
        }
        
        .datetime-picker {
            display: none;
            margin-top: 15px;
        }
        
        .datetime-picker.show {
            display: block;
        }
        
        .datetime-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
        }
        
        .service-options {
            background: white;
            margin: 0 20px 20px;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .service-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .service-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .service-card {
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .service-card.selected {
            border-color: #74b9ff;
            background: #f0f8ff;
        }
        
        .service-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .service-name {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .service-desc {
            font-size: 12px;
            color: #666;
        }
        
        .book-btn {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            border: none;
            padding: 18px;
            margin: 0 20px;
            border-radius: 15px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s ease;
            width: calc(100% - 40px);
        }
        
        .book-btn:active {
            transform: scale(0.98);
        }
        
        .book-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            display: flex;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }
        
        .nav-item {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            background: none;
        }
        
        .nav-item.active {
            color: #74b9ff;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 5px;
        }
        
        .nav-label {
            font-size: 12px;
        }
        
        .quick-locations {
            margin-top: 15px;
        }
        
        .quick-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .quick-list {
            display: flex;
            gap: 10px;
            overflow-x: auto;
            padding-bottom: 5px;
        }
        
        .quick-item {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 8px 15px;
            border-radius: 20px;
            white-space: nowrap;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .quick-item:hover {
            background: #74b9ff;
            color: white;
            border-color: #74b9ff;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="user-info">
                <div class="avatar">👤</div>
                <div>
                    <div class="greeting">您好，李先生</div>
                    <div class="weather-info">北京 晴 22°C</div>
                </div>
            </div>
            <button onclick="showNotifications()">🔔</button>
        </div>
    </div>

    <div class="map-container" onclick="openMap()">
        <div class="map-placeholder">
            🗺️ 点击查看地图
        </div>
    </div>

    <div class="location-panel">
        <div class="location-item" onclick="selectStartLocation()">
            <span class="location-icon">🟢</span>
            <div class="location-content">
                <div class="location-label">出发地</div>
                <div class="location-address" id="startLocation">
                    <span class="location-placeholder">请选择出发地点</span>
                </div>
            </div>
        </div>

        <div class="location-item" onclick="selectEndLocation()">
            <span class="location-icon">🔴</span>
            <div class="location-content">
                <div class="location-label">目的地</div>
                <div class="location-address" id="endLocation">
                    <span class="location-placeholder">请选择目的地</span>
                </div>
            </div>
            <button class="swap-btn" onclick="swapLocations()">⇅</button>
        </div>

        <div class="quick-locations">
            <div class="quick-title">常用地点</div>
            <div class="quick-list">
                <div class="quick-item" onclick="selectQuickLocation('home')">🏠 家</div>
                <div class="quick-item" onclick="selectQuickLocation('company')">🏢 公司</div>
                <div class="quick-item" onclick="selectQuickLocation('airport')">✈️ 机场</div>
                <div class="quick-item" onclick="selectQuickLocation('station')">🚄 火车站</div>
            </div>
        </div>
    </div>

    <div class="time-panel">
        <div class="time-options">
            <button class="time-btn active" onclick="selectTimeOption('now')">立即用车</button>
            <button class="time-btn" onclick="selectTimeOption('schedule')">预约用车</button>
        </div>

        <div class="datetime-picker" id="datetimePicker">
            <input type="datetime-local" class="datetime-input" id="scheduleTime">
        </div>
    </div>

    <div class="service-options">
        <div class="service-title">选择车型</div>
        <div class="service-grid">
            <div class="service-card selected" onclick="selectService('economy')">
                <div class="service-icon">🚗</div>
                <div class="service-name">经济型</div>
                <div class="service-desc">舒适出行</div>
            </div>

            <div class="service-card" onclick="selectService('comfort')">
                <div class="service-icon">🚙</div>
                <div class="service-name">舒适型</div>
                <div class="service-desc">宽敞空间</div>
            </div>

            <div class="service-card" onclick="selectService('business')">
                <div class="service-icon">🚐</div>
                <div class="service-name">商务型</div>
                <div class="service-desc">商务出行</div>
            </div>

            <div class="service-card" onclick="selectService('luxury')">
                <div class="service-icon">🏎️</div>
                <div class="service-name">豪华型</div>
                <div class="service-desc">尊享服务</div>
            </div>
        </div>
    </div>

    <button class="book-btn" onclick="bookRide()" id="bookButton">
        立即叫车
    </button>

    <div class="bottom-nav">
        <button class="nav-item active">
            <div class="nav-icon">🏠</div>
            <div class="nav-label">首页</div>
        </button>
        <button class="nav-item" onclick="goToOrders()">
            <div class="nav-icon">📋</div>
            <div class="nav-label">订单</div>
        </button>
        <button class="nav-item" onclick="goToProfile()">
            <div class="nav-icon">👤</div>
            <div class="nav-label">我的</div>
        </button>
    </div>

    <script>
        let currentTimeOption = 'now';
        let selectedService = 'economy';
        let startLocation = null;
        let endLocation = null;

        // 地图搜索缓存
        const locationCache = new Map();

        // 常用地点配置
        const quickLocations = {
            home: { name: '家', address: '北京市朝阳区三里屯SOHO' },
            company: { name: '公司', address: '北京市海淀区中关村软件园' },
            airport: { name: '首都机场', address: '北京首都国际机场T3航站楼' },
            station: { name: '北京站', address: '北京站东广场' }
        };

        function showNotifications() {
            alert('暂无新消息');
        }

        function openMap() {
            alert('打开地图选择位置');
            // 这里可以集成高德地图或百度地图
        }

        function selectStartLocation() {
            // 模拟地点选择
            const location = prompt('请输入出发地点：');
            if (location) {
                startLocation = { name: location, address: location };
                document.getElementById('startLocation').innerHTML = location;
                document.getElementById('startLocation').classList.remove('location-placeholder');

                // 缓存搜索结果
                cacheLocation(location);
                updateBookButton();
            }
        }

        function selectEndLocation() {
            // 模拟地点选择
            const location = prompt('请输入目的地：');
            if (location) {
                endLocation = { name: location, address: location };
                document.getElementById('endLocation').innerHTML = location;
                document.getElementById('endLocation').classList.remove('location-placeholder');

                // 缓存搜索结果
                cacheLocation(location);
                updateBookButton();
            }
        }

        function swapLocations() {
            if (startLocation && endLocation) {
                const temp = startLocation;
                startLocation = endLocation;
                endLocation = temp;

                document.getElementById('startLocation').innerHTML = startLocation.address;
                document.getElementById('endLocation').innerHTML = endLocation.address;
            }
        }

        function selectQuickLocation(type) {
            const location = quickLocations[type];
            if (!startLocation) {
                startLocation = location;
                document.getElementById('startLocation').innerHTML = location.address;
                document.getElementById('startLocation').classList.remove('location-placeholder');
            } else if (!endLocation) {
                endLocation = location;
                document.getElementById('endLocation').innerHTML = location.address;
                document.getElementById('endLocation').classList.remove('location-placeholder');
            } else {
                // 如果都已选择，替换目的地
                endLocation = location;
                document.getElementById('endLocation').innerHTML = location.address;
            }
            updateBookButton();
        }

        function selectTimeOption(option) {
            currentTimeOption = option;

            // 更新按钮样式
            document.querySelectorAll('.time-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // 显示/隐藏时间选择器
            const picker = document.getElementById('datetimePicker');
            const bookBtn = document.getElementById('bookButton');

            if (option === 'schedule') {
                picker.classList.add('show');
                bookBtn.textContent = '预约用车';

                // 设置默认时间为1小时后
                const now = new Date();
                now.setHours(now.getHours() + 1);
                document.getElementById('scheduleTime').value = now.toISOString().slice(0, 16);
            } else {
                picker.classList.remove('show');
                bookBtn.textContent = '立即叫车';
            }
        }

        function selectService(service) {
            selectedService = service;

            // 更新选中状态
            document.querySelectorAll('.service-card').forEach(card => card.classList.remove('selected'));
            event.target.classList.add('selected');
        }

        function cacheLocation(location) {
            // 模拟地图搜索缓存机制
            if (!locationCache.has(location)) {
                locationCache.set(location, {
                    name: location,
                    coordinates: {
                        lat: 39.9042 + (Math.random() - 0.5) * 0.1,
                        lng: 116.4074 + (Math.random() - 0.5) * 0.1
                    },
                    timestamp: Date.now()
                });
                console.log('缓存地点:', location);
            }
        }

        function updateBookButton() {
            const bookBtn = document.getElementById('bookButton');
            if (startLocation && endLocation) {
                bookBtn.disabled = false;
                bookBtn.style.opacity = '1';
            } else {
                bookBtn.disabled = true;
                bookBtn.style.opacity = '0.6';
            }
        }

        function bookRide() {
            if (!startLocation || !endLocation) {
                alert('请选择出发地和目的地');
                return;
            }

            const orderData = {
                startLocation: startLocation,
                endLocation: endLocation,
                serviceType: selectedService,
                timeOption: currentTimeOption,
                scheduleTime: currentTimeOption === 'schedule' ? document.getElementById('scheduleTime').value : null,
                timestamp: new Date().toISOString()
            };

            console.log('创建订单:', orderData);

            // 模拟订单创建
            alert('正在为您寻找附近的司机...');

            // 跳转到订单页面
            setTimeout(() => {
                window.location.href = 'passenger-order-detail.html?orderId=' + Date.now();
            }, 2000);
        }

        function goToOrders() {
            // 检查是否有登录状态
            const userToken = localStorage.getItem('passengerToken');
            if (!userToken) {
                if (confirm('请先登录后查看订单，是否前往登录？')) {
                    window.location.href = 'passenger-login.html';
                }
                return;
            }
            window.location.href = 'passenger-orders.html';
        }

        function goToProfile() {
            // 检查是否有登录状态
            const userToken = localStorage.getItem('passengerToken');
            if (!userToken) {
                if (confirm('请先登录后查看个人中心，是否前往登录？')) {
                    window.location.href = 'passenger-login.html';
                }
                return;
            }
            window.location.href = 'passenger-profile.html';
        }

        // 页面加载时初始化
        window.addEventListener('load', () => {
            updateBookButton();

            // 模拟获取当前位置
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        console.log('当前位置:', position.coords);
                        // 可以根据当前位置设置默认出发地
                    },
                    (error) => {
                        console.log('获取位置失败:', error);
                    }
                );
            }
        });
    </script>

    <!-- 引入通用导航组件 -->
    <script src="common-navigation.js"></script>
</body>
</html>
