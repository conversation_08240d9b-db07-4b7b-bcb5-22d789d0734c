<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码热力图分析系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        
        .logo-icon {
            margin-right: 10px;
            font-size: 28px;
        }
        
        .header-controls {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .time-selector {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255,255,255,0.1);
            padding: 8px 15px;
            border-radius: 20px;
        }
        
        .time-select {
            background: none;
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 14px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(--accent-color, #27ae60);
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .stat-title {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }
        
        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            background: var(--accent-color, #27ae60);
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .stat-change {
            font-size: 12px;
            color: #27ae60;
        }
        
        .main-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 25px;
            margin-bottom: 25px;
        }
        
        .heatmap-panel {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .panel-header {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .panel-title {
            font-size: 20px;
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        
        .title-icon {
            margin-right: 10px;
            font-size: 24px;
        }
        
        .panel-controls {
            display: flex;
            gap: 10px;
        }
        
        .control-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            border-radius: 5px;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .control-btn:hover, .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        
        .heatmap-container {
            height: 500px;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            overflow: hidden;
        }
        
        .heatmap-overlay {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.95);
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
            color: #333;
            min-width: 200px;
        }
        
        .overlay-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .overlay-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .heatmap-legend {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.95);
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
        }
        
        .legend-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 3px;
            margin-right: 8px;
        }
        
        .color-high { background: #e74c3c; }
        .color-medium { background: #f39c12; }
        .color-low { background: #27ae60; }
        .color-none { background: #95a5a6; }
        
        .heatmap-filters {
            position: absolute;
            bottom: 20px;
            left: 20px;
            display: flex;
            gap: 10px;
        }
        
        .filter-btn {
            background: rgba(255,255,255,0.9);
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 8px 15px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .filter-btn:hover, .filter-btn.active {
            background: #27ae60;
            color: white;
            border-color: #27ae60;
        }
        
        .side-panel {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .qr-analytics {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .analytics-header {
            background: #3498db;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
        }
        
        .analytics-content {
            padding: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .qr-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            transition: all 0.3s ease;
        }
        
        .qr-item:hover {
            background: #f8f9fa;
        }
        
        .qr-item:last-child {
            border-bottom: none;
        }
        
        .qr-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .qr-id {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .qr-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-hot {
            background: #ffebee;
            color: #c62828;
        }
        
        .status-warm {
            background: #fff3e0;
            color: #ef6c00;
        }
        
        .status-cool {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .qr-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            font-size: 12px;
            color: #666;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
        }
        
        .top-locations {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .locations-header {
            background: #9b59b6;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
        }
        
        .locations-content {
            padding: 20px;
        }
        
        .location-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }
        
        .location-item:last-child {
            border-bottom: none;
        }
        
        .location-info {
            flex: 1;
        }
        
        .location-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 3px;
        }
        
        .location-address {
            font-size: 12px;
            color: #666;
        }
        
        .location-stats {
            text-align: right;
        }
        
        .scan-count {
            font-size: 18px;
            font-weight: bold;
            color: #9b59b6;
        }
        
        .scan-rate {
            font-size: 12px;
            color: #666;
        }
        
        .chart-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 25px;
            margin-bottom: 25px;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .chart-placeholder {
            height: 200px;
            background: #f8f9fa;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <span class="logo-icon">📱</span>
                二维码热力图分析系统
            </div>
            <div class="header-controls">
                <div class="time-selector">
                    <span>时间范围:</span>
                    <select class="time-select" onchange="updateTimeRange(this.value)">
                        <option value="today">今日</option>
                        <option value="week">本周</option>
                        <option value="month" selected>本月</option>
                        <option value="quarter">本季度</option>
                    </select>
                </div>
            </div>
            <div class="user-info">
                <div class="user-avatar">👤</div>
                <span>数据分析师 - 李经理</span>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="stats-overview">
            <div class="stat-card" style="--accent-color: #e74c3c;">
                <div class="stat-header">
                    <span class="stat-title">总扫码次数</span>
                    <div class="stat-icon">📊</div>
                </div>
                <div class="stat-value">15,847</div>
                <div class="stat-change">+23% 较上月</div>
            </div>
            
            <div class="stat-card" style="--accent-color: #f39c12;">
                <div class="stat-header">
                    <span class="stat-title">活跃二维码</span>
                    <div class="stat-icon">📱</div>
                </div>
                <div class="stat-value">1,256</div>
                <div class="stat-change">+8% 较上月</div>
            </div>
            
            <div class="stat-card" style="--accent-color: #27ae60;">
                <div class="stat-header">
                    <span class="stat-title">热点区域</span>
                    <div class="stat-icon">🔥</div>
                </div>
                <div class="stat-value">89</div>
                <div class="stat-change">+12% 较上月</div>
            </div>
            
            <div class="stat-card" style="--accent-color: #3498db;">
                <div class="stat-header">
                    <span class="stat-title">平均扫码率</span>
                    <div class="stat-icon">📈</div>
                </div>
                <div class="stat-value">68%</div>
                <div class="stat-change">+5% 较上月</div>
            </div>
        </div>
        
        <div class="main-grid">
            <div class="heatmap-panel">
                <div class="panel-header">
                    <h3 class="panel-title">
                        <span class="title-icon">🗺️</span>
                        扫码热力图分析
                    </h3>
                    <div class="panel-controls">
                        <button class="control-btn active" onclick="toggleHeatmapMode('scan')">扫码热度</button>
                        <button class="control-btn" onclick="toggleHeatmapMode('time')">时间分布</button>
                        <button class="control-btn" onclick="toggleHeatmapMode('device')">设备类型</button>
                    </div>
                </div>
                
                <div class="heatmap-container">
                    🗺️ 二维码扫码热力图
                    <br>
                    <small>集成地图API显示扫码热点分布</small>
                    
                    <div class="heatmap-overlay">
                        <div class="overlay-title">实时统计</div>
                        <div class="overlay-item">
                            <span>今日扫码:</span>
                            <span>2,847次</span>
                        </div>
                        <div class="overlay-item">
                            <span>峰值时段:</span>
                            <span>14:00-16:00</span>
                        </div>
                        <div class="overlay-item">
                            <span>热点区域:</span>
                            <span>商业中心</span>
                        </div>
                        <div class="overlay-item">
                            <span>转化率:</span>
                            <span>72.5%</span>
                        </div>
                    </div>
                    
                    <div class="heatmap-legend">
                        <div class="legend-title">扫码密度</div>
                        <div class="legend-item">
                            <div class="legend-color color-high"></div>
                            <span>高密度 (>100次/小时)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color color-medium"></div>
                            <span>中密度 (50-100次/小时)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color color-low"></div>
                            <span>低密度 (10-50次/小时)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color color-none"></div>
                            <span>无活动 (<10次/小时)</span>
                        </div>
                    </div>
                    
                    <div class="heatmap-filters">
                        <button class="filter-btn active" onclick="filterByTime('all')">全天</button>
                        <button class="filter-btn" onclick="filterByTime('morning')">上午</button>
                        <button class="filter-btn" onclick="filterByTime('afternoon')">下午</button>
                        <button class="filter-btn" onclick="filterByTime('evening')">晚上</button>
                    </div>
                </div>
            </div>
            
            <div class="side-panel">
                <div class="qr-analytics">
                    <div class="analytics-header">
                        📊 热门二维码排行
                    </div>
                    <div class="analytics-content">
                        <div class="qr-item">
                            <div class="qr-header">
                                <span class="qr-id">QR-001-BJ-001</span>
                                <span class="qr-status status-hot">热门</span>
                            </div>
                            <div class="qr-stats">
                                <div class="stat-item">
                                    <span>今日扫码:</span>
                                    <span>456次</span>
                                </div>
                                <div class="stat-item">
                                    <span>转化率:</span>
                                    <span>85%</span>
                                </div>
                                <div class="stat-item">
                                    <span>位置:</span>
                                    <span>王府井大街</span>
                                </div>
                                <div class="stat-item">
                                    <span>状态:</span>
                                    <span>活跃</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="qr-item">
                            <div class="qr-header">
                                <span class="qr-id">QR-002-BJ-015</span>
                                <span class="qr-status status-warm">活跃</span>
                            </div>
                            <div class="qr-stats">
                                <div class="stat-item">
                                    <span>今日扫码:</span>
                                    <span>298次</span>
                                </div>
                                <div class="stat-item">
                                    <span>转化率:</span>
                                    <span>72%</span>
                                </div>
                                <div class="stat-item">
                                    <span>位置:</span>
                                    <span>三里屯太古里</span>
                                </div>
                                <div class="stat-item">
                                    <span>状态:</span>
                                    <span>正常</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="qr-item">
                            <div class="qr-header">
                                <span class="qr-id">QR-003-BJ-028</span>
                                <span class="qr-status status-cool">一般</span>
                            </div>
                            <div class="qr-stats">
                                <div class="stat-item">
                                    <span>今日扫码:</span>
                                    <span>156次</span>
                                </div>
                                <div class="stat-item">
                                    <span>转化率:</span>
                                    <span>58%</span>
                                </div>
                                <div class="stat-item">
                                    <span>位置:</span>
                                    <span>国贸CBD</span>
                                </div>
                                <div class="stat-item">
                                    <span>状态:</span>
                                    <span>正常</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="top-locations">
                    <div class="locations-header">
                        🏆 热点区域排行
                    </div>
                    <div class="locations-content">
                        <div class="location-item">
                            <div class="location-info">
                                <div class="location-name">王府井商业街</div>
                                <div class="location-address">东城区王府井大街</div>
                            </div>
                            <div class="location-stats">
                                <div class="scan-count">1,247</div>
                                <div class="scan-rate">+15%</div>
                            </div>
                        </div>
                        
                        <div class="location-item">
                            <div class="location-info">
                                <div class="location-name">三里屯太古里</div>
                                <div class="location-address">朝阳区三里屯路</div>
                            </div>
                            <div class="location-stats">
                                <div class="scan-count">986</div>
                                <div class="scan-rate">+8%</div>
                            </div>
                        </div>
                        
                        <div class="location-item">
                            <div class="location-info">
                                <div class="location-name">国贸CBD</div>
                                <div class="location-address">朝阳区建国门外大街</div>
                            </div>
                            <div class="location-stats">
                                <div class="scan-count">756</div>
                                <div class="scan-rate">+12%</div>
                            </div>
                        </div>
                        
                        <div class="location-item">
                            <div class="location-info">
                                <div class="location-name">中关村科技园</div>
                                <div class="location-address">海淀区中关村大街</div>
                            </div>
                            <div class="location-stats">
                                <div class="scan-count">623</div>
                                <div class="scan-rate">+5%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="chart-container">
            <div class="chart-header">
                <h3 class="chart-title">📈 扫码趋势分析</h3>
                <div class="panel-controls">
                    <button class="control-btn active" onclick="switchChart('hourly')">小时</button>
                    <button class="control-btn" onclick="switchChart('daily')">日</button>
                    <button class="control-btn" onclick="switchChart('weekly')">周</button>
                </div>
            </div>
            <div class="chart-placeholder">
                📊 扫码趋势图表
                <br>
                <small>集成ECharts/Chart.js显示扫码数据趋势</small>
            </div>
        </div>
    </div>

    <script>
        function updateTimeRange(range) {
            console.log('更新时间范围:', range);
            // 重新加载数据
            refreshHeatmapData(range);
        }
        
        function toggleHeatmapMode(mode) {
            // 更新按钮状态
            document.querySelectorAll('.panel-controls .control-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            console.log('切换热力图模式:', mode);
            // 这里可以切换不同的热力图显示模式
        }
        
        function filterByTime(timeFilter) {
            // 更新按钮状态
            document.querySelectorAll('.heatmap-filters .filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            console.log('时间筛选:', timeFilter);
            // 这里可以根据时间筛选热力图数据
        }
        
        function switchChart(chartType) {
            // 更新按钮状态
            document.querySelectorAll('.chart-header .control-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            console.log('切换图表类型:', chartType);
            // 这里可以切换不同的图表显示
        }
        
        function refreshHeatmapData(timeRange) {
            console.log('刷新热力图数据:', timeRange);
            // 模拟API调用更新数据
        }
        
        // 模拟实时数据更新
        setInterval(() => {
            updateRealTimeStats();
        }, 30000);
        
        function updateRealTimeStats() {
            console.log('更新实时统计数据');
            // 这里可以通过WebSocket或API更新实时数据
        }
        
        // 页面加载时初始化
        window.addEventListener('load', () => {
            console.log('二维码热力图分析系统已加载');
            refreshHeatmapData('month');
        });
    </script>
</body>
</html>
