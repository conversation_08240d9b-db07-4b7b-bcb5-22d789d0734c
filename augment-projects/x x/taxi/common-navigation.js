// 出租车管理系统 - 通用导航组件

// 系统配置
const SYSTEM_CONFIG = {
    name: '出租车管理系统',
    version: '1.0.0',
    modules: {
        driver: {
            name: '司机端',
            icon: '👨‍💼',
            color: '#667eea',
            pages: {
                welcome: { name: '欢迎页', url: 'driver-welcome.html' },
                login: { name: '登录', url: 'driver-login.html' },
                dashboard: { name: '工作台', url: 'driver-dashboard.html' },
                workstation: { name: '智能工作台', url: 'driver-workstation.html' },
                profile: { name: '个人中心', url: 'driver-profile.html' }
            }
        },
        passenger: {
            name: '乘客端',
            icon: '🚖',
            color: '#74b9ff',
            pages: {
                login: { name: '登录', url: 'passenger-login.html' },
                home: { name: '叫车', url: 'passenger-home.html' },
                orders: { name: '订单', url: 'passenger-orders.html' },
                profile: { name: '个人中心', url: 'passenger-profile.html' }
            }
        },
        admin: {
            name: '管理后台',
            icon: '⚙️',
            color: '#2c3e50',
            pages: {
                dashboard: { name: '总览', url: 'admin-dashboard.html' },
                fleet: { name: '车队管理', url: 'admin-fleet-management.html' }
            }
        },
        dispatch: {
            name: '派单系统',
            icon: '📡',
            color: '#34495e',
            pages: {
                dashboard: { name: '派单管理', url: 'dispatch-dashboard.html' },
                intelligent: { name: '智能派单', url: 'dispatch-intelligent-system.html' }
            }
        },
        qrcode: {
            name: '二维码系统',
            icon: '📱',
            color: '#27ae60',
            pages: {
                dashboard: { name: '二维码管理', url: 'qrcode-dashboard.html' },
                heatmap: { name: '热力图分析', url: 'qrcode-heatmap-analysis.html' }
            }
        },
        payment: {
            name: '支付系统',
            icon: '💰',
            color: '#8e44ad',
            pages: {
                dashboard: { name: '支付管理', url: 'payment-system.html' }
            }
        },
        operation: {
            name: '运维系统',
            icon: '🔧',
            color: '#07c160',
            pages: {
                app: { name: '运维APP', url: 'operation-app.html' }
            }
        }
    }
};

// 通用导航功能
class NavigationManager {
    constructor() {
        this.currentModule = this.detectCurrentModule();
        this.currentPage = this.detectCurrentPage();
    }

    // 检测当前模块
    detectCurrentModule() {
        const path = window.location.pathname;
        const filename = path.split('/').pop();
        
        for (const [moduleKey, module] of Object.entries(SYSTEM_CONFIG.modules)) {
            for (const page of Object.values(module.pages)) {
                if (filename === page.url) {
                    return moduleKey;
                }
            }
        }
        return 'home';
    }

    // 检测当前页面
    detectCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop();
        return filename || 'index.html';
    }

    // 生成面包屑导航
    generateBreadcrumb() {
        if (this.currentModule === 'home') {
            return '<span class="breadcrumb-item active">系统首页</span>';
        }

        const module = SYSTEM_CONFIG.modules[this.currentModule];
        if (!module) return '';

        let breadcrumb = `<a href="index.html" class="breadcrumb-item">首页</a>`;
        breadcrumb += `<span class="breadcrumb-separator">></span>`;
        breadcrumb += `<span class="breadcrumb-item">${module.name}</span>`;

        // 查找当前页面
        for (const [pageKey, page] of Object.entries(module.pages)) {
            if (this.currentPage === page.url) {
                breadcrumb += `<span class="breadcrumb-separator">></span>`;
                breadcrumb += `<span class="breadcrumb-item active">${page.name}</span>`;
                break;
            }
        }

        return breadcrumb;
    }

    // 生成模块内导航
    generateModuleNavigation() {
        if (this.currentModule === 'home') return '';

        const module = SYSTEM_CONFIG.modules[this.currentModule];
        if (!module) return '';

        let nav = `<div class="module-nav" style="--module-color: ${module.color}">`;
        nav += `<div class="module-nav-header">`;
        nav += `<span class="module-icon">${module.icon}</span>`;
        nav += `<span class="module-name">${module.name}</span>`;
        nav += `</div>`;
        nav += `<div class="module-nav-items">`;

        for (const [pageKey, page] of Object.entries(module.pages)) {
            const isActive = this.currentPage === page.url ? 'active' : '';
            nav += `<a href="${page.url}" class="module-nav-item ${isActive}">${page.name}</a>`;
        }

        nav += `</div></div>`;
        return nav;
    }

    // 生成快速切换菜单
    generateQuickSwitcher() {
        let switcher = `<div class="quick-switcher">`;
        switcher += `<button class="quick-switcher-btn" onclick="toggleQuickSwitcher()">`;
        switcher += `<span class="current-module-icon">${SYSTEM_CONFIG.modules[this.currentModule]?.icon || '🏠'}</span>`;
        switcher += `<span class="switcher-arrow">▼</span>`;
        switcher += `</button>`;
        switcher += `<div class="quick-switcher-menu" id="quickSwitcherMenu">`;
        
        // 添加首页
        switcher += `<a href="index.html" class="switcher-item ${this.currentModule === 'home' ? 'active' : ''}">`;
        switcher += `<span class="switcher-icon">🏠</span>`;
        switcher += `<span class="switcher-name">系统首页</span>`;
        switcher += `</a>`;

        // 添加各模块
        for (const [moduleKey, module] of Object.entries(SYSTEM_CONFIG.modules)) {
            const isActive = this.currentModule === moduleKey ? 'active' : '';
            const mainPage = Object.values(module.pages)[0]; // 取第一个页面作为主页面
            
            switcher += `<a href="${mainPage.url}" class="switcher-item ${isActive}">`;
            switcher += `<span class="switcher-icon">${module.icon}</span>`;
            switcher += `<span class="switcher-name">${module.name}</span>`;
            switcher += `</a>`;
        }

        switcher += `</div></div>`;
        return switcher;
    }

    // 初始化导航
    init() {
        this.injectNavigationStyles();
        this.injectNavigationHTML();
        this.bindEvents();
    }

    // 注入导航样式
    injectNavigationStyles() {
        const styles = `
        <style>
        .system-navigation {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            padding: 10px 20px;
        }

        .nav-container {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            text-decoration: none;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .breadcrumb-item {
            color: #666;
            text-decoration: none;
        }

        .breadcrumb-item:hover {
            color: #667eea;
        }

        .breadcrumb-item.active {
            color: #2c3e50;
            font-weight: 500;
        }

        .breadcrumb-separator {
            color: #ccc;
        }

        .quick-switcher {
            position: relative;
        }

        .quick-switcher-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            background: #f8f9fa;
            border: 1px solid #e0e6ed;
            border-radius: 8px;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quick-switcher-btn:hover {
            background: #e9ecef;
        }

        .current-module-icon {
            font-size: 16px;
        }

        .switcher-arrow {
            font-size: 12px;
            color: #666;
            transition: transform 0.3s ease;
        }

        .quick-switcher.open .switcher-arrow {
            transform: rotate(180deg);
        }

        .quick-switcher-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e0e6ed;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1001;
        }

        .quick-switcher.open .quick-switcher-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .switcher-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            color: #333;
            text-decoration: none;
            transition: all 0.3s ease;
            border-bottom: 1px solid #f0f0f0;
        }

        .switcher-item:last-child {
            border-bottom: none;
        }

        .switcher-item:hover {
            background: #f8f9fa;
        }

        .switcher-item.active {
            background: #e3f2fd;
            color: #1976d2;
        }

        .switcher-icon {
            font-size: 16px;
        }

        .switcher-name {
            font-size: 14px;
        }

        .module-nav {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px;
            overflow: hidden;
        }

        .module-nav-header {
            background: var(--module-color, #667eea);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: bold;
        }

        .module-icon {
            font-size: 18px;
        }

        .module-nav-items {
            display: flex;
            flex-wrap: wrap;
            padding: 15px 20px;
            gap: 10px;
        }

        .module-nav-item {
            padding: 8px 16px;
            background: #f8f9fa;
            border: 1px solid #e0e6ed;
            border-radius: 20px;
            color: #666;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .module-nav-item:hover {
            background: var(--module-color, #667eea);
            color: white;
            border-color: var(--module-color, #667eea);
        }

        .module-nav-item.active {
            background: var(--module-color, #667eea);
            color: white;
            border-color: var(--module-color, #667eea);
        }

        /* 为有导航的页面添加顶部间距 */
        body.has-navigation {
            padding-top: 70px;
        }

        body.has-module-nav {
            padding-top: 140px;
        }

        @media (max-width: 768px) {
            .nav-container {
                flex-direction: column;
                gap: 10px;
                align-items: flex-start;
            }

            .breadcrumb {
                font-size: 12px;
            }

            .module-nav {
                margin: 10px;
            }

            .module-nav-items {
                padding: 10px 15px;
            }

            body.has-navigation {
                padding-top: 90px;
            }

            body.has-module-nav {
                padding-top: 160px;
            }
        }
        </style>
        `;
        document.head.insertAdjacentHTML('beforeend', styles);
    }

    // 注入导航HTML
    injectNavigationHTML() {
        const navigationHTML = `
        <div class="system-navigation">
            <div class="nav-container">
                <div class="nav-left">
                    <a href="index.html" class="nav-logo">
                        <span>🚕</span>
                        <span>出租车管理系统</span>
                    </a>
                    <div class="breadcrumb">
                        ${this.generateBreadcrumb()}
                    </div>
                </div>
                <div class="nav-right">
                    ${this.generateQuickSwitcher()}
                </div>
            </div>
        </div>
        `;

        document.body.insertAdjacentHTML('afterbegin', navigationHTML);
        document.body.classList.add('has-navigation');

        // 如果不是首页，添加模块导航
        if (this.currentModule !== 'home') {
            const moduleNavHTML = this.generateModuleNavigation();
            if (moduleNavHTML) {
                document.querySelector('.system-navigation').insertAdjacentHTML('afterend', moduleNavHTML);
                document.body.classList.add('has-module-nav');
            }
        }
    }

    // 绑定事件
    bindEvents() {
        // 点击其他地方关闭快速切换菜单
        document.addEventListener('click', (e) => {
            const switcher = document.querySelector('.quick-switcher');
            if (switcher && !switcher.contains(e.target)) {
                switcher.classList.remove('open');
            }
        });
    }
}

// 切换快速切换菜单
function toggleQuickSwitcher() {
    const switcher = document.querySelector('.quick-switcher');
    if (switcher) {
        switcher.classList.toggle('open');
    }
}

// 页面跳转函数
function navigateTo(url, newTab = false) {
    if (newTab) {
        window.open(url, '_blank');
    } else {
        window.location.href = url;
    }
}

// 返回上一页
function goBack() {
    if (window.history.length > 1) {
        window.history.back();
    } else {
        window.location.href = 'index.html';
    }
}

// 刷新当前页面
function refreshPage() {
    window.location.reload();
}

// 显示加载状态
function showLoading(element) {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    if (element) {
        element.classList.add('loading');
    }
}

// 隐藏加载状态
function hideLoading(element) {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    if (element) {
        element.classList.remove('loading');
    }
}

// 显示提示消息
function showMessage(message, type = 'info', duration = 3000) {
    const messageEl = document.createElement('div');
    messageEl.className = `message message-${type}`;
    messageEl.textContent = message;
    
    // 添加样式
    messageEl.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#2ecc71' : type === 'error' ? '#e74c3c' : type === 'warning' ? '#f39c12' : '#3498db'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        z-index: 10000;
        animation: slideInRight 0.3s ease-out;
        max-width: 300px;
        word-wrap: break-word;
    `;
    
    document.body.appendChild(messageEl);
    
    // 自动移除
    setTimeout(() => {
        messageEl.style.animation = 'slideOutRight 0.3s ease-out';
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 300);
    }, duration);
}

// 确认对话框
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// 初始化导航系统
document.addEventListener('DOMContentLoaded', () => {
    const nav = new NavigationManager();
    nav.init();
    
    // 添加动画样式
    const animationStyles = `
    <style>
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    </style>
    `;
    document.head.insertAdjacentHTML('beforeend', animationStyles);
});

// 导出配置供其他脚本使用
window.TAXI_SYSTEM = {
    config: SYSTEM_CONFIG,
    navigation: NavigationManager,
    utils: {
        navigateTo,
        goBack,
        refreshPage,
        showLoading,
        hideLoading,
        showMessage,
        confirmAction
    }
};
