<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统使用指南</title>
    <link rel="stylesheet" href="common-styles.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .guide-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .guide-header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .guide-title {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .guide-subtitle {
            font-size: 18px;
            opacity: 0.9;
        }
        
        .guide-nav {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
        }
        
        .nav-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .nav-btn:hover, .nav-btn.active {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .guide-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
        }
        
        .guide-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .guide-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }
        
        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .section-icon {
            font-size: 32px;
            margin-right: 15px;
        }
        
        .section-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .section-description {
            color: #666;
            margin-bottom: 25px;
            line-height: 1.6;
        }
        
        .feature-list {
            list-style: none;
            margin-bottom: 25px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .feature-item:hover {
            background: #e9ecef;
        }
        
        .feature-icon {
            margin-right: 12px;
            font-size: 16px;
        }
        
        .feature-text {
            flex: 1;
            color: #333;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
        }
        
        .action-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #e9ecef;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-secondary:hover {
            background: #e9ecef;
        }
        
        .workflow-diagram {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .workflow-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .workflow-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .workflow-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            min-width: 120px;
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .step-text {
            text-align: center;
            font-size: 14px;
            color: #666;
        }
        
        .step-arrow {
            font-size: 20px;
            color: #ccc;
            margin: 0 10px;
        }
        
        .quick-start {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            text-align: center;
        }
        
        .quick-start-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .quick-start-text {
            margin-bottom: 25px;
            opacity: 0.9;
        }
        
        .quick-start-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .quick-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            font-weight: 500;
        }
        
        .quick-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .guide-content {
                grid-template-columns: 1fr;
            }
            
            .workflow-steps {
                flex-direction: column;
            }
            
            .step-arrow {
                transform: rotate(90deg);
                margin: 10px 0;
            }
            
            .quick-start-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="guide-container">
        <div class="guide-header">
            <h1 class="guide-title">🚕 出租车管理系统</h1>
            <p class="guide-subtitle">完整的使用指南和快速入门教程</p>
        </div>
        
        <div class="guide-nav">
            <a href="#overview" class="nav-btn active">系统概览</a>
            <a href="#driver" class="nav-btn">司机端</a>
            <a href="#passenger" class="nav-btn">乘客端</a>
            <a href="#admin" class="nav-btn">管理后台</a>
            <a href="#dispatch" class="nav-btn">派单系统</a>
            <a href="index.html" class="nav-btn">返回首页</a>
        </div>
        
        <div class="guide-content">
            <div class="guide-section">
                <div class="section-header">
                    <span class="section-icon">👨‍💼</span>
                    <h2 class="section-title">司机端应用</h2>
                </div>
                <p class="section-description">
                    为出租车司机提供的移动端应用，支持智能接单、实时导航、收入管理等核心功能。
                </p>
                
                <div class="workflow-diagram">
                    <div class="workflow-title">司机工作流程</div>
                    <div class="workflow-steps">
                        <div class="workflow-step">
                            <div class="step-number">1</div>
                            <div class="step-text">登录上线</div>
                        </div>
                        <div class="step-arrow">→</div>
                        <div class="workflow-step">
                            <div class="step-number">2</div>
                            <div class="step-text">接收订单</div>
                        </div>
                        <div class="step-arrow">→</div>
                        <div class="workflow-step">
                            <div class="step-number">3</div>
                            <div class="step-text">前往接客</div>
                        </div>
                        <div class="step-arrow">→</div>
                        <div class="workflow-step">
                            <div class="step-number">4</div>
                            <div class="step-text">完成订单</div>
                        </div>
                    </div>
                </div>
                
                <ul class="feature-list">
                    <li class="feature-item">
                        <span class="feature-icon">📱</span>
                        <span class="feature-text">智能订单推送与倒计时接单</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">💰</span>
                        <span class="feature-text">实时收入统计与钱包管理</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">🗺️</span>
                        <span class="feature-text">GPS定位与导航集成</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">📞</span>
                        <span class="feature-text">乘客联系与消息通知</span>
                    </li>
                </ul>
                
                <div class="action-buttons">
                    <a href="driver-welcome.html" class="action-btn btn-primary">体验司机端</a>
                    <a href="driver-workstation.html" class="action-btn btn-secondary">工作台演示</a>
                </div>
            </div>
            
            <div class="guide-section">
                <div class="section-header">
                    <span class="section-icon">🚖</span>
                    <h2 class="section-title">乘客端应用</h2>
                </div>
                <p class="section-description">
                    响应式H5乘客应用，提供便捷的叫车服务、订单跟踪、个人中心等功能。
                </p>
                
                <div class="workflow-diagram">
                    <div class="workflow-title">乘客叫车流程</div>
                    <div class="workflow-steps">
                        <div class="workflow-step">
                            <div class="step-number">1</div>
                            <div class="step-text">选择地点</div>
                        </div>
                        <div class="step-arrow">→</div>
                        <div class="workflow-step">
                            <div class="step-number">2</div>
                            <div class="step-text">发起叫车</div>
                        </div>
                        <div class="step-arrow">→</div>
                        <div class="workflow-step">
                            <div class="step-number">3</div>
                            <div class="step-text">等待接单</div>
                        </div>
                        <div class="step-arrow">→</div>
                        <div class="workflow-step">
                            <div class="step-number">4</div>
                            <div class="step-text">完成行程</div>
                        </div>
                    </div>
                </div>
                
                <ul class="feature-list">
                    <li class="feature-item">
                        <span class="feature-icon">📍</span>
                        <span class="feature-text">智能地址选择与路线规划</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">⏰</span>
                        <span class="feature-text">即时叫车与预约用车</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">📋</span>
                        <span class="feature-text">订单状态实时跟踪</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">🎫</span>
                        <span class="feature-text">优惠券与积分管理</span>
                    </li>
                </ul>
                
                <div class="action-buttons">
                    <a href="passenger-login.html" class="action-btn btn-primary">体验乘客端</a>
                    <a href="passenger-home.html" class="action-btn btn-secondary">叫车演示</a>
                </div>
            </div>
            
            <div class="guide-section">
                <div class="section-header">
                    <span class="section-icon">⚙️</span>
                    <h2 class="section-title">管理后台</h2>
                </div>
                <p class="section-description">
                    全面的车队管理系统，提供车辆监控、订单统计、财务分析等管理功能。
                </p>
                
                <ul class="feature-list">
                    <li class="feature-item">
                        <span class="feature-icon">🚗</span>
                        <span class="feature-text">实时车队监控与车辆管理</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">📊</span>
                        <span class="feature-text">订单数据统计与分析</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">👥</span>
                        <span class="feature-text">司机档案与绩效管理</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">🔧</span>
                        <span class="feature-text">系统配置与权限控制</span>
                    </li>
                </ul>
                
                <div class="action-buttons">
                    <a href="admin-dashboard.html" class="action-btn btn-primary">管理后台</a>
                    <a href="admin-fleet-management.html" class="action-btn btn-secondary">车队管理</a>
                </div>
            </div>
            
            <div class="guide-section">
                <div class="section-header">
                    <span class="section-icon">📡</span>
                    <h2 class="section-title">智能派单</h2>
                </div>
                <p class="section-description">
                    AI驱动的智能派单系统，支持自动派单、网格化管理、紧急订单处理。
                </p>
                
                <ul class="feature-list">
                    <li class="feature-item">
                        <span class="feature-icon">🤖</span>
                        <span class="feature-text">AI智能派单算法</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">🗺️</span>
                        <span class="feature-text">实时地图网格调度</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">🚨</span>
                        <span class="feature-text">紧急订单优先处理</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">📈</span>
                        <span class="feature-text">派单效率统计分析</span>
                    </li>
                </ul>
                
                <div class="action-buttons">
                    <a href="dispatch-intelligent-system.html" class="action-btn btn-primary">智能派单</a>
                    <a href="dispatch-dashboard.html" class="action-btn btn-secondary">派单管理</a>
                </div>
            </div>
            
            <div class="guide-section">
                <div class="section-header">
                    <span class="section-icon">📱</span>
                    <h2 class="section-title">二维码分析</h2>
                </div>
                <p class="section-description">
                    二维码生成管理和热力图分析系统，支持扫码数据可视化统计。
                </p>
                
                <ul class="feature-list">
                    <li class="feature-item">
                        <span class="feature-icon">🏷️</span>
                        <span class="feature-text">二维码批量生成管理</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">🔥</span>
                        <span class="feature-text">扫码热力图可视化</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">📍</span>
                        <span class="feature-text">热点区域统计分析</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">📈</span>
                        <span class="feature-text">扫码趋势数据报表</span>
                    </li>
                </ul>
                
                <div class="action-buttons">
                    <a href="qrcode-heatmap-analysis.html" class="action-btn btn-primary">热力图分析</a>
                    <a href="qrcode-dashboard.html" class="action-btn btn-secondary">二维码管理</a>
                </div>
            </div>
            
            <div class="guide-section">
                <div class="section-header">
                    <span class="section-icon">📊</span>
                    <h2 class="section-title">系统监控</h2>
                </div>
                <p class="section-description">
                    实时系统监控中心，提供系统状态、性能指标、告警信息等监控功能。
                </p>
                
                <ul class="feature-list">
                    <li class="feature-item">
                        <span class="feature-icon">💻</span>
                        <span class="feature-text">实时系统状态监控</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">⚡</span>
                        <span class="feature-text">性能指标统计分析</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">🚨</span>
                        <span class="feature-text">系统告警与日志管理</span>
                    </li>
                    <li class="feature-item">
                        <span class="feature-icon">🔗</span>
                        <span class="feature-text">系统架构拓扑图</span>
                    </li>
                </ul>
                
                <div class="action-buttons">
                    <a href="system-monitor.html" class="action-btn btn-primary">系统监控</a>
                    <a href="index.html" class="action-btn btn-secondary">返回首页</a>
                </div>
            </div>
        </div>
        
        <div class="quick-start">
            <h2 class="quick-start-title">🚀 快速开始</h2>
            <p class="quick-start-text">
                选择您的角色，立即体验出租车管理系统的强大功能
            </p>
            <div class="quick-start-buttons">
                <a href="driver-welcome.html" class="quick-btn">👨‍💼 我是司机</a>
                <a href="passenger-login.html" class="quick-btn">🚖 我是乘客</a>
                <a href="admin-dashboard.html" class="quick-btn">⚙️ 我是管理员</a>
                <a href="dispatch-intelligent-system.html" class="quick-btn">📡 我是调度员</a>
            </div>
        </div>
    </div>

    <script>
        // 平滑滚动到指定部分
        document.querySelectorAll('.nav-btn[href^="#"]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = btn.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({ behavior: 'smooth' });
                }
                
                // 更新活动状态
                document.querySelectorAll('.nav-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });
        
        // 页面滚动时更新导航状态
        window.addEventListener('scroll', () => {
            const sections = document.querySelectorAll('.guide-section[id]');
            const scrollPos = window.scrollY + 100;
            
            sections.forEach(section => {
                const top = section.offsetTop;
                const bottom = top + section.offsetHeight;
                const id = section.getAttribute('id');
                
                if (scrollPos >= top && scrollPos <= bottom) {
                    document.querySelectorAll('.nav-btn').forEach(btn => btn.classList.remove('active'));
                    document.querySelector(`.nav-btn[href="#${id}"]`)?.classList.add('active');
                }
            });
        });
        
        console.log('系统使用指南已加载');
    </script>
    
    <!-- 引入通用导航组件 -->
    <script src="common-navigation.js"></script>
</body>
</html>
