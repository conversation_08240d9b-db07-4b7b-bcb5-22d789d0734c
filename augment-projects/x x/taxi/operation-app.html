<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>运营运维APP</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f7f7f7;
            color: #333;
            padding-bottom: 80px;
        }
        
        .header {
            background: linear-gradient(135deg, #07c160 0%, #00d4aa 100%);
            color: white;
            padding: 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-size: 18px;
        }
        
        .greeting {
            font-size: 16px;
        }
        
        .menu-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            padding: 20px;
        }
        
        .menu-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .menu-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .menu-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            margin: 0 auto 15px;
            color: white;
        }
        
        .icon-recharge {
            background: linear-gradient(135deg, #07c160 0%, #00d4aa 100%);
        }
        
        .icon-qr {
            background: linear-gradient(135deg, #576574 0%, #2f3542 100%);
        }
        
        .icon-driver {
            background: linear-gradient(135deg, #3742fa 0%, #2f3542 100%);
        }
        
        .icon-report {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }
        
        .menu-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        
        .menu-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }
        
        .quick-stats {
            background: white;
            margin: 0 20px 20px;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        
        .stats-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        
        .stats-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .stat-value {
            font-weight: bold;
            color: #07c160;
        }
        
        .recent-activity {
            background: white;
            margin: 0 20px 20px;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
            color: white;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 3px;
        }
        
        .activity-time {
            font-size: 12px;
            color: #999;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            display: flex;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }
        
        .nav-item {
            flex: 1;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            background: none;
        }
        
        .nav-item.active {
            color: #07c160;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 11px;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }
        
        .modal.show {
            display: flex;
        }
        
        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin: 20px;
            max-width: 350px;
            width: 100%;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }
        
        .form-input,
        .form-select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .form-input:focus,
        .form-select:focus {
            outline: none;
            border-color: #07c160;
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #07c160;
            color: white;
        }
        
        .btn-primary:hover {
            background: #06ad56;
        }
        
        .btn-secondary {
            background: #f0f0f0;
            color: #333;
            margin-top: 10px;
        }
        
        .scanner-container {
            width: 100%;
            height: 200px;
            background: #f0f0f0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 15px 0;
            color: #666;
            font-size: 14px;
            border: 2px dashed #ddd;
        }
        
        .scanner-active {
            border-color: #07c160;
            background: #f0fff4;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="user-info">
                <div class="avatar">🔧</div>
                <div>
                    <div class="greeting">运维人员</div>
                    <div style="font-size: 12px; opacity: 0.9;">工号：OP001</div>
                </div>
            </div>
            <button onclick="showNotifications()" style="background: none; border: none; color: white; font-size: 18px;">🔔</button>
        </div>
    </div>
    
    <div class="quick-stats">
        <div class="stats-title">今日数据</div>
        <div class="stats-row">
            <span class="stat-label">处理充值</span>
            <span class="stat-value">23笔</span>
        </div>
        <div class="stats-row">
            <span class="stat-label">维护二维码</span>
            <span class="stat-value">8个</span>
        </div>
        <div class="stats-row">
            <span class="stat-label">服务司机</span>
            <span class="stat-value">15人</span>
        </div>
    </div>
    
    <div class="menu-grid">
        <div class="menu-card" onclick="showDriverRecharge()">
            <div class="menu-icon icon-recharge">💰</div>
            <div class="menu-title">司机圈存</div>
            <div class="menu-desc">为司机提供现场充值服务</div>
        </div>
        
        <div class="menu-card" onclick="showAgentRecharge()">
            <div class="menu-icon icon-driver">👥</div>
            <div class="menu-title">代理充值</div>
            <div class="menu-desc">代理商账户充值管理</div>
        </div>
        
        <div class="menu-card" onclick="showQRMaintenance()">
            <div class="menu-icon icon-qr">📱</div>
            <div class="menu-title">二维码运维</div>
            <div class="menu-desc">扫码获取位置信息</div>
        </div>
        
        <div class="menu-card" onclick="showReports()">
            <div class="menu-icon icon-report">📊</div>
            <div class="menu-title">工作报表</div>
            <div class="menu-desc">查看工作统计报表</div>
        </div>
    </div>
    
    <div class="recent-activity">
        <div class="stats-title">最近操作</div>
        <div class="activity-item">
            <div class="activity-icon" style="background: #07c160;">💰</div>
            <div class="activity-content">
                <div class="activity-title">为张师傅充值 ¥200</div>
                <div class="activity-time">5分钟前</div>
            </div>
        </div>
        
        <div class="activity-item">
            <div class="activity-icon" style="background: #576574;">📱</div>
            <div class="activity-content">
                <div class="activity-title">维护王府井二维码</div>
                <div class="activity-time">15分钟前</div>
            </div>
        </div>
        
        <div class="activity-item">
            <div class="activity-icon" style="background: #3742fa;">👥</div>
            <div class="activity-content">
                <div class="activity-title">代理商A充值 ¥5000</div>
                <div class="activity-time">1小时前</div>
            </div>
        </div>
    </div>
    
    <div class="bottom-nav">
        <button class="nav-item active">
            <div class="nav-icon">🏠</div>
            <div class="nav-label">首页</div>
        </button>
        <button class="nav-item" onclick="showWorkOrders()">
            <div class="nav-icon">📋</div>
            <div class="nav-label">工单</div>
        </button>
        <button class="nav-item" onclick="showProfile()">
            <div class="nav-icon">👤</div>
            <div class="nav-label">我的</div>
        </button>
    </div>
    
    <!-- 司机圈存弹窗 -->
    <div class="modal" id="driverRechargeModal">
        <div class="modal-content">
            <div class="modal-title">司机圈存</div>
            <form onsubmit="processDriverRecharge(event)">
                <div class="form-group">
                    <label class="form-label">司机姓名</label>
                    <input type="text" class="form-input" placeholder="请输入司机姓名" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">车牌号</label>
                    <input type="text" class="form-input" placeholder="请输入车牌号" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Token编码</label>
                    <input type="text" class="form-input" placeholder="请输入Token编码" required>
                </div>
                
                <button type="submit" class="btn btn-primary">确认圈存</button>
                <button type="button" class="btn btn-secondary" onclick="closeModal('driverRechargeModal')">取消</button>
            </form>
        </div>
    </div>
    
    <!-- 二维码运维弹窗 -->
    <div class="modal" id="qrMaintenanceModal">
        <div class="modal-content">
            <div class="modal-title">二维码运维</div>
            <div class="scanner-container" id="qrScanner" onclick="startQRScan()">
                📱 点击开始扫描二维码
            </div>
            <div class="form-group">
                <label class="form-label">经纬度信息</label>
                <input type="text" class="form-input" id="locationInfo" placeholder="扫码后自动获取" readonly>
            </div>
            <div class="form-group">
                <label class="form-label">网格信息</label>
                <input type="text" class="form-input" id="gridInfo" placeholder="扫码后自动获取" readonly>
            </div>
            <div class="form-group">
                <label class="form-label">操作类型</label>
                <select class="form-select">
                    <option value="enable">启用</option>
                    <option value="disable">禁用</option>
                    <option value="relocate">重新定位</option>
                </select>
            </div>
            <button type="button" class="btn btn-primary" onclick="processQRMaintenance()">执行操作</button>
            <button type="button" class="btn btn-secondary" onclick="closeModal('qrMaintenanceModal')">取消</button>
        </div>
    </div>

    <script>
        function showNotifications() {
            alert('暂无新通知');
        }
        
        function showDriverRecharge() {
            document.getElementById('driverRechargeModal').classList.add('show');
        }
        
        function showAgentRecharge() {
            const amount = prompt('请输入代理商充值金额：');
            if (amount && !isNaN(amount) && parseFloat(amount) > 0) {
                alert(`代理商充值 ¥${amount} 处理中...`);
                console.log('代理商充值:', amount);
            }
        }
        
        function showQRMaintenance() {
            document.getElementById('qrMaintenanceModal').classList.add('show');
        }
        
        function showReports() {
            alert('跳转到工作报表页面');
        }
        
        function showWorkOrders() {
            alert('跳转到工单管理页面');
        }
        
        function showProfile() {
            alert('跳转到个人中心页面');
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }
        
        function processDriverRecharge(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const rechargeData = {
                driverName: event.target.querySelector('input[placeholder="请输入司机姓名"]').value,
                plateNumber: event.target.querySelector('input[placeholder="请输入车牌号"]').value,
                tokenCode: event.target.querySelector('input[placeholder="请输入Token编码"]').value,
                timestamp: new Date().toISOString()
            };
            
            console.log('司机圈存:', rechargeData);
            alert(`司机圈存成功！\n司机：${rechargeData.driverName}\n车牌：${rechargeData.plateNumber}`);
            
            closeModal('driverRechargeModal');
            event.target.reset();
        }
        
        function startQRScan() {
            const scanner = document.getElementById('qrScanner');
            scanner.classList.add('scanner-active');
            scanner.innerHTML = '📱 正在扫描中...';
            
            // 模拟扫码过程
            setTimeout(() => {
                const mockLocation = {
                    lat: 39.9042 + (Math.random() - 0.5) * 0.01,
                    lng: 116.4074 + (Math.random() - 0.5) * 0.01
                };
                
                const gridCode = `G${Math.floor(mockLocation.lng * 100)}_${Math.floor(mockLocation.lat * 100)}`;
                
                document.getElementById('locationInfo').value = `${mockLocation.lat.toFixed(6)}, ${mockLocation.lng.toFixed(6)}`;
                document.getElementById('gridInfo').value = gridCode;
                
                scanner.innerHTML = '✅ 扫码成功';
                
                setTimeout(() => {
                    scanner.classList.remove('scanner-active');
                    scanner.innerHTML = '📱 点击开始扫描二维码';
                }, 2000);
            }, 2000);
        }
        
        function processQRMaintenance() {
            const locationInfo = document.getElementById('locationInfo').value;
            const gridInfo = document.getElementById('gridInfo').value;
            const operation = document.querySelector('#qrMaintenanceModal select').value;
            
            if (!locationInfo) {
                alert('请先扫描二维码');
                return;
            }
            
            console.log('二维码运维:', { locationInfo, gridInfo, operation });
            alert(`二维码运维操作完成！\n位置：${locationInfo}\n网格：${gridInfo}\n操作：${operation}`);
            
            closeModal('qrMaintenanceModal');
            
            // 清空表单
            document.getElementById('locationInfo').value = '';
            document.getElementById('gridInfo').value = '';
        }
        
        // 页面加载时初始化
        window.addEventListener('load', () => {
            console.log('运营运维APP初始化完成');
            
            // 模拟获取当前位置
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        console.log('当前位置:', position.coords);
                    },
                    (error) => {
                        console.log('获取位置失败:', error);
                    }
                );
            }
        });
        
        // 点击模态框背景关闭
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.classList.remove('show');
                }
            });
        });
    </script>
</body>
</html>
