<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>司机工作台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding-bottom: 80px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .driver-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .driver-profile {
            display: flex;
            align-items: center;
        }
        
        .avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-right: 15px;
        }
        
        .driver-details h3 {
            font-size: 18px;
            margin-bottom: 5px;
        }
        
        .driver-details p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .status-toggle {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .status-toggle.online {
            background: #2ed573;
            border-color: #2ed573;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            padding: 20px;
            margin-top: -10px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        
        .order-tabs {
            display: flex;
            background: white;
            margin: 0 20px;
            border-radius: 15px;
            padding: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .tab-btn {
            flex: 1;
            background: none;
            border: none;
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            position: relative;
        }
        
        .tab-btn.active {
            background: #667eea;
            color: white;
        }
        
        .tab-badge {
            position: absolute;
            top: 5px;
            right: 10px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .order-list {
            padding: 20px;
        }
        
        .order-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .order-id {
            font-weight: bold;
            color: #333;
        }
        
        .order-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-pending {
            background: #ffeaa7;
            color: #d63031;
        }
        
        .status-ongoing {
            background: #74b9ff;
            color: white;
        }
        
        .route-info {
            margin-bottom: 15px;
        }
        
        .location {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .location-icon {
            width: 20px;
            text-align: center;
            margin-right: 10px;
        }
        
        .location-text {
            flex: 1;
            font-size: 14px;
        }
        
        .order-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }
        
        .order-actions {
            display: flex;
            gap: 10px;
        }
        
        .action-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-accept {
            background: #2ed573;
            color: white;
        }
        
        .btn-reject {
            background: #ff4757;
            color: white;
        }
        
        .btn-navigate {
            background: #667eea;
            color: white;
        }
        
        .btn-complete {
            background: #ffa502;
            color: white;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            display: flex;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }
        
        .nav-item {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            background: none;
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 5px;
        }
        
        .nav-label {
            font-size: 12px;
        }
        
        .heartbeat-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 10px;
            height: 10px;
            background: #2ed573;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(46, 213, 115, 0.7);
            }
            70% {
                transform: scale(1);
                box-shadow: 0 0 0 10px rgba(46, 213, 115, 0);
            }
            100% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(46, 213, 115, 0);
            }
        }
        
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="heartbeat-indicator" title="在线状态"></div>
    
    <div class="header">
        <div class="driver-info">
            <div class="driver-profile">
                <div class="avatar">👨‍💼</div>
                <div class="driver-details">
                    <h3>张师傅</h3>
                    <p>车牌号：京A12345 | 在线时长：2小时30分</p>
                </div>
            </div>
            <button class="status-toggle online" onclick="toggleStatus()">
                <span id="statusText">在线</span>
            </button>
        </div>
    </div>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">8</div>
            <div class="stat-label">今日订单</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">¥286</div>
            <div class="stat-label">今日收入</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">4.9</div>
            <div class="stat-label">服务评分</div>
        </div>
    </div>
    
    <div class="order-tabs">
        <button class="tab-btn active" onclick="switchOrderTab('pending')">
            待接单
            <span class="tab-badge">3</span>
        </button>
        <button class="tab-btn" onclick="switchOrderTab('ongoing')">
            进行中
            <span class="tab-badge">1</span>
        </button>
        <button class="tab-btn" onclick="switchOrderTab('history')">历史订单</button>
    </div>
    
    <div class="order-list" id="orderList">
        <!-- 待接单订单 -->
        <div class="order-card" data-tab="pending">
            <div class="order-header">
                <span class="order-id">订单 #20241204001</span>
                <span class="order-status status-pending">待接单</span>
            </div>
            
            <div class="route-info">
                <div class="location">
                    <span class="location-icon">🟢</span>
                    <span class="location-text">北京站东广场</span>
                </div>
                <div class="location">
                    <span class="location-icon">🔴</span>
                    <span class="location-text">首都国际机场T3航站楼</span>
                </div>
            </div>
            
            <div class="order-details">
                <span>预估里程：28.5公里</span>
                <span>预估收入：¥85</span>
                <span>距离：1.2公里</span>
            </div>
            
            <div class="order-actions">
                <button class="action-btn btn-reject" onclick="rejectOrder('20241204001')">拒绝</button>
                <button class="action-btn btn-accept" onclick="acceptOrder('20241204001')">接单</button>
            </div>
        </div>
        
        <!-- 进行中订单 -->
        <div class="order-card" data-tab="ongoing" style="display: none;">
            <div class="order-header">
                <span class="order-id">订单 #20241204002</span>
                <span class="order-status status-ongoing">进行中</span>
            </div>
            
            <div class="route-info">
                <div class="location">
                    <span class="location-icon">🟢</span>
                    <span class="location-text">王府井大街</span>
                </div>
                <div class="location">
                    <span class="location-icon">🔴</span>
                    <span class="location-text">三里屯太古里</span>
                </div>
            </div>
            
            <div class="order-details">
                <span>乘客：李先生</span>
                <span>手机：138****5678</span>
                <span>已行驶：5.2公里</span>
            </div>
            
            <div class="order-actions">
                <button class="action-btn btn-navigate" onclick="navigate()">导航</button>
                <button class="action-btn btn-complete" onclick="completeOrder('20241204002')">完成订单</button>
            </div>
        </div>
    </div>
    
    <div class="bottom-nav">
        <button class="nav-item active">
            <div class="nav-icon">🏠</div>
            <div class="nav-label">工作台</div>
        </button>
        <button class="nav-item" onclick="goToProfile()">
            <div class="nav-icon">👤</div>
            <div class="nav-label">个人中心</div>
        </button>
        <button class="nav-item" onclick="goToEarnings()">
            <div class="nav-icon">💰</div>
            <div class="nav-label">收入</div>
        </button>
        <button class="nav-item" onclick="goToSettings()">
            <div class="nav-icon">⚙️</div>
            <div class="nav-label">设置</div>
        </button>
    </div>

    <script>
        let isOnline = true;
        let currentTab = 'pending';
        let heartbeatInterval;
        
        // 心跳报文上传
        function startHeartbeat() {
            heartbeatInterval = setInterval(() => {
                if (isOnline) {
                    // 模拟上传心跳数据
                    const heartbeatData = {
                        driverId: 'D001',
                        timestamp: new Date().toISOString(),
                        location: {
                            lat: 39.9042,
                            lng: 116.4074
                        },
                        status: 'online'
                    };
                    console.log('心跳上传:', heartbeatData);
                }
            }, 30000); // 30秒间隔
        }
        
        function toggleStatus() {
            isOnline = !isOnline;
            const statusBtn = document.querySelector('.status-toggle');
            const statusText = document.getElementById('statusText');
            
            if (isOnline) {
                statusBtn.classList.add('online');
                statusText.textContent = '在线';
                startHeartbeat();
            } else {
                statusBtn.classList.remove('online');
                statusText.textContent = '离线';
                clearInterval(heartbeatInterval);
            }
        }
        
        function switchOrderTab(tab) {
            currentTab = tab;
            
            // 更新标签样式
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // 显示对应订单
            const orderCards = document.querySelectorAll('.order-card');
            orderCards.forEach(card => {
                if (card.dataset.tab === tab) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
            
            // 如果没有订单，显示空状态
            const visibleCards = document.querySelectorAll(`.order-card[data-tab="${tab}"]`);
            if (visibleCards.length === 0) {
                showEmptyState(tab);
            }
        }
        
        function showEmptyState(tab) {
            const orderList = document.getElementById('orderList');
            const emptyMessages = {
                pending: '暂无待接订单',
                ongoing: '暂无进行中订单',
                history: '暂无历史订单'
            };
            
            orderList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📋</div>
                    <p>${emptyMessages[tab]}</p>
                </div>
            `;
        }
        
        function acceptOrder(orderId) {
            if (confirm('确认接受此订单？')) {
                console.log('接受订单:', orderId);
                alert('订单接受成功！');
                // 这里可以添加订单状态更新逻辑
            }
        }
        
        function rejectOrder(orderId) {
            if (confirm('确认拒绝此订单？')) {
                console.log('拒绝订单:', orderId);
                alert('订单已拒绝');
            }
        }
        
        function completeOrder(orderId) {
            if (confirm('确认完成此订单？')) {
                console.log('完成订单:', orderId);
                alert('订单完成！');
            }
        }
        
        function navigate() {
            alert('正在打开导航...');
            // 这里可以调用地图导航API
        }
        
        function goToProfile() {
            window.location.href = 'driver-profile.html';
        }
        
        function goToEarnings() {
            window.location.href = 'driver-earnings.html';
        }
        
        function goToSettings() {
            window.location.href = 'driver-settings.html';
        }
        
        // 页面加载时启动心跳
        window.addEventListener('load', () => {
            if (isOnline) {
                startHeartbeat();
            }
        });
        
        // 页面卸载时清除心跳
        window.addEventListener('beforeunload', () => {
            clearInterval(heartbeatInterval);
        });
    </script>
</body>
</html>
