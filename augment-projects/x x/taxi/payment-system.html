<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>圈存充值系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .layout {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #8e44ad;
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #9b59b6;
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-size: 18px;
            font-weight: bold;
        }
        
        .nav-menu {
            padding: 20px 0;
        }
        
        .nav-item {
            display: block;
            padding: 12px 20px;
            color: #d7bde2;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .nav-item:hover,
        .nav-item.active {
            background: #9b59b6;
            color: white;
            border-left-color: #f39c12;
        }
        
        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--accent-color, #8e44ad);
        }
        
        .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        
        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .panel-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 25px;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .token-generator {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .form-label {
            font-weight: 500;
            color: #2c3e50;
        }
        
        .form-input,
        .form-select {
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-input:focus,
        .form-select:focus {
            outline: none;
            border-color: #8e44ad;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #8e44ad;
            color: white;
        }
        
        .btn-primary:hover {
            background: #9b59b6;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .token-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .token-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #ecf0f1;
            transition: background 0.3s ease;
        }
        
        .token-item:hover {
            background: #f8f9fa;
        }
        
        .token-info {
            flex: 1;
        }
        
        .token-code {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #8e44ad;
            margin-bottom: 5px;
        }
        
        .token-details {
            font-size: 12px;
            color: #7f8c8d;
        }
        
        .token-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .status-unused {
            background: #d5f4e6;
            color: #27ae60;
        }
        
        .status-used {
            background: #fadbd8;
            color: #e74c3c;
        }
        
        .status-expired {
            background: #f4f6f6;
            color: #95a5a6;
        }
        
        .account-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .account-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #ecf0f1;
            transition: background 0.3s ease;
        }
        
        .account-item:hover {
            background: #f8f9fa;
        }
        
        .account-info {
            flex: 1;
        }
        
        .account-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .account-details {
            font-size: 12px;
            color: #7f8c8d;
        }
        
        .account-balance {
            font-size: 18px;
            font-weight: bold;
            color: #27ae60;
            margin-right: 15px;
        }
        
        .transaction-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .transaction-table th,
        .transaction-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .transaction-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .amount-positive {
            color: #27ae60;
            font-weight: bold;
        }
        
        .amount-negative {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .full-width {
            grid-column: 1 / -1;
        }
        
        .search-bar {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .search-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .export-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="layout">
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    💰 圈存充值系统
                </div>
            </div>
            
            <div class="nav-menu">
                <a href="#" class="nav-item active" onclick="showTokenManagement()">
                    🎫 Token管理
                </a>
                <a href="#" class="nav-item" onclick="showAccountManagement()">
                    👥 账户管理
                </a>
                <a href="#" class="nav-item" onclick="showTransactionHistory()">
                    📊 交易记录
                </a>
                <a href="#" class="nav-item" onclick="showReconciliation()">
                    🔍 对账管理
                </a>
                <a href="#" class="nav-item" onclick="showReports()">
                    📈 财务报表
                </a>
            </div>
        </nav>
        
        <main class="main-content">
            <div class="header">
                <h1 class="page-title">Token管理</h1>
                <div class="user-info">
                    <span>财务管理员</span>
                    <button class="btn btn-danger" onclick="logout()">退出</button>
                </div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">12,345</div>
                    <div class="stat-label">总Token数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">8,901</div>
                    <div class="stat-label">已使用Token</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">¥1,234,567</div>
                    <div class="stat-label">总充值金额</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">¥89,012</div>
                    <div class="stat-label">今日充值</div>
                </div>
            </div>
            
            <div class="content-grid">
                <div class="panel-card">
                    <div class="card-title">批量生成Token</div>
                    <form class="token-generator" onsubmit="generateTokens(event)">
                        <div class="form-group">
                            <label class="form-label">代理商</label>
                            <select class="form-select" required>
                                <option value="">请选择代理商</option>
                                <option value="agent001">北京代理商A</option>
                                <option value="agent002">上海代理商B</option>
                                <option value="agent003">广州代理商C</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Token面额</label>
                            <select class="form-select" required>
                                <option value="">请选择面额</option>
                                <option value="50">50元</option>
                                <option value="100">100元</option>
                                <option value="200">200元</option>
                                <option value="500">500元</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">生成数量</label>
                            <input type="number" class="form-input" min="1" max="1000" placeholder="请输入生成数量" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">有效期（天）</label>
                            <input type="number" class="form-input" min="1" max="365" value="30" required>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">批量生成</button>
                    </form>
                </div>
                
                <div class="panel-card">
                    <div class="card-title">Token列表</div>
                    <div class="search-bar">
                        <input type="text" class="search-input" placeholder="搜索Token编码...">
                        <button class="export-btn" onclick="exportTokens()">导出</button>
                    </div>
                    <div class="token-list">
                        <div class="token-item">
                            <div class="token-info">
                                <div class="token-code">TK20241204001</div>
                                <div class="token-details">面额: ¥100 | 代理商: 北京代理商A | 有效期: 2024-12-31</div>
                            </div>
                            <span class="token-status status-unused">未使用</span>
                        </div>
                        
                        <div class="token-item">
                            <div class="token-info">
                                <div class="token-code">TK20241204002</div>
                                <div class="token-details">面额: ¥200 | 代理商: 上海代理商B | 使用时间: 2024-12-04 10:30</div>
                            </div>
                            <span class="token-status status-used">已使用</span>
                        </div>
                        
                        <div class="token-item">
                            <div class="token-info">
                                <div class="token-code">TK20241203001</div>
                                <div class="token-details">面额: ¥50 | 代理商: 广州代理商C | 过期时间: 2024-12-03</div>
                            </div>
                            <span class="token-status status-expired">已过期</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="content-grid">
                <div class="panel-card">
                    <div class="card-title">代理商账户</div>
                    <div class="account-list">
                        <div class="account-item">
                            <div class="account-info">
                                <div class="account-name">北京代理商A</div>
                                <div class="account-details">账户: AG001 | 最后充值: 2024-12-04</div>
                            </div>
                            <div class="account-balance">¥12,345</div>
                            <button class="btn btn-success" onclick="rechargeAccount('AG001')">充值</button>
                        </div>
                        
                        <div class="account-item">
                            <div class="account-info">
                                <div class="account-name">上海代理商B</div>
                                <div class="account-details">账户: AG002 | 最后充值: 2024-12-03</div>
                            </div>
                            <div class="account-balance">¥8,901</div>
                            <button class="btn btn-success" onclick="rechargeAccount('AG002')">充值</button>
                        </div>
                        
                        <div class="account-item">
                            <div class="account-info">
                                <div class="account-name">广州代理商C</div>
                                <div class="account-details">账户: AG003 | 最后充值: 2024-12-02</div>
                            </div>
                            <div class="account-balance">¥5,678</div>
                            <button class="btn btn-success" onclick="rechargeAccount('AG003')">充值</button>
                        </div>
                    </div>
                </div>
                
                <div class="panel-card">
                    <div class="card-title">司机圈存记录</div>
                    <div class="account-list">
                        <div class="account-item">
                            <div class="account-info">
                                <div class="account-name">张师傅 (京A12345)</div>
                                <div class="account-details">圈存时间: 2024-12-04 14:30 | Token: TK20241204002</div>
                            </div>
                            <div class="account-balance">+¥200</div>
                        </div>
                        
                        <div class="account-item">
                            <div class="account-info">
                                <div class="account-name">李师傅 (京B67890)</div>
                                <div class="account-details">圈存时间: 2024-12-04 13:15 | Token: TK20241204001</div>
                            </div>
                            <div class="account-balance">+¥100</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="panel-card full-width">
                <div class="card-title">交易明细</div>
                <table class="transaction-table">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>交易类型</th>
                            <th>账户</th>
                            <th>金额</th>
                            <th>Token编码</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2024-12-04 14:30:25</td>
                            <td>司机圈存</td>
                            <td>张师傅 (京A12345)</td>
                            <td class="amount-positive">+¥200.00</td>
                            <td>TK20241204002</td>
                            <td>成功</td>
                        </tr>
                        <tr>
                            <td>2024-12-04 13:15:10</td>
                            <td>司机圈存</td>
                            <td>李师傅 (京B67890)</td>
                            <td class="amount-positive">+¥100.00</td>
                            <td>TK20241204001</td>
                            <td>成功</td>
                        </tr>
                        <tr>
                            <td>2024-12-04 10:20:45</td>
                            <td>代理充值</td>
                            <td>北京代理商A</td>
                            <td class="amount-negative">-¥1000.00</td>
                            <td>-</td>
                            <td>成功</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </main>
    </div>

    <script>
        function showTokenManagement() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = 'Token管理';
        }
        
        function showAccountManagement() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '账户管理';
        }
        
        function showTransactionHistory() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '交易记录';
        }
        
        function showReconciliation() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '对账管理';
        }
        
        function showReports() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '财务报表';
        }
        
        function updateActiveNav(target) {
            document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
            target.classList.add('active');
        }
        
        function generateTokens(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const tokenData = {
                agent: event.target.querySelector('select').value,
                amount: event.target.querySelectorAll('select')[1].value,
                quantity: event.target.querySelector('input[type="number"]').value,
                validDays: event.target.querySelectorAll('input[type="number"]')[1].value
            };
            
            console.log('生成Token:', tokenData);
            alert(`成功生成 ${tokenData.quantity} 个面额 ¥${tokenData.amount} 的Token`);
            
            // 重置表单
            event.target.reset();
        }
        
        function exportTokens() {
            alert('导出Token列表功能');
            // 这里可以实现CSV或Excel导出
        }
        
        function rechargeAccount(accountId) {
            const amount = prompt('请输入充值金额：');
            if (amount && !isNaN(amount) && parseFloat(amount) > 0) {
                console.log('账户充值:', { accountId, amount });
                alert(`账户 ${accountId} 充值 ¥${amount} 成功`);
            }
        }
        
        function logout() {
            if (confirm('确认退出登录？')) {
                window.location.href = 'payment-login.html';
            }
        }
        
        // 页面加载时初始化
        window.addEventListener('load', () => {
            console.log('圈存充值系统初始化完成');
        });
    </script>
</body>
</html>
