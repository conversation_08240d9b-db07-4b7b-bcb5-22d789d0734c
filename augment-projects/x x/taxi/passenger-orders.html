<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>我的订单</title>
    <link rel="stylesheet" href="common-styles.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding-bottom: 80px;
        }
        
        .header {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            display: flex;
            align-items: center;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            margin-right: 15px;
            cursor: pointer;
        }
        
        .header-title {
            font-size: 18px;
            font-weight: bold;
        }
        
        .order-tabs {
            display: flex;
            background: white;
            margin: 0 20px 20px;
            border-radius: 15px;
            padding: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .tab-btn {
            flex: 1;
            background: none;
            border: none;
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            position: relative;
        }
        
        .tab-btn.active {
            background: #74b9ff;
            color: white;
        }
        
        .tab-badge {
            position: absolute;
            top: 5px;
            right: 10px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .order-list {
            padding: 0 20px;
        }
        
        .order-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .order-id {
            font-weight: bold;
            color: #333;
        }
        
        .order-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-ongoing {
            background: #74b9ff;
            color: white;
        }
        
        .status-completed {
            background: #00b894;
            color: white;
        }
        
        .status-cancelled {
            background: #636e72;
            color: white;
        }
        
        .route-info {
            margin-bottom: 15px;
        }
        
        .location {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .location-icon {
            width: 20px;
            text-align: center;
            margin-right: 10px;
        }
        
        .location-text {
            flex: 1;
            font-size: 14px;
        }
        
        .order-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }
        
        .order-price {
            font-size: 18px;
            font-weight: bold;
            color: #74b9ff;
        }
        
        .order-actions {
            display: flex;
            gap: 10px;
        }
        
        .action-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #74b9ff;
            color: white;
        }
        
        .btn-secondary {
            background: #ddd;
            color: #666;
        }
        
        .btn-danger {
            background: #ff4757;
            color: white;
        }
        
        .driver-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .driver-card {
            display: flex;
            align-items: center;
        }
        
        .driver-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #74b9ff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            margin-right: 15px;
        }
        
        .driver-details h4 {
            font-size: 16px;
            color: #333;
            margin-bottom: 5px;
        }
        
        .driver-details p {
            font-size: 14px;
            color: #666;
        }
        
        .contact-btns {
            display: flex;
            gap: 10px;
            margin-left: auto;
        }
        
        .contact-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .call-btn {
            background: #00b894;
            color: white;
        }
        
        .message-btn {
            background: #74b9ff;
            color: white;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            display: flex;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }
        
        .nav-item {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            background: none;
        }
        
        .nav-item.active {
            color: #74b9ff;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 5px;
        }
        
        .nav-label {
            font-size: 12px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }
        
        .empty-text {
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .empty-btn {
            background: #74b9ff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <button class="back-btn" onclick="goBack()">←</button>
            <h1 class="header-title">我的订单</h1>
        </div>
    </div>
    
    <div class="order-tabs">
        <button class="tab-btn active" onclick="switchTab('ongoing')">
            进行中
            <span class="tab-badge">1</span>
        </button>
        <button class="tab-btn" onclick="switchTab('completed')">已完成</button>
        <button class="tab-btn" onclick="switchTab('cancelled')">已取消</button>
    </div>
    
    <div class="order-list" id="orderList">
        <!-- 进行中订单 -->
        <div class="order-card" data-tab="ongoing">
            <div class="order-header">
                <span class="order-id">订单 #20241204001</span>
                <span class="order-status status-ongoing">进行中</span>
            </div>
            
            <div class="driver-info">
                <div class="driver-card">
                    <div class="driver-avatar">👨‍💼</div>
                    <div class="driver-details">
                        <h4>张师傅</h4>
                        <p>京A12345 | 评分: 4.9⭐</p>
                    </div>
                    <div class="contact-btns">
                        <button class="contact-btn call-btn" onclick="callDriver()">📞</button>
                        <button class="contact-btn message-btn" onclick="messageDriver()">💬</button>
                    </div>
                </div>
            </div>
            
            <div class="route-info">
                <div class="location">
                    <span class="location-icon">🟢</span>
                    <span class="location-text">北京站东广场</span>
                </div>
                <div class="location">
                    <span class="location-icon">🔴</span>
                    <span class="location-text">首都国际机场T3航站楼</span>
                </div>
            </div>
            
            <div class="order-details">
                <span>下单时间: 14:30</span>
                <span class="order-price">¥85.00</span>
            </div>
            
            <div class="order-actions">
                <button class="action-btn btn-danger" onclick="cancelOrder('20241204001')">取消订单</button>
                <button class="action-btn btn-primary" onclick="trackOrder('20241204001')">实时跟踪</button>
            </div>
        </div>
        
        <!-- 已完成订单 -->
        <div class="order-card" data-tab="completed" style="display: none;">
            <div class="order-header">
                <span class="order-id">订单 #20241203001</span>
                <span class="order-status status-completed">已完成</span>
            </div>
            
            <div class="route-info">
                <div class="location">
                    <span class="location-icon">🟢</span>
                    <span class="location-text">王府井大街</span>
                </div>
                <div class="location">
                    <span class="location-icon">🔴</span>
                    <span class="location-text">三里屯太古里</span>
                </div>
            </div>
            
            <div class="order-details">
                <span>完成时间: 昨天 16:45</span>
                <span class="order-price">¥32.00</span>
            </div>
            
            <div class="order-actions">
                <button class="action-btn btn-secondary" onclick="viewReceipt('20241203001')">查看发票</button>
                <button class="action-btn btn-primary" onclick="rateOrder('20241203001')">评价订单</button>
            </div>
        </div>
        
        <!-- 已取消订单 -->
        <div class="order-card" data-tab="cancelled" style="display: none;">
            <div class="order-header">
                <span class="order-id">订单 #20241202001</span>
                <span class="order-status status-cancelled">已取消</span>
            </div>
            
            <div class="route-info">
                <div class="location">
                    <span class="location-icon">🟢</span>
                    <span class="location-text">中关村软件园</span>
                </div>
                <div class="location">
                    <span class="location-icon">🔴</span>
                    <span class="location-text">北京西站</span>
                </div>
            </div>
            
            <div class="order-details">
                <span>取消时间: 前天 09:15</span>
                <span style="color: #666;">已退款</span>
            </div>
            
            <div class="order-actions">
                <button class="action-btn btn-primary" onclick="rebookOrder('20241202001')">重新下单</button>
            </div>
        </div>
    </div>
    
    <div class="bottom-nav">
        <button class="nav-item" onclick="goToHome()">
            <div class="nav-icon">🏠</div>
            <div class="nav-label">首页</div>
        </button>
        <button class="nav-item active">
            <div class="nav-icon">📋</div>
            <div class="nav-label">订单</div>
        </button>
        <button class="nav-item" onclick="goToProfile()">
            <div class="nav-icon">👤</div>
            <div class="nav-label">我的</div>
        </button>
    </div>

    <script>
        let currentTab = 'ongoing';
        
        function goBack() {
            window.history.back();
        }
        
        function switchTab(tab) {
            currentTab = tab;
            
            // 更新标签样式
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // 显示对应订单
            const orderCards = document.querySelectorAll('.order-card');
            orderCards.forEach(card => {
                if (card.dataset.tab === tab) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
            
            // 检查是否有订单
            const visibleCards = document.querySelectorAll(`.order-card[data-tab="${tab}"]`);
            if (visibleCards.length === 0 || Array.from(visibleCards).every(card => card.style.display === 'none')) {
                showEmptyState(tab);
            } else {
                hideEmptyState();
            }
        }
        
        function showEmptyState(tab) {
            const orderList = document.getElementById('orderList');
            const emptyMessages = {
                ongoing: '暂无进行中的订单',
                completed: '暂无已完成的订单',
                cancelled: '暂无已取消的订单'
            };
            
            const existingEmpty = document.querySelector('.empty-state');
            if (existingEmpty) {
                existingEmpty.remove();
            }
            
            const emptyDiv = document.createElement('div');
            emptyDiv.className = 'empty-state';
            emptyDiv.innerHTML = `
                <div class="empty-icon">📋</div>
                <div class="empty-text">${emptyMessages[tab]}</div>
                <button class="empty-btn" onclick="goToHome()">立即叫车</button>
            `;
            orderList.appendChild(emptyDiv);
        }
        
        function hideEmptyState() {
            const existingEmpty = document.querySelector('.empty-state');
            if (existingEmpty) {
                existingEmpty.remove();
            }
        }
        
        function callDriver() {
            alert('正在拨打司机电话...');
        }
        
        function messageDriver() {
            alert('正在打开消息界面...');
        }
        
        function cancelOrder(orderId) {
            if (confirm('确认取消此订单？')) {
                alert(`订单 ${orderId} 已取消`);
                // 这里可以添加取消订单的逻辑
            }
        }
        
        function trackOrder(orderId) {
            alert(`正在跟踪订单 ${orderId}...`);
            // 这里可以跳转到实时跟踪页面
        }
        
        function viewReceipt(orderId) {
            alert(`查看订单 ${orderId} 的发票`);
        }
        
        function rateOrder(orderId) {
            alert(`为订单 ${orderId} 评分`);
        }
        
        function rebookOrder(orderId) {
            alert(`重新预订订单 ${orderId}`);
            window.location.href = 'passenger-home.html';
        }
        
        function goToHome() {
            window.location.href = 'passenger-home.html';
        }
        
        function goToProfile() {
            window.location.href = 'passenger-profile.html';
        }
        
        // 页面加载时初始化
        window.addEventListener('load', () => {
            // 检查当前标签是否有内容
            switchTab(currentTab);

            // 检查登录状态
            checkLoginStatus();

            // 加载订单数据
            loadOrderData();
        });

        function checkLoginStatus() {
            const userToken = localStorage.getItem('passengerToken');
            if (!userToken) {
                if (confirm('请先登录后查看订单，是否前往登录？')) {
                    window.location.href = 'passenger-login.html';
                }
            }
        }

        function loadOrderData() {
            // 这里可以从数据同步管理器获取订单数据
            if (window.TaxiSystemData) {
                const orders = window.TaxiSystemData.getOrders();
                console.log('加载订单数据:', orders);
                // 可以根据实际数据更新页面显示
            }
        }
    </script>

    <!-- 引入数据同步管理器 -->
    <script src="data-sync.js"></script>
    <!-- 引入通用导航组件 -->
    <script src="common-navigation.js"></script>
</body>
</html>
