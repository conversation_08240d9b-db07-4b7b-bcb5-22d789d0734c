<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>乘客登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .login-container {
            max-width: 400px;
            margin: 40px auto 0;
            background: white;
            border-radius: 20px;
            padding: 40px 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .logo-section {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: white;
        }
        
        .app-name {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        
        .app-desc {
            color: #666;
            font-size: 14px;
        }
        
        .auth-tabs {
            display: flex;
            margin-bottom: 30px;
            background: #f5f5f5;
            border-radius: 10px;
            padding: 4px;
        }
        
        .tab-btn {
            flex: 1;
            background: none;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }
        
        .tab-btn.active {
            background: white;
            color: #74b9ff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .form-section {
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #74b9ff;
        }
        
        .sms-input-group {
            display: flex;
            gap: 10px;
        }
        
        .sms-input {
            flex: 1;
        }
        
        .sms-btn {
            background: #74b9ff;
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 10px;
            cursor: pointer;
            white-space: nowrap;
            transition: background 0.3s ease;
        }
        
        .sms-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .password-strength {
            margin-top: 5px;
            font-size: 12px;
        }
        
        .strength-weak { color: #ff4757; }
        .strength-medium { color: #ffa502; }
        .strength-strong { color: #2ed573; }
        
        .agreement-section {
            margin-bottom: 20px;
        }
        
        .checkbox-group {
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }
        
        .checkbox {
            margin-top: 3px;
        }
        
        .agreement-text {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }
        
        .agreement-link {
            color: #74b9ff;
            text-decoration: none;
        }
        
        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            border: none;
            padding: 18px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s ease;
            margin-top: 20px;
        }
        
        .submit-btn:active {
            transform: scale(0.98);
        }
        
        .submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .hidden {
            display: none;
        }
        
        .error-message {
            color: #ff4757;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .success-message {
            color: #2ed573;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .login-options {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        .login-options p {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .social-login {
            display: flex;
            gap: 15px;
            justify-content: center;
        }
        
        .social-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 1px solid #ddd;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .social-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo-section">
            <div class="logo">🚖</div>
            <h1 class="app-name">出行助手</h1>
            <p class="app-desc">便捷出行，安全到达</p>
        </div>
        
        <div class="auth-tabs">
            <button class="tab-btn active" onclick="switchTab('login')">登录</button>
            <button class="tab-btn" onclick="switchTab('register')">注册</button>
        </div>
        
        <!-- 登录表单 -->
        <div id="loginForm" class="form-section">
            <div class="form-group">
                <label class="form-label">手机号</label>
                <input type="tel" class="form-input" id="loginPhone" placeholder="请输入手机号" maxlength="11">
                <div class="error-message" id="loginPhoneError"></div>
            </div>
            
            <div class="form-group">
                <label class="form-label">验证码</label>
                <div class="sms-input-group">
                    <input type="text" class="form-input sms-input" id="loginSms" placeholder="请输入验证码" maxlength="6">
                    <button type="button" class="sms-btn" id="loginSmsBtn" onclick="sendLoginSms()">发送验证码</button>
                </div>
                <div class="error-message" id="loginSmsError"></div>
            </div>
            
            <button type="button" class="submit-btn" onclick="login()">登录</button>
        </div>
        
        <!-- 注册表单 -->
        <div id="registerForm" class="form-section hidden">
            <div class="form-group">
                <label class="form-label">手机号</label>
                <input type="tel" class="form-input" id="registerPhone" placeholder="请输入手机号" maxlength="11">
                <div class="error-message" id="registerPhoneError"></div>
            </div>
            
            <div class="form-group">
                <label class="form-label">验证码</label>
                <div class="sms-input-group">
                    <input type="text" class="form-input sms-input" id="registerSms" placeholder="请输入验证码" maxlength="6">
                    <button type="button" class="sms-btn" id="registerSmsBtn" onclick="sendRegisterSms()">发送验证码</button>
                </div>
                <div class="error-message" id="registerSmsError"></div>
            </div>
            
            <div class="form-group">
                <label class="form-label">姓名</label>
                <input type="text" class="form-input" id="realName" placeholder="请输入真实姓名">
                <div class="error-message" id="realNameError"></div>
            </div>
            
            <div class="form-group">
                <label class="form-label">身份证号</label>
                <input type="text" class="form-input" id="idCard" placeholder="请输入身份证号" maxlength="18">
                <div class="error-message" id="idCardError"></div>
            </div>
            
            <div class="form-group">
                <label class="form-label">设置密码</label>
                <input type="password" class="form-input" id="registerPassword" placeholder="请设置登录密码" onkeyup="checkPasswordStrength()">
                <div class="password-strength" id="passwordStrength"></div>
                <div class="error-message" id="registerPasswordError"></div>
            </div>
            
            <div class="form-group">
                <label class="form-label">确认密码</label>
                <input type="password" class="form-input" id="confirmPassword" placeholder="请再次输入密码">
                <div class="error-message" id="confirmPasswordError"></div>
            </div>
            
            <div class="agreement-section">
                <div class="checkbox-group">
                    <input type="checkbox" class="checkbox" id="agreement">
                    <label for="agreement" class="agreement-text">
                        我已阅读并同意
                        <a href="#" class="agreement-link" onclick="showUserAgreement()">《用户协议》</a>
                        和
                        <a href="#" class="agreement-link" onclick="showPrivacyPolicy()">《隐私政策》</a>
                    </label>
                </div>
                <div class="error-message" id="agreementError"></div>
            </div>
            
            <button type="button" class="submit-btn" onclick="register()">注册</button>
        </div>
        
        <div class="login-options">
            <p>其他登录方式</p>
            <div class="social-login">
                <div class="social-btn" onclick="wechatLogin()">💬</div>
                <div class="social-btn" onclick="alipayLogin()">💰</div>
            </div>
        </div>
    </div>

    <script>
        let currentTab = 'login';
        let loginSmsCountdown = 0;
        let registerSmsCountdown = 0;
        
        function switchTab(tab) {
            currentTab = tab;
            
            // 更新标签样式
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // 切换表单显示
            if (tab === 'login') {
                document.getElementById('loginForm').classList.remove('hidden');
                document.getElementById('registerForm').classList.add('hidden');
            } else {
                document.getElementById('loginForm').classList.add('hidden');
                document.getElementById('registerForm').classList.remove('hidden');
            }
            
            clearErrors();
        }
        
        function sendLoginSms() {
            const phone = document.getElementById('loginPhone').value;
            if (!validatePhone(phone)) {
                document.getElementById('loginPhoneError').textContent = '请输入正确的手机号';
                return;
            }
            
            startSmsCountdown('loginSmsBtn', 'loginSmsCountdown');
            console.log('发送登录验证码到:', phone);
        }
        
        function sendRegisterSms() {
            const phone = document.getElementById('registerPhone').value;
            if (!validatePhone(phone)) {
                document.getElementById('registerPhoneError').textContent = '请输入正确的手机号';
                return;
            }
            
            startSmsCountdown('registerSmsBtn', 'registerSmsCountdown');
            console.log('发送注册验证码到:', phone);
        }
        
        function startSmsCountdown(btnId, countdownVar) {
            let countdown = 60;
            const btn = document.getElementById(btnId);
            btn.disabled = true;
            
            const timer = setInterval(() => {
                btn.textContent = `${countdown}秒后重发`;
                countdown--;
                
                if (countdown < 0) {
                    clearInterval(timer);
                    btn.disabled = false;
                    btn.textContent = '发送验证码';
                }
            }, 1000);
        }
        
        function validatePhone(phone) {
            return /^1[3-9]\d{9}$/.test(phone);
        }
        
        function validateIdCard(idCard) {
            return /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(idCard);
        }
        
        function checkPasswordStrength() {
            const password = document.getElementById('registerPassword').value;
            const strengthEl = document.getElementById('passwordStrength');
            
            if (password.length === 0) {
                strengthEl.textContent = '';
                return;
            }
            
            let strength = 0;
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/\d/.test(password)) strength++;
            if (/[^a-zA-Z\d]/.test(password)) strength++;
            
            if (strength < 2) {
                strengthEl.textContent = '密码强度：弱';
                strengthEl.className = 'password-strength strength-weak';
            } else if (strength < 4) {
                strengthEl.textContent = '密码强度：中';
                strengthEl.className = 'password-strength strength-medium';
            } else {
                strengthEl.textContent = '密码强度：强';
                strengthEl.className = 'password-strength strength-strong';
            }
        }
        
        function clearErrors() {
            document.querySelectorAll('.error-message').forEach(el => el.textContent = '');
        }
        
        function login() {
            clearErrors();
            
            const phone = document.getElementById('loginPhone').value;
            const sms = document.getElementById('loginSms').value;
            let isValid = true;
            
            if (!validatePhone(phone)) {
                document.getElementById('loginPhoneError').textContent = '请输入正确的手机号';
                isValid = false;
            }
            
            if (sms.length !== 6) {
                document.getElementById('loginSmsError').textContent = '请输入6位验证码';
                isValid = false;
            }
            
            if (isValid) {
                console.log('登录:', { phone, sms });
                // 模拟登录
                setTimeout(() => {
                    window.location.href = 'passenger-home.html';
                }, 1000);
            }
        }
        
        function register() {
            clearErrors();
            
            const phone = document.getElementById('registerPhone').value;
            const sms = document.getElementById('registerSms').value;
            const realName = document.getElementById('realName').value;
            const idCard = document.getElementById('idCard').value;
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const agreement = document.getElementById('agreement').checked;
            
            let isValid = true;
            
            if (!validatePhone(phone)) {
                document.getElementById('registerPhoneError').textContent = '请输入正确的手机号';
                isValid = false;
            }
            
            if (sms.length !== 6) {
                document.getElementById('registerSmsError').textContent = '请输入6位验证码';
                isValid = false;
            }
            
            if (!realName.trim()) {
                document.getElementById('realNameError').textContent = '请输入真实姓名';
                isValid = false;
            }
            
            if (!validateIdCard(idCard)) {
                document.getElementById('idCardError').textContent = '请输入正确的身份证号';
                isValid = false;
            }
            
            if (password.length < 6) {
                document.getElementById('registerPasswordError').textContent = '密码至少6位';
                isValid = false;
            }
            
            if (password !== confirmPassword) {
                document.getElementById('confirmPasswordError').textContent = '两次密码输入不一致';
                isValid = false;
            }
            
            if (!agreement) {
                document.getElementById('agreementError').textContent = '请勾选同意用户协议';
                isValid = false;
            }
            
            if (isValid) {
                console.log('注册:', { phone, realName, idCard });
                alert('注册成功！');
                switchTab('login');
            }
        }
        
        function showUserAgreement() {
            alert('显示用户协议');
        }
        
        function showPrivacyPolicy() {
            alert('显示隐私政策');
        }
        
        function wechatLogin() {
            alert('微信登录功能开发中');
        }
        
        function alipayLogin() {
            alert('支付宝登录功能开发中');
        }
    </script>
</body>
</html>
