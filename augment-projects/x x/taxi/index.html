<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>出租车管理系统 - 总览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
        }
        
        .main-title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 20px;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .description {
            font-size: 16px;
            opacity: 0.8;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .systems-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .system-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .system-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .system-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--accent-color, #667eea);
        }
        
        .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: white;
            margin-bottom: 20px;
            background: var(--accent-color, #667eea);
        }
        
        .card-title {
            font-size: 22px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .card-description {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .card-features {
            list-style: none;
            margin-bottom: 20px;
        }
        
        .card-features li {
            color: #666;
            margin-bottom: 5px;
            padding-left: 20px;
            position: relative;
        }
        
        .card-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: var(--accent-color, #667eea);
            font-weight: bold;
        }
        
        .card-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .card-link {
            flex: 1;
            display: inline-block;
            color: white;
            padding: 10px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 14px;
        }

        .card-link.primary {
            background: var(--accent-color, #667eea);
        }

        .card-link.secondary {
            background: rgba(0,0,0,0.1);
            border: 1px solid var(--accent-color, #667eea);
            color: var(--accent-color, #667eea);
        }

        .card-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .card-link.primary:hover {
            background: var(--accent-hover, #5a6fd8);
        }

        .card-link.secondary:hover {
            background: var(--accent-color, #667eea);
            color: white;
        }

        .quick-access {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin: 40px 20px;
            text-align: center;
        }

        .quick-title {
            color: white;
            font-size: 24px;
            margin-bottom: 25px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .quick-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .quick-btn {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .quick-btn:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.6);
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .quick-icon {
            font-size: 32px;
        }

        .quick-text {
            font-size: 16px;
            font-weight: 500;
        }

        .monitor-btn {
            background: linear-gradient(135deg, rgba(0,255,65,0.2) 0%, rgba(0,200,50,0.2) 100%);
            border-color: #00ff41;
        }

        .monitor-btn:hover {
            background: linear-gradient(135deg, rgba(0,255,65,0.4) 0%, rgba(0,200,50,0.4) 100%);
            box-shadow: 0 10px 30px rgba(0,255,65,0.3);
        }

        .guide-btn {
            background: linear-gradient(135deg, rgba(255,193,7,0.2) 0%, rgba(255,152,0,0.2) 100%);
            border-color: #ffc107;
        }

        .guide-btn:hover {
            background: linear-gradient(135deg, rgba(255,193,7,0.4) 0%, rgba(255,152,0,0.4) 100%);
            box-shadow: 0 10px 30px rgba(255,193,7,0.3);
        }
        
        .driver-card {
            --accent-color: #667eea;
            --accent-hover: #5a6fd8;
        }
        
        .passenger-card {
            --accent-color: #74b9ff;
            --accent-hover: #0984e3;
        }
        
        .admin-card {
            --accent-color: #2c3e50;
            --accent-hover: #34495e;
        }
        
        .dispatch-card {
            --accent-color: #34495e;
            --accent-hover: #2c3e50;
        }
        
        .qrcode-card {
            --accent-color: #27ae60;
            --accent-hover: #2ecc71;
        }
        
        .payment-card {
            --accent-color: #8e44ad;
            --accent-hover: #9b59b6;
        }
        
        .operation-card {
            --accent-color: #07c160;
            --accent-hover: #06ad56;
        }
        
        .footer {
            text-align: center;
            color: white;
            opacity: 0.8;
        }
        
        .footer p {
            margin-bottom: 10px;
        }
        
        .tech-stack {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .tech-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .tech-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }
        
        .tech-item {
            background: rgba(255,255,255,0.2);
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        @media (max-width: 768px) {
            .main-title {
                font-size: 36px;
            }
            
            .systems-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .system-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="main-title">🚕 出租车管理系统</h1>
            <p class="subtitle">智能派单 · 便捷出行 · 高效管理</p>
            <p class="description">
                完整的出租车管理解决方案，包含驾驶员APP、乘客端H5、管理后台、派单系统、二维码管理、圈存充值和运营运维等全套功能模块。
            </p>
        </div>
        
        <div class="systems-grid">
            <div class="system-card driver-card" onclick="openSystem('driver-welcome.html')">
                <div class="card-icon">👨‍💼</div>
                <h3 class="card-title">驾驶员APP</h3>
                <p class="card-description">为出租车司机提供的移动端应用，支持智能派单、实时收入统计等功能。</p>
                <ul class="card-features">
                    <li>智能订单推送与接单</li>
                    <li>实时心跳与位置上报</li>
                    <li>收入统计与钱包管理</li>
                    <li>乘客联系与导航集成</li>
                </ul>
                <div class="card-actions">
                    <a href="driver-welcome.html" class="card-link primary">司机端入口 →</a>
                    <a href="driver-workstation.html" class="card-link secondary">工作台 →</a>
                </div>
            </div>

            <div class="system-card passenger-card" onclick="openSystem('passenger-login.html')">
                <div class="card-icon">🚖</div>
                <h3 class="card-title">乘客端H5</h3>
                <p class="card-description">响应式H5乘客应用，支持智能叫车、订单跟踪、个人中心等功能。</p>
                <ul class="card-features">
                    <li>一键叫车与预约用车</li>
                    <li>实时订单状态跟踪</li>
                    <li>司机信息与联系方式</li>
                    <li>优惠券与积分管理</li>
                </ul>
                <div class="card-actions">
                    <a href="passenger-login.html" class="card-link primary">乘客端入口 →</a>
                    <a href="passenger-home.html" class="card-link secondary">叫车页面 →</a>
                </div>
            </div>

            <div class="system-card admin-card" onclick="openSystem('admin-dashboard.html')">
                <div class="card-icon">⚙️</div>
                <h3 class="card-title">管理后台</h3>
                <p class="card-description">出租车管理后台系统，提供车队管理、订单统计、财务分析等功能。</p>
                <ul class="card-features">
                    <li>实时车队监控与管理</li>
                    <li>订单数据统计分析</li>
                    <li>司机与车辆档案管理</li>
                    <li>系统配置与权限控制</li>
                </ul>
                <div class="card-actions">
                    <a href="admin-dashboard.html" class="card-link primary">管理后台 →</a>
                    <a href="admin-fleet-management.html" class="card-link secondary">车队管理 →</a>
                </div>
            </div>

            <div class="system-card dispatch-card" onclick="openSystem('dispatch-intelligent-system.html')">
                <div class="card-icon">📡</div>
                <h3 class="card-title">智能派单</h3>
                <p class="card-description">AI智能派单调度系统，支持自动派单、网格化管理、紧急订单处理。</p>
                <ul class="card-features">
                    <li>AI智能派单算法</li>
                    <li>实时地图网格调度</li>
                    <li>紧急订单优先处理</li>
                    <li>派单效率统计分析</li>
                </ul>
                <div class="card-actions">
                    <a href="dispatch-intelligent-system.html" class="card-link primary">智能派单 →</a>
                    <a href="dispatch-dashboard.html" class="card-link secondary">派单管理 →</a>
                </div>
            </div>

            <div class="system-card qrcode-card" onclick="openSystem('qrcode-heatmap-analysis.html')">
                <div class="card-icon">📱</div>
                <h3 class="card-title">二维码分析</h3>
                <p class="card-description">二维码生成管理和热力图分析系统，支持扫码数据可视化统计。</p>
                <ul class="card-features">
                    <li>二维码批量生成管理</li>
                    <li>扫码热力图可视化</li>
                    <li>热点区域统计分析</li>
                    <li>扫码趋势数据报表</li>
                </ul>
                <div class="card-actions">
                    <a href="qrcode-heatmap-analysis.html" class="card-link primary">热力图分析 →</a>
                    <a href="qrcode-dashboard.html" class="card-link secondary">二维码管理 →</a>
                </div>
            </div>

            <div class="system-card payment-card" onclick="openSystem('payment-system.html')">
                <div class="card-icon">💰</div>
                <h3 class="card-title">支付充值</h3>
                <p class="card-description">Token管理和账户圈存系统，支持代理商充值、司机圈存、交易对账。</p>
                <ul class="card-features">
                    <li>Token批量生成管理</li>
                    <li>代理商账户体系</li>
                    <li>司机圈存充值服务</li>
                    <li>交易流水对账功能</li>
                </ul>
                <div class="card-actions">
                    <a href="payment-system.html" class="card-link primary">支付系统 →</a>
                </div>
            </div>

            <div class="system-card operation-card" onclick="openSystem('operation-app.html')">
                <div class="card-icon">🔧</div>
                <h3 class="card-title">运营运维</h3>
                <p class="card-description">运营运维移动应用，支持现场服务、设备维护、数据统计等功能。</p>
                <ul class="card-features">
                    <li>司机现场圈存充值</li>
                    <li>二维码设备运维</li>
                    <li>代理商充值服务</li>
                    <li>运维工作报表统计</li>
                </ul>
                <div class="card-actions">
                    <a href="operation-app.html" class="card-link primary">运维APP →</a>
                </div>
            </div>
        </div>

        <div class="quick-access">
            <h3 class="quick-title">🚀 快速访问</h3>
            <div class="quick-buttons">
                <button class="quick-btn monitor-btn" onclick="openSystemMonitor()">
                    <span class="quick-icon">📊</span>
                    <span class="quick-text">系统监控</span>
                </button>
                <button class="quick-btn" onclick="window.open('driver-workstation.html', '_blank')">
                    <span class="quick-icon">👨‍💼</span>
                    <span class="quick-text">司机工作台</span>
                </button>
                <button class="quick-btn" onclick="window.open('dispatch-intelligent-system.html', '_blank')">
                    <span class="quick-icon">📡</span>
                    <span class="quick-text">智能派单</span>
                </button>
                <button class="quick-btn" onclick="window.open('qrcode-heatmap-analysis.html', '_blank')">
                    <span class="quick-icon">📱</span>
                    <span class="quick-text">热力图分析</span>
                </button>
                <button class="quick-btn guide-btn" onclick="window.open('system-guide.html', '_blank')">
                    <span class="quick-icon">📖</span>
                    <span class="quick-text">使用指南</span>
                </button>
            </div>
        </div>

        <div class="footer">
            <p>🚀 基于现代Web技术栈构建的完整出租车管理解决方案</p>
            <p>📱 支持移动端、桌面端多平台访问</p>
            
            <div class="tech-stack">
                <div class="tech-title">技术栈</div>
                <div class="tech-list">
                    <span class="tech-item">HTML5</span>
                    <span class="tech-item">CSS3</span>
                    <span class="tech-item">JavaScript</span>
                    <span class="tech-item">响应式设计</span>
                    <span class="tech-item">PWA</span>
                    <span class="tech-item">WebSocket</span>
                    <span class="tech-item">地图API</span>
                    <span class="tech-item">微信小程序</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openSystem(url) {
            window.open(url, '_blank');
        }

        function openSystemMonitor() {
            window.open('system-monitor.html', '_blank');
        }
        
        // 页面加载动画
        window.addEventListener('load', () => {
            const cards = document.querySelectorAll('.system-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
        
        // 添加键盘导航支持
        document.addEventListener('keydown', (e) => {
            if (e.key >= '1' && e.key <= '7') {
                const cardIndex = parseInt(e.key) - 1;
                const cards = document.querySelectorAll('.system-card');
                if (cards[cardIndex]) {
                    const link = cards[cardIndex].querySelector('.card-link');
                    if (link) {
                        window.open(link.href, '_blank');
                    }
                }
            }
        });
        
        console.log('🚕 出租车管理系统已加载完成');
        console.log('📱 支持的功能模块:');
        console.log('1. 驾驶员APP - 移动端司机应用');
        console.log('2. 乘客端H5 - 响应式乘客应用');
        console.log('3. 管理后台 - 车队订单管理');
        console.log('4. 派单管理 - 智能派单调度');
        console.log('5. 二维码管理 - 扫码服务管理');
        console.log('6. 圈存充值 - 财务支付系统');
        console.log('7. 运营运维 - 现场服务应用');
    </script>
</body>
</html>
