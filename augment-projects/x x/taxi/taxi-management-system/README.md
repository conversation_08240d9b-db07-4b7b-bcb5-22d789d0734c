# 出租车管理系统

## 项目概述

这是一个完整的出租车管理系统，包含驾驶员APP、乘客端H5、多个管理后台以及运营运维工具。系统支持订单管理、派单调度、支付结算、数据分析等核心功能。

## 系统架构

### 技术栈选择

#### 前端技术栈
- **驾驶员APP**: React Native / Flutter (跨平台移动应用)
- **乘客端H5**: Vue.js 3 + Vite + TypeScript (响应式Web应用)
- **管理后台**: Vue.js 3 + Element Plus + TypeScript (PC端管理系统)
- **运营运维APP**: 微信小程序 + uni-app (多端兼容)

#### 后端技术栈
- **API服务**: Node.js + Express + TypeScript
- **数据库**: MySQL 8.0 (主数据库) + Redis (缓存)
- **地图服务**: 高德地图API
- **消息推送**: WebSocket + 极光推送
- **文件存储**: 阿里云OSS / 腾讯云COS

#### 基础设施
- **容器化**: Docker + Docker Compose
- **API文档**: Swagger/OpenAPI 3.0
- **代码管理**: Git + GitLab
- **CI/CD**: GitLab CI/CD

### 系统模块

#### 1. 驾驶员APP
- 权限申请模块
- 欢迎页与登录系统
- 订单工作台
- 个人中心

#### 2. 乘客端H5
- 登录与注册
- 预约用车
- 订单管理
- 个人中心

#### 3. 出租车管理后台
- 系统管理
- 车队/车辆管理
- 订单管理
- 规则与用量管理

#### 4. 派单管理后台
- 话务员订单创建
- 派单员派车

#### 5. 二维码管理后台
- 二维码生成与管理
- 二维码热力图

#### 6. 圈存充值系统
- Token生成与管理
- 账户圈存与对账

#### 7. 运营运维APP
- 驾驶员圈存/代理充值
- 二维码运维

## 数据库设计

### 核心数据表

#### 用户相关
- `users` - 用户基础信息表
- `drivers` - 驾驶员信息表
- `passengers` - 乘客信息表
- `user_auth` - 用户认证信息表

#### 车辆相关
- `fleets` - 车队信息表
- `vehicles` - 车辆信息表
- `driver_vehicle_bind` - 驾驶员车辆绑定表

#### 订单相关
- `orders` - 订单主表
- `order_status_log` - 订单状态变更日志
- `order_routes` - 订单路线轨迹表

#### 支付相关
- `accounts` - 账户信息表
- `transactions` - 交易记录表
- `tokens` - 充值Token表

#### 系统相关
- `system_configs` - 系统配置表
- `operation_logs` - 操作日志表
- `qr_codes` - 二维码管理表

## API设计规范

### RESTful API规范
- 使用HTTP动词表示操作类型
- 统一的响应格式
- 标准的HTTP状态码
- JWT Token认证

### 核心API模块
- 用户认证API
- 订单管理API
- 地图服务API
- 支付结算API
- 消息推送API

## 部署方案

### 开发环境
- 本地Docker Compose部署
- 热重载开发模式

### 生产环境
- 云服务器部署
- Nginx反向代理
- SSL证书配置
- 数据库主从复制

## 安全考虑

- API接口鉴权
- 数据传输加密
- 敏感信息脱敏
- 操作日志记录
- 权限分级管理

## 开发计划

1. 项目架构设计 ✓
2. 数据库设计与初始化
3. 后端API开发
4. 前端应用开发
5. 系统集成测试
6. 部署上线

## 目录结构

```
taxi-management-system/
├── backend/                 # 后端API服务
├── driver-app/             # 驾驶员APP
├── passenger-h5/           # 乘客端H5
├── admin-dashboard/        # 管理后台
├── dispatch-dashboard/     # 派单后台
├── qrcode-dashboard/       # 二维码后台
├── payment-system/         # 圈存充值系统
├── operation-app/          # 运营运维APP
├── database/               # 数据库脚本
├── docs/                   # 项目文档
└── docker/                 # Docker配置
```
