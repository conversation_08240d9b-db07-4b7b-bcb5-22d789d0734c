<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车队管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 250px;
            height: 100vh;
            background: #2c3e50;
            color: white;
            padding: 20px 0;
            z-index: 1000;
        }
        
        .logo {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid #34495e;
            margin-bottom: 20px;
        }
        
        .logo h2 {
            font-size: 20px;
            margin-bottom: 5px;
        }
        
        .logo p {
            font-size: 12px;
            opacity: 0.7;
        }
        
        .nav-menu {
            list-style: none;
        }
        
        .nav-item {
            margin-bottom: 5px;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover, .nav-link.active {
            background: #34495e;
            border-right: 3px solid #3498db;
        }
        
        .nav-icon {
            margin-right: 10px;
            font-size: 16px;
            width: 20px;
        }
        
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .header-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-success {
            background: #2ecc71;
            color: white;
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--accent-color, #3498db);
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .stat-title {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
            background: var(--accent-color, #3498db);
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .stat-change {
            font-size: 12px;
            color: #27ae60;
        }
        
        .fleet-overview {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .section-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .filter-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .search-box {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            width: 200px;
        }
        
        .vehicle-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        
        .vehicle-card {
            border: 1px solid #eee;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .vehicle-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .vehicle-status {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-online {
            background: #d4edda;
            color: #155724;
        }
        
        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-busy {
            background: #fff3cd;
            color: #856404;
        }
        
        .vehicle-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .vehicle-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #3498db;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            margin-right: 15px;
        }
        
        .vehicle-info h4 {
            font-size: 16px;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .vehicle-info p {
            font-size: 14px;
            color: #666;
        }
        
        .vehicle-details {
            margin-bottom: 15px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .detail-label {
            color: #666;
        }
        
        .detail-value {
            font-weight: 500;
            color: #2c3e50;
        }
        
        .vehicle-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            flex: 1;
            padding: 8px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .btn-view {
            background: #3498db;
            color: white;
        }
        
        .btn-edit {
            background: #f39c12;
            color: white;
        }
        
        .btn-disable {
            background: #e74c3c;
            color: white;
        }
        
        .real-time-map {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: 400px;
            position: relative;
            overflow: hidden;
        }
        
        .map-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .vehicle-markers {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
        }
        
        .marker-legend {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .marker-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .dot-online { background: #2ecc71; }
        .dot-offline { background: #e74c3c; }
        .dot-busy { background: #f39c12; }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="logo">
            <h2>🚕 出租车管理</h2>
            <p>Fleet Management System</p>
        </div>
        
        <ul class="nav-menu">
            <li class="nav-item">
                <a href="#" class="nav-link">
                    <span class="nav-icon">📊</span>
                    数据概览
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link active">
                    <span class="nav-icon">🚗</span>
                    车队管理
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">
                    <span class="nav-icon">👨‍💼</span>
                    司机管理
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">
                    <span class="nav-icon">📋</span>
                    订单管理
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">
                    <span class="nav-icon">💰</span>
                    财务管理
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">
                    <span class="nav-icon">⚙️</span>
                    系统设置
                </a>
            </li>
        </ul>
    </div>
    
    <div class="main-content">
        <div class="header">
            <h1 class="page-title">车队管理</h1>
            <div class="header-actions">
                <button class="btn btn-success" onclick="addVehicle()">+ 添加车辆</button>
                <button class="btn btn-primary" onclick="exportData()">📊 导出数据</button>
                <button class="btn btn-warning" onclick="batchOperation()">批量操作</button>
            </div>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card" style="--accent-color: #2ecc71;">
                <div class="stat-header">
                    <span class="stat-title">在线车辆</span>
                    <div class="stat-icon">🟢</div>
                </div>
                <div class="stat-value">156</div>
                <div class="stat-change">+12 较昨日</div>
            </div>
            
            <div class="stat-card" style="--accent-color: #e74c3c;">
                <div class="stat-header">
                    <span class="stat-title">离线车辆</span>
                    <div class="stat-icon">🔴</div>
                </div>
                <div class="stat-value">24</div>
                <div class="stat-change">-5 较昨日</div>
            </div>
            
            <div class="stat-card" style="--accent-color: #f39c12;">
                <div class="stat-header">
                    <span class="stat-title">载客中</span>
                    <div class="stat-icon">🚖</div>
                </div>
                <div class="stat-value">89</div>
                <div class="stat-change">+8 较昨日</div>
            </div>
            
            <div class="stat-card" style="--accent-color: #9b59b6;">
                <div class="stat-header">
                    <span class="stat-title">总车辆数</span>
                    <div class="stat-icon">🚗</div>
                </div>
                <div class="stat-value">180</div>
                <div class="stat-change">+2 较昨日</div>
            </div>
        </div>
        
        <div class="real-time-map">
            <div class="section-header">
                <h3 class="section-title">实时车辆分布</h3>
            </div>
            <div class="map-placeholder">
                🗺️ 实时地图显示区域
                <br>
                <small>集成高德/百度地图API显示车辆实时位置</small>
            </div>
            <div class="vehicle-markers">
                <div class="marker-legend">
                    <div class="marker-dot dot-online"></div>
                    <span>在线空车 (67辆)</span>
                </div>
                <div class="marker-legend">
                    <div class="marker-dot dot-busy"></div>
                    <span>载客中 (89辆)</span>
                </div>
                <div class="marker-legend">
                    <div class="marker-dot dot-offline"></div>
                    <span>离线 (24辆)</span>
                </div>
            </div>
        </div>
        
        <div class="fleet-overview">
            <div class="section-header">
                <h3 class="section-title">车辆列表</h3>
                <div class="filter-controls">
                    <select class="filter-select" onchange="filterByStatus(this.value)">
                        <option value="all">全部状态</option>
                        <option value="online">在线</option>
                        <option value="offline">离线</option>
                        <option value="busy">载客中</option>
                    </select>
                    <input type="text" class="search-box" placeholder="搜索车牌号或司机姓名..." onkeyup="searchVehicles(this.value)">
                </div>
            </div>
            
            <div class="vehicle-grid">
                <div class="vehicle-card">
                    <div class="vehicle-status status-online">在线</div>
                    <div class="vehicle-header">
                        <div class="vehicle-icon">🚕</div>
                        <div class="vehicle-info">
                            <h4>京A12345</h4>
                            <p>张师傅 | 4.9⭐</p>
                        </div>
                    </div>
                    <div class="vehicle-details">
                        <div class="detail-row">
                            <span class="detail-label">车型:</span>
                            <span class="detail-value">大众朗逸</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">今日订单:</span>
                            <span class="detail-value">8单</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">今日收入:</span>
                            <span class="detail-value">¥428.50</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">在线时长:</span>
                            <span class="detail-value">7小时32分</span>
                        </div>
                    </div>
                    <div class="vehicle-actions">
                        <button class="action-btn btn-view" onclick="viewVehicle('京A12345')">查看</button>
                        <button class="action-btn btn-edit" onclick="editVehicle('京A12345')">编辑</button>
                        <button class="action-btn btn-disable" onclick="disableVehicle('京A12345')">禁用</button>
                    </div>
                </div>
                
                <div class="vehicle-card">
                    <div class="vehicle-status status-busy">载客中</div>
                    <div class="vehicle-header">
                        <div class="vehicle-icon">🚖</div>
                        <div class="vehicle-info">
                            <h4>京B67890</h4>
                            <p>李师傅 | 4.7⭐</p>
                        </div>
                    </div>
                    <div class="vehicle-details">
                        <div class="detail-row">
                            <span class="detail-label">车型:</span>
                            <span class="detail-value">丰田卡罗拉</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">今日订单:</span>
                            <span class="detail-value">6单</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">今日收入:</span>
                            <span class="detail-value">¥356.80</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">当前订单:</span>
                            <span class="detail-value">#20241204002</span>
                        </div>
                    </div>
                    <div class="vehicle-actions">
                        <button class="action-btn btn-view" onclick="viewVehicle('京B67890')">查看</button>
                        <button class="action-btn btn-edit" onclick="editVehicle('京B67890')">编辑</button>
                        <button class="action-btn btn-disable" onclick="disableVehicle('京B67890')">禁用</button>
                    </div>
                </div>
                
                <div class="vehicle-card">
                    <div class="vehicle-status status-offline">离线</div>
                    <div class="vehicle-header">
                        <div class="vehicle-icon">🚗</div>
                        <div class="vehicle-info">
                            <h4>京C11111</h4>
                            <p>王师傅 | 4.8⭐</p>
                        </div>
                    </div>
                    <div class="vehicle-details">
                        <div class="detail-row">
                            <span class="detail-label">车型:</span>
                            <span class="detail-value">现代伊兰特</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">今日订单:</span>
                            <span class="detail-value">5单</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">今日收入:</span>
                            <span class="detail-value">¥298.20</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">离线时间:</span>
                            <span class="detail-value">2小时15分</span>
                        </div>
                    </div>
                    <div class="vehicle-actions">
                        <button class="action-btn btn-view" onclick="viewVehicle('京C11111')">查看</button>
                        <button class="action-btn btn-edit" onclick="editVehicle('京C11111')">编辑</button>
                        <button class="action-btn btn-disable" onclick="disableVehicle('京C11111')">禁用</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function addVehicle() {
            alert('打开添加车辆对话框');
        }
        
        function exportData() {
            alert('导出车队数据到Excel');
        }
        
        function batchOperation() {
            alert('批量操作车辆');
        }
        
        function filterByStatus(status) {
            console.log('筛选状态:', status);
            // 实现状态筛选逻辑
        }
        
        function searchVehicles(keyword) {
            console.log('搜索关键词:', keyword);
            // 实现搜索逻辑
        }
        
        function viewVehicle(plateNumber) {
            alert(`查看车辆详情: ${plateNumber}`);
        }
        
        function editVehicle(plateNumber) {
            alert(`编辑车辆信息: ${plateNumber}`);
        }
        
        function disableVehicle(plateNumber) {
            if (confirm(`确认禁用车辆 ${plateNumber}？`)) {
                alert(`车辆 ${plateNumber} 已禁用`);
            }
        }
        
        // 模拟实时数据更新
        setInterval(() => {
            updateRealTimeData();
        }, 30000);
        
        function updateRealTimeData() {
            console.log('更新实时车辆数据');
            // 这里可以通过WebSocket或定时API调用更新数据
        }
    </script>
</body>
</html>
