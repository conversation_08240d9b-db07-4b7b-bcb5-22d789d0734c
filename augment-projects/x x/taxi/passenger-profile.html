<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>个人中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding-bottom: 80px;
        }
        
        .header {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            display: flex;
            align-items: center;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            margin-right: 15px;
            cursor: pointer;
        }
        
        .header-title {
            font-size: 18px;
            font-weight: bold;
        }
        
        .profile-section {
            background: white;
            margin: 20px;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .profile-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .avatar-large {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
            margin-right: 20px;
        }
        
        .profile-info h2 {
            font-size: 22px;
            color: #333;
            margin-bottom: 5px;
        }
        
        .profile-info p {
            color: #666;
            font-size: 14px;
        }
        
        .edit-btn {
            background: #74b9ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            margin-left: auto;
        }
        
        .stats-row {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .menu-section {
            background: white;
            margin: 0 20px 20px;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .menu-item:last-child {
            border-bottom: none;
        }
        
        .menu-item:hover {
            background: #f8f9fa;
        }
        
        .menu-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            margin-right: 15px;
        }
        
        .icon-locations {
            background: #e8f5e8;
            color: #2ed573;
        }
        
        .icon-preferences {
            background: #fff3cd;
            color: #ffa502;
        }
        
        .icon-coupons {
            background: #e3f2fd;
            color: #2196f3;
        }
        
        .icon-mileage {
            background: #f3e5f5;
            color: #9c27b0;
        }
        
        .icon-settings {
            background: #f5f5f5;
            color: #666;
        }
        
        .menu-content {
            flex: 1;
        }
        
        .menu-title {
            font-size: 16px;
            color: #333;
            margin-bottom: 3px;
        }
        
        .menu-subtitle {
            font-size: 12px;
            color: #666;
        }
        
        .menu-arrow {
            color: #ccc;
            font-size: 16px;
        }
        
        .menu-badge {
            background: #ff4757;
            color: white;
            border-radius: 10px;
            padding: 2px 8px;
            font-size: 12px;
            margin-right: 10px;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            display: flex;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }
        
        .nav-item {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            background: none;
        }
        
        .nav-item.active {
            color: #74b9ff;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 5px;
        }
        
        .nav-label {
            font-size: 12px;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 20px;
        }
        
        .quick-action {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .quick-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }
        
        .quick-action-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .quick-action-title {
            font-size: 14px;
            font-weight: bold;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <button class="back-btn" onclick="goBack()">←</button>
            <h1 class="header-title">个人中心</h1>
        </div>
    </div>
    
    <div class="profile-section">
        <div class="profile-header">
            <div class="avatar-large">👤</div>
            <div class="profile-info">
                <h2>李先生</h2>
                <p>手机号：138****5678</p>
                <p>注册时间：2024年3月</p>
            </div>
            <button class="edit-btn" onclick="editProfile()">编辑</button>
        </div>
        
        <div class="stats-row">
            <div class="stat-item">
                <div class="stat-value">156</div>
                <div class="stat-label">总订单数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">4.8</div>
                <div class="stat-label">平均评分</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">2,580</div>
                <div class="stat-label">总里程(km)</div>
            </div>
        </div>
    </div>
    
    <div class="quick-actions">
        <div class="quick-action" onclick="viewCoupons()">
            <div class="quick-action-icon">🎫</div>
            <div class="quick-action-title">优惠券</div>
        </div>
        
        <div class="quick-action" onclick="viewMileage()">
            <div class="quick-action-icon">🏃‍♂️</div>
            <div class="quick-action-title">里程积分</div>
        </div>
        
        <div class="quick-action" onclick="viewInvoices()">
            <div class="quick-action-icon">🧾</div>
            <div class="quick-action-title">发票管理</div>
        </div>
        
        <div class="quick-action" onclick="customerService()">
            <div class="quick-action-icon">🎧</div>
            <div class="quick-action-title">客服中心</div>
        </div>
    </div>
    
    <div class="menu-section">
        <div class="menu-item" onclick="manageLocations()">
            <div class="menu-icon icon-locations">📍</div>
            <div class="menu-content">
                <div class="menu-title">常用地点</div>
                <div class="menu-subtitle">管理家、公司等常用地址</div>
            </div>
            <span class="menu-arrow">›</span>
        </div>
        
        <div class="menu-item" onclick="setPreferences()">
            <div class="menu-icon icon-preferences">⚙️</div>
            <div class="menu-content">
                <div class="menu-title">用车偏好</div>
                <div class="menu-subtitle">车型偏好、音乐偏好等</div>
            </div>
            <span class="menu-arrow">›</span>
        </div>
        
        <div class="menu-item" onclick="viewCoupons()">
            <div class="menu-icon icon-coupons">🎫</div>
            <div class="menu-content">
                <div class="menu-title">优惠券</div>
                <div class="menu-subtitle">查看可用优惠券</div>
            </div>
            <span class="menu-badge">3</span>
            <span class="menu-arrow">›</span>
        </div>
        
        <div class="menu-item" onclick="viewMileage()">
            <div class="menu-icon icon-mileage">🏃‍♂️</div>
            <div class="menu-content">
                <div class="menu-title">里程数管理</div>
                <div class="menu-subtitle">当前积分：2,580分</div>
            </div>
            <span class="menu-arrow">›</span>
        </div>
    </div>
    
    <div class="menu-section">
        <div class="menu-item" onclick="appSettings()">
            <div class="menu-icon icon-settings">⚙️</div>
            <div class="menu-content">
                <div class="menu-title">设置</div>
                <div class="menu-subtitle">通知、隐私、账户设置</div>
            </div>
            <span class="menu-arrow">›</span>
        </div>
        
        <div class="menu-item" onclick="helpCenter()">
            <div class="menu-icon icon-settings">❓</div>
            <div class="menu-content">
                <div class="menu-title">帮助中心</div>
                <div class="menu-subtitle">常见问题、使用指南</div>
            </div>
            <span class="menu-arrow">›</span>
        </div>
        
        <div class="menu-item" onclick="aboutApp()">
            <div class="menu-icon icon-settings">ℹ️</div>
            <div class="menu-content">
                <div class="menu-title">关于我们</div>
                <div class="menu-subtitle">版本信息、用户协议</div>
            </div>
            <span class="menu-arrow">›</span>
        </div>
        
        <div class="menu-item" onclick="logout()">
            <div class="menu-icon icon-settings">🚪</div>
            <div class="menu-content">
                <div class="menu-title">退出登录</div>
                <div class="menu-subtitle">安全退出当前账户</div>
            </div>
            <span class="menu-arrow">›</span>
        </div>
    </div>
    
    <div class="bottom-nav">
        <button class="nav-item" onclick="goToHome()">
            <div class="nav-icon">🏠</div>
            <div class="nav-label">首页</div>
        </button>
        <button class="nav-item" onclick="goToOrders()">
            <div class="nav-icon">📋</div>
            <div class="nav-label">订单</div>
        </button>
        <button class="nav-item active">
            <div class="nav-icon">👤</div>
            <div class="nav-label">我的</div>
        </button>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }
        
        function editProfile() {
            alert('跳转到个人信息编辑页面');
        }
        
        function manageLocations() {
            alert('管理常用地点');
        }
        
        function setPreferences() {
            alert('设置用车偏好');
        }
        
        function viewCoupons() {
            alert('查看优惠券\n\n可用优惠券：\n• 新用户专享 - 立减10元\n• 周末出行 - 9折优惠\n• 长途出行 - 满50减5');
        }
        
        function viewMileage() {
            alert('里程积分管理\n\n当前积分：2,580分\n可兑换：\n• 5元代金券 (500分)\n• 10元代金券 (1000分)\n• 免费洗车券 (2000分)');
        }
        
        function viewInvoices() {
            alert('发票管理');
        }
        
        function customerService() {
            alert('客服中心\n\n服务热线：400-123-4567\n在线客服：24小时在线\n常见问题：查看帮助中心');
        }
        
        function appSettings() {
            alert('应用设置');
        }
        
        function helpCenter() {
            alert('帮助中心');
        }
        
        function aboutApp() {
            alert('关于我们\n\n出行助手 v1.0.0\n© 2024 出租车管理系统\n\n用户协议 | 隐私政策');
        }
        
        function logout() {
            if (confirm('确认退出登录？')) {
                localStorage.removeItem('passengerToken');
                window.location.href = 'passenger-login.html';
            }
        }
        
        function goToHome() {
            window.location.href = 'passenger-home.html';
        }
        
        function goToOrders() {
            window.location.href = 'passenger-orders.html';
        }
        
        // 页面加载时获取用户信息
        window.addEventListener('load', () => {
            loadUserProfile();
        });
        
        function loadUserProfile() {
            // 模拟API调用
            const userData = {
                name: '李先生',
                phone: '138****5678',
                registerDate: '2024年3月',
                totalOrders: 156,
                avgRating: 4.8,
                totalMileage: 2580,
                availableCoupons: 3
            };
            
            console.log('用户数据加载完成:', userData);
        }
    </script>
</body>
</html>
