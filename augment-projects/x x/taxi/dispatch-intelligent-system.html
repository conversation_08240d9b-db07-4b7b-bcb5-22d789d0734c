<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能派单调度系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        
        .logo-icon {
            margin-right: 10px;
            font-size: 28px;
        }
        
        .dispatch-status {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            font-size: 14px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #2ecc71;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(46, 213, 115, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(46, 213, 115, 0); }
            100% { box-shadow: 0 0 0 0 rgba(46, 213, 115, 0); }
        }
        
        .auto-dispatch-toggle {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .toggle-switch {
            position: relative;
            width: 60px;
            height: 30px;
            background: rgba(255,255,255,0.3);
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .toggle-switch.active {
            background: #2ecc71;
        }
        
        .toggle-slider {
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        
        .toggle-switch.active .toggle-slider {
            transform: translateX(30px);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .stats-row {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(--accent-color, #3498db);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--accent-color, #3498db);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin: 0 auto 15px;
            font-size: 24px;
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .stat-change {
            font-size: 12px;
            color: #27ae60;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .main-map-area {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .map-header {
            background: #34495e;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .map-title {
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        
        .map-controls {
            display: flex;
            gap: 10px;
        }
        
        .control-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            border-radius: 5px;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .control-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .dispatch-map {
            height: 500px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            position: relative;
            overflow: hidden;
        }
        
        .map-overlay {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.95);
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
            color: #333;
            min-width: 200px;
        }
        
        .overlay-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .overlay-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .grid-selector {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(255,255,255,0.95);
            padding: 15px;
            border-radius: 10px;
            color: #333;
        }
        
        .grid-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .grid-buttons {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 5px;
        }
        
        .grid-btn {
            padding: 8px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }
        
        .grid-btn:hover, .grid-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        
        .side-panel {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .order-queue {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .queue-header {
            background: #e74c3c;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .queue-title {
            font-weight: bold;
            display: flex;
            align-items: center;
        }
        
        .urgent-badge {
            background: #ff4757;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 10px;
            animation: urgentBlink 1s infinite;
        }
        
        @keyframes urgentBlink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.6; }
        }
        
        .queue-content {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .urgent-order {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            border-left: 4px solid #e74c3c;
            background: #fff5f5;
            position: relative;
        }
        
        .order-timer {
            position: absolute;
            top: 10px;
            right: 15px;
            background: #ff4757;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .order-info {
            margin-bottom: 10px;
        }
        
        .order-id {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .order-route {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .route-item {
            display: flex;
            align-items: center;
            margin-bottom: 3px;
        }
        
        .route-icon {
            margin-right: 8px;
            font-size: 12px;
        }
        
        .order-details {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #999;
            margin-bottom: 10px;
        }
        
        .dispatch-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            flex: 1;
            padding: 8px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-auto {
            background: #2ecc71;
            color: white;
        }
        
        .btn-manual {
            background: #3498db;
            color: white;
        }
        
        .available-vehicles {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .vehicles-header {
            background: #2ecc71;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .vehicles-content {
            max-height: 250px;
            overflow-y: auto;
        }
        
        .vehicle-item {
            padding: 12px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .vehicle-item:hover {
            background: #f8f9fa;
        }
        
        .vehicle-item.selected {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        
        .vehicle-info {
            display: flex;
            align-items: center;
        }
        
        .vehicle-icon {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: #2ecc71;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 12px;
            font-size: 14px;
        }
        
        .vehicle-details h4 {
            font-size: 14px;
            color: #2c3e50;
            margin-bottom: 3px;
        }
        
        .vehicle-details p {
            font-size: 12px;
            color: #666;
        }
        
        .vehicle-distance {
            text-align: right;
        }
        
        .distance-value {
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .distance-time {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <span class="logo-icon">📡</span>
                智能派单调度系统
            </div>
            <div class="dispatch-status">
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span>系统运行正常</span>
                </div>
                <div class="auto-dispatch-toggle">
                    <span>智能派单</span>
                    <div class="toggle-switch active" onclick="toggleAutoDispatch()">
                        <div class="toggle-slider"></div>
                    </div>
                </div>
            </div>
            <div class="user-info">
                <div class="user-avatar">👤</div>
                <span>派单员 - 张经理</span>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="stats-row">
            <div class="stat-card" style="--accent-color: #e74c3c;">
                <div class="stat-icon">⏰</div>
                <div class="stat-number">3</div>
                <div class="stat-label">待派订单</div>
                <div class="stat-change">紧急处理</div>
            </div>
            
            <div class="stat-card" style="--accent-color: #f39c12;">
                <div class="stat-icon">🚖</div>
                <div class="stat-number">45</div>
                <div class="stat-label">进行中订单</div>
                <div class="stat-change">+8 较上小时</div>
            </div>
            
            <div class="stat-card" style="--accent-color: #2ecc71;">
                <div class="stat-icon">✅</div>
                <div class="stat-number">128</div>
                <div class="stat-label">今日完成</div>
                <div class="stat-change">+15% 较昨日</div>
            </div>
            
            <div class="stat-card" style="--accent-color: #3498db;">
                <div class="stat-icon">🎯</div>
                <div class="stat-number">96%</div>
                <div class="stat-label">派单成功率</div>
                <div class="stat-change">+2% 较昨日</div>
            </div>
        </div>
        
        <div class="dashboard-grid">
            <div class="main-map-area">
                <div class="map-header">
                    <h3 class="map-title">🗺️ 实时调度地图</h3>
                    <div class="map-controls">
                        <button class="control-btn" onclick="refreshMap()">刷新</button>
                        <button class="control-btn" onclick="toggleHeatmap()">热力图</button>
                        <button class="control-btn" onclick="toggleTraffic()">路况</button>
                    </div>
                </div>
                
                <div class="dispatch-map">
                    🗺️ 智能派单地图
                    <br>
                    <small>集成高德/百度地图API，显示实时车辆位置和网格分布</small>
                    
                    <div class="map-overlay">
                        <div class="overlay-title">实时统计</div>
                        <div class="overlay-item">
                            <span>在线车辆:</span>
                            <span>156辆</span>
                        </div>
                        <div class="overlay-item">
                            <span>空闲车辆:</span>
                            <span>67辆</span>
                        </div>
                        <div class="overlay-item">
                            <span>载客车辆:</span>
                            <span>89辆</span>
                        </div>
                        <div class="overlay-item">
                            <span>平均响应:</span>
                            <span>2.3分钟</span>
                        </div>
                    </div>
                    
                    <div class="grid-selector">
                        <div class="grid-title">网格选择</div>
                        <div class="grid-buttons">
                            <button class="grid-btn active">A1</button>
                            <button class="grid-btn">A2</button>
                            <button class="grid-btn">A3</button>
                            <button class="grid-btn">B1</button>
                            <button class="grid-btn">B2</button>
                            <button class="grid-btn">B3</button>
                            <button class="grid-btn">C1</button>
                            <button class="grid-btn">C2</button>
                            <button class="grid-btn">C3</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="side-panel">
                <div class="order-queue">
                    <div class="queue-header">
                        <h3 class="queue-title">
                            🚨 紧急订单队列
                            <span class="urgent-badge">3</span>
                        </h3>
                    </div>
                    
                    <div class="queue-content">
                        <div class="urgent-order">
                            <div class="order-timer" id="timer1">45s</div>
                            <div class="order-info">
                                <div class="order-id">订单 #20241204001</div>
                                <div class="order-route">
                                    <div class="route-item">
                                        <span class="route-icon">🟢</span>
                                        <span>北京站东广场</span>
                                    </div>
                                    <div class="route-item">
                                        <span class="route-icon">🔴</span>
                                        <span>首都国际机场T3</span>
                                    </div>
                                </div>
                                <div class="order-details">
                                    <span>预约时间: 15:30</span>
                                    <span>预估费用: ¥85</span>
                                </div>
                            </div>
                            <div class="dispatch-actions">
                                <button class="action-btn btn-auto" onclick="autoDispatch('20241204001')">智能派单</button>
                                <button class="action-btn btn-manual" onclick="manualDispatch('20241204001')">手动派单</button>
                            </div>
                        </div>
                        
                        <div class="urgent-order">
                            <div class="order-timer" id="timer2">1m 23s</div>
                            <div class="order-info">
                                <div class="order-id">订单 #20241204002</div>
                                <div class="order-route">
                                    <div class="route-item">
                                        <span class="route-icon">🟢</span>
                                        <span>三里屯太古里</span>
                                    </div>
                                    <div class="route-item">
                                        <span class="route-icon">🔴</span>
                                        <span>国贸CBD</span>
                                    </div>
                                </div>
                                <div class="order-details">
                                    <span>即时用车</span>
                                    <span>预估费用: ¥32</span>
                                </div>
                            </div>
                            <div class="dispatch-actions">
                                <button class="action-btn btn-auto" onclick="autoDispatch('20241204002')">智能派单</button>
                                <button class="action-btn btn-manual" onclick="manualDispatch('20241204002')">手动派单</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="available-vehicles">
                    <div class="vehicles-header">
                        <h3>🚗 附近可用车辆</h3>
                        <span>67辆</span>
                    </div>
                    
                    <div class="vehicles-content">
                        <div class="vehicle-item" onclick="selectVehicle(this)">
                            <div class="vehicle-info">
                                <div class="vehicle-icon">🚕</div>
                                <div class="vehicle-details">
                                    <h4>京A12345 - 张师傅</h4>
                                    <p>⭐ 4.9分 | 空闲中</p>
                                </div>
                            </div>
                            <div class="vehicle-distance">
                                <div class="distance-value">0.8km</div>
                                <div class="distance-time">2分钟</div>
                            </div>
                        </div>
                        
                        <div class="vehicle-item" onclick="selectVehicle(this)">
                            <div class="vehicle-info">
                                <div class="vehicle-icon">🚖</div>
                                <div class="vehicle-details">
                                    <h4>京B67890 - 李师傅</h4>
                                    <p>⭐ 4.7分 | 空闲中</p>
                                </div>
                            </div>
                            <div class="vehicle-distance">
                                <div class="distance-value">1.2km</div>
                                <div class="distance-time">3分钟</div>
                            </div>
                        </div>
                        
                        <div class="vehicle-item" onclick="selectVehicle(this)">
                            <div class="vehicle-info">
                                <div class="vehicle-icon">🚗</div>
                                <div class="vehicle-details">
                                    <h4>京C11111 - 王师傅</h4>
                                    <p>⭐ 4.8分 | 空闲中</p>
                                </div>
                            </div>
                            <div class="vehicle-distance">
                                <div class="distance-value">1.5km</div>
                                <div class="distance-time">4分钟</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let autoDispatchEnabled = true;
        let selectedVehicle = null;
        
        function toggleAutoDispatch() {
            autoDispatchEnabled = !autoDispatchEnabled;
            const toggle = document.querySelector('.toggle-switch');
            
            if (autoDispatchEnabled) {
                toggle.classList.add('active');
                console.log('智能派单已开启');
            } else {
                toggle.classList.remove('active');
                console.log('智能派单已关闭');
            }
        }
        
        function autoDispatch(orderId) {
            alert(`正在为订单 ${orderId} 执行智能派单...`);
            // 模拟智能派单算法
            setTimeout(() => {
                alert(`订单 ${orderId} 智能派单成功！已分配给最优车辆`);
                removeOrderFromQueue(orderId);
            }, 1000);
        }
        
        function manualDispatch(orderId) {
            if (!selectedVehicle) {
                alert('请先选择一辆车辆');
                return;
            }
            
            const vehiclePlate = selectedVehicle.querySelector('h4').textContent.split(' - ')[0];
            alert(`订单 ${orderId} 已手动派给 ${vehiclePlate}`);
            removeOrderFromQueue(orderId);
        }
        
        function selectVehicle(element) {
            // 清除之前的选择
            document.querySelectorAll('.vehicle-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 选择当前车辆
            element.classList.add('selected');
            selectedVehicle = element;
        }
        
        function removeOrderFromQueue(orderId) {
            // 移除订单卡片
            const orderElements = document.querySelectorAll('.urgent-order');
            orderElements.forEach(order => {
                if (order.querySelector('.order-id').textContent.includes(orderId)) {
                    order.remove();
                }
            });
            
            // 更新队列计数
            updateQueueCount();
        }
        
        function updateQueueCount() {
            const remainingOrders = document.querySelectorAll('.urgent-order').length;
            document.querySelector('.urgent-badge').textContent = remainingOrders;
            
            // 更新统计卡片
            document.querySelector('.stat-card .stat-number').textContent = remainingOrders;
        }
        
        function refreshMap() {
            alert('正在刷新地图数据...');
        }
        
        function toggleHeatmap() {
            alert('切换热力图显示');
        }
        
        function toggleTraffic() {
            alert('切换路况信息');
        }
        
        // 模拟倒计时
        function startCountdown(elementId, seconds) {
            const element = document.getElementById(elementId);
            if (!element) return;
            
            const interval = setInterval(() => {
                seconds--;
                const minutes = Math.floor(seconds / 60);
                const remainingSeconds = seconds % 60;
                
                if (minutes > 0) {
                    element.textContent = `${minutes}m ${remainingSeconds}s`;
                } else {
                    element.textContent = `${remainingSeconds}s`;
                }
                
                if (seconds <= 0) {
                    clearInterval(interval);
                    element.textContent = '超时';
                    element.style.background = '#ff4757';
                }
            }, 1000);
        }
        
        // 页面加载时启动倒计时
        window.addEventListener('load', () => {
            startCountdown('timer1', 45);
            startCountdown('timer2', 83);
        });
        
        // 模拟实时数据更新
        setInterval(() => {
            updateRealTimeStats();
        }, 30000);
        
        function updateRealTimeStats() {
            console.log('更新实时统计数据');
            // 这里可以通过WebSocket或API更新实时数据
        }
    </script>
</body>
</html>
