<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>二维码管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .layout {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #27ae60;
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #2ecc71;
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-size: 18px;
            font-weight: bold;
        }
        
        .nav-menu {
            padding: 20px 0;
        }
        
        .nav-item {
            display: block;
            padding: 12px 20px;
            color: #a9dfbf;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .nav-item:hover,
        .nav-item.active {
            background: #2ecc71;
            color: white;
            border-left-color: #f39c12;
        }
        
        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .panel-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 25px;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .qr-generator {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .form-label {
            font-weight: 500;
            color: #2c3e50;
        }
        
        .form-input,
        .form-select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .qr-preview {
            width: 200px;
            height: 200px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px auto;
            background: #f8f9fa;
            font-size: 48px;
        }
        
        .qr-preview.generated {
            border-color: #27ae60;
            background: white;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #27ae60;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2ecc71;
        }
        
        .btn-secondary {
            background: #95a5a6;
            color: white;
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .qr-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .qr-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #ecf0f1;
            transition: background 0.3s ease;
        }
        
        .qr-item:hover {
            background: #f8f9fa;
        }
        
        .qr-info {
            flex: 1;
        }
        
        .qr-code {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .qr-details {
            font-size: 12px;
            color: #7f8c8d;
        }
        
        .qr-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .status-active {
            background: #d5f4e6;
            color: #27ae60;
        }
        
        .status-inactive {
            background: #fadbd8;
            color: #e74c3c;
        }
        
        .qr-actions {
            display: flex;
            gap: 5px;
        }
        
        .heatmap-container {
            height: 400px;
            background: #f8f9fa;
            border-radius: 8px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
            font-size: 16px;
            background-image: 
                radial-gradient(circle at 20% 30%, rgba(231, 76, 60, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 70% 20%, rgba(52, 152, 219, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 50% 70%, rgba(46, 204, 113, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(243, 156, 18, 0.3) 0%, transparent 50%);
        }
        
        .grid-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);
            background-size: 40px 40px;
            pointer-events: none;
        }
        
        .heat-point {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            pointer-events: none;
        }
        
        .heat-high {
            background: radial-gradient(circle, rgba(231, 76, 60, 0.8) 0%, rgba(231, 76, 60, 0.2) 70%, transparent 100%);
        }
        
        .heat-medium {
            background: radial-gradient(circle, rgba(243, 156, 18, 0.8) 0%, rgba(243, 156, 18, 0.2) 70%, transparent 100%);
        }
        
        .heat-low {
            background: radial-gradient(circle, rgba(46, 204, 113, 0.8) 0%, rgba(46, 204, 113, 0.2) 70%, transparent 100%);
        }
        
        .stats-row {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #7f8c8d;
        }
        
        .full-width {
            grid-column: 1 / -1;
        }
    </style>
</head>
<body>
    <div class="layout">
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    📱 二维码管理系统
                </div>
            </div>
            
            <div class="nav-menu">
                <a href="#" class="nav-item active" onclick="showQRManagement()">
                    📱 二维码管理
                </a>
                <a href="#" class="nav-item" onclick="showHeatmap()">
                    🔥 扫码热力图
                </a>
                <a href="#" class="nav-item" onclick="showStatistics()">
                    📊 扫码统计
                </a>
                <a href="#" class="nav-item" onclick="showSettings()">
                    ⚙️ 系统设置
                </a>
            </div>
        </nav>
        
        <main class="main-content">
            <div class="header">
                <h1 class="page-title">二维码管理</h1>
                <div class="user-info">
                    <span>管理员</span>
                    <button class="btn btn-danger" onclick="logout()">退出</button>
                </div>
            </div>
            
            <div class="stats-row">
                <div class="stat-card">
                    <div class="stat-value">1,234</div>
                    <div class="stat-label">总二维码数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">856</div>
                    <div class="stat-label">活跃二维码</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">5,678</div>
                    <div class="stat-label">今日扫码</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">89,012</div>
                    <div class="stat-label">总扫码次数</div>
                </div>
            </div>
            
            <div class="content-grid">
                <div class="panel-card">
                    <div class="card-title">生成二维码</div>
                    <form class="qr-generator" onsubmit="generateQR(event)">
                        <div class="form-group">
                            <label class="form-label">区域名称</label>
                            <input type="text" class="form-input" placeholder="请输入区域名称" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">商户类型</label>
                            <select class="form-select" required>
                                <option value="">请选择商户类型</option>
                                <option value="restaurant">餐饮</option>
                                <option value="hotel">酒店</option>
                                <option value="shopping">购物</option>
                                <option value="entertainment">娱乐</option>
                                <option value="transport">交通</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">地址</label>
                            <input type="text" class="form-input" placeholder="请输入详细地址" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">联系人</label>
                            <input type="text" class="form-input" placeholder="请输入联系人姓名">
                        </div>
                        
                        <div class="qr-preview" id="qrPreview">
                            📱
                        </div>
                        
                        <button type="submit" class="btn btn-primary">生成二维码</button>
                    </form>
                </div>
                
                <div class="panel-card">
                    <div class="card-title">二维码列表</div>
                    <div class="qr-list">
                        <div class="qr-item">
                            <div class="qr-info">
                                <div class="qr-code">QR001 - 王府井大街</div>
                                <div class="qr-details">餐饮 | 今日扫码: 45次 | 创建时间: 2024-12-04</div>
                            </div>
                            <div class="qr-actions">
                                <span class="qr-status status-active">启用</span>
                                <button class="btn btn-secondary" onclick="viewQR('QR001')">查看</button>
                                <button class="btn btn-warning" onclick="toggleQR('QR001')">禁用</button>
                            </div>
                        </div>
                        
                        <div class="qr-item">
                            <div class="qr-info">
                                <div class="qr-code">QR002 - 三里屯SOHO</div>
                                <div class="qr-details">购物 | 今日扫码: 78次 | 创建时间: 2024-12-03</div>
                            </div>
                            <div class="qr-actions">
                                <span class="qr-status status-active">启用</span>
                                <button class="btn btn-secondary" onclick="viewQR('QR002')">查看</button>
                                <button class="btn btn-warning" onclick="toggleQR('QR002')">禁用</button>
                            </div>
                        </div>
                        
                        <div class="qr-item">
                            <div class="qr-info">
                                <div class="qr-code">QR003 - 首都机场T3</div>
                                <div class="qr-details">交通 | 今日扫码: 156次 | 创建时间: 2024-12-02</div>
                            </div>
                            <div class="qr-actions">
                                <span class="qr-status status-inactive">禁用</span>
                                <button class="btn btn-secondary" onclick="viewQR('QR003')">查看</button>
                                <button class="btn btn-primary" onclick="toggleQR('QR003')">启用</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="panel-card full-width">
                <div class="card-title">扫码热力图</div>
                <div class="heatmap-container">
                    <div class="grid-overlay"></div>
                    🔥 扫码数据热力图
                    <br>
                    <small>颜色深度表示扫码频次，红色为高频区域</small>
                    
                    <!-- 模拟热力点 -->
                    <div class="heat-point heat-high" style="top: 20%; left: 30%;"></div>
                    <div class="heat-point heat-medium" style="top: 60%; left: 70%;"></div>
                    <div class="heat-point heat-low" style="top: 40%; left: 50%;"></div>
                    <div class="heat-point heat-high" style="top: 80%; left: 20%;"></div>
                    <div class="heat-point heat-medium" style="top: 30%; left: 80%;"></div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function showQRManagement() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '二维码管理';
        }
        
        function showHeatmap() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '扫码热力图';
        }
        
        function showStatistics() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '扫码统计';
        }
        
        function showSettings() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '系统设置';
        }
        
        function updateActiveNav(target) {
            document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
            target.classList.add('active');
        }
        
        function generateQR(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const qrData = {
                area: formData.get('area') || event.target.querySelector('input[placeholder="请输入区域名称"]').value,
                type: event.target.querySelector('select').value,
                address: formData.get('address') || event.target.querySelector('input[placeholder="请输入详细地址"]').value,
                contact: formData.get('contact') || event.target.querySelector('input[placeholder="请输入联系人姓名"]').value,
                qrId: 'QR' + String(Date.now()).slice(-6),
                timestamp: new Date().toISOString()
            };
            
            console.log('生成二维码:', qrData);
            
            // 更新预览
            const preview = document.getElementById('qrPreview');
            preview.innerHTML = `
                <div style="font-size: 12px; text-align: center;">
                    <div style="font-weight: bold;">${qrData.qrId}</div>
                    <div>${qrData.area}</div>
                </div>
            `;
            preview.classList.add('generated');
            
            alert(`二维码生成成功！\n编号：${qrData.qrId}\n区域：${qrData.area}`);
            
            // 重置表单
            event.target.reset();
            setTimeout(() => {
                preview.innerHTML = '📱';
                preview.classList.remove('generated');
            }, 3000);
        }
        
        function viewQR(qrId) {
            alert(`查看二维码详情：${qrId}`);
            // 这里可以打开详情弹窗或跳转到详情页
        }
        
        function toggleQR(qrId) {
            const confirmed = confirm(`确认切换二维码 ${qrId} 的状态？`);
            if (confirmed) {
                console.log('切换二维码状态:', qrId);
                alert(`二维码 ${qrId} 状态已切换`);
                // 这里可以更新UI状态
            }
        }
        
        function logout() {
            if (confirm('确认退出登录？')) {
                window.location.href = 'qrcode-login.html';
            }
        }
        
        // 页面加载时初始化
        window.addEventListener('load', () => {
            console.log('二维码管理系统初始化完成');
        });
    </script>
</body>
</html>
