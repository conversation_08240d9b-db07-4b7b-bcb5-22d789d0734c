<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>个人中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding-bottom: 80px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            display: flex;
            align-items: center;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            margin-right: 15px;
            cursor: pointer;
        }
        
        .header-title {
            font-size: 18px;
            font-weight: bold;
        }
        
        .profile-section {
            background: white;
            margin: 20px;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .profile-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .avatar-large {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
            margin-right: 20px;
        }
        
        .profile-info h2 {
            font-size: 22px;
            color: #333;
            margin-bottom: 5px;
        }
        
        .profile-info p {
            color: #666;
            font-size: 14px;
        }
        
        .edit-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            margin-left: auto;
        }
        
        .stats-row {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .menu-section {
            background: white;
            margin: 0 20px 20px;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .menu-item:last-child {
            border-bottom: none;
        }
        
        .menu-item:hover {
            background: #f8f9fa;
        }
        
        .menu-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            margin-right: 15px;
        }
        
        .icon-earnings {
            background: #e8f5e8;
            color: #2ed573;
        }
        
        .icon-wallet {
            background: #fff3cd;
            color: #ffa502;
        }
        
        .icon-recharge {
            background: #e3f2fd;
            color: #2196f3;
        }
        
        .icon-history {
            background: #f3e5f5;
            color: #9c27b0;
        }
        
        .icon-settings {
            background: #f5f5f5;
            color: #666;
        }
        
        .menu-content {
            flex: 1;
        }
        
        .menu-title {
            font-size: 16px;
            color: #333;
            margin-bottom: 3px;
        }
        
        .menu-subtitle {
            font-size: 12px;
            color: #666;
        }
        
        .menu-arrow {
            color: #ccc;
            font-size: 16px;
        }
        
        .menu-badge {
            background: #ff4757;
            color: white;
            border-radius: 10px;
            padding: 2px 8px;
            font-size: 12px;
            margin-right: 10px;
        }
        
        .wallet-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 20px;
        }
        
        .wallet-balance {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .wallet-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .quick-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .quick-btn {
            flex: 1;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 12px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }
        
        .quick-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            display: flex;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }
        
        .nav-item {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            background: none;
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 5px;
        }
        
        .nav-label {
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <button class="back-btn" onclick="goBack()">←</button>
            <h1 class="header-title">个人中心</h1>
        </div>
    </div>
    
    <div class="profile-section">
        <div class="profile-header">
            <div class="avatar-large">👨‍💼</div>
            <div class="profile-info">
                <h2>张师傅</h2>
                <p>工号：D001 | 从业5年</p>
                <p>车牌：京A12345 | 服务评分：4.9⭐</p>
            </div>
            <button class="edit-btn" onclick="editProfile()">编辑</button>
        </div>
        
        <div class="stats-row">
            <div class="stat-item">
                <div class="stat-value">1,256</div>
                <div class="stat-label">总订单数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">98.5%</div>
                <div class="stat-label">完成率</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">4.9</div>
                <div class="stat-label">平均评分</div>
            </div>
        </div>
    </div>
    
    <div class="menu-section">
        <div class="wallet-card">
            <div class="wallet-balance">¥1,286.50</div>
            <div class="wallet-label">账户余额</div>
            <div class="quick-actions">
                <button class="quick-btn" onclick="recharge()">充值</button>
                <button class="quick-btn" onclick="withdraw()">提现</button>
                <button class="quick-btn" onclick="viewTransactions()">明细</button>
            </div>
        </div>
    </div>
    
    <div class="menu-section">
        <div class="menu-item" onclick="viewEarnings()">
            <div class="menu-icon icon-earnings">💰</div>
            <div class="menu-content">
                <div class="menu-title">收入查询</div>
                <div class="menu-subtitle">今日收入 ¥286</div>
            </div>
            <span class="menu-arrow">›</span>
        </div>
        
        <div class="menu-item" onclick="viewWallet()">
            <div class="menu-icon icon-wallet">👛</div>
            <div class="menu-content">
                <div class="menu-title">账户钱包</div>
                <div class="menu-subtitle">余额管理</div>
            </div>
            <span class="menu-arrow">›</span>
        </div>
        
        <div class="menu-item" onclick="rechargeAgent()">
            <div class="menu-icon icon-recharge">🔄</div>
            <div class="menu-content">
                <div class="menu-title">充值代理</div>
                <div class="menu-subtitle">代理充值服务</div>
            </div>
            <span class="menu-arrow">›</span>
        </div>
        
        <div class="menu-item" onclick="viewOrderHistory()">
            <div class="menu-icon icon-history">📋</div>
            <div class="menu-content">
                <div class="menu-title">历史订单</div>
                <div class="menu-subtitle">查看所有订单记录</div>
            </div>
            <span class="menu-badge">3</span>
            <span class="menu-arrow">›</span>
        </div>
    </div>
    
    <div class="menu-section">
        <div class="menu-item" onclick="driverSettings()">
            <div class="menu-icon icon-settings">⚙️</div>
            <div class="menu-content">
                <div class="menu-title">设置</div>
                <div class="menu-subtitle">账户设置、隐私设置</div>
            </div>
            <span class="menu-arrow">›</span>
        </div>
        
        <div class="menu-item" onclick="helpCenter()">
            <div class="menu-icon icon-settings">❓</div>
            <div class="menu-content">
                <div class="menu-title">帮助中心</div>
                <div class="menu-subtitle">常见问题、联系客服</div>
            </div>
            <span class="menu-arrow">›</span>
        </div>
        
        <div class="menu-item" onclick="logout()">
            <div class="menu-icon icon-settings">🚪</div>
            <div class="menu-content">
                <div class="menu-title">退出登录</div>
                <div class="menu-subtitle">安全退出当前账户</div>
            </div>
            <span class="menu-arrow">›</span>
        </div>
    </div>
    
    <div class="bottom-nav">
        <button class="nav-item" onclick="goToDashboard()">
            <div class="nav-icon">🏠</div>
            <div class="nav-label">工作台</div>
        </button>
        <button class="nav-item active">
            <div class="nav-icon">👤</div>
            <div class="nav-label">个人中心</div>
        </button>
        <button class="nav-item" onclick="goToEarnings()">
            <div class="nav-icon">💰</div>
            <div class="nav-label">收入</div>
        </button>
        <button class="nav-item" onclick="goToSettings()">
            <div class="nav-icon">⚙️</div>
            <div class="nav-label">设置</div>
        </button>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }
        
        function editProfile() {
            alert('跳转到个人信息编辑页面');
            // window.location.href = 'driver-edit-profile.html';
        }
        
        function recharge() {
            alert('跳转到充值页面');
            // window.location.href = 'driver-recharge.html';
        }
        
        function withdraw() {
            alert('跳转到提现页面');
            // window.location.href = 'driver-withdraw.html';
        }
        
        function viewTransactions() {
            alert('跳转到交易明细页面');
            // window.location.href = 'driver-transactions.html';
        }
        
        function viewEarnings() {
            alert('跳转到收入查询页面');
            // window.location.href = 'driver-earnings.html';
        }
        
        function viewWallet() {
            alert('跳转到钱包管理页面');
            // window.location.href = 'driver-wallet.html';
        }
        
        function rechargeAgent() {
            alert('跳转到充值代理页面');
            // window.location.href = 'driver-recharge-agent.html';
        }
        
        function viewOrderHistory() {
            alert('跳转到历史订单页面');
            // window.location.href = 'driver-order-history.html';
        }
        
        function driverSettings() {
            alert('跳转到设置页面');
            // window.location.href = 'driver-settings.html';
        }
        
        function helpCenter() {
            alert('跳转到帮助中心');
            // window.location.href = 'driver-help.html';
        }
        
        function logout() {
            if (confirm('确认退出登录？')) {
                // 清除本地存储的登录信息
                localStorage.removeItem('driverToken');
                // 跳转到登录页
                window.location.href = 'driver-login.html';
            }
        }
        
        function goToDashboard() {
            window.location.href = 'driver-dashboard.html';
        }
        
        function goToEarnings() {
            window.location.href = 'driver-earnings.html';
        }
        
        function goToSettings() {
            window.location.href = 'driver-settings.html';
        }
        
        // 页面加载时获取用户信息
        window.addEventListener('load', () => {
            // 模拟获取用户数据
            loadUserProfile();
        });
        
        function loadUserProfile() {
            // 模拟API调用
            const userData = {
                name: '张师傅',
                id: 'D001',
                experience: '5年',
                plate: '京A12345',
                rating: 4.9,
                totalOrders: 1256,
                completionRate: 98.5,
                balance: 1286.50,
                todayEarnings: 286
            };
            
            console.log('用户数据加载完成:', userData);
        }
    </script>
</body>
</html>
