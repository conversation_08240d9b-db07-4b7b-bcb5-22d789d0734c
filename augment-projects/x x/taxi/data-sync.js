// 出租车管理系统 - 数据同步和状态管理

// 全局数据存储
const SystemData = {
    // 司机数据
    drivers: new Map(),
    // 乘客数据
    passengers: new Map(),
    // 订单数据
    orders: new Map(),
    // 车辆数据
    vehicles: new Map(),
    // 系统状态
    systemStatus: {
        onlineDrivers: 0,
        activeOrders: 0,
        totalRevenue: 0,
        systemLoad: 0,
        lastUpdate: null
    }
};

// 数据同步管理器
class DataSyncManager {
    constructor() {
        this.syncInterval = 30000; // 30秒同步一次
        this.isOnline = navigator.onLine;
        this.syncQueue = [];
        this.listeners = new Map();
        
        this.init();
    }
    
    init() {
        // 监听网络状态
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.processSyncQueue();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
        });
        
        // 启动定时同步
        this.startPeriodicSync();
        
        // 页面卸载时保存数据
        window.addEventListener('beforeunload', () => {
            this.saveToLocalStorage();
        });
        
        // 页面加载时恢复数据
        this.loadFromLocalStorage();
    }
    
    // 启动定时同步
    startPeriodicSync() {
        setInterval(() => {
            if (this.isOnline) {
                this.syncWithServer();
            }
        }, this.syncInterval);
    }
    
    // 与服务器同步数据
    async syncWithServer() {
        try {
            // 模拟API调用
            const response = await this.mockApiCall('/api/sync', {
                method: 'POST',
                body: JSON.stringify({
                    lastSync: SystemData.systemStatus.lastUpdate,
                    pendingChanges: this.syncQueue
                })
            });
            
            if (response.success) {
                // 更新本地数据
                this.updateLocalData(response.data);
                // 清空同步队列
                this.syncQueue = [];
                // 更新最后同步时间
                SystemData.systemStatus.lastUpdate = new Date().toISOString();
                
                // 通知监听器
                this.notifyListeners('sync', response.data);
            }
        } catch (error) {
            console.error('数据同步失败:', error);
            this.handleSyncError(error);
        }
    }
    
    // 模拟API调用
    async mockApiCall(url, options = {}) {
        return new Promise((resolve) => {
            setTimeout(() => {
                // 模拟服务器响应
                const mockData = this.generateMockData();
                resolve({
                    success: true,
                    data: mockData,
                    timestamp: new Date().toISOString()
                });
            }, Math.random() * 1000 + 500); // 500-1500ms延迟
        });
    }
    
    // 生成模拟数据
    generateMockData() {
        return {
            drivers: this.generateDriverData(),
            orders: this.generateOrderData(),
            systemMetrics: this.generateSystemMetrics()
        };
    }
    
    // 生成司机数据
    generateDriverData() {
        const drivers = [];
        const plateNumbers = ['京A12345', '京B67890', '京C11111', '京D22222', '京E33333'];
        const names = ['张师傅', '李师傅', '王师傅', '赵师傅', '刘师傅'];
        
        plateNumbers.forEach((plate, index) => {
            drivers.push({
                id: `driver_${index + 1}`,
                name: names[index],
                plateNumber: plate,
                status: Math.random() > 0.3 ? 'online' : 'offline',
                location: {
                    lat: 39.9042 + (Math.random() - 0.5) * 0.1,
                    lng: 116.4074 + (Math.random() - 0.5) * 0.1
                },
                todayOrders: Math.floor(Math.random() * 15) + 1,
                todayEarnings: Math.floor(Math.random() * 500) + 100,
                rating: (4.5 + Math.random() * 0.5).toFixed(1),
                lastUpdate: new Date().toISOString()
            });
        });
        
        return drivers;
    }
    
    // 生成订单数据
    generateOrderData() {
        const orders = [];
        const statuses = ['pending', 'assigned', 'ongoing', 'completed', 'cancelled'];
        
        for (let i = 1; i <= 10; i++) {
            orders.push({
                id: `order_${Date.now()}_${i}`,
                passengerId: `passenger_${Math.floor(Math.random() * 100) + 1}`,
                driverId: Math.random() > 0.5 ? `driver_${Math.floor(Math.random() * 5) + 1}` : null,
                status: statuses[Math.floor(Math.random() * statuses.length)],
                pickup: {
                    address: '北京站东广场',
                    lat: 39.9042,
                    lng: 116.4074
                },
                destination: {
                    address: '首都国际机场T3航站楼',
                    lat: 40.0799,
                    lng: 116.6031
                },
                fare: Math.floor(Math.random() * 100) + 20,
                createTime: new Date(Date.now() - Math.random() * 3600000).toISOString(),
                estimatedDuration: Math.floor(Math.random() * 60) + 15
            });
        }
        
        return orders;
    }
    
    // 生成系统指标
    generateSystemMetrics() {
        return {
            onlineDrivers: Math.floor(Math.random() * 50) + 100,
            activePassengers: Math.floor(Math.random() * 1000) + 2000,
            pendingOrders: Math.floor(Math.random() * 10) + 1,
            completedOrdersToday: Math.floor(Math.random() * 500) + 1000,
            totalRevenueToday: Math.floor(Math.random() * 50000) + 30000,
            systemLoad: Math.floor(Math.random() * 40) + 60,
            responseTime: (Math.random() * 2 + 1).toFixed(1),
            successRate: (95 + Math.random() * 5).toFixed(1)
        };
    }
    
    // 更新本地数据
    updateLocalData(serverData) {
        if (serverData.drivers) {
            serverData.drivers.forEach(driver => {
                SystemData.drivers.set(driver.id, driver);
            });
        }
        
        if (serverData.orders) {
            serverData.orders.forEach(order => {
                SystemData.orders.set(order.id, order);
            });
        }
        
        if (serverData.systemMetrics) {
            Object.assign(SystemData.systemStatus, serverData.systemMetrics);
        }
        
        // 保存到本地存储
        this.saveToLocalStorage();
    }
    
    // 添加到同步队列
    addToSyncQueue(action, data) {
        this.syncQueue.push({
            action,
            data,
            timestamp: new Date().toISOString()
        });
        
        // 如果在线，立即尝试同步
        if (this.isOnline) {
            this.syncWithServer();
        }
    }
    
    // 处理同步错误
    handleSyncError(error) {
        console.error('同步错误:', error);
        
        // 可以在这里添加错误处理逻辑
        // 比如显示离线提示、重试机制等
        this.notifyListeners('syncError', error);
    }
    
    // 保存到本地存储
    saveToLocalStorage() {
        try {
            const dataToSave = {
                drivers: Array.from(SystemData.drivers.entries()),
                passengers: Array.from(SystemData.passengers.entries()),
                orders: Array.from(SystemData.orders.entries()),
                vehicles: Array.from(SystemData.vehicles.entries()),
                systemStatus: SystemData.systemStatus,
                syncQueue: this.syncQueue
            };
            
            localStorage.setItem('taxiSystemData', JSON.stringify(dataToSave));
        } catch (error) {
            console.error('保存到本地存储失败:', error);
        }
    }
    
    // 从本地存储加载
    loadFromLocalStorage() {
        try {
            const savedData = localStorage.getItem('taxiSystemData');
            if (savedData) {
                const data = JSON.parse(savedData);
                
                SystemData.drivers = new Map(data.drivers || []);
                SystemData.passengers = new Map(data.passengers || []);
                SystemData.orders = new Map(data.orders || []);
                SystemData.vehicles = new Map(data.vehicles || []);
                SystemData.systemStatus = data.systemStatus || SystemData.systemStatus;
                this.syncQueue = data.syncQueue || [];
                
                console.log('从本地存储恢复数据成功');
            }
        } catch (error) {
            console.error('从本地存储加载失败:', error);
        }
    }
    
    // 添加数据监听器
    addListener(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }
    
    // 移除监听器
    removeListener(event, callback) {
        if (this.listeners.has(event)) {
            const callbacks = this.listeners.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }
    
    // 通知监听器
    notifyListeners(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('监听器回调错误:', error);
                }
            });
        }
    }
    
    // 处理同步队列
    processSyncQueue() {
        if (this.syncQueue.length > 0 && this.isOnline) {
            this.syncWithServer();
        }
    }
    
    // 获取数据
    getData(type, id = null) {
        const dataMap = SystemData[type];
        if (!dataMap) return null;
        
        if (id) {
            return dataMap.get(id);
        }
        
        return Array.from(dataMap.values());
    }
    
    // 更新数据
    updateData(type, id, data) {
        const dataMap = SystemData[type];
        if (!dataMap) return false;
        
        dataMap.set(id, { ...dataMap.get(id), ...data, lastUpdate: new Date().toISOString() });
        
        // 添加到同步队列
        this.addToSyncQueue('update', { type, id, data });
        
        // 通知监听器
        this.notifyListeners(`${type}Updated`, { id, data });
        
        return true;
    }
    
    // 删除数据
    deleteData(type, id) {
        const dataMap = SystemData[type];
        if (!dataMap) return false;
        
        const deleted = dataMap.delete(id);
        
        if (deleted) {
            // 添加到同步队列
            this.addToSyncQueue('delete', { type, id });
            
            // 通知监听器
            this.notifyListeners(`${type}Deleted`, { id });
        }
        
        return deleted;
    }
    
    // 获取系统状态
    getSystemStatus() {
        return { ...SystemData.systemStatus };
    }
}

// 创建全局数据同步管理器实例
const dataSyncManager = new DataSyncManager();

// 导出给其他脚本使用
window.TaxiSystemData = {
    manager: dataSyncManager,
    data: SystemData,
    
    // 便捷方法
    getDrivers: () => dataSyncManager.getData('drivers'),
    getOrders: () => dataSyncManager.getData('orders'),
    getSystemStatus: () => dataSyncManager.getSystemStatus(),
    
    updateDriver: (id, data) => dataSyncManager.updateData('drivers', id, data),
    updateOrder: (id, data) => dataSyncManager.updateData('orders', id, data),
    
    onDataUpdate: (callback) => dataSyncManager.addListener('sync', callback),
    onSyncError: (callback) => dataSyncManager.addListener('syncError', callback)
};

console.log('数据同步管理器已初始化');
