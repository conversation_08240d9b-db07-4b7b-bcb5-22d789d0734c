<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>出租车司机端 - 欢迎页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            overflow: hidden;
        }
        
        .welcome-container {
            text-align: center;
            animation: fadeInUp 1s ease-out;
        }
        
        .logo {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            margin: 0 auto 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            animation: pulse 2s infinite;
        }
        
        .app-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .app-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 50px;
        }
        
        .countdown {
            position: absolute;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 18px;
            opacity: 0.8;
        }
        
        .countdown-number {
            font-size: 24px;
            font-weight: bold;
            color: #FFD700;
        }
        
        .skip-btn {
            position: absolute;
            top: 50px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .skip-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }
        
        .loading-dots {
            display: inline-block;
            margin-left: 10px;
        }
        
        .loading-dots span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: white;
            margin: 0 2px;
            animation: loading 1.4s infinite ease-in-out both;
        }
        
        .loading-dots span:nth-child(1) { animation-delay: -0.32s; }
        .loading-dots span:nth-child(2) { animation-delay: -0.16s; }
        
        @keyframes loading {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <button class="skip-btn" onclick="skipWelcome()">跳过</button>
    
    <div class="welcome-container">
        <div class="logo">🚕</div>
        <h1 class="app-title">出租车司机端</h1>
        <p class="app-subtitle">智能派单 · 便捷接单 · 高效服务</p>
        
        <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
        </div>
    </div>
    
    <div class="countdown">
        <span class="countdown-number" id="countdownNumber">3</span> 秒后自动跳转
    </div>

    <script>
        let countdown = 3;
        const countdownElement = document.getElementById('countdownNumber');
        
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                goToLogin();
            }
        }, 1000);
        
        function skipWelcome() {
            clearInterval(timer);
            goToLogin();
        }
        
        function goToLogin() {
            // 添加淡出动画
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease';
            
            setTimeout(() => {
                window.location.href = 'driver-login.html';
            }, 500);
        }
        
        // 检查权限状态
        function checkPermissions() {
            // 模拟权限检查
            if ('geolocation' in navigator) {
                navigator.geolocation.getCurrentPosition(
                    () => console.log('定位权限已获取'),
                    () => console.log('定位权限被拒绝')
                );
            }
        }
        
        // 页面加载完成后检查权限
        window.addEventListener('load', checkPermissions);
    </script>
</body>
</html>
