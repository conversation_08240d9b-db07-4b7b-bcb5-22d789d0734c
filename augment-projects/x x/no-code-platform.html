<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>零代码搭建平台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            gap: 20px;
            height: calc(100vh - 80px);
        }
        
        .sidebar {
            width: 280px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            padding: 20px;
            overflow-y: auto;
        }
        
        .main-content {
            flex: 1;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            display: flex;
            flex-direction: column;
        }
        
        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 12px 12px 0 0;
            padding: 0;
            margin: 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            background: transparent;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            color: #6c757d;
            transition: all 0.3s;
            border-radius: 12px 12px 0 0;
        }
        
        .nav-tab.active {
            background: white;
            color: #667eea;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
        }
        
        .tab-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .content-panel {
            display: none;
        }
        
        .content-panel.active {
            display: block;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-outline {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
        }
        
        .btn-outline:hover {
            background: #667eea;
            color: white;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .action-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
        }
        
        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .action-card i {
            font-size: 32px;
            margin-bottom: 10px;
            display: block;
        }
        
        .action-card h3 {
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .action-card p {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .service-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .service-card:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .service-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        
        .service-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .service-category {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
        }
        
        .service-description {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .service-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #6c757d;
        }
        
        .service-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .component-list {
            margin-bottom: 20px;
        }
        
        .component-category {
            margin-bottom: 15px;
        }
        
        .category-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .component-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin-bottom: 5px;
            background: #f8f9fa;
            border-radius: 8px;
            cursor: grab;
            transition: all 0.3s;
        }
        
        .component-item:hover {
            background: #e9ecef;
        }
        
        .component-item i {
            margin-right: 10px;
            color: #667eea;
        }
        
        .search-box {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 20px;
        }
        
        .filter-group {
            margin-bottom: 20px;
        }
        
        .filter-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .filter-options {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .filter-tag {
            padding: 6px 12px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .filter-tag.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }
        
        .workflow-canvas {
            background: #f8f9fa;
            border: 2px dashed #e9ecef;
            border-radius: 12px;
            min-height: 400px;
            padding: 20px;
            position: relative;
            margin-top: 20px;
        }
        
        .workflow-node {
            background: white;
            border: 2px solid #667eea;
            border-radius: 8px;
            padding: 15px;
            position: absolute;
            cursor: move;
            min-width: 120px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .node-start {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }
        
        .node-end {
            background: #dc3545;
            color: white;
            border-color: #dc3545;
        }
        
        .node-process {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .node-decision {
            background: #ffc107;
            color: #212529;
            border-color: #ffc107;
            transform: rotate(45deg);
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .close {
            font-size: 24px;
            cursor: pointer;
            color: #6c757d;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-cogs"></i> 零代码搭建平台
            </div>
            <div class="user-info">
                <span>欢迎，管理员</span>
                <i class="fas fa-user-circle" style="font-size: 24px;"></i>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="sidebar">
            <div class="section-title">
                <i class="fas fa-puzzle-piece"></i>
                组件库
            </div>
            
            <div class="component-list">
                <div class="component-category">
                    <div class="category-title">基础信息类</div>
                    <div class="component-item" draggable="true" data-type="input">
                        <i class="fas fa-edit"></i>
                        单行文本
                    </div>
                    <div class="component-item" draggable="true" data-type="textarea">
                        <i class="fas fa-align-left"></i>
                        多行文本
                    </div>
                    <div class="component-item" draggable="true" data-type="number">
                        <i class="fas fa-hashtag"></i>
                        数字输入
                    </div>
                    <div class="component-item" draggable="true" data-type="date">
                        <i class="fas fa-calendar"></i>
                        日期选择
                    </div>
                    <div class="component-item" draggable="true" data-type="select">
                        <i class="fas fa-list"></i>
                        下拉选择
                    </div>
                    <div class="component-item" draggable="true" data-type="radio">
                        <i class="fas fa-dot-circle"></i>
                        单选框
                    </div>
                    <div class="component-item" draggable="true" data-type="checkbox">
                        <i class="fas fa-check-square"></i>
                        复选框
                    </div>
                </div>
                
                <div class="component-category">
                    <div class="category-title">数据引用类</div>
                    <div class="component-item" draggable="true" data-type="user-select">
                        <i class="fas fa-users"></i>
                        人员选择
                    </div>
                    <div class="component-item" draggable="true" data-type="dept-select">
                        <i class="fas fa-sitemap"></i>
                        部门选择
                    </div>
                    <div class="component-item" draggable="true" data-type="data-table">
                        <i class="fas fa-table"></i>
                        数据表格
                    </div>
                    <div class="component-item" draggable="true" data-type="file-upload">
                        <i class="fas fa-upload"></i>
                        文件上传
                    </div>
                </div>
                
                <div class="component-category">
                    <div class="category-title">高级组件</div>
                    <div class="component-item" draggable="true" data-type="signature">
                        <i class="fas fa-signature"></i>
                        电子签名
                    </div>
                    <div class="component-item" draggable="true" data-type="location">
                        <i class="fas fa-map-marker-alt"></i>
                        位置选择
                    </div>
                    <div class="component-item" draggable="true" data-type="qrcode">
                        <i class="fas fa-qrcode"></i>
                        二维码
                    </div>
                </div>
            </div>
            
            <div class="section-title">
                <i class="fas fa-project-diagram"></i>
                流程节点
            </div>
            
            <div class="component-list">
                <div class="component-item" draggable="true" data-type="start-node">
                    <i class="fas fa-play"></i>
                    开始节点
                </div>
                <div class="component-item" draggable="true" data-type="approval-node">
                    <i class="fas fa-user-check"></i>
                    审批节点
                </div>
                <div class="component-item" draggable="true" data-type="condition-node">
                    <i class="fas fa-code-branch"></i>
                    条件分支
                </div>
                <div class="component-item" draggable="true" data-type="parallel-node">
                    <i class="fas fa-code-branch"></i>
                    并行节点
                </div>
                <div class="component-item" draggable="true" data-type="end-node">
                    <i class="fas fa-stop"></i>
                    结束节点
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="showTab('dashboard')">
                    <i class="fas fa-tachometer-alt"></i> 工作台
                </button>
                <button class="nav-tab" onclick="showTab('services')">
                    <i class="fas fa-cog"></i> 服务管理
                </button>
                <button class="nav-tab" onclick="showTab('form-design')">
                    <i class="fas fa-edit"></i> 表单设计
                </button>
                <button class="nav-tab" onclick="showTab('workflow-design')">
                    <i class="fas fa-project-diagram"></i> 流程设计
                </button>
                <button class="nav-tab" onclick="showTab('templates')">
                    <i class="fas fa-layer-group"></i> 模板库
                </button>
            </div>

            <div class="tab-content">
                <!-- 工作台 -->
                <div id="dashboard" class="content-panel active">
                    <div class="section-title">
                        <i class="fas fa-rocket"></i>
                        快速开始
                    </div>

                    <div class="quick-actions">
                        <div class="action-card" onclick="createBlankForm()">
                            <i class="fas fa-file-alt"></i>
                            <h3>创建空白表单</h3>
                            <p>从零开始设计表单</p>
                        </div>
                        <div class="action-card" onclick="createBlankWorkflow()">
                            <i class="fas fa-sitemap"></i>
                            <h3>创建空白流程</h3>
                            <p>设计业务流程</p>
                        </div>
                        <div class="action-card" onclick="showTab('templates')">
                            <i class="fas fa-magic"></i>
                            <h3>使用模板</h3>
                            <p>快速创建常用服务</p>
                        </div>
                        <div class="action-card" onclick="showTab('services')">
                            <i class="fas fa-list"></i>
                            <h3>管理服务</h3>
                            <p>查看和编辑已有服务</p>
                        </div>
                    </div>

                    <div class="section-title">
                        <i class="fas fa-chart-bar"></i>
                        数据统计
                    </div>

                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value">127</div>
                            <div class="stat-label">已创建服务</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">8</div>
                            <div class="stat-label">应用分类</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">1,234</div>
                            <div class="stat-label">本月使用次数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">95.6%</div>
                            <div class="stat-label">服务可用率</div>
                        </div>
                    </div>

                    <div class="section-title">
                        <i class="fas fa-clock"></i>
                        最近创建的服务
                    </div>

                    <div class="service-grid">
                        <div class="service-card">
                            <div class="service-header">
                                <div>
                                    <div class="service-title">重点事项督办</div>
                                    <div class="service-category">办公类</div>
                                </div>
                                <div style="color: #28a745;">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                            </div>
                            <div class="service-description">
                                用于重点工作事项的督办和跟踪管理，支持多级审批和进度监控。
                            </div>
                            <div class="service-meta">
                                <span>创建时间: 2024-01-15</span>
                                <span>使用次数: 45</span>
                            </div>
                            <div class="service-actions">
                                <button class="btn btn-primary" onclick="editService('督办')">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button class="btn btn-success" onclick="useService('督办')">
                                    <i class="fas fa-play"></i> 使用
                                </button>
                            </div>
                        </div>

                        <div class="service-card">
                            <div class="service-header">
                                <div>
                                    <div class="service-title">课程缓考申请</div>
                                    <div class="service-category">教务类</div>
                                </div>
                                <div style="color: #28a745;">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                            </div>
                            <div class="service-description">
                                学生因特殊情况无法按时参加考试时的缓考申请流程。
                            </div>
                            <div class="service-meta">
                                <span>创建时间: 2024-01-12</span>
                                <span>使用次数: 23</span>
                            </div>
                            <div class="service-actions">
                                <button class="btn btn-primary" onclick="editService('缓考')">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button class="btn btn-success" onclick="useService('缓考')">
                                    <i class="fas fa-play"></i> 使用
                                </button>
                            </div>
                        </div>

                        <div class="service-card">
                            <div class="service-header">
                                <div>
                                    <div class="service-title">会议室申请</div>
                                    <div class="service-category">教保类</div>
                                </div>
                                <div style="color: #ffc107;">
                                    <i class="fas fa-clock"></i>
                                </div>
                            </div>
                            <div class="service-description">
                                会议室预约和使用申请，支持时间冲突检测和自动通知。
                            </div>
                            <div class="service-meta">
                                <span>创建时间: 2024-01-10</span>
                                <span>使用次数: 67</span>
                            </div>
                            <div class="service-actions">
                                <button class="btn btn-primary" onclick="editService('会议室')">
                                    <i class="fas fa-edit"></i> 编辑
                                </button>
                                <button class="btn btn-success" onclick="useService('会议室')">
                                    <i class="fas fa-play"></i> 使用
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 服务管理 -->
                <div id="services" class="content-panel">
                    <div class="section-title">
                        <i class="fas fa-cog"></i>
                        服务管理
                        <div style="margin-left: auto; display: flex; gap: 10px;">
                            <button class="btn btn-primary" onclick="createNewService()">
                                <i class="fas fa-plus"></i> 新建服务
                            </button>
                            <button class="btn btn-success" onclick="exportServices()">
                                <i class="fas fa-download"></i> 导出服务包
                            </button>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        当前平台支持90+个服务流程，涵盖办公、教务、教保、科研、政工、纪检、安管、供保等8大类应用。
                    </div>

                    <div style="display: flex; gap: 20px; margin-bottom: 20px;">
                        <input type="text" class="search-box" placeholder="搜索服务名称、描述..." style="flex: 1;">
                        <button class="btn btn-outline">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                    </div>

                    <div class="filter-group">
                        <div class="filter-title">应用分类</div>
                        <div class="filter-options">
                            <div class="filter-tag active" data-category="all">全部</div>
                            <div class="filter-tag" data-category="office">办公类 (8+)</div>
                            <div class="filter-tag" data-category="academic">教务类 (9+)</div>
                            <div class="filter-tag" data-category="support">教保类 (18+)</div>
                            <div class="filter-tag" data-category="research">科研类 (16+)</div>
                            <div class="filter-tag" data-category="personnel">政工类 (12+)</div>
                            <div class="filter-tag" data-category="discipline">纪检类 (2+)</div>
                            <div class="filter-tag" data-category="security">安管类 (11+)</div>
                            <div class="filter-tag" data-category="logistics">供保类 (13+)</div>
                        </div>
                    </div>

                    <div class="filter-group">
                        <div class="filter-title">服务状态</div>
                        <div class="filter-options">
                            <div class="filter-tag active" data-status="all">全部</div>
                            <div class="filter-tag" data-status="published">已发布</div>
                            <div class="filter-tag" data-status="draft">草稿</div>
                            <div class="filter-tag" data-status="disabled">已停用</div>
                        </div>
                    </div>

                    <div id="servicesList" class="service-grid">
                        <!-- 服务列表将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 表单设计 -->
                <div id="form-design" class="content-panel">
                    <div class="section-title">
                        <i class="fas fa-edit"></i>
                        表单设计器
                        <div style="margin-left: auto; display: flex; gap: 10px;">
                            <button class="btn btn-outline" onclick="previewForm()">
                                <i class="fas fa-eye"></i> 预览
                            </button>
                            <button class="btn btn-success" onclick="saveForm()">
                                <i class="fas fa-save"></i> 保存
                            </button>
                            <button class="btn btn-primary" onclick="publishForm()">
                                <i class="fas fa-rocket"></i> 发布
                            </button>
                        </div>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-lightbulb"></i>
                        拖拽左侧组件到设计区域，支持基础信息类、数据引用类等多种组件类型。
                    </div>

                    <div style="display: flex; gap: 20px; height: 600px;">
                        <div style="flex: 1; background: #f8f9fa; border: 2px dashed #e9ecef; border-radius: 12px; padding: 20px; overflow-y: auto;" id="formDesignArea">
                            <div style="text-align: center; color: #6c757d; margin-top: 100px;">
                                <i class="fas fa-mouse-pointer" style="font-size: 48px; margin-bottom: 20px;"></i>
                                <h3>拖拽组件到此处开始设计表单</h3>
                                <p>支持多种类型组件配置，包括基础信息类、数据引用等高级分类</p>
                            </div>
                        </div>

                        <div style="width: 300px; background: white; border: 1px solid #e9ecef; border-radius: 12px; padding: 20px;">
                            <div class="section-title" style="font-size: 16px; margin-bottom: 15px;">
                                <i class="fas fa-cog"></i>
                                属性配置
                            </div>

                            <div id="propertyPanel">
                                <div style="text-align: center; color: #6c757d; margin-top: 50px;">
                                    <i class="fas fa-hand-pointer" style="font-size: 32px; margin-bottom: 15px;"></i>
                                    <p>选择组件查看属性配置</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 流程设计 -->
                <div id="workflow-design" class="content-panel">
                    <div class="section-title">
                        <i class="fas fa-project-diagram"></i>
                        流程设计器
                        <div style="margin-left: auto; display: flex; gap: 10px;">
                            <button class="btn btn-outline" onclick="validateWorkflow()">
                                <i class="fas fa-check"></i> 验证
                            </button>
                            <button class="btn btn-success" onclick="saveWorkflow()">
                                <i class="fas fa-save"></i> 保存
                            </button>
                            <button class="btn btn-primary" onclick="deployWorkflow()">
                                <i class="fas fa-rocket"></i> 部署
                            </button>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        支持顺序、分支、条件路由、并行、串行、会签等多种流程模式。条件分支支持设置多条并行路径，且可在条件分支下再次创建子分支。
                    </div>

                    <div class="workflow-canvas" id="workflowCanvas">
                        <div style="text-align: center; color: #6c757d; margin-top: 150px;">
                            <i class="fas fa-project-diagram" style="font-size: 48px; margin-bottom: 20px;"></i>
                            <h3>拖拽流程节点到此处开始设计流程</h3>
                            <p>支持审批节点基础设置、多种审批操作按钮配置、审批人员类型配置等</p>
                        </div>
                    </div>
                </div>

                <!-- 模板库 -->
                <div id="templates" class="content-panel">
                    <div class="section-title">
                        <i class="fas fa-layer-group"></i>
                        模板库
                        <div style="margin-left: auto;">
                            <button class="btn btn-primary" onclick="createTemplate()">
                                <i class="fas fa-plus"></i> 创建模板
                            </button>
                        </div>
                    </div>

                    <div class="alert alert-success">
                        <i class="fas fa-magic"></i>
                        预置10个校内高频服务应用模板，支持通过模板快速创建服务，大幅提升搭建效率。
                    </div>

                    <div class="filter-group">
                        <div class="filter-title">模板分类</div>
                        <div class="filter-options">
                            <div class="filter-tag active" data-template="all">全部模板</div>
                            <div class="filter-tag" data-template="office">办公类模板</div>
                            <div class="filter-tag" data-template="academic">教务类模板</div>
                            <div class="filter-tag" data-template="support">教保类模板</div>
                            <div class="filter-tag" data-template="research">科研类模板</div>
                            <div class="filter-tag" data-template="popular">热门模板</div>
                        </div>
                    </div>

                    <div class="service-grid" id="templatesList">
                        <!-- 模板列表将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="serviceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">新建服务</h3>
                <span class="close" onclick="closeModal('serviceModal')">&times;</span>
            </div>

            <form id="serviceForm">
                <div class="form-group">
                    <label class="form-label">服务名称</label>
                    <input type="text" class="form-control" id="serviceName" placeholder="请输入服务名称">
                </div>

                <div class="form-group">
                    <label class="form-label">服务描述</label>
                    <textarea class="form-control" id="serviceDescription" rows="3" placeholder="请输入服务描述"></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label">应用分类</label>
                    <select class="form-control" id="serviceCategory">
                        <option value="">请选择分类</option>
                        <option value="office">办公类</option>
                        <option value="academic">教务类</option>
                        <option value="support">教保类</option>
                        <option value="research">科研类</option>
                        <option value="personnel">政工类</option>
                        <option value="discipline">纪检类</option>
                        <option value="security">安管类</option>
                        <option value="logistics">供保类</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">子管理员</label>
                    <input type="text" class="form-control" id="subAdmin" placeholder="请选择子管理员">
                </div>

                <div class="form-group">
                    <label class="form-label">填报次数限制</label>
                    <select class="form-control" id="submitLimit">
                        <option value="unlimited">不限制</option>
                        <option value="once">仅一次</option>
                        <option value="daily">每日一次</option>
                        <option value="weekly">每周一次</option>
                        <option value="monthly">每月一次</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">菜单授权</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="auth1" value="create">
                            <label for="auth1">创建权限</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="auth2" value="edit">
                            <label for="auth2">编辑权限</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="auth3" value="delete">
                            <label for="auth3">删除权限</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="auth4" value="view">
                            <label for="auth4">查看权限</label>
                        </div>
                    </div>
                </div>

                <div style="text-align: right; margin-top: 30px;">
                    <button type="button" class="btn btn-outline" onclick="closeModal('serviceModal')">取消</button>
                    <button type="submit" class="btn btn-primary">确定</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 审批节点配置模态框 -->
    <div id="approvalModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>审批节点配置</h3>
                <span class="close" onclick="closeModal('approvalModal')">&times;</span>
            </div>

            <form id="approvalForm">
                <div class="form-group">
                    <label class="form-label">节点ID</label>
                    <input type="text" class="form-control" id="nodeId" placeholder="请输入节点ID">
                </div>

                <div class="form-group">
                    <label class="form-label">节点名称</label>
                    <input type="text" class="form-control" id="nodeName" placeholder="请输入节点名称">
                </div>

                <div class="form-group">
                    <label class="form-label">审核状态</label>
                    <select class="form-control" id="approvalStatus">
                        <option value="pending">待审核</option>
                        <option value="approved">已通过</option>
                        <option value="rejected">已拒绝</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">超时设置（小时）</label>
                    <input type="number" class="form-control" id="timeout" placeholder="请输入超时时间">
                </div>

                <div class="form-group">
                    <label class="form-label">审批人员类型</label>
                    <select class="form-control" id="approverType">
                        <option value="single">指定单人</option>
                        <option value="multiple">指定多人</option>
                        <option value="role">按角色</option>
                        <option value="department">按部门</option>
                        <option value="countersign">会签</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">审批操作按钮</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="btn1" value="approve" checked>
                            <label for="btn1">同意</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="btn2" value="reject" checked>
                            <label for="btn2">拒绝</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="btn3" value="return">
                            <label for="btn3">退回</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="btn4" value="transfer">
                            <label for="btn4">转办</label>
                        </div>
                    </div>
                </div>

                <div style="text-align: right; margin-top: 30px;">
                    <button type="button" class="btn btn-outline" onclick="closeModal('approvalModal')">取消</button>
                    <button type="submit" class="btn btn-primary">确定</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 全局变量
        let currentService = null;
        let draggedComponent = null;
        let workflowNodes = [];

        // 标签切换
        function showTab(tabId) {
            // 隐藏所有面板
            const panels = document.querySelectorAll('.content-panel');
            panels.forEach(panel => panel.classList.remove('active'));

            // 移除所有标签的active状态
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的面板
            document.getElementById(tabId).classList.add('active');

            // 激活对应的标签
            event.target.classList.add('active');

            // 根据标签加载对应内容
            if (tabId === 'services') {
                loadServices();
            } else if (tabId === 'templates') {
                loadTemplates();
            }
        }

        // 创建空白表单
        function createBlankForm() {
            showTab('form-design');
            document.querySelector('[onclick="showTab(\'form-design\')"]').classList.add('active');
        }

        // 创建空白流程
        function createBlankWorkflow() {
            showTab('workflow-design');
            document.querySelector('[onclick="showTab(\'workflow-design\')"]').classList.add('active');
        }

        // 创建新服务
        function createNewService() {
            document.getElementById('modalTitle').textContent = '新建服务';
            document.getElementById('serviceForm').reset();
            document.getElementById('serviceModal').style.display = 'block';
        }

        // 编辑服务
        function editService(serviceName) {
            alert(`编辑服务: ${serviceName}`);
            showTab('form-design');
        }

        // 使用服务
        function useService(serviceName) {
            alert(`启动服务: ${serviceName}`);
        }

        // 导出服务包
        function exportServices() {
            alert('正在导出服务包...');
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 预览表单
        function previewForm() {
            alert('表单预览功能');
        }

        // 保存表单
        function saveForm() {
            alert('表单已保存');
        }

        // 发布表单
        function publishForm() {
            alert('表单已发布');
        }

        // 验证流程
        function validateWorkflow() {
            alert('流程验证通过');
        }

        // 保存流程
        function saveWorkflow() {
            alert('流程已保存');
        }

        // 部署流程
        function deployWorkflow() {
            alert('流程已部署');
        }

        // 创建模板
        function createTemplate() {
            alert('创建模板功能');
        }

        // 加载服务列表
        function loadServices() {
            const servicesList = document.getElementById('servicesList');
            const services = [
                {
                    name: '重点事项督办',
                    category: '办公类',
                    description: '用于重点工作事项的督办和跟踪管理，支持多级审批和进度监控。',
                    status: 'published',
                    createTime: '2024-01-15',
                    useCount: 45
                },
                {
                    name: '信息服务报送',
                    category: '办公类',
                    description: '各类信息的收集、整理和上报流程管理。',
                    status: 'published',
                    createTime: '2024-01-14',
                    useCount: 32
                },
                {
                    name: '课程缓考申请',
                    category: '教务类',
                    description: '学生因特殊情况无法按时参加考试时的缓考申请流程。',
                    status: 'published',
                    createTime: '2024-01-12',
                    useCount: 23
                },
                {
                    name: '会议室申请',
                    category: '教保类',
                    description: '会议室预约和使用申请，支持时间冲突检测和自动通知。',
                    status: 'draft',
                    createTime: '2024-01-10',
                    useCount: 67
                },
                {
                    name: '科研项目申报',
                    category: '科研类',
                    description: '科研项目的申报、审批和管理流程。',
                    status: 'published',
                    createTime: '2024-01-08',
                    useCount: 18
                },
                {
                    name: '车辆门禁办理',
                    category: '安管类',
                    description: '车辆进出校园的门禁卡申请和管理。',
                    status: 'published',
                    createTime: '2024-01-05',
                    useCount: 89
                }
            ];

            servicesList.innerHTML = services.map(service => `
                <div class="service-card">
                    <div class="service-header">
                        <div>
                            <div class="service-title">${service.name}</div>
                            <div class="service-category">${service.category}</div>
                        </div>
                        <div style="color: ${service.status === 'published' ? '#28a745' : '#ffc107'};">
                            <i class="fas fa-${service.status === 'published' ? 'check-circle' : 'clock'}"></i>
                        </div>
                    </div>
                    <div class="service-description">${service.description}</div>
                    <div class="service-meta">
                        <span>创建时间: ${service.createTime}</span>
                        <span>使用次数: ${service.useCount}</span>
                    </div>
                    <div class="service-actions">
                        <button class="btn btn-primary" onclick="editService('${service.name}')">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button class="btn btn-success" onclick="useService('${service.name}')">
                            <i class="fas fa-play"></i> 使用
                        </button>
                        <button class="btn btn-danger" onclick="deleteService('${service.name}')">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 删除服务
        function deleteService(serviceName) {
            if (confirm(`确定要删除服务"${serviceName}"吗？`)) {
                alert(`服务"${serviceName}"已删除`);
                loadServices();
            }
        }

        // 加载模板列表
        function loadTemplates() {
            const templatesList = document.getElementById('templatesList');
            const templates = [
                {
                    name: '重点事项督办模板',
                    category: '办公类',
                    description: '包含事项基本信息、责任人、完成时限、督办流程等完整模板。',
                    type: 'popular',
                    useCount: 156
                },
                {
                    name: '课程缓考申请模板',
                    category: '教务类',
                    description: '学生缓考申请的标准流程模板，包含申请理由、证明材料等。',
                    type: 'popular',
                    useCount: 89
                },
                {
                    name: '会议室预约模板',
                    category: '教保类',
                    description: '会议室申请和预约的标准模板，支持时间冲突检测。',
                    type: 'popular',
                    useCount: 234
                },
                {
                    name: '科研项目申报模板',
                    category: '科研类',
                    description: '科研项目申报的完整流程模板，包含项目信息、预算等。',
                    type: 'normal',
                    useCount: 67
                },
                {
                    name: '人员信息采集模板',
                    category: '政工类',
                    description: '人员基础信息采集和更新的标准模板。',
                    type: 'normal',
                    useCount: 123
                },
                {
                    name: '车辆门禁办理模板',
                    category: '安管类',
                    description: '车辆门禁卡申请和管理的标准流程模板。',
                    type: 'normal',
                    useCount: 78
                }
            ];

            templatesList.innerHTML = templates.map(template => `
                <div class="service-card">
                    <div class="service-header">
                        <div>
                            <div class="service-title">${template.name}</div>
                            <div class="service-category">${template.category}</div>
                        </div>
                        <div style="color: ${template.type === 'popular' ? '#ff6b6b' : '#667eea'};">
                            <i class="fas fa-${template.type === 'popular' ? 'fire' : 'layer-group'}"></i>
                        </div>
                    </div>
                    <div class="service-description">${template.description}</div>
                    <div class="service-meta">
                        <span>使用次数: ${template.useCount}</span>
                        <span>${template.type === 'popular' ? '热门模板' : '标准模板'}</span>
                    </div>
                    <div class="service-actions">
                        <button class="btn btn-primary" onclick="useTemplate('${template.name}')">
                            <i class="fas fa-magic"></i> 使用模板
                        </button>
                        <button class="btn btn-outline" onclick="previewTemplate('${template.name}')">
                            <i class="fas fa-eye"></i> 预览
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 使用模板
        function useTemplate(templateName) {
            alert(`正在使用模板: ${templateName}`);
            showTab('form-design');
        }

        // 预览模板
        function previewTemplate(templateName) {
            alert(`预览模板: ${templateName}`);
        }

        // 拖拽功能
        function initDragAndDrop() {
            const components = document.querySelectorAll('.component-item');
            const formDesignArea = document.getElementById('formDesignArea');
            const workflowCanvas = document.getElementById('workflowCanvas');

            components.forEach(component => {
                component.addEventListener('dragstart', function(e) {
                    draggedComponent = this.dataset.type;
                    e.dataTransfer.effectAllowed = 'copy';
                });
            });

            // 表单设计区域
            formDesignArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'copy';
            });

            formDesignArea.addEventListener('drop', function(e) {
                e.preventDefault();
                if (draggedComponent) {
                    addFormComponent(draggedComponent, e.offsetX, e.offsetY);
                    draggedComponent = null;
                }
            });

            // 流程设计区域
            workflowCanvas.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'copy';
            });

            workflowCanvas.addEventListener('drop', function(e) {
                e.preventDefault();
                if (draggedComponent) {
                    addWorkflowNode(draggedComponent, e.offsetX, e.offsetY);
                    draggedComponent = null;
                }
            });
        }

        // 添加表单组件
        function addFormComponent(type, x, y) {
            const formDesignArea = document.getElementById('formDesignArea');

            // 清空提示文字
            if (formDesignArea.children.length === 1 && formDesignArea.children[0].style.textAlign === 'center') {
                formDesignArea.innerHTML = '';
            }

            const componentMap = {
                'input': '单行文本',
                'textarea': '多行文本',
                'number': '数字输入',
                'date': '日期选择',
                'select': '下拉选择',
                'radio': '单选框',
                'checkbox': '复选框',
                'user-select': '人员选择',
                'dept-select': '部门选择',
                'data-table': '数据表格',
                'file-upload': '文件上传',
                'signature': '电子签名',
                'location': '位置选择',
                'qrcode': '二维码'
            };

            const componentDiv = document.createElement('div');
            componentDiv.style.cssText = `
                background: white;
                border: 2px solid #667eea;
                border-radius: 8px;
                padding: 15px;
                margin: 10px 0;
                cursor: pointer;
                transition: all 0.3s;
            `;
            componentDiv.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span><i class="fas fa-edit"></i> ${componentMap[type] || type}</span>
                    <button onclick="this.parentElement.parentElement.remove()" style="background: #dc3545; color: white; border: none; border-radius: 4px; padding: 5px 10px; cursor: pointer;">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;

            componentDiv.addEventListener('click', function() {
                showComponentProperties(type);
            });

            formDesignArea.appendChild(componentDiv);
        }

        // 添加流程节点
        function addWorkflowNode(type, x, y) {
            const workflowCanvas = document.getElementById('workflowCanvas');

            // 清空提示文字
            if (workflowCanvas.children.length === 1 && workflowCanvas.children[0].style.textAlign === 'center') {
                workflowCanvas.innerHTML = '';
            }

            const nodeMap = {
                'start-node': { name: '开始', class: 'node-start' },
                'approval-node': { name: '审批', class: 'node-process' },
                'condition-node': { name: '条件', class: 'node-decision' },
                'parallel-node': { name: '并行', class: 'node-process' },
                'end-node': { name: '结束', class: 'node-end' }
            };

            const nodeInfo = nodeMap[type] || { name: type, class: 'node-process' };

            const nodeDiv = document.createElement('div');
            nodeDiv.className = `workflow-node ${nodeInfo.class}`;
            nodeDiv.style.left = x + 'px';
            nodeDiv.style.top = y + 'px';
            nodeDiv.innerHTML = nodeInfo.name;

            nodeDiv.addEventListener('click', function() {
                if (type === 'approval-node') {
                    document.getElementById('approvalModal').style.display = 'block';
                }
            });

            // 添加拖拽功能
            nodeDiv.addEventListener('mousedown', function(e) {
                let isDragging = true;
                let startX = e.clientX - nodeDiv.offsetLeft;
                let startY = e.clientY - nodeDiv.offsetTop;

                function mouseMoveHandler(e) {
                    if (isDragging) {
                        nodeDiv.style.left = (e.clientX - startX) + 'px';
                        nodeDiv.style.top = (e.clientY - startY) + 'px';
                    }
                }

                function mouseUpHandler() {
                    isDragging = false;
                    document.removeEventListener('mousemove', mouseMoveHandler);
                    document.removeEventListener('mouseup', mouseUpHandler);
                }

                document.addEventListener('mousemove', mouseMoveHandler);
                document.addEventListener('mouseup', mouseUpHandler);
            });

            workflowCanvas.appendChild(nodeDiv);
            workflowNodes.push({ type, element: nodeDiv });
        }

        // 显示组件属性
        function showComponentProperties(type) {
            const propertyPanel = document.getElementById('propertyPanel');
            propertyPanel.innerHTML = `
                <div class="form-group">
                    <label class="form-label">组件类型</label>
                    <input type="text" class="form-control" value="${type}" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">字段名称</label>
                    <input type="text" class="form-control" placeholder="请输入字段名称">
                </div>
                <div class="form-group">
                    <label class="form-label">是否必填</label>
                    <select class="form-control">
                        <option value="false">否</option>
                        <option value="true">是</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">默认值</label>
                    <input type="text" class="form-control" placeholder="请输入默认值">
                </div>
                <div class="form-group">
                    <label class="form-label">提示文字</label>
                    <input type="text" class="form-control" placeholder="请输入提示文字">
                </div>
            `;
        }

        // 筛选功能
        function initFilters() {
            const filterTags = document.querySelectorAll('.filter-tag');
            filterTags.forEach(tag => {
                tag.addEventListener('click', function() {
                    // 移除同组其他标签的active状态
                    const siblings = this.parentElement.querySelectorAll('.filter-tag');
                    siblings.forEach(sibling => sibling.classList.remove('active'));

                    // 激活当前标签
                    this.classList.add('active');

                    // 执行筛选逻辑
                    const category = this.dataset.category;
                    const status = this.dataset.status;
                    const template = this.dataset.template;

                    if (category || status || template) {
                        // 这里可以添加实际的筛选逻辑
                        console.log('筛选条件:', { category, status, template });
                    }
                });
            });
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initDragAndDrop();
            initFilters();
            loadServices();
            loadTemplates();

            // 表单提交处理
            document.getElementById('serviceForm').addEventListener('submit', function(e) {
                e.preventDefault();
                alert('服务创建成功！');
                closeModal('serviceModal');
            });

            document.getElementById('approvalForm').addEventListener('submit', function(e) {
                e.preventDefault();
                alert('审批节点配置已保存！');
                closeModal('approvalModal');
            });

            // 点击模态框外部关闭
            window.addEventListener('click', function(e) {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    if (e.target === modal) {
                        modal.style.display = 'none';
                    }
                });
            });
        });
    </script>
</body>
</html>
