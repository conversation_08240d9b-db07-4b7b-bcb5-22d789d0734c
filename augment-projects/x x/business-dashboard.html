<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>业务驾驶舱系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            color: white;
            padding: 15px 0;
            text-align: center;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 5px;
        }
        
        .header p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .nav-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
            flex-wrap: wrap;
        }
        
        .nav-tab {
            flex: 1;
            min-width: 120px;
            padding: 15px 10px;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            color: #1e3c72;
        }
        
        .nav-tab.active {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(30, 60, 114, 0.4);
        }
        
        .nav-tab:hover {
            background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
            color: white;
        }
        
        .content-panel {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .content-panel.active {
            display: block;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .dashboard-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s;
            border-left: 4px solid #1e3c72;
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(30, 60, 114, 0.15);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        
        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        }
        
        .card-value {
            font-size: 32px;
            font-weight: bold;
            color: #1e3c72;
            margin-bottom: 8px;
        }
        
        .card-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .card-trend {
            display: flex;
            align-items: center;
            font-size: 12px;
        }
        
        .trend-up {
            color: #28a745;
        }
        
        .trend-down {
            color: #dc3545;
        }
        
        .trend-stable {
            color: #6c757d;
        }
        
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }
        
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }
        
        .chart-small {
            height: 200px;
        }
        
        .chart-medium {
            height: 250px;
        }
        
        .chart-large {
            height: 300px;
        }
        
        .chart-small canvas,
        .chart-medium canvas,
        .chart-large canvas {
            width: 100% !important;
            height: 100% !important;
            max-height: 200px;
        }
        
        .row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .col {
            flex: 1;
        }
        
        .col-2 {
            flex: 2;
        }
        
        .col-3 {
            flex: 3;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .data-table tr:hover {
            background: #f8f9ff;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .status-normal {
            background: #d4edda;
            color: #155724;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-danger {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            margin: 8px 0;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s;
            background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
        }
        
        .alert {
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }
        
        .quick-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #1e3c72;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2a5298;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .metric-item {
            text-align: center;
            padding: 15px;
            background: #f8f9ff;
            border-radius: 8px;
            border: 1px solid rgba(30, 60, 114, 0.1);
        }
        
        .metric-number {
            font-size: 24px;
            font-weight: bold;
            color: #1e3c72;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 业务驾驶舱系统</h1>
        <p>实时监控各部门业务运行状况，支持可定制化数据展示</p>
    </div>

    <div class="container">
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showPanel('office')">📋 办公室</button>
            <button class="nav-tab" onclick="showPanel('academic')">📚 教务处</button>
            <button class="nav-tab" onclick="showPanel('support')">🔧 教保处</button>
            <button class="nav-tab" onclick="showPanel('research')">🔬 科研处</button>
            <button class="nav-tab" onclick="showPanel('personnel')">👥 政工处</button>
            <button class="nav-tab" onclick="showPanel('security')">🛡️ 安管处</button>
            <button class="nav-tab" onclick="showPanel('logistics')">🏢 供保处</button>
        </div>

        <!-- 办公室业务看板 -->
        <div id="office" class="content-panel active">
            <h2>📋 办公室业务看板</h2>
            <div class="alert alert-info">
                重点事项督办、信息服务报送情况、人员在位情况、用印情况等业务监控
            </div>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">重点事项督办</div>
                        <div class="card-icon">📌</div>
                    </div>
                    <div class="card-value">23</div>
                    <div class="card-label">待办事项</div>
                    <div class="card-trend trend-up">↗ 较昨日 +3</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 76%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">完成率: 76%</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">信息服务报送</div>
                        <div class="card-icon">📊</div>
                    </div>
                    <div class="card-value">156</div>
                    <div class="card-label">本月报送数量</div>
                    <div class="card-trend trend-up">↗ 较上月 +12</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 89%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">及时率: 89%</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">人员在位情况</div>
                        <div class="card-icon">👤</div>
                    </div>
                    <div class="card-value">186</div>
                    <div class="card-label">当前在位人数</div>
                    <div class="card-trend trend-stable">→ 出勤率 95.4%</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 95.4%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">总人数: 195人</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">用印情况</div>
                        <div class="card-icon">🔖</div>
                    </div>
                    <div class="card-value">45</div>
                    <div class="card-label">本周用印次数</div>
                    <div class="card-trend trend-down">↘ 较上周 -8</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 68%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">审批通过率: 98%</div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">重点事项督办完成趋势</div>
                        <div class="chart-small">
                            <canvas id="officeTaskChart" style="width: 100%; height: 200px;"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">信息报送类型分布</div>
                        <div class="chart-small">
                            <canvas id="officeReportChart" style="width: 100%; height: 200px;"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="chart-container">
                <div class="chart-title">人员在位情况统计</div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>部门</th>
                            <th>总人数</th>
                            <th>在位人数</th>
                            <th>出勤率</th>
                            <th>请假人数</th>
                            <th>出差人数</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>办公室</td>
                            <td>12</td>
                            <td>11</td>
                            <td>91.7%</td>
                            <td>1</td>
                            <td>0</td>
                            <td><span class="status-badge status-normal">正常</span></td>
                        </tr>
                        <tr>
                            <td>教务处</td>
                            <td>25</td>
                            <td>24</td>
                            <td>96.0%</td>
                            <td>0</td>
                            <td>1</td>
                            <td><span class="status-badge status-normal">正常</span></td>
                        </tr>
                        <tr>
                            <td>科研处</td>
                            <td>18</td>
                            <td>16</td>
                            <td>88.9%</td>
                            <td>1</td>
                            <td>1</td>
                            <td><span class="status-badge status-warning">关注</span></td>
                        </tr>
                        <tr>
                            <td>学生处</td>
                            <td>22</td>
                            <td>21</td>
                            <td>95.5%</td>
                            <td>1</td>
                            <td>0</td>
                            <td><span class="status-badge status-normal">正常</span></td>
                        </tr>
                        <tr>
                            <td>后勤处</td>
                            <td>35</td>
                            <td>32</td>
                            <td>91.4%</td>
                            <td>2</td>
                            <td>1</td>
                            <td><span class="status-badge status-normal">正常</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="quick-actions">
                <button class="btn btn-primary">新增督办事项</button>
                <button class="btn btn-success">信息报送</button>
                <button class="btn btn-info">考勤统计</button>
                <button class="btn btn-warning">用印申请</button>
            </div>
        </div>

        <!-- 教务处业务看板 -->
        <div id="academic" class="content-panel">
            <h2>📚 教务处业务看板</h2>
            <div class="alert alert-info">
                开课情况、教材使用、课程教学计划、教学成果及奖励、调停课情况、领导听查课、教学督导、课堂评价、试讲验收、评教评学、教师培训等业务监控
            </div>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">开课情况</div>
                        <div class="card-icon">📖</div>
                    </div>
                    <div class="card-value">342</div>
                    <div class="card-label">本学期开课门数</div>
                    <div class="card-trend trend-up">↗ 较上学期 +15</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 98%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">开课率: 98%</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">教材使用情况</div>
                        <div class="card-icon">📚</div>
                    </div>
                    <div class="card-value">89.6%</div>
                    <div class="card-label">教材到位率</div>
                    <div class="card-trend trend-up">↗ 较上学期 +2.3%</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 89.6%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">优质教材占比: 76%</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">调停课情况</div>
                        <div class="card-icon">🔄</div>
                    </div>
                    <div class="card-value">23</div>
                    <div class="card-label">本周调停课次数</div>
                    <div class="card-trend trend-down">↘ 较上周 -5</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 15%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">调课率: 1.5%</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">课堂评价</div>
                        <div class="card-icon">⭐</div>
                    </div>
                    <div class="card-value">4.6</div>
                    <div class="card-label">平均评分</div>
                    <div class="card-trend trend-up">↗ 较上月 +0.2</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 92%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">满意度: 92%</div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">教学成果及奖励统计</div>
                        <div class="chart-small">
                            <canvas id="academicAwardChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">领导听查课情况</div>
                        <div class="chart-small">
                            <canvas id="academicInspectionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="metric-grid">
                <div class="metric-item">
                    <div class="metric-number">156</div>
                    <div class="metric-label">教学督导次数</div>
                </div>
                <div class="metric-item">
                    <div class="metric-number">89</div>
                    <div class="metric-label">试讲验收人数</div>
                </div>
                <div class="metric-item">
                    <div class="metric-number">95.2%</div>
                    <div class="metric-label">评教参与率</div>
                </div>
                <div class="metric-item">
                    <div class="metric-number">78</div>
                    <div class="metric-label">教师培训人次</div>
                </div>
                <div class="metric-item">
                    <div class="metric-number">234</div>
                    <div class="metric-label">课程教学计划</div>
                </div>
            </div>

            <div class="chart-container">
                <div class="chart-title">教学质量监控详情</div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>监控项目</th>
                            <th>计划数量</th>
                            <th>完成数量</th>
                            <th>完成率</th>
                            <th>质量评级</th>
                            <th>负责人</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>课堂教学检查</td>
                            <td>120</td>
                            <td>115</td>
                            <td>95.8%</td>
                            <td>优秀</td>
                            <td>张主任</td>
                            <td><span class="status-badge status-normal">进行中</span></td>
                        </tr>
                        <tr>
                            <td>教学计划审核</td>
                            <td>234</td>
                            <td>234</td>
                            <td>100%</td>
                            <td>优秀</td>
                            <td>李副处长</td>
                            <td><span class="status-badge status-normal">已完成</span></td>
                        </tr>
                        <tr>
                            <td>试讲验收</td>
                            <td>95</td>
                            <td>89</td>
                            <td>93.7%</td>
                            <td>良好</td>
                            <td>王老师</td>
                            <td><span class="status-badge status-warning">进行中</span></td>
                        </tr>
                        <tr>
                            <td>教师培训</td>
                            <td>80</td>
                            <td>78</td>
                            <td>97.5%</td>
                            <td>优秀</td>
                            <td>刘主任</td>
                            <td><span class="status-badge status-normal">进行中</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 教保处业务看板 -->
        <div id="support" class="content-panel">
            <h2>🔧 教保处业务看板</h2>
            <div class="alert alert-info">
                重点项目、场地、信息化、实验室等板块业务监控
            </div>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">重点项目</div>
                        <div class="card-icon">🎯</div>
                    </div>
                    <div class="card-value">15</div>
                    <div class="card-label">在建项目数量</div>
                    <div class="card-trend trend-up">↗ 新增 3个</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 73%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">平均进度: 73%</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">场地管理</div>
                        <div class="card-icon">🏢</div>
                    </div>
                    <div class="card-value">85.6%</div>
                    <div class="card-label">场地使用率</div>
                    <div class="card-trend trend-up">↗ 较上月 +3.2%</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 85.6%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">可用场地: 156个</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">信息化建设</div>
                        <div class="card-icon">💻</div>
                    </div>
                    <div class="card-value">12</div>
                    <div class="card-label">信息化项目</div>
                    <div class="card-trend trend-stable">→ 运行正常</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 92%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">系统可用率: 99.2%</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">实验室管理</div>
                        <div class="card-icon">🔬</div>
                    </div>
                    <div class="card-value">45</div>
                    <div class="card-label">实验室数量</div>
                    <div class="card-trend trend-up">↗ 新建 2个</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 88%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">设备完好率: 96%</div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">重点项目进度统计</div>
                        <div class="chart-small">
                            <canvas id="supportProjectChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">实验室使用情况</div>
                        <div class="chart-small">
                            <canvas id="supportLabChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 科研处业务看板 -->
        <div id="research" class="content-panel">
            <h2>🔬 科研处业务看板</h2>
            <div class="alert alert-info">
                科研项目、学术活动、学术成果、创新平台等板块业务监控
            </div>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">科研项目</div>
                        <div class="card-icon">📋</div>
                    </div>
                    <div class="card-value">107</div>
                    <div class="card-label">在研项目数量</div>
                    <div class="card-trend trend-up">↗ 新增 8个</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 78%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">总经费: 2856万</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">学术活动</div>
                        <div class="card-icon">🎓</div>
                    </div>
                    <div class="card-value">23</div>
                    <div class="card-label">本月学术活动</div>
                    <div class="card-trend trend-up">↗ 较上月 +5</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 85%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">参与人次: 1,234</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">学术成果</div>
                        <div class="card-icon">📄</div>
                    </div>
                    <div class="card-value">245</div>
                    <div class="card-label">本年度发表论文</div>
                    <div class="card-trend trend-up">↗ 较去年 +23</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 92%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">SCI论文: 89篇</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">创新平台</div>
                        <div class="card-icon">🏛️</div>
                    </div>
                    <div class="card-value">6</div>
                    <div class="card-label">科研平台数量</div>
                    <div class="card-trend trend-stable">→ 运行良好</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 95%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">国家级: 1个</div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">科研项目类型分布</div>
                        <div class="chart-small">
                            <canvas id="researchProjectChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">学术成果趋势</div>
                        <div class="chart-small">
                            <canvas id="researchOutputChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 政工处业务看板 -->
        <div id="personnel" class="content-panel">
            <h2>👥 政工处业务看板</h2>
            <div class="alert alert-info">
                人员基础数据整体情况、教师信息、学生信息、职工信息、社聘人员信息等板块业务监控
            </div>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">教师信息</div>
                        <div class="card-icon">👨‍🏫</div>
                    </div>
                    <div class="card-value">186</div>
                    <div class="card-label">在职教师总数</div>
                    <div class="card-trend trend-up">↗ 新增 5人</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 93%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">博士比例: 83.9%</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">学生信息</div>
                        <div class="card-icon">👨‍🎓</div>
                    </div>
                    <div class="card-value">3,256</div>
                    <div class="card-label">在校学生总数</div>
                    <div class="card-trend trend-up">↗ 较上年 +78</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 98%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">本科生: 2,456人</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">职工信息</div>
                        <div class="card-icon">👷</div>
                    </div>
                    <div class="card-value">89</div>
                    <div class="card-label">管理职工总数</div>
                    <div class="card-trend trend-stable">→ 人员稳定</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 95%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">在岗率: 95%</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">社聘人员</div>
                        <div class="card-icon">🤝</div>
                    </div>
                    <div class="card-value">45</div>
                    <div class="card-label">社聘人员总数</div>
                    <div class="card-trend trend-up">↗ 新增 3人</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 88%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">合同续签率: 92%</div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">人员结构分布</div>
                        <div class="chart-small">
                            <canvas id="personnelStructureChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">人员变动趋势</div>
                        <div class="chart-small">
                            <canvas id="personnelTrendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 安管处业务看板 -->
        <div id="security" class="content-panel">
            <h2>🛡️ 安管处业务看板</h2>
            <div class="alert alert-info">
                管理、安全、保密、保卫、运投、油料等板块业务监控
            </div>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">安全管理</div>
                        <div class="card-icon">🔒</div>
                    </div>
                    <div class="card-value">0</div>
                    <div class="card-label">本月安全事故</div>
                    <div class="card-trend trend-stable">→ 安全稳定</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 100%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">安全检查: 156次</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">保密工作</div>
                        <div class="card-icon">🔐</div>
                    </div>
                    <div class="card-value">98.5%</div>
                    <div class="card-label">保密检查合格率</div>
                    <div class="card-trend trend-up">↗ 较上月 +1.2%</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 98.5%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">培训人次: 234</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">保卫工作</div>
                        <div class="card-icon">👮</div>
                    </div>
                    <div class="card-value">24/7</div>
                    <div class="card-label">全天候值守</div>
                    <div class="card-trend trend-stable">→ 运行正常</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 100%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">巡逻次数: 48次/日</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">油料管理</div>
                        <div class="card-icon">⛽</div>
                    </div>
                    <div class="card-value">85.6%</div>
                    <div class="card-label">油料库存率</div>
                    <div class="card-trend trend-down">↘ 需要补充</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 85.6%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">月消耗: 1,234升</div>
                </div>
            </div>
        </div>

        <!-- 供保处业务看板 -->
        <div id="logistics" class="content-panel">
            <h2>🏢 供保处业务看板</h2>
            <div class="alert alert-info">
                采购和公共用房等板块业务监控
            </div>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">采购管理</div>
                        <div class="card-icon">🛒</div>
                    </div>
                    <div class="card-value">156</div>
                    <div class="card-label">本月采购项目</div>
                    <div class="card-trend trend-up">↗ 较上月 +12</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 89%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">采购金额: 234万</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">公共用房</div>
                        <div class="card-icon">🏠</div>
                    </div>
                    <div class="card-value">92.3%</div>
                    <div class="card-label">用房使用率</div>
                    <div class="card-trend trend-up">↗ 较上月 +2.1%</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 92.3%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">总面积: 15.6万㎡</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">维修保养</div>
                        <div class="card-icon">🔧</div>
                    </div>
                    <div class="card-value">45</div>
                    <div class="card-label">本月维修工单</div>
                    <div class="card-trend trend-down">↘ 较上月 -8</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 95%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">完成率: 95%</div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <div class="card-title">能耗管理</div>
                        <div class="card-icon">⚡</div>
                    </div>
                    <div class="card-value">-5.2%</div>
                    <div class="card-label">能耗同比变化</div>
                    <div class="card-trend trend-up">↗ 节能效果显著</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 78%;"></div>
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">月用电: 45万度</div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">采购类型分布</div>
                        <div class="chart-small">
                            <canvas id="logisticsProcurementChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="chart-container">
                        <div class="chart-title">能耗趋势分析</div>
                        <div class="chart-small">
                            <canvas id="logisticsEnergyChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 基础图表配置
        const baseChartConfig = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        };

        // 切换面板显示
        function showPanel(panelId) {
            // 隐藏所有面板
            const panels = document.querySelectorAll('.content-panel');
            panels.forEach(panel => panel.classList.remove('active'));

            // 移除所有标签的active状态
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的面板
            document.getElementById(panelId).classList.add('active');

            // 激活对应的标签
            event.target.classList.add('active');

            // 延迟初始化当前面板的图表
            setTimeout(() => {
                initPanelCharts(panelId);
            }, 100);
        }

        // 初始化特定面板的图表
        function initPanelCharts(panelId) {
            switch(panelId) {
                case 'office':
                    initOfficeCharts();
                    break;
                case 'academic':
                    initAcademicCharts();
                    break;
                case 'support':
                    initSupportCharts();
                    break;
                case 'research':
                    initResearchCharts();
                    break;
                case 'personnel':
                    initPersonnelCharts();
                    break;
                case 'logistics':
                    initLogisticsCharts();
                    break;
            }
        }

        // 初始化图表
        function initCharts() {
            // 办公室图表
            initOfficeCharts();
            // 教务处图表
            initAcademicCharts();
            // 教保处图表
            initSupportCharts();
            // 科研处图表
            initResearchCharts();
            // 政工处图表
            initPersonnelCharts();
            // 供保处图表
            initLogisticsCharts();
        }

        // 办公室图表
        function initOfficeCharts() {
            console.log('开始初始化办公室图表...');

            // 重点事项督办完成趋势
            const officeTaskCtx = document.getElementById('officeTaskChart');
            console.log('officeTaskChart元素:', officeTaskCtx);
            if (officeTaskCtx) {
                try {
                    new Chart(officeTaskCtx, {
                    type: 'line',
                    data: {
                        labels: ['周一', '周二', '周三', '周四', '周五'],
                        datasets: [{
                            label: '完成事项',
                            data: [8, 12, 15, 18, 23],
                            borderColor: '#1e3c72',
                            fill: false
                        }, {
                            label: '新增事项',
                            data: [5, 8, 6, 9, 7],
                            borderColor: '#2a5298',
                            fill: false
                        }]
                    },
                    options: baseChartConfig
                });
                console.log('重点事项督办图表创建成功');
                } catch (error) {
                    console.error('创建重点事项督办图表失败:', error);
                }
            } else {
                console.error('未找到officeTaskChart元素');
            }

            // 信息报送类型分布
            const officeReportCtx = document.getElementById('officeReportChart');
            if (officeReportCtx) {
                new Chart(officeReportCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['工作简报', '统计报表', '专项报告', '会议纪要', '其他'],
                        datasets: [{
                            data: [45, 32, 28, 25, 26],
                            backgroundColor: [
                                '#1e3c72',
                                '#2a5298',
                                '#4a90e2',
                                '#7bb3f0',
                                '#a8d0f7'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        }

        // 教务处图表
        function initAcademicCharts() {
            // 教学成果及奖励统计
            const academicAwardCtx = document.getElementById('academicAwardChart');
            if (academicAwardCtx) {
                new Chart(academicAwardCtx, {
                    type: 'bar',
                    data: {
                        labels: ['国家级', '省部级', '校级', '院级'],
                        datasets: [{
                            label: '教学成果奖',
                            data: [3, 8, 15, 23],
                            backgroundColor: '#1e3c72'
                        }, {
                            label: '教学竞赛奖',
                            data: [2, 12, 18, 28],
                            backgroundColor: '#2a5298'
                        }]
                    },
                    options: baseChartConfig
                });
            }

            // 领导听查课情况
            const academicInspectionCtx = document.getElementById('academicInspectionChart');
            if (academicInspectionCtx) {
                new Chart(academicInspectionCtx, {
                    type: 'line',
                    data: {
                        labels: ['第1周', '第2周', '第3周', '第4周'],
                        datasets: [{
                            label: '听课次数',
                            data: [12, 15, 18, 20],
                            borderColor: '#1e3c72',
                            fill: false
                        }, {
                            label: '查课次数',
                            data: [8, 10, 12, 14],
                            borderColor: '#2a5298',
                            fill: false
                        }]
                    },
                    options: baseChartConfig
                });
            }
        }

        // 教保处图表
        function initSupportCharts() {
            // 重点项目进度统计
            const supportProjectCtx = document.getElementById('supportProjectChart');
            if (supportProjectCtx) {
                new Chart(supportProjectCtx, {
                    type: 'bar',
                    data: {
                        labels: ['智慧教室', '实验室改造', '网络升级', '设备采购', '基础设施'],
                        datasets: [{
                            label: '完成进度(%)',
                            data: [85, 73, 92, 68, 76],
                            backgroundColor: [
                                '#1e3c72',
                                '#2a5298',
                                '#4a90e2',
                                '#7bb3f0',
                                '#a8d0f7'
                            ]
                        }]
                    },
                    options: baseChartConfig
                });
            }

            // 实验室使用情况
            const supportLabCtx = document.getElementById('supportLabChart');
            if (supportLabCtx) {
                new Chart(supportLabCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['计算机实验室', '物理实验室', '化学实验室', '工程实验室', '其他'],
                        datasets: [{
                            data: [25, 18, 15, 20, 12],
                            backgroundColor: [
                                '#1e3c72',
                                '#2a5298',
                                '#4a90e2',
                                '#7bb3f0',
                                '#a8d0f7'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        }

        // 科研处图表
        function initResearchCharts() {
            // 科研项目类型分布
            const researchProjectCtx = document.getElementById('researchProjectChart');
            if (researchProjectCtx) {
                new Chart(researchProjectCtx, {
                    type: 'pie',
                    data: {
                        labels: ['国家级', '省部级', '军队级', '企业合作', '其他'],
                        datasets: [{
                            data: [26, 35, 28, 18, 8],
                            backgroundColor: [
                                '#1e3c72',
                                '#2a5298',
                                '#4a90e2',
                                '#7bb3f0',
                                '#a8d0f7'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            // 学术成果趋势
            const researchOutputCtx = document.getElementById('researchOutputChart');
            if (researchOutputCtx) {
                new Chart(researchOutputCtx, {
                    type: 'line',
                    data: {
                        labels: ['2022年', '2023年', '2024年'],
                        datasets: [{
                            label: '发表论文',
                            data: [198, 213, 245],
                            borderColor: '#1e3c72',
                            fill: false
                        }, {
                            label: '授权专利',
                            data: [56, 69, 89],
                            borderColor: '#2a5298',
                            fill: false
                        }]
                    },
                    options: baseChartConfig
                });
            }
        }

        // 政工处图表
        function initPersonnelCharts() {
            // 人员结构分布
            const personnelStructureCtx = document.getElementById('personnelStructureChart');
            if (personnelStructureCtx) {
                new Chart(personnelStructureCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['教师', '学生', '职工', '社聘人员'],
                        datasets: [{
                            data: [186, 3256, 89, 45],
                            backgroundColor: [
                                '#1e3c72',
                                '#2a5298',
                                '#4a90e2',
                                '#7bb3f0'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            // 人员变动趋势
            const personnelTrendCtx = document.getElementById('personnelTrendChart');
            if (personnelTrendCtx) {
                new Chart(personnelTrendCtx, {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                        datasets: [{
                            label: '新入职',
                            data: [5, 3, 8, 6, 4, 7],
                            borderColor: '#1e3c72',
                            fill: false
                        }, {
                            label: '离职',
                            data: [2, 1, 3, 2, 1, 2],
                            borderColor: '#2a5298',
                            fill: false
                        }]
                    },
                    options: baseChartConfig
                });
            }
        }

        // 供保处图表
        function initLogisticsCharts() {
            // 采购类型分布
            const logisticsProcurementCtx = document.getElementById('logisticsProcurementChart');
            if (logisticsProcurementCtx) {
                new Chart(logisticsProcurementCtx, {
                    type: 'bar',
                    data: {
                        labels: ['设备采购', '办公用品', '维修材料', '服务采购', '其他'],
                        datasets: [{
                            label: '采购金额(万元)',
                            data: [89, 45, 32, 28, 40],
                            backgroundColor: [
                                '#1e3c72',
                                '#2a5298',
                                '#4a90e2',
                                '#7bb3f0',
                                '#a8d0f7'
                            ]
                        }]
                    },
                    options: baseChartConfig
                });
            }

            // 能耗趋势分析
            const logisticsEnergyCtx = document.getElementById('logisticsEnergyChart');
            if (logisticsEnergyCtx) {
                new Chart(logisticsEnergyCtx, {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                        datasets: [{
                            label: '用电量(万度)',
                            data: [52, 48, 45, 43, 41, 45],
                            borderColor: '#1e3c72',
                            fill: false
                        }, {
                            label: '用水量(万吨)',
                            data: [8.5, 7.8, 7.2, 6.9, 6.5, 7.1],
                            borderColor: '#2a5298',
                            fill: false
                        }]
                    },
                    options: baseChartConfig
                });
            }
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            console.log('业务驾驶舱系统已加载');

            // 检查Chart.js是否加载
            if (typeof Chart === 'undefined') {
                console.error('Chart.js未加载');
                return;
            }

            // 延迟初始化默认面板（办公室）的图表
            setTimeout(() => {
                try {
                    initOfficeCharts();
                    console.log('办公室图表初始化完成');
                } catch (error) {
                    console.error('图表初始化失败:', error);
                }
            }, 1000);

            // 添加动态效果
            const cards = document.querySelectorAll('.dashboard-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });
    </script>
</body>
</html>
