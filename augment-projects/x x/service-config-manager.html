<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>填报配置管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            gap: 20px;
            height: calc(100vh - 80px);
        }
        
        .sidebar {
            width: 300px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            padding: 20px;
            overflow-y: auto;
        }
        
        .main-content {
            flex: 1;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            display: flex;
            flex-direction: column;
        }
        
        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 12px 12px 0 0;
            padding: 0;
            margin: 0;
            border-bottom: 1px solid #e9ecef;
            flex-wrap: wrap;
        }
        
        .nav-tab {
            flex: 1;
            min-width: 120px;
            padding: 15px 10px;
            background: transparent;
            border: none;
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            color: #6c757d;
            transition: all 0.3s;
            border-radius: 12px 12px 0 0;
            text-align: center;
        }
        
        .nav-tab.active {
            background: white;
            color: #4f46e5;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
        }
        
        .tab-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .content-panel {
            display: none;
        }
        
        .content-panel.active {
            display: block;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4f46e5;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #4f46e5;
            color: white;
        }
        
        .btn-primary:hover {
            background: #4338ca;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-warning {
            background: #f59e0b;
            color: white;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .btn-outline {
            background: transparent;
            border: 2px solid #4f46e5;
            color: #4f46e5;
        }
        
        .btn-outline:hover {
            background: #4f46e5;
            color: white;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        .service-list {
            margin-bottom: 20px;
        }
        
        .service-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .service-item:hover {
            background: #e9ecef;
        }
        
        .service-item.active {
            background: #4f46e5;
            color: white;
            border-color: #4f46e5;
        }
        
        .service-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .service-meta {
            font-size: 12px;
            opacity: 0.8;
            display: flex;
            justify-content: space-between;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-col {
            flex: 1;
        }
        
        .component-library {
            margin-bottom: 20px;
        }
        
        .component-category {
            margin-bottom: 15px;
        }
        
        .category-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .component-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin-bottom: 5px;
            background: #f8f9fa;
            border-radius: 8px;
            cursor: grab;
            transition: all 0.3s;
            border: 1px solid #e9ecef;
        }
        
        .component-item:hover {
            background: #e9ecef;
            border-color: #4f46e5;
        }
        
        .component-item i {
            margin-right: 10px;
            color: #4f46e5;
            width: 16px;
        }
        
        .design-area {
            background: #f8f9fa;
            border: 2px dashed #e9ecef;
            border-radius: 12px;
            min-height: 500px;
            padding: 20px;
            position: relative;
            margin-bottom: 20px;
        }
        
        .design-placeholder {
            text-align: center;
            color: #6c757d;
            margin-top: 150px;
        }
        
        .design-placeholder i {
            font-size: 48px;
            margin-bottom: 20px;
            display: block;
        }
        
        .workflow-canvas {
            background: #f8f9fa;
            border: 2px dashed #e9ecef;
            border-radius: 12px;
            min-height: 400px;
            padding: 20px;
            position: relative;
            margin-bottom: 20px;
        }
        
        .workflow-node {
            background: white;
            border: 2px solid #4f46e5;
            border-radius: 8px;
            padding: 15px;
            position: absolute;
            cursor: move;
            min-width: 120px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            font-size: 12px;
        }
        
        .node-start {
            background: #10b981;
            color: white;
            border-color: #10b981;
        }
        
        .node-end {
            background: #ef4444;
            color: white;
            border-color: #ef4444;
        }
        
        .node-approval {
            background: #4f46e5;
            color: white;
            border-color: #4f46e5;
        }
        
        .node-condition {
            background: #f59e0b;
            color: white;
            border-color: #f59e0b;
            transform: rotate(45deg);
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }
        
        .alert-info {
            background: #dbeafe;
            color: #1e40af;
            border-left-color: #3b82f6;
        }
        
        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border-left-color: #10b981;
        }
        
        .alert-warning {
            background: #fef3c7;
            color: #92400e;
            border-left-color: #f59e0b;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 3% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 900px;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: #4f46e5;
            color: white;
        }
        
        .modal-body {
            padding: 20px;
            max-height: 70vh;
            overflow-y: auto;
        }
        
        .close {
            font-size: 24px;
            cursor: pointer;
            color: white;
            opacity: 0.8;
        }
        
        .close:hover {
            opacity: 1;
        }
        
        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .radio-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .radio-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .status-published {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-draft {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-disabled {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .property-panel {
            width: 300px;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin-left: 20px;
        }
        
        .tabs-container {
            display: flex;
            gap: 20px;
        }
        
        .tabs-content {
            flex: 1;
        }
        
        .search-box {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 20px;
        }
        
        .filter-group {
            margin-bottom: 20px;
        }
        
        .filter-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .filter-options {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .filter-tag {
            padding: 6px 12px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .filter-tag.active {
            background: #4f46e5;
            color: white;
            border-color: #4f46e5;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-cogs"></i>
                填报配置管理系统
            </div>
            <div class="user-info">
                <span>管理员</span>
                <i class="fas fa-user-circle" style="font-size: 24px;"></i>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="sidebar">
            <div class="section-title" style="font-size: 16px; margin-bottom: 15px;">
                <i class="fas fa-list"></i>
                已发布服务
            </div>
            
            <input type="text" class="search-box" placeholder="搜索服务..." id="serviceSearch">
            
            <div class="filter-group">
                <div class="filter-title">服务状态</div>
                <div class="filter-options">
                    <div class="filter-tag active" data-status="all">全部</div>
                    <div class="filter-tag" data-status="published">已发布</div>
                    <div class="filter-tag" data-status="draft">草稿</div>
                    <div class="filter-tag" data-status="disabled">已停用</div>
                </div>
            </div>
            
            <div class="service-list" id="serviceList">
                <!-- 服务列表将通过JavaScript动态生成 -->
            </div>
        </div>

        <div class="main-content">
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="showTab('basic-settings')">
                    <i class="fas fa-cog"></i> 基本设置
                </button>
                <button class="nav-tab" onclick="showTab('form-design')">
                    <i class="fas fa-edit"></i> 页面设计
                </button>
                <button class="nav-tab" onclick="showTab('workflow-design')">
                    <i class="fas fa-project-diagram"></i> 流程设计
                </button>
                <button class="nav-tab" onclick="showTab('publish-settings')">
                    <i class="fas fa-rocket"></i> 发布设置
                </button>
                <button class="nav-tab" onclick="showTab('user-groups')">
                    <i class="fas fa-users"></i> 用户组管理
                </button>
            </div>

            <div class="tab-content">
                <!-- 基本设置 -->
                <div id="basic-settings" class="content-panel active">
                    <div class="section-title">
                        <i class="fas fa-cog"></i>
                        基本设置
                        <div style="margin-left: auto;">
                            <button class="btn btn-success" onclick="saveBasicSettings()">
                                <i class="fas fa-save"></i> 保存设置
                            </button>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        支持管理员管理已发布服务，包括修改表单、流程、发布状态和填报范围。
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">服务名称</label>
                                <input type="text" class="form-control" id="serviceName" placeholder="请输入服务名称">
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">服务编码</label>
                                <input type="text" class="form-control" id="serviceCode" placeholder="请输入服务编码">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">服务描述</label>
                        <textarea class="form-control" id="serviceDescription" rows="3" placeholder="请输入服务描述"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">应用分类</label>
                                <select class="form-control" id="serviceCategory">
                                    <option value="">请选择分类</option>
                                    <option value="office">办公类</option>
                                    <option value="academic">教务类</option>
                                    <option value="support">教保类</option>
                                    <option value="research">科研类</option>
                                    <option value="personnel">政工类</option>
                                    <option value="discipline">纪检类</option>
                                    <option value="security">安管类</option>
                                    <option value="logistics">供保类</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">发布状态</label>
                                <select class="form-control" id="publishStatus">
                                    <option value="draft">草稿</option>
                                    <option value="published">已发布</option>
                                    <option value="disabled">已停用</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">数据存储表名</label>
                                <input type="text" class="form-control" id="tableName" placeholder="请输入数据存储表名">
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">服务图标</label>
                                <input type="text" class="form-control" id="serviceIcon" placeholder="请输入图标类名">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">填报人员条件</label>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="condition1" value="all">
                                <label for="condition1">全体人员</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="condition2" value="teacher">
                                <label for="condition2">教师</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="condition3" value="student">
                                <label for="condition3">学生</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="condition4" value="staff">
                                <label for="condition4">职工</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="condition5" value="custom">
                                <label for="condition5">自定义条件</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">服务开放时间</label>
                                <div class="form-row">
                                    <div class="form-col">
                                        <input type="datetime-local" class="form-control" id="startTime">
                                    </div>
                                    <div class="form-col">
                                        <input type="datetime-local" class="form-control" id="endTime">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">填报次数限制</label>
                                <select class="form-control" id="submitLimit">
                                    <option value="unlimited">不限制</option>
                                    <option value="once">仅一次</option>
                                    <option value="daily">每日一次</option>
                                    <option value="weekly">每周一次</option>
                                    <option value="monthly">每月一次</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">是否允许多次填报</label>
                        <div class="radio-group">
                            <div class="radio-item">
                                <input type="radio" id="multiSubmitYes" name="multiSubmit" value="yes">
                                <label for="multiSubmitYes">允许</label>
                            </div>
                            <div class="radio-item">
                                <input type="radio" id="multiSubmitNo" name="multiSubmit" value="no" checked>
                                <label for="multiSubmitNo">不允许</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 页面设计 -->
                <div id="form-design" class="content-panel">
                    <div class="section-title">
                        <i class="fas fa-edit"></i>
                        页面设计器
                        <div style="margin-left: auto;">
                            <button class="btn btn-outline" onclick="previewForm()">
                                <i class="fas fa-eye"></i> 预览
                            </button>
                            <button class="btn btn-success" onclick="saveFormDesign()">
                                <i class="fas fa-save"></i> 保存
                            </button>
                        </div>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-lightbulb"></i>
                        支持根据业务需求快速构建业务表单，设计器可进行表单页面设计及元素属性设置。表单组件库含系统组件和模型组件。
                    </div>

                    <div class="tabs-container">
                        <div class="tabs-content">
                            <div class="design-area" id="formDesignArea">
                                <div class="design-placeholder">
                                    <i class="fas fa-mouse-pointer"></i>
                                    <h3>拖拽组件到此处开始设计表单</h3>
                                    <p>支持系统组件和模型组件，可配置数据源和字段映射</p>
                                </div>
                            </div>

                            <div class="action-buttons">
                                <button class="btn btn-primary" onclick="addModelComponent()">
                                    <i class="fas fa-plus"></i> 批量添加模型组件
                                </button>
                                <button class="btn btn-outline" onclick="clearForm()">
                                    <i class="fas fa-trash"></i> 清空表单
                                </button>
                            </div>
                        </div>

                        <div class="property-panel">
                            <div class="section-title" style="font-size: 16px; margin-bottom: 15px;">
                                <i class="fas fa-cog"></i>
                                属性配置
                            </div>

                            <div id="propertyPanel">
                                <div style="text-align: center; color: #6c757d; margin-top: 50px;">
                                    <i class="fas fa-hand-pointer" style="font-size: 32px; margin-bottom: 15px;"></i>
                                    <p>选择组件查看属性配置</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 流程设计 -->
                <div id="workflow-design" class="content-panel">
                    <div class="section-title">
                        <i class="fas fa-project-diagram"></i>
                        流程设计器
                        <div style="margin-left: auto;">
                            <button class="btn btn-outline" onclick="validateWorkflow()">
                                <i class="fas fa-check"></i> 验证流程
                            </button>
                            <button class="btn btn-success" onclick="saveWorkflow()">
                                <i class="fas fa-save"></i> 保存流程
                            </button>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        支持拖拽方式设计流程，支持多种流程模式和操作权限。流程节点任务处理人类型包括指定单个或多个审批人、上一节点自选、流程发起人、用户组、组织管理任职等。
                    </div>

                    <div class="workflow-canvas" id="workflowCanvas">
                        <div class="design-placeholder">
                            <i class="fas fa-project-diagram"></i>
                            <h3>拖拽流程节点到此处开始设计流程</h3>
                            <p>支持会签模式、抄送设置、消息推送配置等高级功能</p>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="addApprovalNode()">
                            <i class="fas fa-user-check"></i> 添加审批节点
                        </button>
                        <button class="btn btn-warning" onclick="addConditionNode()">
                            <i class="fas fa-code-branch"></i> 添加条件分支
                        </button>
                        <button class="btn btn-success" onclick="addParallelNode()">
                            <i class="fas fa-code-branch"></i> 添加并行节点
                        </button>
                        <button class="btn btn-outline" onclick="clearWorkflow()">
                            <i class="fas fa-trash"></i> 清空流程
                        </button>
                    </div>
                </div>

                <!-- 发布设置 -->
                <div id="publish-settings" class="content-panel">
                    <div class="section-title">
                        <i class="fas fa-rocket"></i>
                        发布设置
                        <div style="margin-left: auto;">
                            <button class="btn btn-success" onclick="publishService()">
                                <i class="fas fa-rocket"></i> 发布服务
                            </button>
                        </div>
                    </div>

                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        支持设置填报人员条件，精准筛选匹配。支持设置服务开放时间，配置是否允许多次填报。
                    </div>

                    <div class="form-group">
                        <label class="form-label">消息推送配置</label>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="pushPortal" value="portal" checked>
                                <label for="pushPortal">门户推送</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="pushEmail" value="email">
                                <label for="pushEmail">邮件推送</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="pushSMS" value="sms">
                                <label for="pushSMS">短信推送</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="pushWeChat" value="wechat">
                                <label for="pushWeChat">微信推送</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">任务标题模板</label>
                        <input type="text" class="form-control" id="taskTitle" placeholder="支持引入字段，如：{申请人}的{服务名称}申请">
                    </div>

                    <div class="form-group">
                        <label class="form-label">消息内容模板</label>
                        <textarea class="form-control" id="messageTemplate" rows="4" placeholder="您有一个新的{服务名称}任务需要处理，申请人：{申请人}，申请时间：{申请时间}"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">抄送对象</label>
                                <input type="text" class="form-control" id="ccUsers" placeholder="请选择抄送对象">
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label class="form-label">会签通过方案</label>
                                <select class="form-control" id="countersignMode">
                                    <option value="all">全部通过</option>
                                    <option value="majority">超过半数</option>
                                    <option value="any">任意一人</option>
                                    <option value="custom">自定义比例</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">节点表单字段权限</label>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="fieldRead" value="read" checked>
                                <label for="fieldRead">只读</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="fieldEdit" value="edit">
                                <label for="fieldEdit">可编辑</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="fieldRequired" value="required">
                                <label for="fieldRequired">必填</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="fieldHidden" value="hidden">
                                <label for="fieldHidden">隐藏</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">消息发送开关</label>
                        <div class="radio-group">
                            <div class="radio-item">
                                <input type="radio" id="messageOn" name="messageSwitch" value="on" checked>
                                <label for="messageOn">开启</label>
                            </div>
                            <div class="radio-item">
                                <input type="radio" id="messageOff" name="messageSwitch" value="off">
                                <label for="messageOff">关闭</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户组管理 -->
                <div id="user-groups" class="content-panel">
                    <div class="section-title">
                        <i class="fas fa-users"></i>
                        用户组管理
                        <div style="margin-left: auto;">
                            <button class="btn btn-primary" onclick="addUserGroup()">
                                <i class="fas fa-plus"></i> 添加用户组
                            </button>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        提供用户组菜单授权，支持添加用户组及成员管理，针对不同用户组，可选择不同功能授权。
                    </div>

                    <div class="service-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 20px;">
                        <div class="service-item" style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <h4>管理员组</h4>
                                <div>
                                    <button class="btn btn-sm btn-outline" onclick="editUserGroup('admin')">
                                        <i class="fas fa-edit"></i> 编辑
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteUserGroup('admin')">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </div>
                            </div>
                            <p style="color: #6c757d; margin-bottom: 15px;">系统管理员用户组，拥有所有权限</p>
                            <div style="margin-bottom: 15px;">
                                <strong>成员数量：</strong> 3人
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>功能权限：</strong>
                                <div style="display: flex; flex-wrap: wrap; gap: 5px; margin-top: 5px;">
                                    <span class="status-badge status-published">创建</span>
                                    <span class="status-badge status-published">编辑</span>
                                    <span class="status-badge status-published">删除</span>
                                    <span class="status-badge status-published">查看</span>
                                    <span class="status-badge status-published">发布</span>
                                </div>
                            </div>
                        </div>

                        <div class="service-item" style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <h4>教师组</h4>
                                <div>
                                    <button class="btn btn-sm btn-outline" onclick="editUserGroup('teacher')">
                                        <i class="fas fa-edit"></i> 编辑
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteUserGroup('teacher')">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </div>
                            </div>
                            <p style="color: #6c757d; margin-bottom: 15px;">教师用户组，可使用教学相关服务</p>
                            <div style="margin-bottom: 15px;">
                                <strong>成员数量：</strong> 186人
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>功能权限：</strong>
                                <div style="display: flex; flex-wrap: wrap; gap: 5px; margin-top: 5px;">
                                    <span class="status-badge status-published">创建</span>
                                    <span class="status-badge status-published">查看</span>
                                    <span class="status-badge status-draft">编辑</span>
                                </div>
                            </div>
                        </div>

                        <div class="service-item" style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <h4>学生组</h4>
                                <div>
                                    <button class="btn btn-sm btn-outline" onclick="editUserGroup('student')">
                                        <i class="fas fa-edit"></i> 编辑
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteUserGroup('student')">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </div>
                            </div>
                            <p style="color: #6c757d; margin-bottom: 15px;">学生用户组，可使用学生相关服务</p>
                            <div style="margin-bottom: 15px;">
                                <strong>成员数量：</strong> 3,256人
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>功能权限：</strong>
                                <div style="display: flex; flex-wrap: wrap; gap: 5px; margin-top: 5px;">
                                    <span class="status-badge status-published">创建</span>
                                    <span class="status-badge status-published">查看</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 组件库侧边栏 -->
    <div style="position: fixed; right: 20px; top: 100px; width: 280px; background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); padding: 20px; max-height: calc(100vh - 140px); overflow-y: auto; z-index: 100;" id="componentLibrary">
        <div class="section-title" style="font-size: 16px; margin-bottom: 15px;">
            <i class="fas fa-puzzle-piece"></i>
            组件库
            <button style="margin-left: auto; background: none; border: none; cursor: pointer;" onclick="toggleComponentLibrary()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="component-library">
            <div class="component-category">
                <div class="category-title">系统组件</div>
                <div class="component-item" draggable="true" data-type="text">
                    <i class="fas fa-font"></i>
                    单行文本
                </div>
                <div class="component-item" draggable="true" data-type="textarea">
                    <i class="fas fa-align-left"></i>
                    多行文本
                </div>
                <div class="component-item" draggable="true" data-type="number">
                    <i class="fas fa-hashtag"></i>
                    数字输入
                </div>
                <div class="component-item" draggable="true" data-type="select">
                    <i class="fas fa-list"></i>
                    下拉选择
                </div>
                <div class="component-item" draggable="true" data-type="radio">
                    <i class="fas fa-dot-circle"></i>
                    单选框
                </div>
                <div class="component-item" draggable="true" data-type="checkbox">
                    <i class="fas fa-check-square"></i>
                    复选框
                </div>
                <div class="component-item" draggable="true" data-type="date">
                    <i class="fas fa-calendar"></i>
                    日期选择
                </div>
                <div class="component-item" draggable="true" data-type="file">
                    <i class="fas fa-upload"></i>
                    文件上传
                </div>
            </div>

            <div class="component-category">
                <div class="category-title">模型组件</div>
                <div class="component-item" draggable="true" data-type="model-field">
                    <i class="fas fa-database"></i>
                    模型字段
                </div>
                <div class="component-item" draggable="true" data-type="model-table">
                    <i class="fas fa-table"></i>
                    模型表格
                </div>
                <div class="component-item" draggable="true" data-type="user-select">
                    <i class="fas fa-users"></i>
                    人员选择
                </div>
                <div class="component-item" draggable="true" data-type="dept-select">
                    <i class="fas fa-sitemap"></i>
                    部门选择
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="nodeConfigModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>审批节点配置</h3>
                <span class="close" onclick="closeModal('nodeConfigModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">节点名称</label>
                    <input type="text" class="form-control" id="nodeName" placeholder="请输入节点名称">
                </div>

                <div class="form-group">
                    <label class="form-label">处理人类型</label>
                    <select class="form-control" id="approverType">
                        <option value="single">指定单个审批人</option>
                        <option value="multiple">指定多个审批人</option>
                        <option value="previous">上一节点自选</option>
                        <option value="initiator">流程发起人</option>
                        <option value="usergroup">用户组</option>
                        <option value="position">组织管理任职</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">审批人员</label>
                    <input type="text" class="form-control" id="approvers" placeholder="请选择审批人员">
                </div>

                <div class="form-group">
                    <label class="form-label">会签模式</label>
                    <div class="radio-group">
                        <div class="radio-item">
                            <input type="radio" id="countersignOff" name="countersign" value="off" checked>
                            <label for="countersignOff">关闭</label>
                        </div>
                        <div class="radio-item">
                            <input type="radio" id="countersignOn" name="countersign" value="on">
                            <label for="countersignOn">开启</label>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">操作权限</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="approve" value="approve" checked>
                            <label for="approve">同意</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="reject" value="reject" checked>
                            <label for="reject">拒绝</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="return" value="return">
                            <label for="return">退回</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="transfer" value="transfer">
                            <label for="transfer">转办</label>
                        </div>
                    </div>
                </div>

                <div style="text-align: right; margin-top: 30px;">
                    <button class="btn btn-outline" onclick="closeModal('nodeConfigModal')">取消</button>
                    <button class="btn btn-primary" onclick="saveNodeConfig()">确定</button>
                </div>
            </div>
        </div>
    </div>

    <div id="userGroupModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>用户组配置</h3>
                <span class="close" onclick="closeModal('userGroupModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">用户组名称</label>
                    <input type="text" class="form-control" id="groupName" placeholder="请输入用户组名称">
                </div>

                <div class="form-group">
                    <label class="form-label">用户组描述</label>
                    <textarea class="form-control" id="groupDescription" rows="3" placeholder="请输入用户组描述"></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label">功能权限</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="permCreate" value="create">
                            <label for="permCreate">创建权限</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="permEdit" value="edit">
                            <label for="permEdit">编辑权限</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="permDelete" value="delete">
                            <label for="permDelete">删除权限</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="permView" value="view" checked>
                            <label for="permView">查看权限</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="permPublish" value="publish">
                            <label for="permPublish">发布权限</label>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">用户组成员</label>
                    <input type="text" class="form-control" id="groupMembers" placeholder="请选择用户组成员">
                </div>

                <div style="text-align: right; margin-top: 30px;">
                    <button class="btn btn-outline" onclick="closeModal('userGroupModal')">取消</button>
                    <button class="btn btn-primary" onclick="saveUserGroup()">确定</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentService = null;
        let draggedComponent = null;
        let workflowNodes = [];
        let componentLibraryVisible = false;

        // 服务数据
        const services = [
            {
                id: 1,
                name: '重点事项督办',
                category: 'office',
                status: 'published',
                description: '用于重点工作事项的督办和跟踪管理',
                createTime: '2024-01-15',
                useCount: 45
            },
            {
                id: 2,
                name: '课程缓考申请',
                category: 'academic',
                status: 'published',
                description: '学生因特殊情况无法按时参加考试时的缓考申请流程',
                createTime: '2024-01-12',
                useCount: 23
            },
            {
                id: 3,
                name: '会议室申请',
                category: 'support',
                status: 'draft',
                description: '会议室预约和使用申请，支持时间冲突检测',
                createTime: '2024-01-10',
                useCount: 67
            },
            {
                id: 4,
                name: '科研项目申报',
                category: 'research',
                status: 'published',
                description: '科研项目的申报、审批和管理流程',
                createTime: '2024-01-08',
                useCount: 18
            },
            {
                id: 5,
                name: '车辆门禁办理',
                category: 'security',
                status: 'disabled',
                description: '车辆进出校园的门禁卡申请和管理',
                createTime: '2024-01-05',
                useCount: 89
            }
        ];

        // 标签切换
        function showTab(tabId) {
            // 隐藏所有面板
            const panels = document.querySelectorAll('.content-panel');
            panels.forEach(panel => panel.classList.remove('active'));

            // 移除所有标签的active状态
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的面板
            document.getElementById(tabId).classList.add('active');

            // 激活对应的标签
            event.target.classList.add('active');

            // 显示组件库（仅在表单设计和流程设计时）
            if (tabId === 'form-design' || tabId === 'workflow-design') {
                showComponentLibrary();
            } else {
                hideComponentLibrary();
            }
        }

        // 显示/隐藏组件库
        function showComponentLibrary() {
            document.getElementById('componentLibrary').style.display = 'block';
            componentLibraryVisible = true;
        }

        function hideComponentLibrary() {
            document.getElementById('componentLibrary').style.display = 'none';
            componentLibraryVisible = false;
        }

        function toggleComponentLibrary() {
            if (componentLibraryVisible) {
                hideComponentLibrary();
            } else {
                showComponentLibrary();
            }
        }

        // 加载服务列表
        function loadServices() {
            const serviceList = document.getElementById('serviceList');
            serviceList.innerHTML = services.map(service => {
                const statusClass = service.status === 'published' ? 'status-published' :
                                  service.status === 'draft' ? 'status-draft' : 'status-disabled';
                const statusText = service.status === 'published' ? '已发布' :
                                 service.status === 'draft' ? '草稿' : '已停用';

                return `
                    <div class="service-item ${currentService?.id === service.id ? 'active' : ''}"
                         onclick="selectService(${service.id})">
                        <div class="service-name">${service.name}</div>
                        <div class="service-meta">
                            <span class="status-badge ${statusClass}">${statusText}</span>
                            <span>使用${service.useCount}次</span>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 选择服务
        function selectService(serviceId) {
            currentService = services.find(s => s.id === serviceId);
            loadServices(); // 重新加载以更新选中状态
            loadServiceData();
        }

        // 加载服务数据到表单
        function loadServiceData() {
            if (!currentService) return;

            document.getElementById('serviceName').value = currentService.name;
            document.getElementById('serviceCode').value = currentService.name.replace(/\s+/g, '_').toLowerCase();
            document.getElementById('serviceDescription').value = currentService.description;
            document.getElementById('serviceCategory').value = currentService.category;
            document.getElementById('publishStatus').value = currentService.status;
        }

        // 保存基本设置
        function saveBasicSettings() {
            alert('基本设置已保存');
        }

        // 预览表单
        function previewForm() {
            alert('表单预览功能');
        }

        // 保存表单设计
        function saveFormDesign() {
            alert('表单设计已保存');
        }

        // 验证流程
        function validateWorkflow() {
            alert('流程验证通过');
        }

        // 保存流程
        function saveWorkflow() {
            alert('流程已保存');
        }

        // 发布服务
        function publishService() {
            if (confirm('确定要发布此服务吗？')) {
                alert('服务发布成功');
            }
        }

        // 添加模型组件
        function addModelComponent() {
            alert('批量添加模型组件功能');
        }

        // 清空表单
        function clearForm() {
            if (confirm('确定要清空表单吗？')) {
                document.getElementById('formDesignArea').innerHTML = `
                    <div class="design-placeholder">
                        <i class="fas fa-mouse-pointer"></i>
                        <h3>拖拽组件到此处开始设计表单</h3>
                        <p>支持系统组件和模型组件，可配置数据源和字段映射</p>
                    </div>
                `;
            }
        }

        // 添加审批节点
        function addApprovalNode() {
            document.getElementById('nodeConfigModal').style.display = 'block';
        }

        // 添加条件节点
        function addConditionNode() {
            alert('添加条件分支节点');
        }

        // 添加并行节点
        function addParallelNode() {
            alert('添加并行节点');
        }

        // 清空流程
        function clearWorkflow() {
            if (confirm('确定要清空流程吗？')) {
                document.getElementById('workflowCanvas').innerHTML = `
                    <div class="design-placeholder">
                        <i class="fas fa-project-diagram"></i>
                        <h3>拖拽流程节点到此处开始设计流程</h3>
                        <p>支持会签模式、抄送设置、消息推送配置等高级功能</p>
                    </div>
                `;
            }
        }

        // 添加用户组
        function addUserGroup() {
            document.getElementById('userGroupModal').style.display = 'block';
        }

        // 编辑用户组
        function editUserGroup(groupId) {
            alert(`编辑用户组: ${groupId}`);
            document.getElementById('userGroupModal').style.display = 'block';
        }

        // 删除用户组
        function deleteUserGroup(groupId) {
            if (confirm(`确定要删除用户组"${groupId}"吗？`)) {
                alert(`用户组"${groupId}"已删除`);
            }
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 保存节点配置
        function saveNodeConfig() {
            alert('节点配置已保存');
            closeModal('nodeConfigModal');
        }

        // 保存用户组
        function saveUserGroup() {
            alert('用户组已保存');
            closeModal('userGroupModal');
        }

        // 拖拽功能
        function initDragAndDrop() {
            const components = document.querySelectorAll('.component-item');
            const formDesignArea = document.getElementById('formDesignArea');
            const workflowCanvas = document.getElementById('workflowCanvas');

            components.forEach(component => {
                component.addEventListener('dragstart', function(e) {
                    draggedComponent = this.dataset.type;
                    e.dataTransfer.effectAllowed = 'copy';
                });
            });

            // 表单设计区域
            if (formDesignArea) {
                formDesignArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'copy';
                });

                formDesignArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    if (draggedComponent) {
                        addFormComponent(draggedComponent);
                        draggedComponent = null;
                    }
                });
            }

            // 流程设计区域
            if (workflowCanvas) {
                workflowCanvas.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    e.dataTransfer.dropEffect = 'copy';
                });

                workflowCanvas.addEventListener('drop', function(e) {
                    e.preventDefault();
                    if (draggedComponent) {
                        addWorkflowNode(draggedComponent, e.offsetX, e.offsetY);
                        draggedComponent = null;
                    }
                });
            }
        }

        // 添加表单组件
        function addFormComponent(type) {
            const formDesignArea = document.getElementById('formDesignArea');

            // 清空提示文字
            if (formDesignArea.querySelector('.design-placeholder')) {
                formDesignArea.innerHTML = '';
            }

            const componentMap = {
                'text': '单行文本',
                'textarea': '多行文本',
                'number': '数字输入',
                'select': '下拉选择',
                'radio': '单选框',
                'checkbox': '复选框',
                'date': '日期选择',
                'file': '文件上传',
                'model-field': '模型字段',
                'model-table': '模型表格',
                'user-select': '人员选择',
                'dept-select': '部门选择'
            };

            const componentDiv = document.createElement('div');
            componentDiv.style.cssText = `
                background: white;
                border: 2px solid #4f46e5;
                border-radius: 8px;
                padding: 15px;
                margin: 10px 0;
                cursor: pointer;
                transition: all 0.3s;
            `;
            componentDiv.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span><i class="fas fa-edit"></i> ${componentMap[type] || type}</span>
                    <button onclick="this.parentElement.parentElement.remove()"
                            style="background: #ef4444; color: white; border: none; border-radius: 4px; padding: 5px 10px; cursor: pointer;">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;

            componentDiv.addEventListener('click', function() {
                showComponentProperties(type);
            });

            formDesignArea.appendChild(componentDiv);
        }

        // 添加流程节点
        function addWorkflowNode(type, x, y) {
            const workflowCanvas = document.getElementById('workflowCanvas');

            // 清空提示文字
            if (workflowCanvas.querySelector('.design-placeholder')) {
                workflowCanvas.innerHTML = '';
            }

            const nodeMap = {
                'start-node': { name: '开始', class: 'node-start' },
                'approval-node': { name: '审批', class: 'node-approval' },
                'condition-node': { name: '条件', class: 'node-condition' },
                'end-node': { name: '结束', class: 'node-end' }
            };

            const nodeInfo = nodeMap[type] || { name: type, class: 'node-approval' };

            const nodeDiv = document.createElement('div');
            nodeDiv.className = `workflow-node ${nodeInfo.class}`;
            nodeDiv.style.left = (x || 100) + 'px';
            nodeDiv.style.top = (y || 100) + 'px';
            nodeDiv.innerHTML = nodeInfo.name;

            nodeDiv.addEventListener('click', function() {
                if (type === 'approval-node') {
                    document.getElementById('nodeConfigModal').style.display = 'block';
                }
            });

            workflowCanvas.appendChild(nodeDiv);
        }

        // 显示组件属性
        function showComponentProperties(type) {
            const propertyPanel = document.getElementById('propertyPanel');
            propertyPanel.innerHTML = `
                <div class="form-group">
                    <label class="form-label">组件类型</label>
                    <input type="text" class="form-control" value="${type}" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">字段名称</label>
                    <input type="text" class="form-control" placeholder="请输入字段名称">
                </div>
                <div class="form-group">
                    <label class="form-label">是否必填</label>
                    <select class="form-control">
                        <option value="false">否</option>
                        <option value="true">是</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">校验规则</label>
                    <input type="text" class="form-control" placeholder="请输入校验规则">
                </div>
                <div class="form-group">
                    <label class="form-label">联动规则</label>
                    <input type="text" class="form-control" placeholder="请输入联动规则">
                </div>
                <div class="form-group">
                    <label class="form-label">数据源</label>
                    <input type="text" class="form-control" placeholder="请配置数据源">
                </div>
            `;
        }

        // 筛选功能
        function initFilters() {
            const filterTags = document.querySelectorAll('.filter-tag');
            filterTags.forEach(tag => {
                tag.addEventListener('click', function() {
                    const siblings = this.parentElement.querySelectorAll('.filter-tag');
                    siblings.forEach(sibling => sibling.classList.remove('active'));
                    this.classList.add('active');

                    const status = this.dataset.status;
                    filterServices(status);
                });
            });
        }

        // 筛选服务
        function filterServices(status) {
            // 这里可以添加实际的筛选逻辑
            console.log('筛选状态:', status);
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadServices();
            initDragAndDrop();
            initFilters();
            hideComponentLibrary(); // 初始隐藏组件库

            // 搜索功能
            document.getElementById('serviceSearch').addEventListener('input', function(e) {
                const searchTerm = e.target.value.toLowerCase();
                // 这里可以添加搜索逻辑
                console.log('搜索:', searchTerm);
            });

            // 点击模态框外部关闭
            window.addEventListener('click', function(e) {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    if (e.target === modal) {
                        modal.style.display = 'none';
                    }
                });
            });
        });
    </script>
</body>
</html>
