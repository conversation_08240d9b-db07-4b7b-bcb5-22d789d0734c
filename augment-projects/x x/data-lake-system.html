<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据湖管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            flex-wrap: wrap;
        }
        
        .nav-tab {
            flex: 1;
            min-width: 120px;
            padding: 15px 10px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 13px;
            font-weight: 500;
            text-align: center;
        }
        
        .nav-tab.active {
            background: #667eea;
            color: white;
        }
        
        .nav-tab:hover {
            background: #5a6fd8;
            color: white;
        }
        
        .content-panel {
            display: none;
            background: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .content-panel.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 13px;
        }
        
        .data-table th,
        .data-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            position: sticky;
            top: 0;
        }
        
        .data-table tr:hover {
            background: #f8f9ff;
        }
        
        .row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .col {
            flex: 1;
        }
        
        .col-2 {
            flex: 2;
        }
        
        .col-3 {
            flex: 3;
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .status-running {
            background: #d4edda;
            color: #155724;
        }
        
        .status-completed {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .collection-mode {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .mode-card {
            flex: 1;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .mode-card:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .mode-card.selected {
            border-color: #667eea;
            background: #e3f2fd;
        }
        
        .log-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .log-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .log-card:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
        }
        
        .log-card.selected {
            border-color: #667eea;
            background: #e3f2fd;
        }
        
        .table-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .overview-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #667eea;
        }
        
        .overview-card h4 {
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .overview-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .overview-item .label {
            color: #666;
        }
        
        .overview-item .value {
            font-weight: 500;
        }
        
        .lineage-canvas {
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
            min-height: 400px;
            position: relative;
            overflow: auto;
        }
        
        .lineage-node {
            position: absolute;
            background: white;
            border: 2px solid #667eea;
            border-radius: 8px;
            padding: 10px;
            min-width: 120px;
            text-align: center;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .lineage-node.source {
            background: #e3f2fd;
        }
        
        .lineage-node.warehouse {
            background: #f3e5f5;
        }
        
        .lineage-node.mart {
            background: #e8f5e8;
        }
        
        .blacklist-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .visual-editor {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
            min-height: 300px;
        }
        
        .editor-toolbar {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #ddd;
        }
        
        .editor-node {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 10px;
            margin: 10px;
            cursor: pointer;
            display: inline-block;
            min-width: 100px;
            text-align: center;
        }
        
        .editor-node:hover {
            border-color: #667eea;
            box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
        }
        
        .metric-builder {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
        }
        
        .metric-config {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }
        
        .metric-preview {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
        }
        
        .search-box {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .search-box input {
            flex: 1;
        }
        
        .tab-content {
            margin-top: 20px;
        }
        
        .sub-tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        
        .sub-tab {
            padding: 10px 20px;
            background: none;
            border: none;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .sub-tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: 500;
        }
        
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            background: #28a745;
            height: 100%;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>数据湖管理系统</h1>
        <p>高效数据采集、智能数据治理、可视化数据血缘、灵活指标开发的一体化平台</p>
    </div>

    <div class="container">
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showPanel('collection')">数据采集</button>
            <button class="nav-tab" onclick="showPanel('logs')">日志管理</button>
            <button class="nav-tab" onclick="showPanel('tables')">贴源表管理</button>
            <button class="nav-tab" onclick="showPanel('cleaning')">数据清洗</button>
            <button class="nav-tab" onclick="showPanel('lineage')">数据血缘</button>
            <button class="nav-tab" onclick="showPanel('warehouse')">数据仓库</button>
            <button class="nav-tab" onclick="showPanel('standard')">标准表管理</button>
            <button class="nav-tab" onclick="showPanel('metrics')">指标开发</button>
            <button class="nav-tab" onclick="showPanel('topics')">业务专题</button>
        </div>

        <!-- 数据采集面板 -->
        <div id="collection" class="content-panel active">
            <h2>数据采集管理</h2>
            <div class="alert alert-info">
                支持高效执行外部数据源的批量数据采集任务，支持全量采集和增量采集模式，具备容错机制
            </div>

            <h3>采集模式选择</h3>
            <div class="collection-mode">
                <div class="mode-card" onclick="selectMode(this, 'full')">
                    <div style="font-size: 24px; margin-bottom: 10px;">📦</div>
                    <h4>全量采集</h4>
                    <p>采集数据源的全部数据</p>
                </div>
                <div class="mode-card" onclick="selectMode(this, 'incremental')">
                    <div style="font-size: 24px; margin-bottom: 10px;">📈</div>
                    <h4>增量采集</h4>
                    <p>仅采集新增或变更的数据</p>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>采集任务名称</label>
                        <input type="text" class="form-control" placeholder="请输入采集任务名称">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>数据源</label>
                        <select class="form-control">
                            <option>请选择数据源</option>
                            <option>MySQL-学生管理系统</option>
                            <option>Oracle-教务系统</option>
                            <option>PostgreSQL-科研系统</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>源表配置</label>
                        <textarea class="form-control" rows="3" placeholder="请输入源表名或SQL查询语句"></textarea>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>增量字段（增量采集时必填）</label>
                        <input type="text" class="form-control" placeholder="如：update_time, id">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>容错配置</label>
                        <select class="form-control">
                            <option>跳过错误数据继续执行</option>
                            <option>记录错误后继续执行</option>
                            <option>遇到错误立即停止</option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>批处理大小</label>
                        <input type="number" class="form-control" placeholder="1000" value="1000">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <button class="btn btn-primary">创建采集任务</button>
                <button class="btn btn-success">测试连接</button>
                <button class="btn btn-info">预览数据</button>
            </div>

            <h3>采集任务列表</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>任务名称</th>
                        <th>采集模式</th>
                        <th>数据源</th>
                        <th>状态</th>
                        <th>采集记录数</th>
                        <th>错误记录数</th>
                        <th>最后执行时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>学生信息全量采集</td>
                        <td>全量采集</td>
                        <td>MySQL-学生管理系统</td>
                        <td><span class="status-badge status-completed">已完成</span></td>
                        <td>15,234</td>
                        <td>12</td>
                        <td>2024-01-15 10:30:00</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">执行</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">日志</button>
                        </td>
                    </tr>
                    <tr>
                        <td>成绩数据增量采集</td>
                        <td>增量采集</td>
                        <td>Oracle-教务系统</td>
                        <td><span class="status-badge status-running">运行中</span></td>
                        <td>3,456</td>
                        <td>5</td>
                        <td>2024-01-15 11:00:00</td>
                        <td>
                            <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">停止</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">监控</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 日志管理面板 -->
        <div id="logs" class="content-panel">
            <h2>日志管理</h2>
            <div class="alert alert-info">
                支持多种日志类型：SNMP网络日志、文件日志、Syslog日志以及自定义日志类型
            </div>

            <h3>日志类型选择</h3>
            <div class="log-types">
                <div class="log-card" onclick="selectLogType(this, 'snmp')">
                    <div style="font-size: 24px; margin-bottom: 10px;">🌐</div>
                    <h4>SNMP网络日志</h4>
                    <p>网络设备监控日志</p>
                </div>
                <div class="log-card" onclick="selectLogType(this, 'file')">
                    <div style="font-size: 24px; margin-bottom: 10px;">📄</div>
                    <h4>文件日志</h4>
                    <p>应用程序文件日志</p>
                </div>
                <div class="log-card" onclick="selectLogType(this, 'syslog')">
                    <div style="font-size: 24px; margin-bottom: 10px;">🖥️</div>
                    <h4>Syslog日志</h4>
                    <p>系统级别日志</p>
                </div>
                <div class="log-card" onclick="selectLogType(this, 'custom')">
                    <div style="font-size: 24px; margin-bottom: 10px;">⚙️</div>
                    <h4>自定义日志</h4>
                    <p>用户自定义格式日志</p>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>日志采集任务名称</label>
                        <input type="text" class="form-control" placeholder="请输入日志采集任务名称">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>日志源地址</label>
                        <input type="text" class="form-control" placeholder="如：192.168.1.100:514">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>日志格式配置</label>
                        <textarea class="form-control" rows="4" placeholder="请输入日志格式规则或正则表达式">%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} %{GREEDYDATA:message}</textarea>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>过滤条件</label>
                        <textarea class="form-control" rows="4" placeholder="请输入日志过滤条件">level != "DEBUG"
timestamp >= "2024-01-01"</textarea>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <button class="btn btn-primary">创建日志采集任务</button>
                <button class="btn btn-success">测试配置</button>
                <button class="btn btn-info">预览日志</button>
            </div>

            <h3>日志采集任务列表</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>任务名称</th>
                        <th>日志类型</th>
                        <th>日志源</th>
                        <th>状态</th>
                        <th>采集条数</th>
                        <th>最后采集时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>网络设备监控日志</td>
                        <td>SNMP网络日志</td>
                        <td>192.168.1.100:161</td>
                        <td><span class="status-badge status-running">运行中</span></td>
                        <td>45,678</td>
                        <td>2024-01-15 11:30:00</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">停止</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看</button>
                        </td>
                    </tr>
                    <tr>
                        <td>应用系统日志</td>
                        <td>文件日志</td>
                        <td>/var/log/app.log</td>
                        <td><span class="status-badge status-completed">已完成</span></td>
                        <td>12,345</td>
                        <td>2024-01-15 10:45:00</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">重启</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 贴源表管理面板 -->
        <div id="tables" class="content-panel">
            <h2>贴源表信息概览</h2>
            <div class="alert alert-info">
                提供详尽的贴源表信息概览，查看源头表及当前表的关键信息
            </div>

            <div class="search-box">
                <input type="text" class="form-control" placeholder="搜索表名...">
                <select class="form-control" style="flex: 0 0 150px;">
                    <option>全部数据源</option>
                    <option>MySQL数据源</option>
                    <option>Oracle数据源</option>
                    <option>PostgreSQL数据源</option>
                </select>
                <button class="btn btn-primary">搜索</button>
            </div>

            <div class="table-overview">
                <div class="overview-card">
                    <h4>源头表信息</h4>
                    <div class="overview-item">
                        <span class="label">数据源名称：</span>
                        <span class="value">MySQL-学生管理系统</span>
                    </div>
                    <div class="overview-item">
                        <span class="label">原始表名：</span>
                        <span class="value">student_info</span>
                    </div>
                    <div class="overview-item">
                        <span class="label">原始数据量：</span>
                        <span class="value">15,234 条</span>
                    </div>
                    <div class="overview-item">
                        <span class="label">表结构：</span>
                        <span class="value">23 个字段</span>
                    </div>
                </div>

                <div class="overview-card">
                    <h4>当前表信息</h4>
                    <div class="overview-item">
                        <span class="label">逻辑表名：</span>
                        <span class="value">ods_student_info</span>
                    </div>
                    <div class="overview-item">
                        <span class="label">物理存储表名：</span>
                        <span class="value">ods_student_info_20240115</span>
                    </div>
                    <div class="overview-item">
                        <span class="label">字段总数：</span>
                        <span class="value">25 个字段</span>
                    </div>
                    <div class="overview-item">
                        <span class="label">当前数据量：</span>
                        <span class="value">15,222 条</span>
                    </div>
                </div>

                <div class="overview-card">
                    <h4>管理信息</h4>
                    <div class="overview-item">
                        <span class="label">最近更新时间：</span>
                        <span class="value">2024-01-15 10:30:00</span>
                    </div>
                    <div class="overview-item">
                        <span class="label">负责管理单位：</span>
                        <span class="value">学生处</span>
                    </div>
                    <div class="overview-item">
                        <span class="label">数据集成任务：</span>
                        <span class="value">学生信息全量采集</span>
                    </div>
                    <div class="overview-item">
                        <span class="label">数据质量评分：</span>
                        <span class="value" style="color: #28a745;">95.2%</span>
                    </div>
                </div>
            </div>

            <h3>贴源表列表</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>逻辑表名</th>
                        <th>物理表名</th>
                        <th>数据源</th>
                        <th>原始表名</th>
                        <th>字段数</th>
                        <th>数据量</th>
                        <th>最后更新</th>
                        <th>负责单位</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>ods_student_info</td>
                        <td>ods_student_info_20240115</td>
                        <td>MySQL-学生管理系统</td>
                        <td>student_info</td>
                        <td>25</td>
                        <td>15,222</td>
                        <td>2024-01-15 10:30</td>
                        <td>学生处</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">血缘</button>
                        </td>
                    </tr>
                    <tr>
                        <td>ods_teacher_info</td>
                        <td>ods_teacher_info_20240115</td>
                        <td>Oracle-教务系统</td>
                        <td>teacher_basic</td>
                        <td>18</td>
                        <td>1,234</td>
                        <td>2024-01-15 09:45</td>
                        <td>人事处</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">血缘</button>
                        </td>
                    </tr>
                    <tr>
                        <td>ods_course_info</td>
                        <td>ods_course_info_20240115</td>
                        <td>PostgreSQL-科研系统</td>
                        <td>course_master</td>
                        <td>15</td>
                        <td>567</td>
                        <td>2024-01-15 08:30</td>
                        <td>教务处</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">血缘</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 数据清洗面板 -->
        <div id="cleaning" class="content-panel">
            <h2>数据清洗管理</h2>
            <div class="alert alert-info">
                支持用户自定义数据清洗规则，剔除不符合要求或存在问题的脏数据，清洗过滤的数据统一纳入黑名单数据库管理
            </div>

            <div class="sub-tabs">
                <button class="sub-tab active" onclick="showSubPanel('rules')">清洗规则</button>
                <button class="sub-tab" onclick="showSubPanel('blacklist')">黑名单管理</button>
                <button class="sub-tab" onclick="showSubPanel('quality')">数据质量</button>
            </div>

            <div id="rules" class="tab-content">
                <h3>自定义清洗规则</h3>
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>规则名称</label>
                            <input type="text" class="form-control" placeholder="请输入清洗规则名称">
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>应用表</label>
                            <select class="form-control">
                                <option>ods_student_info</option>
                                <option>ods_teacher_info</option>
                                <option>ods_course_info</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>清洗条件</label>
                    <textarea class="form-control" rows="4" placeholder="请输入清洗条件，如：age < 0 OR age > 150">-- 年龄异常数据
age < 0 OR age > 150

-- 手机号格式错误
phone NOT REGEXP '^1[3-9][0-9]{9}$'

-- 邮箱格式错误
email NOT REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'</textarea>
                </div>

                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>处理方式</label>
                            <select class="form-control">
                                <option>移入黑名单</option>
                                <option>标记为异常</option>
                                <option>自动修正</option>
                                <option>删除记录</option>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>执行频率</label>
                            <select class="form-control">
                                <option>实时执行</option>
                                <option>每小时执行</option>
                                <option>每日执行</option>
                                <option>手动执行</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <button class="btn btn-primary">保存规则</button>
                    <button class="btn btn-success">测试规则</button>
                    <button class="btn btn-info">执行清洗</button>
                </div>

                <h3>清洗规则列表</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>规则名称</th>
                            <th>应用表</th>
                            <th>处理方式</th>
                            <th>执行频率</th>
                            <th>最后执行</th>
                            <th>处理记录数</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>学生年龄异常检测</td>
                            <td>ods_student_info</td>
                            <td>移入黑名单</td>
                            <td>每日执行</td>
                            <td>2024-01-15 02:00</td>
                            <td>12</td>
                            <td><span class="status-badge status-completed">正常</span></td>
                            <td>
                                <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                                <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">执行</button>
                            </td>
                        </tr>
                        <tr>
                            <td>联系方式格式校验</td>
                            <td>ods_student_info</td>
                            <td>自动修正</td>
                            <td>实时执行</td>
                            <td>2024-01-15 11:30</td>
                            <td>45</td>
                            <td><span class="status-badge status-running">运行中</span></td>
                            <td>
                                <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">停止</button>
                                <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">日志</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div id="blacklist" class="tab-content" style="display: none;">
                <h3>黑名单数据管理</h3>
                <div class="blacklist-stats">
                    <div class="stat-card">
                        <div class="stat-number">1,234</div>
                        <div class="stat-label">总黑名单记录</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">45</div>
                        <div class="stat-label">今日新增</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">12</div>
                        <div class="stat-label">待处理</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">98.5%</div>
                        <div class="stat-label">数据质量</div>
                    </div>
                </div>

                <div class="search-box">
                    <input type="text" class="form-control" placeholder="搜索黑名单数据...">
                    <select class="form-control" style="flex: 0 0 150px;">
                        <option>全部表</option>
                        <option>ods_student_info</option>
                        <option>ods_teacher_info</option>
                    </select>
                    <select class="form-control" style="flex: 0 0 120px;">
                        <option>全部类型</option>
                        <option>格式错误</option>
                        <option>数值异常</option>
                        <option>重复数据</option>
                    </select>
                    <button class="btn btn-primary">搜索</button>
                </div>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>表名</th>
                            <th>记录ID</th>
                            <th>错误类型</th>
                            <th>错误描述</th>
                            <th>原始数据</th>
                            <th>加入时间</th>
                            <th>处理状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>ods_student_info</td>
                            <td>STU001234</td>
                            <td>数值异常</td>
                            <td>年龄为负数</td>
                            <td>{"age": -5, "name": "张三"}</td>
                            <td>2024-01-15 10:30</td>
                            <td><span class="status-badge status-pending">待处理</span></td>
                            <td>
                                <button class="btn btn-success" style="padding: 4px 8px; font-size: 11px;">修正</button>
                                <button class="btn btn-danger" style="padding: 4px 8px; font-size: 11px;">删除</button>
                                <button class="btn btn-info" style="padding: 4px 8px; font-size: 11px;">详情</button>
                            </td>
                        </tr>
                        <tr>
                            <td>ods_student_info</td>
                            <td>STU001235</td>
                            <td>格式错误</td>
                            <td>手机号格式不正确</td>
                            <td>{"phone": "123456", "name": "李四"}</td>
                            <td>2024-01-15 09:45</td>
                            <td><span class="status-badge status-completed">已处理</span></td>
                            <td>
                                <button class="btn btn-info" style="padding: 4px 8px; font-size: 11px;">查看</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div id="quality" class="tab-content" style="display: none;">
                <h3>数据质量监控</h3>
                <div class="alert alert-success">
                    数据质量整体评分：<strong>95.2%</strong> - 优秀
                </div>

                <div class="row">
                    <div class="col">
                        <h4>完整性检查</h4>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 98%;"></div>
                        </div>
                        <p>98% - 缺失值较少</p>
                    </div>
                    <div class="col">
                        <h4>准确性检查</h4>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 94%;"></div>
                        </div>
                        <p>94% - 数据准确度良好</p>
                    </div>
                    <div class="col">
                        <h4>一致性检查</h4>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 96%;"></div>
                        </div>
                        <p>96% - 数据一致性较好</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据血缘面板 -->
        <div id="lineage" class="content-panel">
            <h2>数据血缘关系管理</h2>
            <div class="alert alert-info">
                支持对贴源表和标准表之间的数据血缘关系进行全面管理，构建涵盖数据湖、数据仓库层以及数据集市层的完整数据链路视图
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>选择表</label>
                        <select class="form-control" onchange="loadLineage(this.value)">
                            <option>请选择要查看血缘的表</option>
                            <option value="ods_student_info">ods_student_info</option>
                            <option value="dwd_student_info">dwd_student_info</option>
                            <option value="dws_student_summary">dws_student_summary</option>
                            <option value="ads_student_report">ads_student_report</option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>血缘方向</label>
                        <select class="form-control">
                            <option>上下游血缘</option>
                            <option>上游血缘</option>
                            <option>下游血缘</option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>血缘层级</label>
                        <select class="form-control">
                            <option>全部层级</option>
                            <option>1级血缘</option>
                            <option>2级血缘</option>
                            <option>3级血缘</option>
                        </select>
                    </div>
                </div>
            </div>

            <h3>数据血缘视图</h3>
            <div class="lineage-canvas" id="lineageCanvas">
                <!-- 数据湖层 -->
                <div class="lineage-node source" style="top: 50px; left: 50px;">
                    <strong>数据源</strong><br>
                    MySQL-学生系统<br>
                    student_info
                </div>

                <!-- 贴源层 -->
                <div class="lineage-node source" style="top: 50px; left: 250px;">
                    <strong>贴源层(ODS)</strong><br>
                    ods_student_info<br>
                    15,222条
                </div>

                <!-- 数据仓库层 -->
                <div class="lineage-node warehouse" style="top: 50px; left: 450px;">
                    <strong>明细层(DWD)</strong><br>
                    dwd_student_info<br>
                    15,200条
                </div>

                <div class="lineage-node warehouse" style="top: 200px; left: 450px;">
                    <strong>汇总层(DWS)</strong><br>
                    dws_student_summary<br>
                    1,234条
                </div>

                <!-- 数据集市层 -->
                <div class="lineage-node mart" style="top: 50px; left: 650px;">
                    <strong>应用层(ADS)</strong><br>
                    ads_student_report<br>
                    567条
                </div>

                <div class="lineage-node mart" style="top: 200px; left: 650px;">
                    <strong>应用层(ADS)</strong><br>
                    ads_student_analysis<br>
                    234条
                </div>

                <!-- 连接线 -->
                <svg style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none;">
                    <line x1="170" y1="85" x2="250" y2="85" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="370" y1="85" x2="450" y2="85" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="570" y1="85" x2="650" y2="85" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="520" y1="120" x2="520" y2="200" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)"/>
                    <line x1="570" y1="235" x2="650" y2="235" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)"/>

                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#667eea"/>
                        </marker>
                    </defs>
                </svg>
            </div>

            <h3>血缘关系详情</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>源表</th>
                        <th>目标表</th>
                        <th>关系类型</th>
                        <th>转换逻辑</th>
                        <th>字段映射</th>
                        <th>最后更新</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>student_info</td>
                        <td>ods_student_info</td>
                        <td>数据采集</td>
                        <td>全量同步</td>
                        <td>1:1映射</td>
                        <td>2024-01-15 10:30</td>
                        <td>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看</button>
                        </td>
                    </tr>
                    <tr>
                        <td>ods_student_info</td>
                        <td>dwd_student_info</td>
                        <td>数据清洗</td>
                        <td>清洗+标准化</td>
                        <td>字段转换</td>
                        <td>2024-01-15 11:00</td>
                        <td>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看</button>
                        </td>
                    </tr>
                    <tr>
                        <td>dwd_student_info</td>
                        <td>dws_student_summary</td>
                        <td>数据聚合</td>
                        <td>按班级汇总</td>
                        <td>聚合计算</td>
                        <td>2024-01-15 11:30</td>
                        <td>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 数据仓库面板 -->
        <div id="warehouse" class="content-panel">
            <h2>数据仓库管理</h2>
            <div class="alert alert-info">
                支持数据仓库层数据模型的批量导入功能，确保相关引用表及引用代码表能够同步导入
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>模型文件上传</label>
                        <input type="file" class="form-control" accept=".json,.xml,.sql" multiple>
                        <small class="form-text text-muted">支持JSON、XML、SQL格式的模型文件</small>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>导入模式</label>
                        <select class="form-control">
                            <option>增量导入</option>
                            <option>全量覆盖</option>
                            <option>仅验证不导入</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" checked> 自动导入引用表
                </label>
                <br>
                <label>
                    <input type="checkbox" checked> 自动导入代码表
                </label>
                <br>
                <label>
                    <input type="checkbox"> 覆盖已存在的表
                </label>
            </div>

            <div class="form-group">
                <button class="btn btn-primary">开始导入</button>
                <button class="btn btn-success">验证模型</button>
                <button class="btn btn-info">预览导入</button>
            </div>

            <h3>导入进度</h3>
            <div class="alert alert-success">
                正在导入数据模型... 已完成 15/20 个表
            </div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 75%;"></div>
            </div>

            <h3>数据仓库层级结构</h3>
            <div class="row">
                <div class="col">
                    <h4>明细层 (DWD)</h4>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>表名</th>
                                <th>记录数</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>dwd_student_info</td>
                                <td>15,200</td>
                                <td><span class="status-badge status-completed">正常</span></td>
                            </tr>
                            <tr>
                                <td>dwd_teacher_info</td>
                                <td>1,230</td>
                                <td><span class="status-badge status-completed">正常</span></td>
                            </tr>
                            <tr>
                                <td>dwd_course_info</td>
                                <td>565</td>
                                <td><span class="status-badge status-running">更新中</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col">
                    <h4>汇总层 (DWS)</h4>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>表名</th>
                                <th>记录数</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>dws_student_summary</td>
                                <td>1,234</td>
                                <td><span class="status-badge status-completed">正常</span></td>
                            </tr>
                            <tr>
                                <td>dws_teacher_summary</td>
                                <td>123</td>
                                <td><span class="status-badge status-completed">正常</span></td>
                            </tr>
                            <tr>
                                <td>dws_course_summary</td>
                                <td>56</td>
                                <td><span class="status-badge status-pending">待更新</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <h3>引用表管理</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>引用表名</th>
                        <th>引用类型</th>
                        <th>被引用次数</th>
                        <th>最后更新</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>dim_department</td>
                        <td>维度表</td>
                        <td>15</td>
                        <td>2024-01-15 09:00</td>
                        <td><span class="status-badge status-completed">正常</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">引用关系</button>
                        </td>
                    </tr>
                    <tr>
                        <td>code_gender</td>
                        <td>代码表</td>
                        <td>8</td>
                        <td>2024-01-10 15:30</td>
                        <td><span class="status-badge status-completed">正常</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">引用关系</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 标准表管理面板 -->
        <div id="standard" class="content-panel">
            <h2>标准表管理</h2>
            <div class="alert alert-info">
                支持查看标准表的综合概览，包括逻辑表名、物理存储表名、所引用的模型详情、字段总数等核心信息
            </div>

            <div class="sub-tabs">
                <button class="sub-tab active" onclick="showSubPanel('overview')">表概览</button>
                <button class="sub-tab" onclick="showSubPanel('details')">数据明细</button>
                <button class="sub-tab" onclick="showSubPanel('alignment')">标准对齐</button>
                <button class="sub-tab" onclick="showSubPanel('tasks')">清洗任务</button>
            </div>

            <div id="overview" class="tab-content">
                <h3>标准表综合概览</h3>
                <div class="table-overview">
                    <div class="overview-card">
                        <h4>基本信息</h4>
                        <div class="overview-item">
                            <span class="label">逻辑表名：</span>
                            <span class="value">dwd_student_info</span>
                        </div>
                        <div class="overview-item">
                            <span class="label">物理存储表名：</span>
                            <span class="value">dwd_student_info_20240115</span>
                        </div>
                        <div class="overview-item">
                            <span class="label">引用模型：</span>
                            <span class="value">学生信息标准模型v2.1</span>
                        </div>
                        <div class="overview-item">
                            <span class="label">字段总数：</span>
                            <span class="value">28 个字段</span>
                        </div>
                    </div>

                    <div class="overview-card">
                        <h4>数据统计</h4>
                        <div class="overview-item">
                            <span class="label">当前数据量：</span>
                            <span class="value">15,200 条</span>
                        </div>
                        <div class="overview-item">
                            <span class="label">最近数据更新：</span>
                            <span class="value">2024-01-15 11:00:00</span>
                        </div>
                        <div class="overview-item">
                            <span class="label">数据增长率：</span>
                            <span class="value" style="color: #28a745;">+2.3%</span>
                        </div>
                        <div class="overview-item">
                            <span class="label">数据质量评分：</span>
                            <span class="value" style="color: #28a745;">96.8%</span>
                        </div>
                    </div>

                    <div class="overview-card">
                        <h4>管理信息</h4>
                        <div class="overview-item">
                            <span class="label">负责单位：</span>
                            <span class="value">学生处</span>
                        </div>
                        <div class="overview-item">
                            <span class="label">数据集成状态：</span>
                            <span class="value" style="color: #28a745;">正常</span>
                        </div>
                        <div class="overview-item">
                            <span class="label">关联任务：</span>
                            <span class="value">学生数据清洗任务</span>
                        </div>
                        <div class="overview-item">
                            <span class="label">变更历史：</span>
                            <span class="value">15 次变更</span>
                        </div>
                    </div>
                </div>

                <h3>标准表列表</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>逻辑表名</th>
                            <th>物理表名</th>
                            <th>引用模型</th>
                            <th>字段数</th>
                            <th>数据量</th>
                            <th>最后更新</th>
                            <th>负责单位</th>
                            <th>集成状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>dwd_student_info</td>
                            <td>dwd_student_info_20240115</td>
                            <td>学生信息标准模型v2.1</td>
                            <td>28</td>
                            <td>15,200</td>
                            <td>2024-01-15 11:00</td>
                            <td>学生处</td>
                            <td><span class="status-badge status-completed">正常</span></td>
                            <td>
                                <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                                <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">对齐</button>
                            </td>
                        </tr>
                        <tr>
                            <td>dwd_teacher_info</td>
                            <td>dwd_teacher_info_20240115</td>
                            <td>教师信息标准模型v1.8</td>
                            <td>22</td>
                            <td>1,230</td>
                            <td>2024-01-15 10:30</td>
                            <td>人事处</td>
                            <td><span class="status-badge status-running">更新中</span></td>
                            <td>
                                <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                                <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">待对齐</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div id="details" class="tab-content" style="display: none;">
                <h3>标准表数据明细</h3>
                <div class="search-box">
                    <input type="text" class="form-control" placeholder="搜索数据...">
                    <select class="form-control" style="flex: 0 0 150px;">
                        <option>dwd_student_info</option>
                        <option>dwd_teacher_info</option>
                        <option>dwd_course_info</option>
                    </select>
                    <button class="btn btn-primary">搜索</button>
                    <button class="btn btn-info">导出数据</button>
                </div>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>学生ID</th>
                            <th>姓名</th>
                            <th>性别</th>
                            <th>年龄</th>
                            <th>专业</th>
                            <th>班级</th>
                            <th>入学时间</th>
                            <th>状态</th>
                            <th>最后更新</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>STU001234</td>
                            <td>张三</td>
                            <td>男</td>
                            <td>20</td>
                            <td>计算机科学与技术</td>
                            <td>计科2021-1班</td>
                            <td>2021-09-01</td>
                            <td>在校</td>
                            <td>2024-01-15 11:00</td>
                        </tr>
                        <tr>
                            <td>STU001235</td>
                            <td>李四</td>
                            <td>女</td>
                            <td>19</td>
                            <td>软件工程</td>
                            <td>软工2022-1班</td>
                            <td>2022-09-01</td>
                            <td>在校</td>
                            <td>2024-01-15 10:45</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div id="alignment" class="tab-content" style="display: none;">
                <h3>标准对齐检测</h3>
                <div class="alert alert-warning">
                    检测到 3 个字段与数据元素标准存在差异，建议进行标准对齐操作
                </div>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>字段名</th>
                            <th>当前定义</th>
                            <th>标准定义</th>
                            <th>差异类型</th>
                            <th>建议操作</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>student_age</td>
                            <td>INT(3)</td>
                            <td>TINYINT(3)</td>
                            <td>数据类型差异</td>
                            <td>修改为TINYINT</td>
                            <td>
                                <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">对齐</button>
                                <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">忽略</button>
                            </td>
                        </tr>
                        <tr>
                            <td>phone_number</td>
                            <td>VARCHAR(15)</td>
                            <td>VARCHAR(11)</td>
                            <td>长度差异</td>
                            <td>修改长度为11</td>
                            <td>
                                <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">对齐</button>
                                <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">忽略</button>
                            </td>
                        </tr>
                        <tr>
                            <td>enrollment_date</td>
                            <td>DATETIME</td>
                            <td>DATE</td>
                            <td>精度差异</td>
                            <td>修改为DATE类型</td>
                            <td>
                                <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">对齐</button>
                                <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">忽略</button>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <div style="margin-top: 20px;">
                    <button class="btn btn-primary">批量对齐</button>
                    <button class="btn btn-success">生成对齐脚本</button>
                    <button class="btn btn-info">查看对齐历史</button>
                </div>
            </div>

            <div id="tasks" class="tab-content" style="display: none;">
                <h3>数据清洗及转换任务</h3>
                <div class="visual-editor">
                    <div class="editor-toolbar">
                        <button class="btn btn-primary">添加数据源</button>
                        <button class="btn btn-success">添加转换</button>
                        <button class="btn btn-info">添加输出</button>
                        <button class="btn btn-warning">保存任务</button>
                    </div>

                    <div style="display: flex; align-items: center; justify-content: space-around; margin-top: 20px;">
                        <div class="editor-node" style="background: #e3f2fd;">
                            <strong>数据输入源</strong><br>
                            ods_student_info
                        </div>

                        <div style="text-align: center;">
                            <div class="editor-node" style="background: #f3e5f5;">
                                <strong>左关联</strong><br>
                                dim_department<br>
                                ON dept_id
                            </div>
                            <div class="editor-node" style="background: #fff3e0; margin-top: 10px;">
                                <strong>过滤条件</strong><br>
                                status = 'active'<br>
                                age BETWEEN 16 AND 30
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <div class="editor-node" style="background: #e8f5e8;">
                                <strong>计算字段</strong><br>
                                full_name = CONCAT(first_name, last_name)<br>
                                age_group = CASE WHEN age < 20 THEN '青年' ELSE '成年' END
                            </div>
                            <div class="editor-node" style="background: #fce4ec; margin-top: 10px;">
                                <strong>字段映射</strong><br>
                                student_id → std_id<br>
                                student_name → std_name
                            </div>
                        </div>

                        <div class="editor-node" style="background: #e0f2f1;">
                            <strong>数据输出</strong><br>
                            dwd_student_info
                        </div>
                    </div>
                </div>

                <h4>清洗转换任务列表</h4>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>任务名称</th>
                            <th>输入表</th>
                            <th>输出表</th>
                            <th>转换规则</th>
                            <th>最后执行</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>学生信息标准化</td>
                            <td>ods_student_info</td>
                            <td>dwd_student_info</td>
                            <td>清洗+关联+映射</td>
                            <td>2024-01-15 11:00</td>
                            <td><span class="status-badge status-completed">已完成</span></td>
                            <td>
                                <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                                <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">执行</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 指标开发面板 -->
        <div id="metrics" class="content-panel">
            <h2>指标开发管理</h2>
            <div class="alert alert-info">
                支持通过可视化模式或SQL模式定义并开发指标，包含基于数据集开发、基于单指标开发、基于多指标开发3种方式
            </div>

            <div class="sub-tabs">
                <button class="sub-tab active" onclick="showSubPanel('dataset')">基于数据集开发</button>
                <button class="sub-tab" onclick="showSubPanel('single')">基于单指标开发</button>
                <button class="sub-tab" onclick="showSubPanel('multiple')">基于多指标开发</button>
                <button class="sub-tab" onclick="showSubPanel('sql')">SQL模式开发</button>
            </div>

            <div id="dataset" class="tab-content">
                <h3>基于数据集开发指标</h3>
                <div class="metric-builder">
                    <div class="metric-config">
                        <h4>基础信息</h4>
                        <div class="form-group">
                            <label>指标名称</label>
                            <input type="text" class="form-control" placeholder="请输入指标名称">
                        </div>
                        <div class="form-group">
                            <label>指标编码</label>
                            <input type="text" class="form-control" placeholder="请输入指标编码">
                        </div>
                        <div class="form-group">
                            <label>指标描述</label>
                            <textarea class="form-control" rows="3" placeholder="请输入指标描述"></textarea>
                        </div>

                        <h4>业务属性</h4>
                        <div class="form-group">
                            <label>业务域</label>
                            <select class="form-control">
                                <option>学生管理域</option>
                                <option>教学管理域</option>
                                <option>科研管理域</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>指标类型</label>
                            <select class="form-control">
                                <option>原子指标</option>
                                <option>派生指标</option>
                                <option>复合指标</option>
                            </select>
                        </div>

                        <h4>计算逻辑</h4>
                        <div class="form-group">
                            <label>数据集来源</label>
                            <select class="form-control" onchange="loadDatasetFields(this.value)">
                                <option>请选择数据集</option>
                                <option value="dwd_student_info">学生信息数据集</option>
                                <option value="dwd_course_info">课程信息数据集</option>
                                <option value="dwd_score_info">成绩信息数据集</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>分析维度</label>
                            <select class="form-control" multiple>
                                <option>专业</option>
                                <option>班级</option>
                                <option>年级</option>
                                <option>性别</option>
                                <option>入学年份</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>统计方式</label>
                            <select class="form-control" onchange="showStatConfig(this.value)">
                                <option value="aggregate">聚合统计</option>
                                <option value="ratio">占比统计</option>
                                <option value="rank">排名统计</option>
                                <option value="value">取值统计</option>
                                <option value="period">环比统计</option>
                            </select>
                        </div>

                        <div id="statConfig">
                            <div class="form-group">
                                <label>聚合函数</label>
                                <select class="form-control">
                                    <option>求和(SUM)</option>
                                    <option>计数(COUNT)</option>
                                    <option>去重计数(COUNT DISTINCT)</option>
                                    <option>平均值(AVG)</option>
                                    <option>最大值(MAX)</option>
                                    <option>最小值(MIN)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>聚合字段</label>
                                <select class="form-control">
                                    <option>student_id</option>
                                    <option>age</option>
                                    <option>score</option>
                                    <option>credit</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>过滤条件</label>
                            <textarea class="form-control" rows="3" placeholder="请输入过滤条件，如：status = 'active' AND age >= 18"></textarea>
                        </div>

                        <h4>任务调度配置</h4>
                        <div class="form-group">
                            <label>调度频率</label>
                            <select class="form-control">
                                <option>每日</option>
                                <option>每周</option>
                                <option>每月</option>
                                <option>实时</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>执行时间</label>
                            <input type="time" class="form-control" value="02:00">
                        </div>
                    </div>

                    <div class="metric-preview">
                        <h4>指标预览</h4>
                        <div class="alert alert-info">
                            <strong>指标名称：</strong>在校学生总数<br>
                            <strong>计算逻辑：</strong>COUNT(DISTINCT student_id) FROM dwd_student_info WHERE status = 'active'<br>
                            <strong>分析维度：</strong>专业、班级<br>
                            <strong>统计方式：</strong>去重计数
                        </div>

                        <h5>预计算结果</h5>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>专业</th>
                                    <th>班级</th>
                                    <th>学生总数</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>计算机科学与技术</td>
                                    <td>计科2021-1班</td>
                                    <td>45</td>
                                </tr>
                                <tr>
                                    <td>计算机科学与技术</td>
                                    <td>计科2021-2班</td>
                                    <td>43</td>
                                </tr>
                                <tr>
                                    <td>软件工程</td>
                                    <td>软工2021-1班</td>
                                    <td>48</td>
                                </tr>
                            </tbody>
                        </table>

                        <div style="margin-top: 20px;">
                            <button class="btn btn-primary">保存指标</button>
                            <button class="btn btn-success">测试计算</button>
                            <button class="btn btn-info">生成SQL</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="single" class="tab-content" style="display: none;">
                <h3>基于单指标开发</h3>
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>基础指标</label>
                            <select class="form-control">
                                <option>在校学生总数</option>
                                <option>教师总数</option>
                                <option>课程总数</option>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>派生方式</label>
                            <select class="form-control">
                                <option>时间维度派生</option>
                                <option>地域维度派生</option>
                                <option>业务维度派生</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>派生规则</label>
                    <textarea class="form-control" rows="4" placeholder="请输入派生规则">-- 按年级派生学生总数
SELECT
    grade,
    COUNT(DISTINCT student_id) as student_count
FROM dwd_student_info
WHERE status = 'active'
GROUP BY grade</textarea>
                </div>
            </div>

            <div id="multiple" class="tab-content" style="display: none;">
                <h3>基于多指标开发</h3>
                <div class="form-group">
                    <label>选择基础指标</label>
                    <select class="form-control" multiple>
                        <option>在校学生总数</option>
                        <option>毕业学生总数</option>
                        <option>新入学学生总数</option>
                        <option>教师总数</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>复合计算公式</label>
                    <textarea class="form-control" rows="4" placeholder="请输入复合计算公式">-- 师生比计算
teacher_count / student_count as teacher_student_ratio

-- 毕业率计算
graduate_count / total_student_count * 100 as graduation_rate</textarea>
                </div>
            </div>

            <div id="sql" class="tab-content" style="display: none;">
                <h3>SQL模式开发</h3>
                <div class="form-group">
                    <label>SQL查询语句</label>
                    <textarea class="form-control" rows="10" placeholder="请输入SQL查询语句">-- 学生平均成绩指标
SELECT
    s.major,
    s.grade,
    AVG(sc.score) as avg_score,
    COUNT(DISTINCT s.student_id) as student_count,
    COUNT(sc.score) as score_count
FROM dwd_student_info s
LEFT JOIN dwd_score_info sc ON s.student_id = sc.student_id
WHERE s.status = 'active'
    AND sc.score IS NOT NULL
    AND sc.exam_date >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)
GROUP BY s.major, s.grade
ORDER BY avg_score DESC</textarea>
                </div>

                <div class="form-group">
                    <button class="btn btn-primary">验证SQL</button>
                    <button class="btn btn-success">执行查询</button>
                    <button class="btn btn-info">保存为指标</button>
                </div>
            </div>

            <h3>指标列表</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>指标名称</th>
                        <th>指标编码</th>
                        <th>指标类型</th>
                        <th>业务域</th>
                        <th>开发方式</th>
                        <th>调度频率</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>在校学生总数</td>
                        <td>STU_TOTAL_COUNT</td>
                        <td>原子指标</td>
                        <td>学生管理域</td>
                        <td>数据集开发</td>
                        <td>每日</td>
                        <td><span class="status-badge status-completed">正常</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">执行</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看</button>
                        </td>
                    </tr>
                    <tr>
                        <td>平均成绩</td>
                        <td>SCORE_AVG</td>
                        <td>派生指标</td>
                        <td>教学管理域</td>
                        <td>SQL开发</td>
                        <td>每周</td>
                        <td><span class="status-badge status-running">计算中</span></td>
                        <td>
                            <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">停止</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">日志</button>
                        </td>
                    </tr>
                    <tr>
                        <td>师生比</td>
                        <td>TEACHER_STUDENT_RATIO</td>
                        <td>复合指标</td>
                        <td>综合管理域</td>
                        <td>多指标开发</td>
                        <td>每月</td>
                        <td><span class="status-badge status-pending">待执行</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">启动</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 业务专题面板 -->
        <div id="topics" class="content-panel">
            <h2>业务专题管理</h2>
            <div class="alert alert-info">
                支持根据不同的业务场景建设不同的业务专题数据集，完成各个场景下的用数的归集工作
            </div>

            <div class="sub-tabs">
                <button class="sub-tab active" onclick="showSubPanel('object')">面向业务对象</button>
                <button class="sub-tab" onclick="showSubPanel('process')">面向业务过程</button>
                <button class="sub-tab" onclick="showSubPanel('import')">批量导入</button>
                <button class="sub-tab" onclick="showSubPanel('themes')">主题构建</button>
            </div>

            <div id="object" class="tab-content">
                <h3>面向业务对象分类管理</h3>
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>业务对象</label>
                            <select class="form-control">
                                <option>学生</option>
                                <option>教师</option>
                                <option>课程</option>
                                <option>专业</option>
                                <option>院系</option>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>专题名称</label>
                            <input type="text" class="form-control" placeholder="请输入专题名称">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>关联数据集</label>
                    <select class="form-control" multiple>
                        <option>学生基本信息数据集</option>
                        <option>学生成绩数据集</option>
                        <option>学生选课数据集</option>
                        <option>学生奖惩数据集</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>专题描述</label>
                    <textarea class="form-control" rows="3" placeholder="请输入专题描述"></textarea>
                </div>

                <h4>学生主题数据集</h4>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>数据集名称</th>
                            <th>数据表</th>
                            <th>记录数</th>
                            <th>更新频率</th>
                            <th>最后更新</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>学生基本信息</td>
                            <td>ads_student_basic</td>
                            <td>15,200</td>
                            <td>每日</td>
                            <td>2024-01-15 02:00</td>
                            <td><span class="status-badge status-completed">正常</span></td>
                            <td>
                                <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                                <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">调用</button>
                            </td>
                        </tr>
                        <tr>
                            <td>学生学业表现</td>
                            <td>ads_student_performance</td>
                            <td>12,345</td>
                            <td>每周</td>
                            <td>2024-01-14 02:00</td>
                            <td><span class="status-badge status-completed">正常</span></td>
                            <td>
                                <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                                <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">调用</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div id="process" class="tab-content" style="display: none;">
                <h3>面向业务过程分类管理</h3>
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>业务过程</label>
                            <select class="form-control">
                                <option>招生录取</option>
                                <option>教学管理</option>
                                <option>考试评价</option>
                                <option>毕业就业</option>
                                <option>科研管理</option>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>过程阶段</label>
                            <select class="form-control">
                                <option>全过程</option>
                                <option>过程开始</option>
                                <option>过程进行</option>
                                <option>过程结束</option>
                            </select>
                        </div>
                    </div>
                </div>

                <h4>教学管理过程数据集</h4>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>过程阶段</th>
                            <th>数据集名称</th>
                            <th>涉及表数</th>
                            <th>数据量</th>
                            <th>关键指标</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>课程安排</td>
                            <td>教学计划数据集</td>
                            <td>5</td>
                            <td>2,345</td>
                            <td>开课数、选课人数</td>
                            <td><span class="status-badge status-completed">正常</span></td>
                            <td>
                                <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                                <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">维护</button>
                            </td>
                        </tr>
                        <tr>
                            <td>教学实施</td>
                            <td>教学过程数据集</td>
                            <td>8</td>
                            <td>45,678</td>
                            <td>出勤率、作业完成率</td>
                            <td><span class="status-badge status-running">更新中</span></td>
                            <td>
                                <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">监控</button>
                                <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">日志</button>
                            </td>
                        </tr>
                        <tr>
                            <td>考试评价</td>
                            <td>考试成绩数据集</td>
                            <td>6</td>
                            <td>23,456</td>
                            <td>平均分、及格率</td>
                            <td><span class="status-badge status-completed">正常</span></td>
                            <td>
                                <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                                <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">维护</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div id="import" class="tab-content" style="display: none;">
                <h3>批量导入专题层数据模型</h3>
                <div class="form-group">
                    <label>模型文件上传</label>
                    <input type="file" class="form-control" accept=".json,.xml,.xlsx" multiple>
                    <small class="form-text text-muted">支持JSON、XML、Excel格式的专题模型文件</small>
                </div>

                <div class="form-group">
                    <label>导入配置</label>
                    <div>
                        <label><input type="checkbox" checked> 自动创建不存在的表</label><br>
                        <label><input type="checkbox"> 覆盖已存在的专题</label><br>
                        <label><input type="checkbox" checked> 验证数据完整性</label><br>
                        <label><input type="checkbox" checked> 生成导入报告</label>
                    </div>
                </div>

                <div class="form-group">
                    <button class="btn btn-primary">开始导入</button>
                    <button class="btn btn-success">验证模型</button>
                    <button class="btn btn-info">下载模板</button>
                </div>

                <h4>导入历史</h4>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>导入时间</th>
                            <th>文件名</th>
                            <th>专题数量</th>
                            <th>成功数量</th>
                            <th>失败数量</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2024-01-15 10:30</td>
                            <td>student_topics.json</td>
                            <td>5</td>
                            <td>5</td>
                            <td>0</td>
                            <td><span class="status-badge status-completed">成功</span></td>
                            <td>
                                <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看报告</button>
                            </td>
                        </tr>
                        <tr>
                            <td>2024-01-14 15:20</td>
                            <td>teaching_topics.xlsx</td>
                            <td>8</td>
                            <td>6</td>
                            <td>2</td>
                            <td><span class="status-badge status-failed">部分失败</span></td>
                            <td>
                                <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">重试失败项</button>
                                <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看报告</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div id="themes" class="tab-content" style="display: none;">
                <h3>业务主题构建</h3>
                <div class="alert alert-info">
                    基于本项目中的分析展示内容完成业务主题的构建工作
                </div>

                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>主题名称</label>
                            <input type="text" class="form-control" placeholder="请输入主题名称">
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>主题类型</label>
                            <select class="form-control">
                                <option>综合分析主题</option>
                                <option>专项分析主题</option>
                                <option>对比分析主题</option>
                                <option>趋势分析主题</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>包含的分析内容</label>
                    <div style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                        <label><input type="checkbox"> 学生基本情况分析</label><br>
                        <label><input type="checkbox"> 学生学业表现分析</label><br>
                        <label><input type="checkbox"> 教师教学质量分析</label><br>
                        <label><input type="checkbox"> 课程开设情况分析</label><br>
                        <label><input type="checkbox"> 专业发展趋势分析</label><br>
                        <label><input type="checkbox"> 就业质量分析</label><br>
                        <label><input type="checkbox"> 科研成果分析</label><br>
                        <label><input type="checkbox"> 资源配置分析</label><br>
                        <label><input type="checkbox"> 财务状况分析</label><br>
                        <label><input type="checkbox"> 对外合作分析</label>
                    </div>
                </div>

                <div class="form-group">
                    <label>关联指标</label>
                    <select class="form-control" multiple>
                        <option>在校学生总数</option>
                        <option>平均成绩</option>
                        <option>师生比</option>
                        <option>就业率</option>
                        <option>科研项目数</option>
                    </select>
                </div>

                <div class="form-group">
                    <button class="btn btn-primary">创建主题</button>
                    <button class="btn btn-success">预览主题</button>
                    <button class="btn btn-info">导出配置</button>
                </div>

                <h4>已构建主题列表</h4>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>主题名称</th>
                            <th>主题类型</th>
                            <th>包含内容数</th>
                            <th>关联指标数</th>
                            <th>创建时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>学生综合分析主题</td>
                            <td>综合分析主题</td>
                            <td>6</td>
                            <td>12</td>
                            <td>2024-01-15</td>
                            <td><span class="status-badge status-completed">已发布</span></td>
                            <td>
                                <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                                <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看</button>
                                <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">调用</button>
                            </td>
                        </tr>
                        <tr>
                            <td>教学质量分析主题</td>
                            <td>专项分析主题</td>
                            <td>4</td>
                            <td>8</td>
                            <td>2024-01-14</td>
                            <td><span class="status-badge status-running">构建中</span></td>
                            <td>
                                <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">监控</button>
                                <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">日志</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        let selectedMode = null;
        let selectedLogType = null;

        // 切换面板显示
        function showPanel(panelId) {
            // 隐藏所有面板
            const panels = document.querySelectorAll('.content-panel');
            panels.forEach(panel => panel.classList.remove('active'));

            // 移除所有标签的active状态
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的面板
            document.getElementById(panelId).classList.add('active');

            // 激活对应的标签
            event.target.classList.add('active');
        }

        // 切换子面板
        function showSubPanel(subPanelId) {
            // 隐藏当前面板下的所有子内容
            const currentPanel = document.querySelector('.content-panel.active');
            const subContents = currentPanel.querySelectorAll('.tab-content');
            subContents.forEach(content => content.style.display = 'none');

            // 移除所有子标签的active状态
            const subTabs = currentPanel.querySelectorAll('.sub-tab');
            subTabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的子内容
            const targetContent = currentPanel.querySelector(`#${subPanelId}`);
            if (targetContent) {
                targetContent.style.display = 'block';
            }

            // 激活对应的子标签
            event.target.classList.add('active');
        }

        // 选择采集模式
        function selectMode(element, mode) {
            // 移除其他选中状态
            document.querySelectorAll('.mode-card').forEach(card => {
                card.classList.remove('selected');
            });

            // 添加选中状态
            element.classList.add('selected');
            selectedMode = mode;

            console.log('选择采集模式:', mode);
        }

        // 选择日志类型
        function selectLogType(element, logType) {
            // 移除其他选中状态
            document.querySelectorAll('.log-card').forEach(card => {
                card.classList.remove('selected');
            });

            // 添加选中状态
            element.classList.add('selected');
            selectedLogType = logType;

            console.log('选择日志类型:', logType);
        }

        // 加载数据血缘
        function loadLineage(tableName) {
            console.log('加载血缘关系:', tableName);
            // 这里可以根据选择的表动态更新血缘视图
        }

        // 加载数据集字段
        function loadDatasetFields(dataset) {
            console.log('加载数据集字段:', dataset);
            // 这里可以根据选择的数据集动态更新字段选项
        }

        // 显示统计配置
        function showStatConfig(statType) {
            const configDiv = document.getElementById('statConfig');
            let configHTML = '';

            switch(statType) {
                case 'aggregate':
                    configHTML = `
                        <div class="form-group">
                            <label>聚合函数</label>
                            <select class="form-control">
                                <option>求和(SUM)</option>
                                <option>计数(COUNT)</option>
                                <option>去重计数(COUNT DISTINCT)</option>
                                <option>平均值(AVG)</option>
                                <option>最大值(MAX)</option>
                                <option>最小值(MIN)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>聚合字段</label>
                            <select class="form-control">
                                <option>student_id</option>
                                <option>age</option>
                                <option>score</option>
                                <option>credit</option>
                            </select>
                        </div>
                    `;
                    break;
                case 'ratio':
                    configHTML = `
                        <div class="form-group">
                            <label>占比字段</label>
                            <select class="form-control">
                                <option>student_count</option>
                                <option>score_sum</option>
                                <option>credit_total</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>数据范围</label>
                            <select class="form-control">
                                <option>全部数据</option>
                                <option>部分数据</option>
                            </select>
                        </div>
                    `;
                    break;
                case 'rank':
                    configHTML = `
                        <div class="form-group">
                            <label>排名字段</label>
                            <select class="form-control">
                                <option>score</option>
                                <option>gpa</option>
                                <option>credit</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>排序方式</label>
                            <select class="form-control">
                                <option>降序</option>
                                <option>升序</option>
                            </select>
                        </div>
                    `;
                    break;
                case 'value':
                    configHTML = `
                        <div class="form-group">
                            <label>取值字段</label>
                            <select class="form-control">
                                <option>student_name</option>
                                <option>major</option>
                                <option>grade</option>
                            </select>
                        </div>
                    `;
                    break;
                case 'period':
                    configHTML = `
                        <div class="form-group">
                            <label>环比字段</label>
                            <select class="form-control">
                                <option>student_count</option>
                                <option>avg_score</option>
                                <option>graduation_rate</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>统计类型</label>
                            <select class="form-control">
                                <option>环比值</option>
                                <option>环比增长量</option>
                                <option>环比增长率</option>
                            </select>
                        </div>
                    `;
                    break;
            }

            configDiv.innerHTML = configHTML;
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('数据湖管理系统已加载');

            // 初始化默认统计配置
            showStatConfig('aggregate');

            // 模拟实时数据更新
            setInterval(function() {
                // 这里可以添加实时数据更新逻辑
                console.log('更新监控数据...');
            }, 30000); // 每30秒更新一次
        });
    </script>
</body>
</html>
