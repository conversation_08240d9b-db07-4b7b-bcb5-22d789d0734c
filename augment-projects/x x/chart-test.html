<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图表测试</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .chart-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            text-align: center;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: bold;
        }
        canvas {
            max-height: 400px;
        }
    </style>
</head>
<body>
    <h1>图表功能测试</h1>
    
    <div class="chart-container">
        <div class="chart-title">学生数量趋势图</div>
        <canvas id="studentChart"></canvas>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">科研成果统计</div>
        <canvas id="researchChart"></canvas>
    </div>
    
    <div class="chart-container">
        <div class="chart-title">师资结构分布</div>
        <canvas id="facultyChart"></canvas>
    </div>

    <script>
        // 等待页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始初始化图表...');
            
            // 检查Chart.js是否加载
            if (typeof Chart === 'undefined') {
                console.error('Chart.js未加载');
                return;
            }
            
            // 1. 学生数量趋势图
            const studentCtx = document.getElementById('studentChart');
            if (studentCtx) {
                new Chart(studentCtx, {
                    type: 'line',
                    data: {
                        labels: ['2022年', '2023年', '2024年'],
                        datasets: [{
                            label: '本科生',
                            data: [2234, 2298, 2456],
                            borderColor: '#667eea',
                            backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            fill: false
                        }, {
                            label: '硕士生',
                            data: [598, 623, 654],
                            borderColor: '#43e97b',
                            backgroundColor: 'rgba(67, 233, 123, 0.1)',
                            fill: false
                        }, {
                            label: '博士生',
                            data: [257, 257, 146],
                            borderColor: '#ffa726',
                            backgroundColor: 'rgba(255, 167, 38, 0.1)',
                            fill: false
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
                console.log('学生数量趋势图创建成功');
            }
            
            // 2. 科研成果统计
            const researchCtx = document.getElementById('researchChart');
            if (researchCtx) {
                new Chart(researchCtx, {
                    type: 'bar',
                    data: {
                        labels: ['2022年', '2023年', '2024年'],
                        datasets: [{
                            label: '发表论文',
                            data: [198, 213, 245],
                            backgroundColor: '#667eea'
                        }, {
                            label: '授权专利',
                            data: [56, 69, 89],
                            backgroundColor: '#43e97b'
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
                console.log('科研成果统计图创建成功');
            }
            
            // 3. 师资结构分布
            const facultyCtx = document.getElementById('facultyChart');
            if (facultyCtx) {
                new Chart(facultyCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['教授', '副教授', '讲师', '助教'],
                        datasets: [{
                            data: [45, 68, 58, 15],
                            backgroundColor: [
                                '#667eea',
                                '#43e97b',
                                '#ffa726',
                                '#f093fb'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
                console.log('师资结构分布图创建成功');
            }
            
            console.log('所有图表初始化完成');
        });
    </script>
</body>
</html>
