<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据标准管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
            font-weight: 500;
        }
        
        .nav-tab.active {
            background: #667eea;
            color: white;
        }
        
        .nav-tab:hover {
            background: #5a6fd8;
            color: white;
        }
        
        .content-panel {
            display: none;
            background: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .content-panel.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            margin-right: 10px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .file-upload {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            transition: border-color 0.3s;
            cursor: pointer;
        }
        
        .file-upload:hover {
            border-color: #667eea;
        }
        
        .file-upload.dragover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        
        .version-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-draft {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-standard {
            background: #d4edda;
            color: #155724;
        }
        
        .status-obsolete {
            background: #f8d7da;
            color: #721c24;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .data-table tr:hover {
            background: #f8f9ff;
        }
        
        .domain-tree {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            background: #fafafa;
        }
        
        .domain-item {
            margin: 8px 0;
            padding: 8px;
            cursor: pointer;
            border-radius: 4px;
            transition: background 0.3s;
        }
        
        .domain-item:hover {
            background: #e9ecef;
        }
        
        .domain-level-1 {
            font-weight: bold;
            color: #495057;
        }
        
        .domain-level-2 {
            margin-left: 20px;
            color: #6c757d;
        }
        
        .domain-level-3 {
            margin-left: 40px;
            color: #868e96;
        }
        
        .search-box {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .search-box input {
            flex: 1;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
        
        .row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .col {
            flex: 1;
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .supported-formats {
            font-size: 12px;
            color: #6c757d;
            margin-top: 8px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>数据标准管理系统</h1>
        <p>代码标准、数据标准及编码规范端到端管理平台</p>
    </div>

    <div class="container">
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showPanel('standards')">标准管理</button>
            <button class="nav-tab" onclick="showPanel('versions')">版本管理</button>
            <button class="nav-tab" onclick="showPanel('files')">文件管理</button>
            <button class="nav-tab" onclick="showPanel('publish')">发布管理</button>
            <button class="nav-tab" onclick="showPanel('domains')">主题域分类</button>
            <button class="nav-tab" onclick="showPanel('query')">查询服务</button>
        </div>

        <!-- 标准管理面板 -->
        <div id="standards" class="content-panel active">
            <h2>标准管理</h2>
            <div class="alert alert-info">
                支持代码标准、数据标准及编码规范的创建、导入、提交发布请求、审核批准等关键流程
            </div>
            
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>标准类型</label>
                        <select class="form-control">
                            <option>代码标准</option>
                            <option>数据标准</option>
                            <option>编码规范</option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>标准名称</label>
                        <input type="text" class="form-control" placeholder="请输入标准名称">
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label>标准描述</label>
                <textarea class="form-control" rows="4" placeholder="请输入标准描述"></textarea>
            </div>
            
            <div class="form-group">
                <button class="btn btn-primary">创建标准</button>
                <button class="btn btn-success">导入标准</button>
                <button class="btn btn-warning">提交发布请求</button>
                <button class="btn btn-primary">审核批准</button>
            </div>
        </div>

        <!-- 版本管理面板 -->
        <div id="versions" class="content-panel">
            <h2>版本管理</h2>
            <div class="alert alert-info">
                支持同一数据元素的多个版本管理，只有一个版本可以被设定为标准状态，新版本注册时自动转换状态
            </div>

            <div class="search-box">
                <input type="text" class="form-control" placeholder="搜索数据元素...">
                <button class="btn btn-primary">搜索</button>
            </div>

            <table class="data-table">
                <thead>
                    <tr>
                        <th>数据元素名称</th>
                        <th>版本号</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>学生基本信息</td>
                        <td>v2.1</td>
                        <td><span class="version-status status-standard">标准</span></td>
                        <td>2024-01-15</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                            <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">新建版本</button>
                        </td>
                    </tr>
                    <tr>
                        <td>学生基本信息</td>
                        <td>v2.0</td>
                        <td><span class="version-status status-obsolete">废止</span></td>
                        <td>2023-12-10</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                        </td>
                    </tr>
                    <tr>
                        <td>教师信息标准</td>
                        <td>v1.2</td>
                        <td><span class="version-status status-draft">草稿</span></td>
                        <td>2024-01-20</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">设为标准</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 文件管理面板 -->
        <div id="files" class="content-panel">
            <h2>文件管理</h2>
            <div class="alert alert-info">
                支持pdf、doc、docx、png、jpg、jpeg、xls、xlsx、zip等多种文件格式的上传与管理
            </div>

            <div class="file-upload" onclick="document.getElementById('fileInput').click()">
                <input type="file" id="fileInput" style="display: none;" multiple
                       accept=".pdf,.doc,.docx,.png,.jpg,.jpeg,.xls,.xlsx,.zip">
                <div>
                    <h3>点击或拖拽文件到此处上传</h3>
                    <div class="supported-formats">
                        支持格式：PDF、DOC、DOCX、PNG、JPG、JPEG、XLS、XLSX、ZIP
                    </div>
                </div>
            </div>

            <table class="data-table">
                <thead>
                    <tr>
                        <th>文件名</th>
                        <th>文件类型</th>
                        <th>文件大小</th>
                        <th>上传时间</th>
                        <th>关联标准</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>学生信息标准规范.pdf</td>
                        <td>PDF</td>
                        <td>2.5MB</td>
                        <td>2024-01-15</td>
                        <td>学生基本信息 v2.1</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">下载</button>
                            <button class="btn btn-danger" style="padding: 6px 12px; font-size: 12px;">删除</button>
                        </td>
                    </tr>
                    <tr>
                        <td>编码规范手册.docx</td>
                        <td>DOCX</td>
                        <td>1.8MB</td>
                        <td>2024-01-10</td>
                        <td>编码规范 v1.0</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">下载</button>
                            <button class="btn btn-danger" style="padding: 6px 12px; font-size: 12px;">删除</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 发布管理面板 -->
        <div id="publish" class="content-panel">
            <h2>发布管理</h2>
            <div class="alert alert-info">
                支持用户设置标准号、实施日期以及被替代的标准等关键信息
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>标准号</label>
                        <input type="text" class="form-control" placeholder="如：GB/T 7408-2005">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>实施日期</label>
                        <input type="date" class="form-control">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>被替代的标准</label>
                        <input type="text" class="form-control" placeholder="请输入被替代的标准号">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>发布状态</label>
                        <select class="form-control">
                            <option>草稿</option>
                            <option>待审核</option>
                            <option>已发布</option>
                            <option>已废止</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>发布说明</label>
                <textarea class="form-control" rows="4" placeholder="请输入发布说明"></textarea>
            </div>

            <div class="form-group">
                <button class="btn btn-primary">保存草稿</button>
                <button class="btn btn-success">提交审核</button>
                <button class="btn btn-warning">发布标准</button>
            </div>
        </div>

        <!-- 主题域分类面板 -->
        <div id="domains" class="content-panel">
            <h2>主题域分类</h2>
            <div class="alert alert-info">
                按照数据资源所涉及的范围，将学院数据按照主题域进行分类，采用大类、中类和小类三级分类法
            </div>

            <div class="domain-tree">
                <div class="domain-item domain-level-1" onclick="toggleDomain(this)">
                    📁 人力资源管理域
                    <div style="display: none;">
                        <div class="domain-item domain-level-2" onclick="toggleDomain(this)">
                            📂 教职工信息管理
                            <div style="display: none;">
                                <div class="domain-item domain-level-3">📄 教师基本信息</div>
                                <div class="domain-item domain-level-3">📄 职工档案信息</div>
                                <div class="domain-item domain-level-3">📄 薪酬福利信息</div>
                            </div>
                        </div>
                        <div class="domain-item domain-level-2" onclick="toggleDomain(this)">
                            📂 人事考核管理
                            <div style="display: none;">
                                <div class="domain-item domain-level-3">📄 绩效考核标准</div>
                                <div class="domain-item domain-level-3">📄 职称评定信息</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="domain-item domain-level-1" onclick="toggleDomain(this)">
                    📁 学生管理域
                    <div style="display: none;">
                        <div class="domain-item domain-level-2" onclick="toggleDomain(this)">
                            📂 学生基本信息
                            <div style="display: none;">
                                <div class="domain-item domain-level-3">📄 学生档案</div>
                                <div class="domain-item domain-level-3">📄 学籍信息</div>
                                <div class="domain-item domain-level-3">📄 奖惩记录</div>
                            </div>
                        </div>
                        <div class="domain-item domain-level-2" onclick="toggleDomain(this)">
                            📂 学生事务管理
                            <div style="display: none;">
                                <div class="domain-item domain-level-3">📄 宿舍管理</div>
                                <div class="domain-item domain-level-3">📄 社团活动</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="domain-item domain-level-1" onclick="toggleDomain(this)">
                    📁 教学资源与管理域
                    <div style="display: none;">
                        <div class="domain-item domain-level-2" onclick="toggleDomain(this)">
                            📂 课程管理
                            <div style="display: none;">
                                <div class="domain-item domain-level-3">📄 课程信息</div>
                                <div class="domain-item domain-level-3">📄 教学计划</div>
                                <div class="domain-item domain-level-3">📄 成绩管理</div>
                            </div>
                        </div>
                        <div class="domain-item domain-level-2" onclick="toggleDomain(this)">
                            📂 教学资源
                            <div style="display: none;">
                                <div class="domain-item domain-level-3">📄 教材管理</div>
                                <div class="domain-item domain-level-3">📄 实验室资源</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="domain-item domain-level-1" onclick="toggleDomain(this)">
                    📁 科研管理域
                    <div style="display: none;">
                        <div class="domain-item domain-level-2" onclick="toggleDomain(this)">
                            📂 科研项目管理
                            <div style="display: none;">
                                <div class="domain-item domain-level-3">📄 项目申报</div>
                                <div class="domain-item domain-level-3">📄 项目执行</div>
                                <div class="domain-item domain-level-3">📄 成果管理</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="domain-item domain-level-1" onclick="toggleDomain(this)">
                    📁 财务管理域
                    <div style="display: none;">
                        <div class="domain-item domain-level-2" onclick="toggleDomain(this)">
                            📂 预算管理
                            <div style="display: none;">
                                <div class="domain-item domain-level-3">📄 预算编制</div>
                                <div class="domain-item domain-level-3">📄 预算执行</div>
                            </div>
                        </div>
                        <div class="domain-item domain-level-2" onclick="toggleDomain(this)">
                            📂 财务核算
                            <div style="display: none;">
                                <div class="domain-item domain-level-3">📄 收费管理</div>
                                <div class="domain-item domain-level-3">📄 报销管理</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="domain-item domain-level-1" onclick="toggleDomain(this)">
                    📁 资产管理域
                    <div style="display: none;">
                        <div class="domain-item domain-level-2" onclick="toggleDomain(this)">
                            📂 固定资产
                            <div style="display: none;">
                                <div class="domain-item domain-level-3">📄 设备管理</div>
                                <div class="domain-item domain-level-3">📄 房产管理</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="domain-item domain-level-1" onclick="toggleDomain(this)">
                    📁 行政管理域
                    <div style="display: none;">
                        <div class="domain-item domain-level-2" onclick="toggleDomain(this)">
                            📂 办公管理
                            <div style="display: none;">
                                <div class="domain-item domain-level-3">📄 公文管理</div>
                                <div class="domain-item domain-level-3">📄 会议管理</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="domain-item domain-level-1" onclick="toggleDomain(this)">
                    📁 公共服务域
                    <div style="display: none;">
                        <div class="domain-item domain-level-2" onclick="toggleDomain(this)">
                            📂 信息服务
                            <div style="display: none;">
                                <div class="domain-item domain-level-3">📄 图书管理</div>
                                <div class="domain-item domain-level-3">📄 网络服务</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 查询服务面板 -->
        <div id="query" class="content-panel">
            <h2>查询服务</h2>
            <div class="alert alert-info">
                提供高效的查询服务，支持对代码进行作废和删除操作
            </div>

            <div class="search-box">
                <input type="text" class="form-control" placeholder="请输入关键词搜索标准...">
                <select class="form-control" style="flex: 0 0 150px;">
                    <option>全部类型</option>
                    <option>代码标准</option>
                    <option>数据标准</option>
                    <option>编码规范</option>
                </select>
                <select class="form-control" style="flex: 0 0 120px;">
                    <option>全部状态</option>
                    <option>标准</option>
                    <option>草稿</option>
                    <option>废止</option>
                </select>
                <button class="btn btn-primary">搜索</button>
            </div>

            <table class="data-table">
                <thead>
                    <tr>
                        <th>标准名称</th>
                        <th>标准类型</th>
                        <th>版本</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>主题域</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>学生基本信息标准</td>
                        <td>数据标准</td>
                        <td>v2.1</td>
                        <td><span class="version-status status-standard">标准</span></td>
                        <td>2024-01-15</td>
                        <td>学生管理域</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                            <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;" onclick="obsoleteStandard(this)">作废</button>
                            <button class="btn btn-danger" style="padding: 6px 12px; font-size: 12px;" onclick="deleteStandard(this)">删除</button>
                        </td>
                    </tr>
                    <tr>
                        <td>教师信息编码规范</td>
                        <td>编码规范</td>
                        <td>v1.0</td>
                        <td><span class="version-status status-standard">标准</span></td>
                        <td>2024-01-10</td>
                        <td>人力资源管理域</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>
                            <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;" onclick="obsoleteStandard(this)">作废</button>
                            <button class="btn btn-danger" style="padding: 6px 12px; font-size: 12px;" onclick="deleteStandard(this)">删除</button>
                        </td>
                    </tr>
                    <tr>
                        <td>课程代码标准</td>
                        <td>代码标准</td>
                        <td>v1.5</td>
                        <td><span class="version-status status-draft">草稿</span></td>
                        <td>2024-01-20</td>
                        <td>教学资源与管理域</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">发布</button>
                            <button class="btn btn-danger" style="padding: 6px 12px; font-size: 12px;" onclick="deleteStandard(this)">删除</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h3 id="modalTitle">确认操作</h3>
            <p id="modalMessage">您确定要执行此操作吗？</p>
            <div style="text-align: right; margin-top: 20px;">
                <button class="btn btn-primary" onclick="closeModal()">取消</button>
                <button class="btn btn-danger" id="confirmBtn" onclick="confirmAction()">确认</button>
            </div>
        </div>
    </div>

    <script>
        let currentAction = null;
        let currentElement = null;

        // 切换面板显示
        function showPanel(panelId) {
            // 隐藏所有面板
            const panels = document.querySelectorAll('.content-panel');
            panels.forEach(panel => panel.classList.remove('active'));

            // 移除所有标签的active状态
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的面板
            document.getElementById(panelId).classList.add('active');

            // 激活对应的标签
            event.target.classList.add('active');
        }

        // 切换主题域展开/收起
        function toggleDomain(element) {
            const children = element.querySelector('div');
            if (children) {
                if (children.style.display === 'none' || children.style.display === '') {
                    children.style.display = 'block';
                    element.innerHTML = element.innerHTML.replace('📁', '📂').replace('📂', '📂');
                } else {
                    children.style.display = 'none';
                    element.innerHTML = element.innerHTML.replace('📂', '📁');
                }
            }
        }

        // 作废标准
        function obsoleteStandard(button) {
            currentAction = 'obsolete';
            currentElement = button;
            document.getElementById('modalTitle').textContent = '作废标准';
            document.getElementById('modalMessage').textContent = '确定要将此标准设置为废止状态吗？';
            document.getElementById('confirmModal').style.display = 'block';
        }

        // 删除标准
        function deleteStandard(button) {
            currentAction = 'delete';
            currentElement = button;
            document.getElementById('modalTitle').textContent = '删除标准';
            document.getElementById('modalMessage').textContent = '确定要删除此标准吗？此操作不可恢复！';
            document.getElementById('confirmModal').style.display = 'block';
        }

        // 确认操作
        function confirmAction() {
            if (currentAction === 'obsolete') {
                const row = currentElement.closest('tr');
                const statusSpan = row.querySelector('.version-status');
                statusSpan.className = 'version-status status-obsolete';
                statusSpan.textContent = '废止';

                // 更新操作按钮
                const actionCell = row.querySelector('td:last-child');
                actionCell.innerHTML = '<button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">查看</button>';

                alert('标准已设置为废止状态');
            } else if (currentAction === 'delete') {
                const row = currentElement.closest('tr');
                row.remove();
                alert('标准已删除');
            }
            closeModal();
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('confirmModal').style.display = 'none';
            currentAction = null;
            currentElement = null;
        }

        // 文件上传处理
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const files = e.target.files;
            if (files.length > 0) {
                alert(`已选择 ${files.length} 个文件，准备上传...`);
                // 这里可以添加实际的文件上传逻辑
            }
        });

        // 拖拽上传
        const fileUpload = document.querySelector('.file-upload');

        fileUpload.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });

        fileUpload.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });

        fileUpload.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                alert(`已拖拽 ${files.length} 个文件，准备上传...`);
                // 这里可以添加实际的文件上传逻辑
            }
        });

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('confirmModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
