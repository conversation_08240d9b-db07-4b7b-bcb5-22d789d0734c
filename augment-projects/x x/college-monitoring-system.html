<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学院发展监控系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .nav-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .nav-tab {
            flex: 1;
            padding: 18px 20px;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            color: #667eea;
        }
        
        .nav-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .nav-tab:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            color: white;
        }
        
        .content-panel {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .content-panel.active {
            display: block;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            transition: all 0.3s;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.15);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 24px;
            color: white;
        }
        
        .icon-talent {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .icon-faculty {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .icon-research {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .icon-facilities {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9ff;
            border-radius: 8px;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .chart-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
            text-align: center;
        }

        .chart-placeholder {
            height: 200px;
            background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #667eea;
            font-size: 14px;
            border: 2px dashed rgba(102, 126, 234, 0.3);
        }

        .chart-small {
            height: 180px;
        }

        .chart-medium {
            height: 220px;
        }

        .chart-large {
            height: 280px;
        }

        .chart-small canvas,
        .chart-medium canvas,
        .chart-large canvas {
            width: 100% !important;
            height: 100% !important;
        }
        
        .trend-indicator {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 10px;
        }
        
        .trend-up {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .trend-down {
            background: #ffebee;
            color: #c62828;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .data-table th {
            background: #f8f9ff;
            font-weight: 600;
            color: #333;
        }
        
        .data-table tr:hover {
            background: #f8f9ff;
        }
        
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            margin: 5px 0;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s;
        }
        
        .progress-primary {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
        
        .progress-success {
            background: linear-gradient(90deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .progress-warning {
            background: linear-gradient(90deg, #ffa726 0%, #fb8c00 100%);
        }
        
        .sub-tabs {
            display: flex;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 25px;
        }
        
        .sub-tab {
            padding: 12px 24px;
            background: none;
            border: none;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
            font-weight: 500;
            color: #666;
        }
        
        .sub-tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
        }
        
        .sub-tab:hover {
            color: #667eea;
            background: #f8f9ff;
        }
        
        .talent-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            display: flex;
            align-items: center;
            transition: all 0.3s;
        }
        
        .talent-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .talent-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: bold;
            margin-right: 20px;
        }
        
        .talent-info {
            flex: 1;
        }
        
        .talent-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .talent-title {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .talent-research {
            color: #999;
            font-size: 12px;
        }
        
        .award-card {
            background: linear-gradient(135deg, #ffd700 0%, #ffb300 100%);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .award-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 25px rgba(255, 215, 0, 0.4);
        }
        
        .award-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .award-project {
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .award-team {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .platform-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-left: 4px solid #667eea;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .platform-card:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .platform-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }
        
        .platform-level {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            margin-bottom: 8px;
        }
        
        .platform-subject {
            color: #666;
            font-size: 14px;
        }
        
        .word-cloud {
            background: white;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            min-height: 200px;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .word-item {
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .word-large {
            font-size: 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .word-medium {
            font-size: 18px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .word-small {
            font-size: 14px;
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }
        
        .word-item:hover {
            transform: scale(1.1);
        }
        
        .map-container {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            text-align: center;
        }
        
        .map-placeholder {
            height: 400px;
            background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #667eea;
            font-size: 16px;
            border: 2px dashed rgba(102, 126, 234, 0.3);
        }
        
        .policy-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
        }
        
        .policy-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .policy-content {
            font-size: 14px;
            line-height: 1.6;
            opacity: 0.9;
        }
        
        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            font-size: 12px;
        }
        
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 3px;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎓 学院发展监控系统</h1>
        <p>全面监控学院人才培养、师资队伍、科学研究、办学条件等核心发展指标</p>
    </div>

    <div class="container">
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showPanel('overview')">📊 院情总览</button>
            <button class="nav-tab" onclick="showPanel('development')">📈 核心发展指数</button>
        </div>

        <!-- 院情总览面板 -->
        <div id="overview" class="content-panel active">
            <h2>📊 院情总览</h2>

            <div class="sub-tabs">
                <button class="sub-tab active" onclick="showSubPanel('talent-cultivation')">👨‍🎓 人才培养</button>
                <button class="sub-tab" onclick="showSubPanel('faculty-team')">👨‍🏫 师资队伍</button>
                <button class="sub-tab" onclick="showSubPanel('scientific-research')">🔬 科学研究</button>
                <button class="sub-tab" onclick="showSubPanel('school-conditions')">🏫 办学条件</button>
            </div>

            <!-- 人才培养 -->
            <div id="talent-cultivation" class="tab-content">
                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-talent">🏛️</div>
                            <div class="card-title">学院基本情况</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">8</div>
                                <div class="stat-label">系室数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">15</div>
                                <div class="stat-label">专业数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">342</div>
                                <div class="stat-label">课程数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">3,256</div>
                                <div class="stat-label">在校学生数</div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-talent">📚</div>
                            <div class="card-title">在校生培养层次结构</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">2,456</div>
                                <div class="stat-label">本科生</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-primary" style="width: 75.4%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">654</div>
                                <div class="stat-label">硕士生</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-success" style="width: 20.1%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">146</div>
                                <div class="stat-label">博士生</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-warning" style="width: 4.5%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-title">近3年学生数量变化趋势</div>
                    <div class="chart-small">
                        <canvas id="studentTrendChart"></canvas>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-title">不同系室或专业课程开设数量</div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>系室/专业</th>
                                <th>课程数量</th>
                                <th>必修课</th>
                                <th>选修课</th>
                                <th>实践课</th>
                                <th>占比</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>计算机科学系</td>
                                <td>68</td>
                                <td>42</td>
                                <td>18</td>
                                <td>8</td>
                                <td>19.9%</td>
                            </tr>
                            <tr>
                                <td>软件工程系</td>
                                <td>56</td>
                                <td>35</td>
                                <td>15</td>
                                <td>6</td>
                                <td>16.4%</td>
                            </tr>
                            <tr>
                                <td>信息安全系</td>
                                <td>45</td>
                                <td>28</td>
                                <td>12</td>
                                <td>5</td>
                                <td>13.2%</td>
                            </tr>
                            <tr>
                                <td>人工智能系</td>
                                <td>52</td>
                                <td>32</td>
                                <td>14</td>
                                <td>6</td>
                                <td>15.2%</td>
                            </tr>
                            <tr>
                                <td>数据科学系</td>
                                <td>38</td>
                                <td>24</td>
                                <td>10</td>
                                <td>4</td>
                                <td>11.1%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 师资队伍 -->
            <div id="faculty-team" class="tab-content" style="display: none;">
                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-faculty">👥</div>
                            <div class="card-title">教职工基本情况</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">186</div>
                                <div class="stat-label">教职工总数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">142</div>
                                <div class="stat-label">专任教师</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">44</div>
                                <div class="stat-label">管理人员</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">1:17.5</div>
                                <div class="stat-label">师生比</div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-faculty">🎖️</div>
                            <div class="card-title">高层次人才分布</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">3</div>
                                <div class="stat-label">院士</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">8</div>
                                <div class="stat-label">长江学者</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">12</div>
                                <div class="stat-label">杰青</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">25</div>
                                <div class="stat-label">优青</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-faculty">🎓</div>
                            <div class="card-title">学历结构</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">156</div>
                                <div class="stat-label">博士</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-primary" style="width: 83.9%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">26</div>
                                <div class="stat-label">硕士</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-success" style="width: 14.0%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">4</div>
                                <div class="stat-label">本科</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-warning" style="width: 2.1%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-faculty">🏆</div>
                            <div class="card-title">职称结构</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">45</div>
                                <div class="stat-label">教授</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-primary" style="width: 24.2%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">68</div>
                                <div class="stat-label">副教授</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-success" style="width: 36.6%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">73</div>
                                <div class="stat-label">讲师及以下</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-warning" style="width: 39.2%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-title">近3年教职工数量变化趋势</div>
                    <div class="chart-small">
                        <canvas id="facultyTrendChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 科学研究 -->
            <div id="scientific-research" class="tab-content" style="display: none;">
                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-research">📄</div>
                            <div class="card-title">科研成果统计</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">245</div>
                                <div class="stat-label">发表论文数</div>
                                <span class="trend-indicator trend-up">↗ +15.2%</span>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">18</div>
                                <div class="stat-label">出版著作数</div>
                                <span class="trend-indicator trend-up">↗ +12.5%</span>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">89</div>
                                <div class="stat-label">授权专利数</div>
                                <span class="trend-indicator trend-up">↗ +28.6%</span>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">12</div>
                                <div class="stat-label">科研获奖</div>
                                <span class="trend-indicator trend-up">↗ +9.1%</span>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-research">💰</div>
                            <div class="card-title">科研经费情况</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">2,856万</div>
                                <div class="stat-label">总经费</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">1,245万</div>
                                <div class="stat-label">国家级</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-primary" style="width: 43.6%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">856万</div>
                                <div class="stat-label">省部级</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-success" style="width: 30.0%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">755万</div>
                                <div class="stat-label">企业合作</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-warning" style="width: 26.4%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-title">近3年科研成果变化趋势</div>
                    <div class="chart-small">
                        <canvas id="researchTrendChart"></canvas>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-title">不同学科或系室科研项目数量和经费</div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>学科/系室</th>
                                <th>项目数量</th>
                                <th>经费金额(万元)</th>
                                <th>国家级项目</th>
                                <th>省部级项目</th>
                                <th>平均经费</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>计算机科学系</td>
                                <td>28</td>
                                <td>856</td>
                                <td>8</td>
                                <td>12</td>
                                <td>30.6万</td>
                            </tr>
                            <tr>
                                <td>人工智能系</td>
                                <td>24</td>
                                <td>742</td>
                                <td>6</td>
                                <td>10</td>
                                <td>30.9万</td>
                            </tr>
                            <tr>
                                <td>软件工程系</td>
                                <td>22</td>
                                <td>634</td>
                                <td>5</td>
                                <td>9</td>
                                <td>28.8万</td>
                            </tr>
                            <tr>
                                <td>信息安全系</td>
                                <td>18</td>
                                <td>456</td>
                                <td>4</td>
                                <td>7</td>
                                <td>25.3万</td>
                            </tr>
                            <tr>
                                <td>数据科学系</td>
                                <td>15</td>
                                <td>368</td>
                                <td>3</td>
                                <td>6</td>
                                <td>24.5万</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 办学条件 -->
            <div id="school-conditions" class="tab-content" style="display: none;">
                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-facilities">🏢</div>
                            <div class="card-title">场地面积统计</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">15.6万</div>
                                <div class="stat-label">占地面积(㎡)</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">8.2万</div>
                                <div class="stat-label">教学用房(㎡)</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-primary" style="width: 52.6%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">4.5万</div>
                                <div class="stat-label">学生宿舍(㎡)</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-success" style="width: 28.8%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">2.9万</div>
                                <div class="stat-label">行政用房(㎡)</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-warning" style="width: 18.6%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-facilities">💻</div>
                            <div class="card-title">设备资产统计</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">2.8亿</div>
                                <div class="stat-label">固定资产总额</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">1.2亿</div>
                                <div class="stat-label">教学科研设备</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">156</div>
                                <div class="stat-label">大型设备数量</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">85.6%</div>
                                <div class="stat-label">教学用房使用率</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-title">大型设备类型分布</div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>设备类型</th>
                                <th>数量</th>
                                <th>总价值(万元)</th>
                                <th>使用率</th>
                                <th>维护状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>高性能计算集群</td>
                                <td>8</td>
                                <td>2,400</td>
                                <td>92.3%</td>
                                <td>良好</td>
                            </tr>
                            <tr>
                                <td>服务器设备</td>
                                <td>45</td>
                                <td>1,800</td>
                                <td>88.7%</td>
                                <td>良好</td>
                            </tr>
                            <tr>
                                <td>网络设备</td>
                                <td>68</td>
                                <td>1,200</td>
                                <td>95.1%</td>
                                <td>优秀</td>
                            </tr>
                            <tr>
                                <td>实验仪器</td>
                                <td>35</td>
                                <td>980</td>
                                <td>76.4%</td>
                                <td>良好</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="chart-container">
                    <div class="chart-title">近3年办学条件关键指标变化趋势</div>
                    <div class="chart-small">
                        <canvas id="facilitiesTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 核心发展指数面板 -->
        <div id="development" class="content-panel">
            <h2>📈 核心发展指数</h2>

            <div class="sub-tabs">
                <button class="sub-tab active" onclick="showSubPanel('talent-scale')">👥 人才培养规模</button>
                <button class="sub-tab" onclick="showSubPanel('graduation')">🎓 毕业情况</button>
                <button class="sub-tab" onclick="showSubPanel('competition')">🏆 学生竞赛获奖</button>
                <button class="sub-tab" onclick="showSubPanel('research-projects')">🔬 科研项目情况</button>
                <button class="sub-tab" onclick="showSubPanel('research-results')">📊 科研成果情况</button>
                <button class="sub-tab" onclick="showSubPanel('research-platforms')">🏛️ 科研平台情况</button>
                <button class="sub-tab" onclick="showSubPanel('faculty-structure')">👨‍🏫 师资规模结构</button>
                <button class="sub-tab" onclick="showSubPanel('faculty-capability')">⭐ 师资能力水平</button>
                <button class="sub-tab" onclick="showSubPanel('talent-recruitment')">🎯 人才引进情况</button>
                <button class="sub-tab" onclick="showSubPanel('major-tasks')">📋 大项任务情况</button>
            </div>

            <!-- 人才培养规模 -->
            <div id="talent-scale" class="tab-content">
                <div class="row">
                    <div class="col">
                        <div class="chart-container">
                            <div class="chart-title">近五年院校在校生人数变化趋势</div>
                            <div class="chart-small">
                                <canvas id="enrollmentTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="chart-container">
                            <div class="chart-title">近五年毕业生人数变化趋势</div>
                            <div class="chart-small">
                                <canvas id="graduationTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-talent">📊</div>
                            <div class="card-title">2024年在校生分布</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">2,456</div>
                                <div class="stat-label">本科生 (75.4%)</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-primary" style="width: 75.4%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">654</div>
                                <div class="stat-label">硕士生 (20.1%)</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-success" style="width: 20.1%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">146</div>
                                <div class="stat-label">博士生 (4.5%)</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-warning" style="width: 4.5%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-talent">🎯</div>
                            <div class="card-title">2024年毕业生分布</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">678</div>
                                <div class="stat-label">本科毕业生</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">156</div>
                                <div class="stat-label">硕士毕业生</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">58</div>
                                <div class="stat-label">博士毕业生</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">892</div>
                                <div class="stat-label">毕业生总数</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 毕业情况 -->
            <div id="graduation" class="tab-content" style="display: none;">
                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-talent">🎓</div>
                            <div class="card-title">2024年毕业率和学位授予率</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">96.8%</div>
                                <div class="stat-label">毕业率</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-success" style="width: 96.8%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">95.2%</div>
                                <div class="stat-label">学位授予率</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-primary" style="width: 95.2%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-talent">💼</div>
                            <div class="card-title">就业单位类型分布</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">342</div>
                                <div class="stat-label">国有企业</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">298</div>
                                <div class="stat-label">民营企业</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">156</div>
                                <div class="stat-label">事业单位</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">96</div>
                                <div class="stat-label">继续深造</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="map-container">
                    <div class="chart-title">毕业生分配去向地区分布</div>
                    <div class="map-placeholder">
                        🗺️ 中国地图 - 毕业生就业地区分布<br>
                        北京: 156人 | 上海: 134人 | 深圳: 98人 | 杭州: 87人 | 广州: 76人<br>
                        成都: 65人 | 南京: 54人 | 西安: 43人 | 武汉: 38人 | 其他: 141人
                    </div>
                    <div class="legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background: #1a237e;"></div>
                            <span>100人以上</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #3f51b5;"></div>
                            <span>50-99人</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #9c27b0;"></div>
                            <span>20-49人</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #e91e63;"></div>
                            <span>20人以下</span>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-title">就业单位类型详细统计</div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>单位类型</th>
                                <th>人数</th>
                                <th>占比</th>
                                <th>平均薪资</th>
                                <th>主要行业</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>国有企业</td>
                                <td>342</td>
                                <td>38.3%</td>
                                <td>12.8万</td>
                                <td>通信、金融、能源</td>
                            </tr>
                            <tr>
                                <td>民营企业</td>
                                <td>298</td>
                                <td>33.4%</td>
                                <td>15.6万</td>
                                <td>互联网、软件开发</td>
                            </tr>
                            <tr>
                                <td>事业单位</td>
                                <td>156</td>
                                <td>17.5%</td>
                                <td>9.8万</td>
                                <td>教育、科研院所</td>
                            </tr>
                            <tr>
                                <td>继续深造</td>
                                <td>96</td>
                                <td>10.8%</td>
                                <td>-</td>
                                <td>国内外高校</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

            <!-- 学生竞赛获奖情况 -->
            <div id="competition" class="tab-content" style="display: none;">
                <div class="chart-container">
                    <div class="chart-title">近三年学生竞赛获奖数量变化趋势</div>
                    <div class="chart-small">
                        <canvas id="competitionTrendChart"></canvas>
                    </div>
                </div>

                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-talent">🏆</div>
                            <div class="card-title">2024年竞赛获奖统计</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">45</div>
                                <div class="stat-label">国家级获奖</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="background: #ffd700; width: 19.2%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">89</div>
                                <div class="stat-label">省部级获奖</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="background: #c0c0c0; width: 38.0%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">100</div>
                                <div class="stat-label">校级获奖</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="background: #cd7f32; width: 42.8%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">234</div>
                                <div class="stat-label">获奖总数</div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-talent">⚔️</div>
                            <div class="card-title">竞赛类型分布</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">156</div>
                                <div class="stat-label">学科竞赛</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">45</div>
                                <div class="stat-label">创新创业</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">23</div>
                                <div class="stat-label">军事技能</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">10</div>
                                <div class="stat-label">文体竞赛</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-title">主要竞赛获奖详情</div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>竞赛名称</th>
                                <th>级别</th>
                                <th>获奖等级</th>
                                <th>获奖人数</th>
                                <th>指导教师</th>
                                <th>获奖时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>全国大学生数学建模竞赛</td>
                                <td>国家级</td>
                                <td>一等奖</td>
                                <td>3</td>
                                <td>张教授</td>
                                <td>2024.11</td>
                            </tr>
                            <tr>
                                <td>ACM-ICPC程序设计竞赛</td>
                                <td>国家级</td>
                                <td>金奖</td>
                                <td>3</td>
                                <td>李教授</td>
                                <td>2024.10</td>
                            </tr>
                            <tr>
                                <td>中国大学生计算机设计大赛</td>
                                <td>国家级</td>
                                <td>二等奖</td>
                                <td>4</td>
                                <td>王教授</td>
                                <td>2024.09</td>
                            </tr>
                            <tr>
                                <td>"挑战杯"全国大学生课外学术科技作品竞赛</td>
                                <td>国家级</td>
                                <td>三等奖</td>
                                <td>5</td>
                                <td>刘教授</td>
                                <td>2024.08</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 科研项目情况 -->
            <div id="research-projects" class="tab-content" style="display: none;">
                <div class="chart-container">
                    <div class="chart-title">近五年科研项目数量及经费总额</div>
                    <div class="chart-medium">
                        <canvas id="projectTrendChart"></canvas>
                    </div>
                </div>

                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-research">📊</div>
                            <div class="card-title">2024年项目级别分布</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">26</div>
                                <div class="stat-label">国家级项目</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-primary" style="width: 24.3%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">35</div>
                                <div class="stat-label">省部级项目</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-success" style="width: 32.7%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">28</div>
                                <div class="stat-label">军队级项目</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-warning" style="width: 26.2%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">18</div>
                                <div class="stat-label">企业合作项目</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="background: #f093fb; width: 16.8%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-research">💰</div>
                            <div class="card-title">2024年经费分布</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">1,245万</div>
                                <div class="stat-label">国家级经费</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">856万</div>
                                <div class="stat-label">省部级经费</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">455万</div>
                                <div class="stat-label">军队级经费</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">300万</div>
                                <div class="stat-label">企业合作经费</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="word-cloud">
                    <div class="word-item word-large" onclick="showProjectList('人工智能')">人工智能</div>
                    <div class="word-item word-medium" onclick="showProjectList('大数据')">大数据</div>
                    <div class="word-item word-large" onclick="showProjectList('网络安全')">网络安全</div>
                    <div class="word-item word-small" onclick="showProjectList('云计算')">云计算</div>
                    <div class="word-item word-medium" onclick="showProjectList('物联网')">物联网</div>
                    <div class="word-item word-small" onclick="showProjectList('区块链')">区块链</div>
                    <div class="word-item word-large" onclick="showProjectList('机器学习')">机器学习</div>
                    <div class="word-item word-medium" onclick="showProjectList('软件工程')">软件工程</div>
                    <div class="word-item word-small" onclick="showProjectList('计算机视觉')">计算机视觉</div>
                    <div class="word-item word-medium" onclick="showProjectList('自然语言处理')">自然语言处理</div>
                </div>

                <div class="chart-container">
                    <div class="chart-title">重点科研项目列表</div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>项目名称</th>
                                <th>项目级别</th>
                                <th>负责人</th>
                                <th>经费(万元)</th>
                                <th>起止时间</th>
                                <th>进展状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>基于深度学习的智能网络安全防护技术研究</td>
                                <td>国家自然科学基金</td>
                                <td>张教授</td>
                                <td>58</td>
                                <td>2022-2025</td>
                                <td>进行中</td>
                            </tr>
                            <tr>
                                <td>大规模分布式系统容错机制研究</td>
                                <td>国家重点研发计划</td>
                                <td>李教授</td>
                                <td>156</td>
                                <td>2023-2026</td>
                                <td>进行中</td>
                            </tr>
                            <tr>
                                <td>面向5G的边缘计算关键技术研究</td>
                                <td>省部级重点项目</td>
                                <td>王教授</td>
                                <td>45</td>
                                <td>2021-2024</td>
                                <td>即将结题</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

            <!-- 科研成果情况 -->
            <div id="research-results" class="tab-content" style="display: none;">
                <div class="row">
                    <div class="col">
                        <div class="chart-container">
                            <div class="chart-title">近三年核心期刊论文发表数量变化趋势</div>
                            <div class="chart-small">
                                <canvas id="paperTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="chart-container">
                            <div class="chart-title">近五年专利授权数量</div>
                            <div class="chart-small">
                                <canvas id="patentTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-title">高被引论文列表</div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>论文题目</th>
                                <th>第一作者</th>
                                <th>发表期刊</th>
                                <th>发表年份</th>
                                <th>被引次数</th>
                                <th>影响因子</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Deep Learning for Network Security: A Comprehensive Survey</td>
                                <td>张教授</td>
                                <td>IEEE Transactions on Network Science and Engineering</td>
                                <td>2023</td>
                                <td>156</td>
                                <td>5.033</td>
                            </tr>
                            <tr>
                                <td>Federated Learning with Differential Privacy: A Survey</td>
                                <td>李教授</td>
                                <td>ACM Computing Surveys</td>
                                <td>2022</td>
                                <td>234</td>
                                <td>14.324</td>
                            </tr>
                            <tr>
                                <td>Blockchain-based Secure Data Sharing in IoT Systems</td>
                                <td>王教授</td>
                                <td>IEEE Internet of Things Journal</td>
                                <td>2023</td>
                                <td>189</td>
                                <td>10.238</td>
                            </tr>
                            <tr>
                                <td>Edge Computing for Real-time AI Applications</td>
                                <td>刘教授</td>
                                <td>Nature Machine Intelligence</td>
                                <td>2024</td>
                                <td>98</td>
                                <td>25.898</td>
                            </tr>
                        </tbody>
                    </table>
                </div>



                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-research">📜</div>
                            <div class="card-title">2024年专利分布</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">56</div>
                                <div class="stat-label">发明专利</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-primary" style="width: 62.9%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">28</div>
                                <div class="stat-label">实用新型</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-success" style="width: 31.5%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">5</div>
                                <div class="stat-label">外观设计</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-warning" style="width: 5.6%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">89</div>
                                <div class="stat-label">专利总数</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-title">近五年重大科研奖项荣誉墙</div>
                    <div class="dashboard-grid">
                        <div class="award-card" onclick="showAwardDetail('国家科技进步二等奖')">
                            <div class="award-title">🏆 国家科技进步二等奖</div>
                            <div class="award-project">智能网络安全防护系统关键技术及应用</div>
                            <div class="award-team">获奖团队：张教授团队 | 2023年</div>
                        </div>
                        <div class="award-card" onclick="showAwardDetail('教育部科技进步一等奖')">
                            <div class="award-title">🥇 教育部科技进步一等奖</div>
                            <div class="award-project">大规模分布式系统容错关键技术</div>
                            <div class="award-team">获奖团队：李教授团队 | 2022年</div>
                        </div>
                        <div class="award-card" onclick="showAwardDetail('省科技进步一等奖')">
                            <div class="award-title">🥇 省科技进步一等奖</div>
                            <div class="award-project">面向5G的边缘计算平台技术</div>
                            <div class="award-team">获奖团队：王教授团队 | 2024年</div>
                        </div>
                        <div class="award-card" onclick="showAwardDetail('中国计算机学会科技进步奖')">
                            <div class="award-title">🏅 中国计算机学会科技进步奖</div>
                            <div class="award-project">基于区块链的数据安全共享技术</div>
                            <div class="award-team">获奖团队：刘教授团队 | 2023年</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 科研平台情况 -->
            <div id="research-platforms" class="tab-content" style="display: none;">
                <div class="chart-container">
                    <div class="chart-title">学院科研平台概览</div>
                    <div class="dashboard-grid">
                        <div class="platform-card" onclick="showPlatformDetail('国家重点实验室')">
                            <div class="platform-name">网络空间安全国家重点实验室</div>
                            <div class="platform-level">国家级</div>
                            <div class="platform-subject">依托学科：网络空间安全、计算机科学与技术</div>
                        </div>

                        <div class="platform-card" onclick="showPlatformDetail('教育部重点实验室')">
                            <div class="platform-name">智能信息处理教育部重点实验室</div>
                            <div class="platform-level">部级</div>
                            <div class="platform-subject">依托学科：计算机科学与技术、人工智能</div>
                        </div>

                        <div class="platform-card" onclick="showPlatformDetail('工程技术研究中心')">
                            <div class="platform-name">大数据工程技术研究中心</div>
                            <div class="platform-level">省级</div>
                            <div class="platform-subject">依托学科：数据科学与大数据技术</div>
                        </div>

                        <div class="platform-card" onclick="showPlatformDetail('协同创新中心')">
                            <div class="platform-name">人工智能协同创新中心</div>
                            <div class="platform-level">省级</div>
                            <div class="platform-subject">依托学科：人工智能、机器学习</div>
                        </div>

                        <div class="platform-card" onclick="showPlatformDetail('产学研基地')">
                            <div class="platform-name">软件工程产学研合作基地</div>
                            <div class="platform-level">校级</div>
                            <div class="platform-subject">依托学科：软件工程</div>
                        </div>

                        <div class="platform-card" onclick="showPlatformDetail('联合实验室')">
                            <div class="platform-name">区块链技术联合实验室</div>
                            <div class="platform-level">校级</div>
                            <div class="platform-subject">依托学科：计算机科学与技术</div>
                        </div>
                    </div>
                </div>

                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-research">🏛️</div>
                            <div class="card-title">平台级别分布</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">1</div>
                                <div class="stat-label">国家级平台</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">1</div>
                                <div class="stat-label">部级平台</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">2</div>
                                <div class="stat-label">省级平台</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">2</div>
                                <div class="stat-label">校级平台</div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-research">📊</div>
                            <div class="card-title">平台建设成效</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">156</div>
                                <div class="stat-label">承担项目数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">2.8亿</div>
                                <div class="stat-label">累计经费</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">342</div>
                                <div class="stat-label">发表论文数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">89</div>
                                <div class="stat-label">授权专利数</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

            <!-- 师资规模结构情况 -->
            <div id="faculty-structure" class="tab-content" style="display: none;">
                <div class="row">
                    <div class="col">
                        <div class="chart-container">
                            <div class="chart-title">近五年教师数量变化趋势</div>
                            <div class="chart-small">
                                <canvas id="teacherTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="chart-container">
                            <div class="chart-title">近三年教师培训进修情况</div>
                            <div class="chart-small">
                                <canvas id="trainingTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-faculty">👥</div>
                            <div class="card-title">教师年龄结构</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">45</div>
                                <div class="stat-label">35岁以下 (24.2%)</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-success" style="width: 24.2%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">89</div>
                                <div class="stat-label">35-50岁 (47.8%)</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-primary" style="width: 47.8%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">52</div>
                                <div class="stat-label">50岁以上 (28.0%)</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-warning" style="width: 28.0%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-faculty">🎓</div>
                            <div class="card-title">教师学历结构</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">156</div>
                                <div class="stat-label">博士 (83.9%)</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-primary" style="width: 83.9%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">26</div>
                                <div class="stat-label">硕士 (14.0%)</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-success" style="width: 14.0%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">4</div>
                                <div class="stat-label">本科 (2.1%)</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-warning" style="width: 2.1%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-title">教师结构详细数据</div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>结构类型</th>
                                <th>分类</th>
                                <th>人数</th>
                                <th>占比</th>
                                <th>近三年变化</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td rowspan="3">职称结构</td>
                                <td>教授</td>
                                <td>45</td>
                                <td>24.2%</td>
                                <td>+8人</td>
                            </tr>
                            <tr>
                                <td>副教授</td>
                                <td>68</td>
                                <td>36.6%</td>
                                <td>+12人</td>
                            </tr>
                            <tr>
                                <td>讲师及以下</td>
                                <td>73</td>
                                <td>39.2%</td>
                                <td>+15人</td>
                            </tr>
                            <tr>
                                <td rowspan="3">学缘结构</td>
                                <td>本校毕业</td>
                                <td>56</td>
                                <td>30.1%</td>
                                <td>-3人</td>
                            </tr>
                            <tr>
                                <td>国内其他高校</td>
                                <td>98</td>
                                <td>52.7%</td>
                                <td>+18人</td>
                            </tr>
                            <tr>
                                <td>海外高校</td>
                                <td>32</td>
                                <td>17.2%</td>
                                <td>+8人</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 师资能力水平情况 -->
            <div id="faculty-capability" class="tab-content" style="display: none;">
                <div class="chart-container">
                    <div class="chart-title">高层次人才风采展示</div>
                    <div class="dashboard-grid">
                        <div class="talent-card">
                            <div class="talent-avatar">张</div>
                            <div class="talent-info">
                                <div class="talent-name">张明华 教授</div>
                                <div class="talent-title">长江学者特聘教授、博士生导师</div>
                                <div class="talent-research">研究方向：网络安全、人工智能安全</div>
                            </div>
                        </div>

                        <div class="talent-card">
                            <div class="talent-avatar">李</div>
                            <div class="talent-info">
                                <div class="talent-name">李建国 教授</div>
                                <div class="talent-title">国家杰出青年基金获得者</div>
                                <div class="talent-research">研究方向：分布式系统、云计算</div>
                            </div>
                        </div>

                        <div class="talent-card">
                            <div class="talent-avatar">王</div>
                            <div class="talent-info">
                                <div class="talent-name">王晓东 教授</div>
                                <div class="talent-title">国家优秀青年基金获得者</div>
                                <div class="talent-research">研究方向：机器学习、深度学习</div>
                            </div>
                        </div>

                        <div class="talent-card">
                            <div class="talent-avatar">刘</div>
                            <div class="talent-info">
                                <div class="talent-name">刘海燕 教授</div>
                                <div class="talent-title">青年拔尖人才、博士生导师</div>
                                <div class="talent-research">研究方向：区块链技术、数据安全</div>
                            </div>
                        </div>
                    </div>
                </div>



                <div class="chart-container">
                    <div class="chart-title">教师教学科研突出成果</div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>教师姓名</th>
                                <th>成果类型</th>
                                <th>成果名称</th>
                                <th>获得时间</th>
                                <th>级别</th>
                                <th>详情</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>张明华</td>
                                <td>教学名师</td>
                                <td>国家级教学名师</td>
                                <td>2023年</td>
                                <td>国家级</td>
                                <td><button class="btn btn-info" style="padding: 4px 8px; font-size: 11px;">查看详情</button></td>
                            </tr>
                            <tr>
                                <td>李建国</td>
                                <td>优秀教材</td>
                                <td>《分布式系统原理与实践》</td>
                                <td>2024年</td>
                                <td>国家级</td>
                                <td><button class="btn btn-info" style="padding: 4px 8px; font-size: 11px;">查看详情</button></td>
                            </tr>
                            <tr>
                                <td>王晓东</td>
                                <td>重大科研项目</td>
                                <td>国家重点研发计划项目负责人</td>
                                <td>2023年</td>
                                <td>国家级</td>
                                <td><button class="btn btn-info" style="padding: 4px 8px; font-size: 11px;">查看详情</button></td>
                            </tr>
                            <tr>
                                <td>刘海燕</td>
                                <td>青年人才</td>
                                <td>国家优秀青年基金获得者</td>
                                <td>2022年</td>
                                <td>国家级</td>
                                <td><button class="btn btn-info" style="padding: 4px 8px; font-size: 11px;">查看详情</button></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 人才引进情况 -->
            <div id="talent-recruitment" class="tab-content" style="display: none;">
                <div class="policy-card">
                    <div class="policy-title">🎯 学院人才引进政策</div>
                    <div class="policy-content">
                        <strong>引进人才类型：</strong><br>
                        • 顶尖人才：院士、长江学者等国家级人才<br>
                        • 领军人才：杰青、优青等高层次人才<br>
                        • 青年才俊：海内外优秀博士、博士后<br><br>

                        <strong>待遇保障：</strong><br>
                        • 薪酬待遇：提供具有竞争力的薪酬体系<br>
                        • 住房保障：提供人才公寓或住房补贴<br>
                        • 子女教育：协助解决子女入学问题<br>
                        • 配偶工作：协助安排配偶工作<br><br>

                        <strong>科研支持：</strong><br>
                        • 启动经费：根据人才层次提供50-500万启动经费<br>
                        • 实验室建设：提供独立实验室和设备支持<br>
                        • 团队建设：支持组建科研团队<br>
                        • 研究生招生：优先保障博士生、硕士生招生指标
                    </div>
                </div>

                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-faculty">📈</div>
                            <div class="card-title">近三年人才引进统计</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">45</div>
                                <div class="stat-label">引进人才总数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">8</div>
                                <div class="stat-label">高层次人才</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">23</div>
                                <div class="stat-label">海外人才</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">14</div>
                                <div class="stat-label">青年博士</div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-faculty">🌍</div>
                            <div class="card-title">人才来源分布</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">23</div>
                                <div class="stat-label">海外引进</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-primary" style="width: 51.1%;"></div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">22</div>
                                <div class="stat-label">国内引进</div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-success" style="width: 48.9%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 大项任务情况 -->
            <div id="major-tasks" class="tab-content" style="display: none;">
                <div class="chart-container">
                    <div class="chart-title">教师参加大项任务活动情况</div>
                    <div class="chart-small">
                        <canvas id="majorTaskChart"></canvas>
                    </div>
                </div>

                <div class="dashboard-grid">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-icon icon-research">📋</div>
                            <div class="card-title">2024年大项任务统计</div>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">76</div>
                                <div class="stat-label">参与人次</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">23</div>
                                <div class="stat-label">任务项目数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">5</div>
                                <div class="stat-label">任务类型</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">1.2亿</div>
                                <div class="stat-label">任务经费</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-title">重大任务项目列表</div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>任务名称</th>
                                <th>任务类型</th>
                                <th>负责人</th>
                                <th>参与人数</th>
                                <th>任务周期</th>
                                <th>完成状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>国家网络安全重大专项</td>
                                <td>国家重大专项</td>
                                <td>张明华</td>
                                <td>15</td>
                                <td>2022-2025</td>
                                <td>进行中</td>
                            </tr>
                            <tr>
                                <td>军队信息化建设重大任务</td>
                                <td>军队重大任务</td>
                                <td>李建国</td>
                                <td>23</td>
                                <td>2023-2026</td>
                                <td>进行中</td>
                            </tr>
                            <tr>
                                <td>中欧人工智能合作项目</td>
                                <td>国际合作项目</td>
                                <td>王晓东</td>
                                <td>18</td>
                                <td>2021-2024</td>
                                <td>即将完成</td>
                            </tr>
                            <tr>
                                <td>智慧城市重大工程</td>
                                <td>重大工程建设</td>
                                <td>刘海燕</td>
                                <td>12</td>
                                <td>2024-2027</td>
                                <td>启动阶段</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 切换面板显示
        function showPanel(panelId) {
            // 隐藏所有面板
            const panels = document.querySelectorAll('.content-panel');
            panels.forEach(panel => panel.classList.remove('active'));

            // 移除所有标签的active状态
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的面板
            document.getElementById(panelId).classList.add('active');

            // 激活对应的标签
            event.target.classList.add('active');
        }

        // 切换子面板
        function showSubPanel(subPanelId) {
            // 隐藏当前面板下的所有子内容
            const currentPanel = document.querySelector('.content-panel.active');
            const subContents = currentPanel.querySelectorAll('.tab-content');
            subContents.forEach(content => content.style.display = 'none');

            // 移除所有子标签的active状态
            const subTabs = currentPanel.querySelectorAll('.sub-tab');
            subTabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的子内容
            const targetContent = currentPanel.querySelector(`#${subPanelId}`);
            if (targetContent) {
                targetContent.style.display = 'block';
            }

            // 激活对应的子标签
            event.target.classList.add('active');
        }

        // 显示项目列表
        function showProjectList(field) {
            alert(`查看${field}领域的具体项目列表`);
        }

        // 显示奖项详情
        function showAwardDetail(award) {
            alert(`查看${award}的详细信息`);
        }

        // 显示平台详情
        function showPlatformDetail(platform) {
            alert(`查看${platform}的详细介绍`);
        }

        // 基础图表配置
        const baseChartConfig = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        };

        // 初始化图表
        function initCharts() {
            console.log('开始初始化图表...');

            // 检查Chart.js是否加载
            if (typeof Chart === 'undefined') {
                console.error('Chart.js 未加载');
                return;
            }

            // 1. 学生数量趋势图
            const studentTrendCtx = document.getElementById('studentTrendChart');
            console.log('studentTrendChart元素:', studentTrendCtx);
            if (studentTrendCtx) {
                try {
                    new Chart(studentTrendCtx, {
                        type: 'line',
                        data: {
                            labels: ['2022年', '2023年', '2024年'],
                            datasets: [{
                                label: '本科生',
                                data: [2234, 2298, 2456],
                                borderColor: '#667eea',
                                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                                fill: false
                            }, {
                                label: '硕士生',
                                data: [598, 623, 654],
                                borderColor: '#43e97b',
                                backgroundColor: 'rgba(67, 233, 123, 0.1)',
                                fill: false
                            }, {
                                label: '博士生',
                                data: [257, 257, 146],
                                borderColor: '#ffa726',
                                backgroundColor: 'rgba(255, 167, 38, 0.1)',
                                fill: false
                            }]
                        },
                        options: baseChartConfig
                    });
                console.log('学生数量趋势图创建成功');
                } catch (error) {
                    console.error('创建学生数量趋势图失败:', error);
                }
            } else {
                console.error('未找到studentTrendChart元素');
            }

            // 2. 教职工数量趋势图
            const facultyTrendCtx = document.getElementById('facultyTrendChart');
            if (facultyTrendCtx) {
                new Chart(facultyTrendCtx, {
                    type: 'bar',
                    data: {
                        labels: ['2022年', '2023年', '2024年'],
                        datasets: [{
                            label: '专任教师',
                            data: [128, 135, 142],
                            backgroundColor: '#667eea'
                        }, {
                            label: '管理人员',
                            data: [40, 40, 44],
                            backgroundColor: '#43e97b'
                        }]
                    },
                    options: baseChartConfig
                });
            }

            // 3. 科研成果趋势图
            const researchTrendCtx = document.getElementById('researchTrendChart');
            if (researchTrendCtx) {
                new Chart(researchTrendCtx, {
                    type: 'line',
                    data: {
                        labels: ['2022年', '2023年', '2024年'],
                        datasets: [{
                            label: '发表论文',
                            data: [198, 213, 245],
                            borderColor: '#667eea',
                            backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            yAxisID: 'y'
                        }, {
                            label: '授权专利',
                            data: [56, 69, 89],
                            borderColor: '#f093fb',
                            backgroundColor: 'rgba(240, 147, 251, 0.1)',
                            yAxisID: 'y1'
                        }]
                    },
                    options: baseChartConfig
                });
            }

            // 4. 办学条件趋势图
            const facilitiesTrendCtx = document.getElementById('facilitiesTrendChart');
            if (facilitiesTrendCtx) {
                new Chart(facilitiesTrendCtx, {
                    type: 'bar',
                    data: {
                        labels: ['2022年', '2023年', '2024年'],
                        datasets: [{
                            label: '固定资产(亿元)',
                            data: [2.3, 2.5, 2.8],
                            backgroundColor: '#667eea'
                        }, {
                            label: '教学设备(亿元)',
                            data: [0.9, 1.0, 1.2],
                            backgroundColor: '#43e97b'
                        }]
                    },
                    options: baseChartConfig
                });
            }

            // 5. 在校生人数五年趋势
            const enrollmentTrendCtx = document.getElementById('enrollmentTrendChart');
            if (enrollmentTrendCtx) {
                new Chart(enrollmentTrendCtx, {
                    type: 'line',
                    data: {
                        labels: ['2020年', '2021年', '2022年', '2023年', '2024年'],
                        datasets: [{
                            label: '本科生',
                            data: [2156, 2234, 2298, 2345, 2456],
                            borderColor: '#667eea',
                            fill: false
                        }, {
                            label: '硕士生',
                            data: [456, 498, 523, 578, 654],
                            borderColor: '#43e97b',
                            fill: false
                        }, {
                            label: '博士生',
                            data: [244, 213, 268, 255, 146],
                            borderColor: '#ffa726',
                            fill: false
                        }]
                    },
                    options: baseChartConfig
                });
            }

            // 6. 毕业生人数五年趋势
            const graduationTrendCtx = document.getElementById('graduationTrendChart');
            if (graduationTrendCtx) {
                new Chart(graduationTrendCtx, {
                    type: 'bar',
                    data: {
                        labels: ['2020年', '2021年', '2022年', '2023年', '2024年'],
                        datasets: [{
                            label: '本科毕业生',
                            data: [567, 589, 623, 645, 678],
                            backgroundColor: '#667eea'
                        }, {
                            label: '硕士毕业生',
                            data: [134, 142, 145, 152, 156],
                            backgroundColor: '#43e97b'
                        }, {
                            label: '博士毕业生',
                            data: [55, 58, 55, 59, 58],
                            backgroundColor: '#ffa726'
                        }]
                    },
                    options: baseChartConfig
                });
            }

            // 7. 竞赛获奖趋势图
            const competitionTrendCtx = document.getElementById('competitionTrendChart');
            if (competitionTrendCtx) {
                new Chart(competitionTrendCtx, {
                    type: 'line',
                    data: {
                        labels: ['2022年', '2023年', '2024年'],
                        datasets: [{
                            label: '国家级竞赛',
                            data: [28, 35, 45],
                            borderColor: '#ffd700',
                            backgroundColor: 'rgba(255, 215, 0, 0.1)',
                            fill: false
                        }, {
                            label: '省部级竞赛',
                            data: [65, 78, 89],
                            borderColor: '#c0c0c0',
                            backgroundColor: 'rgba(192, 192, 192, 0.1)',
                            fill: false
                        }, {
                            label: '校级竞赛',
                            data: [63, 76, 100],
                            borderColor: '#cd7f32',
                            backgroundColor: 'rgba(205, 127, 50, 0.1)',
                            fill: false
                        }]
                    },
                    options: baseChartConfig
                });
            }

            // 8. 科研项目趋势图
            const projectTrendCtx = document.getElementById('projectTrendChart');
            if (projectTrendCtx) {
                new Chart(projectTrendCtx, {
                    type: 'bar',
                    data: {
                        labels: ['2020年', '2021年', '2022年', '2023年', '2024年'],
                        datasets: [{
                            label: '国家级项目',
                            data: [18, 22, 24, 25, 26],
                            backgroundColor: '#667eea'
                        }, {
                            label: '省部级项目',
                            data: [25, 28, 30, 32, 35],
                            backgroundColor: '#43e97b'
                        }, {
                            label: '军队级项目',
                            data: [20, 22, 24, 26, 28],
                            backgroundColor: '#ffa726'
                        }, {
                            label: '企业合作项目',
                            data: [15, 17, 17, 19, 18],
                            backgroundColor: '#f093fb'
                        }]
                    },
                    options: baseChartConfig
                });
            }

            // 9. 论文发表趋势图
            const paperTrendCtx = document.getElementById('paperTrendChart');
            if (paperTrendCtx) {
                new Chart(paperTrendCtx, {
                    type: 'line',
                    data: {
                        labels: ['2022年', '2023年', '2024年'],
                        datasets: [{
                            label: '计算机科学',
                            data: [78, 85, 98],
                            borderColor: '#667eea',
                            backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            tension: 0.4
                        }, {
                            label: '人工智能',
                            data: [45, 52, 67],
                            borderColor: '#43e97b',
                            backgroundColor: 'rgba(67, 233, 123, 0.1)',
                            tension: 0.4
                        }, {
                            label: '网络安全',
                            data: [38, 42, 48],
                            borderColor: '#ffa726',
                            backgroundColor: 'rgba(255, 167, 38, 0.1)',
                            tension: 0.4
                        }, {
                            label: '软件工程',
                            data: [37, 34, 32],
                            borderColor: '#f093fb',
                            backgroundColor: 'rgba(240, 147, 251, 0.1)',
                            fill: false
                        }]
                    },
                    options: baseChartConfig
                });
            }

            // 10. 专利授权趋势图
            const patentTrendCtx = document.getElementById('patentTrendChart');
            if (patentTrendCtx) {
                new Chart(patentTrendCtx, {
                    type: 'bar',
                    data: {
                        labels: ['2020年', '2021年', '2022年', '2023年', '2024年'],
                        datasets: [{
                            label: '发明专利',
                            data: [28, 32, 35, 43, 56],
                            backgroundColor: '#667eea'
                        }, {
                            label: '实用新型专利',
                            data: [15, 18, 19, 24, 28],
                            backgroundColor: '#43e97b'
                        }, {
                            label: '外观设计专利',
                            data: [2, 2, 2, 2, 5],
                            backgroundColor: '#ffa726'
                        }]
                    },
                    options: baseChartConfig
                });
            }

            // 11. 教师数量趋势图
            const teacherTrendCtx = document.getElementById('teacherTrendChart');
            if (teacherTrendCtx) {
                new Chart(teacherTrendCtx, {
                    type: 'line',
                    data: {
                        labels: ['2020年', '2021年', '2022年', '2023年', '2024年'],
                        datasets: [{
                            label: '教师总数',
                            data: [142, 148, 156, 168, 186],
                            borderColor: '#667eea',
                            backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            fill: false
                        }]
                    },
                    options: baseChartConfig
                });
            }

            // 12. 教师培训趋势图
            const trainingTrendCtx = document.getElementById('trainingTrendChart');
            if (trainingTrendCtx) {
                new Chart(trainingTrendCtx, {
                    type: 'bar',
                    data: {
                        labels: ['2022年', '2023年', '2024年'],
                        datasets: [{
                            label: '学术会议',
                            data: [45, 52, 68],
                            backgroundColor: '#667eea'
                        }, {
                            label: '短期培训',
                            data: [32, 35, 42],
                            backgroundColor: '#43e97b'
                        }, {
                            label: '访学进修',
                            data: [12, 15, 16],
                            backgroundColor: '#ffa726'
                        }]
                    },
                    options: baseChartConfig
                });
            }

            // 13. 大项任务参与图
            const majorTaskCtx = document.getElementById('majorTaskChart');
            if (majorTaskCtx) {
                new Chart(majorTaskCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['国家重大专项', '军队重大任务', '国际合作项目', '重大工程建设', '应急科技攻关'],
                        datasets: [{
                            data: [15, 23, 18, 12, 8],
                            backgroundColor: [
                                '#667eea',
                                '#43e97b',
                                '#ffa726',
                                '#f093fb',
                                '#4facfe'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    usePointStyle: true,
                                    padding: 20
                                }
                            }
                        }
                    }
                });
            }
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            console.log('学院发展监控系统已加载');

            // 延迟初始化图表，确保DOM完全渲染
            setTimeout(() => {
                initCharts();
            }, 500);

            // 添加一些动态效果
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });
    </script>
</body>
</html>
