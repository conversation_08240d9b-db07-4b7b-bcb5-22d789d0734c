<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>师资发展与学生成长数智建设系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .header-content {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .nav-menu {
            display: flex;
            gap: 30px;
        }
        
        .nav-item {
            color: #333;
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s;
        }
        
        .nav-item:hover, .nav-item.active {
            background: #667eea;
            color: white;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            color: #333;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .main-layout {
            display: flex;
            gap: 20px;
            min-height: calc(100vh - 120px);
        }
        
        .sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            height: fit-content;
            position: sticky;
            top: 100px;
        }
        
        .main-content {
            flex: 1;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
            padding-bottom: 15px;
            border-bottom: 2px solid #667eea;
        }
        
        .menu-group {
            margin-bottom: 25px;
        }
        
        .menu-group-title {
            font-size: 14px;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            margin-bottom: 5px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
            color: #666;
            text-decoration: none;
        }
        
        .menu-item:hover {
            background: #f0f4ff;
            color: #667eea;
            transform: translateX(5px);
        }
        
        .menu-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .menu-item i {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }
        
        .content-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .content-title {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .content-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }
        
        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-primary:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-warning {
            background: #f59e0b;
            color: white;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .btn-outline {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
        }
        
        .btn-outline:hover {
            background: #667eea;
            color: white;
        }
        
        .content-body {
            padding: 30px;
        }
        
        .content-panel {
            display: none;
        }
        
        .content-panel.active {
            display: block;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border-left: 4px solid #667eea;
            transition: all 0.3s;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .stat-trend {
            font-size: 12px;
            color: #10b981;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .data-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .table-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .search-box {
            padding: 8px 15px;
            border: 1px solid #e9ecef;
            border-radius: 20px;
            font-size: 14px;
            width: 250px;
        }
        
        .table-content {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }
        
        .alert-info {
            background: #dbeafe;
            color: #1e40af;
            border-left-color: #3b82f6;
        }
        
        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border-left-color: #10b981;
        }
        
        .alert-warning {
            background: #fef3c7;
            color: #92400e;
            border-left-color: #f59e0b;
        }
        
        .alert-danger {
            background: #fee2e2;
            color: #991b1b;
            border-left-color: #ef4444;
        }
        
        .tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 8px 8px 0 0;
            margin-bottom: 0;
        }
        
        .tab {
            flex: 1;
            padding: 15px 20px;
            background: transparent;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s;
        }
        
        .tab.active {
            background: white;
            color: #667eea;
            border-bottom: 2px solid #667eea;
        }
        
        .tab-content {
            background: white;
            padding: 20px;
            border-radius: 0 0 8px 8px;
        }
        
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            margin: 8px 0;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 10px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 2000;
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }
        
        .modal-header {
            background: #667eea;
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-body {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .close {
            font-size: 24px;
            cursor: pointer;
            color: white;
            opacity: 0.8;
        }
        
        .close:hover {
            opacity: 1;
        }
        
        .filter-bar {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .filter-label {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-graduation-cap"></i>
                师资发展与学生成长数智建设系统
            </div>
            <nav class="nav-menu">
                <a href="#" class="nav-item active" onclick="switchSystem('teacher')">师资发展</a>
                <a href="#" class="nav-item" onclick="switchSystem('student')">学生成长</a>
                <a href="#" class="nav-item" onclick="switchSystem('analysis')">异常分析</a>
            </nav>
            <div class="user-info">
                <span>管理员</span>
                <i class="fas fa-user-circle" style="font-size: 24px;"></i>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="main-layout">
            <div class="sidebar">
                <div class="sidebar-title">
                    <i class="fas fa-list"></i>
                    功能导航
                </div>
                
                <!-- 师资发展菜单 -->
                <div id="teacherMenu" class="menu-system">
                    <div class="menu-group">
                        <div class="menu-group-title">教师个人数据中心</div>
                        <a href="#" class="menu-item active" onclick="showContent('teacher-profile')">
                            <i class="fas fa-user"></i>
                            个人信息档案
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('teacher-data-mgmt')">
                            <i class="fas fa-database"></i>
                            教师数据管理
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('data-model-mgmt')">
                            <i class="fas fa-cogs"></i>
                            数据模型管理
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('model-permission')">
                            <i class="fas fa-shield-alt"></i>
                            模型权限管理
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('user-auth-mgmt')">
                            <i class="fas fa-users-cog"></i>
                            用户授权管理
                        </a>
                    </div>
                    
                    <div class="menu-group">
                        <div class="menu-group-title">教师数字画像</div>
                        <a href="#" class="menu-item" onclick="showContent('teacher-portrait')">
                            <i class="fas fa-chart-line"></i>
                            个人数字画像
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('group-tools')">
                            <i class="fas fa-users"></i>
                            画像群工具
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('portrait-compare')">
                            <i class="fas fa-balance-scale"></i>
                            画像对比分析
                        </a>
                    </div>
                </div>
                
                <!-- 学生成长菜单 -->
                <div id="studentMenu" class="menu-system" style="display: none;">
                    <div class="menu-group">
                        <div class="menu-group-title">学生个人数据中心</div>
                        <a href="#" class="menu-item" onclick="showContent('student-profile')">
                            <i class="fas fa-user-graduate"></i>
                            个人信息档案
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('student-data-mgmt')">
                            <i class="fas fa-database"></i>
                            学生数据管理
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('student-model-mgmt')">
                            <i class="fas fa-cogs"></i>
                            数据模型管理
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('student-permission')">
                            <i class="fas fa-shield-alt"></i>
                            模型权限管理
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('student-auth-mgmt')">
                            <i class="fas fa-users-cog"></i>
                            用户授权管理
                        </a>
                    </div>
                    
                    <div class="menu-group">
                        <div class="menu-group-title">学生成长评价</div>
                        <a href="#" class="menu-item" onclick="showContent('growth-evaluation')">
                            <i class="fas fa-chart-area"></i>
                            成长数智评价
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('student-portrait')">
                            <i class="fas fa-user-chart"></i>
                            个人数字画像
                        </a>
                    </div>
                </div>
                
                <!-- 异常分析菜单 -->
                <div id="analysisMenu" class="menu-system" style="display: none;">
                    <div class="menu-group">
                        <div class="menu-group-title">学生异常分析</div>
                        <a href="#" class="menu-item" onclick="showContent('academic-warning')">
                            <i class="fas fa-exclamation-triangle"></i>
                            学业成绩异常预警
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('consumption-analysis')">
                            <i class="fas fa-credit-card"></i>
                            消费活动异常
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('training-analysis')">
                            <i class="fas fa-running"></i>
                            训练活动异常
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('daily-analysis')">
                            <i class="fas fa-calendar-day"></i>
                            日常活动异常
                        </a>
                    </div>
                </div>
            </div>

            <div class="main-content">
                <div class="content-header">
                    <div class="content-title">
                        <i class="fas fa-user" id="contentIcon"></i>
                        <span id="contentTitle">教师个人信息档案</span>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-primary" onclick="exportData()">
                            <i class="fas fa-download"></i> 导出数据
                        </button>
                        <button class="btn btn-primary" onclick="importData()">
                            <i class="fas fa-upload"></i> 导入数据
                        </button>
                        <button class="btn btn-primary" onclick="addRecord()">
                            <i class="fas fa-plus"></i> 新增记录
                        </button>
                    </div>
                </div>

                <div class="content-body">
                    <!-- 教师个人信息档案 -->
                    <div id="teacher-profile" class="content-panel active">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            支持教师信息分类汇聚展示，包含基本信息、人事信息、教学信息、科研信息等相关信息。支持信息分类呈现，可按照不同的数据来源、数据内容自定义分类。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">186</div>
                                <div class="stat-label">在职教师总数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上月 +5
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">83.9%</div>
                                <div class="stat-label">博士学位比例</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上年 +2.1%
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">45</div>
                                <div class="stat-label">教授人数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上年 +3
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">234</div>
                                <div class="stat-label">培训活动参与</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    本月完成
                                </div>
                            </div>
                        </div>

                        <div class="tabs">
                            <button class="tab active" onclick="showTab('basic-info')">基本信息</button>
                            <button class="tab" onclick="showTab('personnel-info')">人事信息</button>
                            <button class="tab" onclick="showTab('teaching-info')">教学信息</button>
                            <button class="tab" onclick="showTab('research-info')">科研信息</button>
                            <button class="tab" onclick="showTab('training-info')">培训信息</button>
                            <button class="tab" onclick="showTab('competition-info')">教学比赛</button>
                        </div>

                        <div class="tab-content">
                            <div id="basic-info" class="tab-panel active">
                                <div class="filter-bar">
                                    <div class="filter-group">
                                        <span class="filter-label">搜索:</span>
                                        <input type="text" class="form-control" placeholder="工号/姓名" style="width: 200px;">
                                    </div>
                                    <div class="filter-group">
                                        <span class="filter-label">所属机构:</span>
                                        <select class="form-control" style="width: 150px;">
                                            <option>全部</option>
                                            <option>计算机系</option>
                                            <option>数学系</option>
                                            <option>物理系</option>
                                        </select>
                                    </div>
                                    <div class="filter-group">
                                        <span class="filter-label">在职状态:</span>
                                        <select class="form-control" style="width: 120px;">
                                            <option>全部</option>
                                            <option>在职</option>
                                            <option>离职</option>
                                            <option>退休</option>
                                        </select>
                                    </div>
                                    <button class="btn btn-outline">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                </div>

                                <div class="data-table">
                                    <div class="table-header">
                                        <div class="table-title">教师基本信息列表</div>
                                        <div class="action-buttons">
                                            <button class="btn btn-success" onclick="customizeColumns()">
                                                <i class="fas fa-columns"></i> 自定义列
                                            </button>
                                        </div>
                                    </div>
                                    <div class="table-content">
                                        <table>
                                            <thead>
                                                <tr>
                                                    <th>工号</th>
                                                    <th>姓名</th>
                                                    <th>性别</th>
                                                    <th>职称</th>
                                                    <th>学历</th>
                                                    <th>所属系室</th>
                                                    <th>在职状态</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>T001</td>
                                                    <td>张教授</td>
                                                    <td>男</td>
                                                    <td>教授</td>
                                                    <td>博士</td>
                                                    <td>计算机系</td>
                                                    <td><span class="badge badge-success">在职</span></td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="viewDetail('T001')">查看</button>
                                                        <button class="btn btn-warning" onclick="editRecord('T001')">编辑</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>T002</td>
                                                    <td>李副教授</td>
                                                    <td>女</td>
                                                    <td>副教授</td>
                                                    <td>博士</td>
                                                    <td>数学系</td>
                                                    <td><span class="badge badge-success">在职</span></td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="viewDetail('T002')">查看</button>
                                                        <button class="btn btn-warning" onclick="editRecord('T002')">编辑</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>T003</td>
                                                    <td>王讲师</td>
                                                    <td>男</td>
                                                    <td>讲师</td>
                                                    <td>硕士</td>
                                                    <td>物理系</td>
                                                    <td><span class="badge badge-success">在职</span></td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="viewDetail('T003')">查看</button>
                                                        <button class="btn btn-warning" onclick="editRecord('T003')">编辑</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 学生个人信息档案 -->
                    <div id="student-profile" class="content-panel">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            支持学生信息分类汇聚展示，包括基本信息、培养方案、家庭信息、获奖记录、违纪记录、困难补助、助学金、助学贷款、宿舍考勤及卫生、一卡通消费、图书借阅、活动竞赛、学业成绩、训练成绩等信息。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">3,256</div>
                                <div class="stat-label">在校学生总数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上年 +78
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">95.6%</div>
                                <div class="stat-label">学业正常率</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上学期 +1.2%
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">234</div>
                                <div class="stat-label">获奖学生数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    本学期统计
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">12</div>
                                <div class="stat-label">预警学生数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-down"></i>
                                    较上月 -5
                                </div>
                            </div>
                        </div>

                        <div class="tabs">
                            <button class="tab active" onclick="showTab('student-basic')">基本信息</button>
                            <button class="tab" onclick="showTab('academic-info')">学业成绩</button>
                            <button class="tab" onclick="showTab('award-info')">获奖记录</button>
                            <button class="tab" onclick="showTab('assistance-info')">资助信息</button>
                            <button class="tab" onclick="showTab('campus-life')">校园生活</button>
                        </div>

                        <div class="tab-content">
                            <div id="student-basic" class="tab-panel active">
                                <div class="filter-bar">
                                    <div class="filter-group">
                                        <span class="filter-label">搜索:</span>
                                        <input type="text" class="form-control" placeholder="学号/姓名" style="width: 200px;">
                                    </div>
                                    <div class="filter-group">
                                        <span class="filter-label">所属班级:</span>
                                        <select class="form-control" style="width: 150px;">
                                            <option>全部</option>
                                            <option>计算机1班</option>
                                            <option>计算机2班</option>
                                            <option>数学1班</option>
                                        </select>
                                    </div>
                                    <div class="filter-group">
                                        <span class="filter-label">年级:</span>
                                        <select class="form-control" style="width: 120px;">
                                            <option>全部</option>
                                            <option>2024级</option>
                                            <option>2023级</option>
                                            <option>2022级</option>
                                            <option>2021级</option>
                                        </select>
                                    </div>
                                    <button class="btn btn-outline">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                </div>

                                <div class="data-table">
                                    <div class="table-header">
                                        <div class="table-title">学生基本信息列表</div>
                                        <div class="action-buttons">
                                            <button class="btn btn-success" onclick="customizeStudentColumns()">
                                                <i class="fas fa-columns"></i> 自定义列
                                            </button>
                                        </div>
                                    </div>
                                    <div class="table-content">
                                        <table>
                                            <thead>
                                                <tr>
                                                    <th>学号</th>
                                                    <th>姓名</th>
                                                    <th>性别</th>
                                                    <th>专业</th>
                                                    <th>班级</th>
                                                    <th>年级</th>
                                                    <th>学籍状态</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>S2024001</td>
                                                    <td>张同学</td>
                                                    <td>男</td>
                                                    <td>计算机科学与技术</td>
                                                    <td>计算机1班</td>
                                                    <td>2024级</td>
                                                    <td><span class="badge badge-success">在读</span></td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="viewStudentDetail('S2024001')">查看</button>
                                                        <button class="btn btn-warning" onclick="editStudentRecord('S2024001')">编辑</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>S2024002</td>
                                                    <td>李同学</td>
                                                    <td>女</td>
                                                    <td>数学与应用数学</td>
                                                    <td>数学1班</td>
                                                    <td>2024级</td>
                                                    <td><span class="badge badge-success">在读</span></td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="viewStudentDetail('S2024002')">查看</button>
                                                        <button class="btn btn-warning" onclick="editStudentRecord('S2024002')">编辑</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 学业成绩异常预警 -->
                    <div id="academic-warning" class="content-panel">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            通过动态监测学业表现数据，出勤状况、学分获取情况以及成绩状况等内容，构建异常指标体系判定学生学业情况是否存在异常。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">12</div>
                                <div class="stat-label">学业预警学生</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-down"></i>
                                    较上月 -3
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">5</div>
                                <div class="stat-label">毕业学分不足</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-down"></i>
                                    较上月 -2
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">3</div>
                                <div class="stat-label">第二课堂学分不足</div>
                                <div class="stat-trend">
                                    <i class="fas fa-minus"></i>
                                    无变化
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">2</div>
                                <div class="stat-label">退学预警</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-down"></i>
                                    较上月 -1
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-success" onclick="setWarningConditions()">
                                <i class="fas fa-cog"></i> 设置预警条件
                            </button>
                            <button class="btn btn-outline" onclick="exportWarningData()">
                                <i class="fas fa-download"></i> 导出预警数据
                            </button>
                            <button class="btn btn-outline" onclick="warningStatistics()">
                                <i class="fas fa-chart-bar"></i> 统计分析
                            </button>
                        </div>

                        <div class="data-table">
                            <div class="table-header">
                                <div class="table-title">学业预警学生列表</div>
                                <input type="text" class="search-box" placeholder="搜索学生姓名或学号...">
                            </div>
                            <div class="table-content">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>学号</th>
                                            <th>姓名</th>
                                            <th>专业班级</th>
                                            <th>预警类型</th>
                                            <th>预警等级</th>
                                            <th>预警时间</th>
                                            <th>处理状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>S2023001</td>
                                            <td>王同学</td>
                                            <td>计算机2班</td>
                                            <td>学分不足</td>
                                            <td><span class="badge badge-danger">高风险</span></td>
                                            <td>2024-01-15</td>
                                            <td><span class="badge badge-warning">处理中</span></td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewWarningDetail('S2023001')">查看</button>
                                                <button class="btn btn-success" onclick="processWarning('S2023001')">处理</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>S2023002</td>
                                            <td>赵同学</td>
                                            <td>数学1班</td>
                                            <td>出勤异常</td>
                                            <td><span class="badge badge-warning">中风险</span></td>
                                            <td>2024-01-12</td>
                                            <td><span class="badge badge-info">待处理</span></td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewWarningDetail('S2023002')">查看</button>
                                                <button class="btn btn-success" onclick="processWarning('S2023002')">处理</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 教师数据管理 -->
                    <div id="teacher-data-mgmt" class="content-panel">
                        <div class="alert alert-info">
                            <i class="fas fa-database"></i>
                            管理员可根据数据权限管理一定范围内的教师档案，支持按组织架构展示管理教师信息，提供快速检索和数据管理功能。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">8</div>
                                <div class="stat-label">管理部门数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-building"></i>
                                    院系级管理
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">156</div>
                                <div class="stat-label">可管理教师数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-users"></i>
                                    权限范围内
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">23</div>
                                <div class="stat-label">本月数据更新</div>
                                <div class="stat-trend">
                                    <i class="fas fa-sync"></i>
                                    数据同步
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">98.5%</div>
                                <div class="stat-label">数据完整率</div>
                                <div class="stat-trend">
                                    <i class="fas fa-check"></i>
                                    质量良好
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-success" onclick="batchImport()">
                                <i class="fas fa-upload"></i> 批量导入
                            </button>
                            <button class="btn btn-outline" onclick="dataCorrection()">
                                <i class="fas fa-edit"></i> 数据纠错
                            </button>
                            <button class="btn btn-outline" onclick="dataSync()">
                                <i class="fas fa-sync"></i> 数据同步
                            </button>
                            <button class="btn btn-outline" onclick="exportTeacherData()">
                                <i class="fas fa-download"></i> 导出数据
                            </button>
                        </div>

                        <div class="tabs">
                            <button class="tab active" onclick="showTab('org-structure')">组织架构</button>
                            <button class="tab" onclick="showTab('data-classification')">数据分类</button>
                            <button class="tab" onclick="showTab('data-quality')">数据质量</button>
                        </div>

                        <div class="tab-content">
                            <div id="org-structure" class="tab-panel active">
                                <div class="filter-bar">
                                    <div class="filter-group">
                                        <span class="filter-label">组织机构:</span>
                                        <select class="form-control" style="width: 200px;">
                                            <option>全部机构</option>
                                            <option>计算机学院</option>
                                            <option>数学学院</option>
                                            <option>物理学院</option>
                                            <option>化学学院</option>
                                        </select>
                                    </div>
                                    <div class="filter-group">
                                        <span class="filter-label">用人方式:</span>
                                        <select class="form-control" style="width: 150px;">
                                            <option>全部</option>
                                            <option>正式编制</option>
                                            <option>合同制</option>
                                            <option>临时聘用</option>
                                        </select>
                                    </div>
                                    <div class="filter-group">
                                        <span class="filter-label">来院年月:</span>
                                        <input type="month" class="form-control" style="width: 150px;">
                                    </div>
                                    <button class="btn btn-outline">
                                        <i class="fas fa-search"></i> 筛选
                                    </button>
                                </div>

                                <div class="data-table">
                                    <div class="table-header">
                                        <div class="table-title">按组织架构管理教师信息</div>
                                        <input type="text" class="search-box" placeholder="工号/姓名搜索...">
                                    </div>
                                    <div class="table-content">
                                        <table>
                                            <thead>
                                                <tr>
                                                    <th>工号</th>
                                                    <th>姓名</th>
                                                    <th>所属机构</th>
                                                    <th>用人方式</th>
                                                    <th>来院年月</th>
                                                    <th>当前状态</th>
                                                    <th>数据完整度</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>T001</td>
                                                    <td>张教授</td>
                                                    <td>计算机学院</td>
                                                    <td>正式编制</td>
                                                    <td>2018-09</td>
                                                    <td><span class="badge badge-success">在职</span></td>
                                                    <td>
                                                        <div class="progress-bar">
                                                            <div class="progress-fill" style="width: 95%;"></div>
                                                        </div>
                                                        95%
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="manageTeacherData('T001')">管理</button>
                                                        <button class="btn btn-warning" onclick="correctData('T001')">纠错</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>T002</td>
                                                    <td>李副教授</td>
                                                    <td>数学学院</td>
                                                    <td>正式编制</td>
                                                    <td>2020-03</td>
                                                    <td><span class="badge badge-success">在职</span></td>
                                                    <td>
                                                        <div class="progress-bar">
                                                            <div class="progress-fill" style="width: 88%;"></div>
                                                        </div>
                                                        88%
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="manageTeacherData('T002')">管理</button>
                                                        <button class="btn btn-warning" onclick="correctData('T002')">纠错</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据模型管理 -->
                    <div id="data-model-mgmt" class="content-panel">
                        <div class="alert alert-success">
                            <i class="fas fa-cogs"></i>
                            提供预置的教师数据模型，可根据学院实际情况，灵活管理模型，包括对数据分类、数据模型表、数据字段的增删改查等。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">12</div>
                                <div class="stat-label">数据模型分类</div>
                                <div class="stat-trend">
                                    <i class="fas fa-layer-group"></i>
                                    系统预置
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">45</div>
                                <div class="stat-label">数据模型表</div>
                                <div class="stat-trend">
                                    <i class="fas fa-table"></i>
                                    可配置
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">256</div>
                                <div class="stat-label">数据字段</div>
                                <div class="stat-trend">
                                    <i class="fas fa-columns"></i>
                                    灵活管理
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">8</div>
                                <div class="stat-label">自定义模型</div>
                                <div class="stat-trend">
                                    <i class="fas fa-plus"></i>
                                    用户创建
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-success" onclick="createDataModel()">
                                <i class="fas fa-plus"></i> 新增模型
                            </button>
                            <button class="btn btn-outline" onclick="importModel()">
                                <i class="fas fa-upload"></i> 导入模型
                            </button>
                            <button class="btn btn-outline" onclick="exportModel()">
                                <i class="fas fa-download"></i> 导出模型
                            </button>
                            <button class="btn btn-outline" onclick="modelMaintenance()">
                                <i class="fas fa-wrench"></i> 模型维护
                            </button>
                        </div>

                        <div class="data-table">
                            <div class="table-header">
                                <div class="table-title">教师数据模型列表</div>
                                <input type="text" class="search-box" placeholder="搜索模型名称...">
                            </div>
                            <div class="table-content">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>模型分类</th>
                                            <th>模型名称</th>
                                            <th>表名</th>
                                            <th>字段数量</th>
                                            <th>主键字段</th>
                                            <th>创建时间</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>基本信息</td>
                                            <td>教师基本信息表</td>
                                            <td>teacher_basic_info</td>
                                            <td>25</td>
                                            <td>teacher_id</td>
                                            <td>2024-01-01</td>
                                            <td><span class="badge badge-success">启用</span></td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewModelDetail('basic')">查看</button>
                                                <button class="btn btn-warning" onclick="editModel('basic')">编辑</button>
                                                <button class="btn btn-success" onclick="addField('basic')">新增字段</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>人事信息</td>
                                            <td>教师人事信息表</td>
                                            <td>teacher_personnel_info</td>
                                            <td>18</td>
                                            <td>teacher_id</td>
                                            <td>2024-01-01</td>
                                            <td><span class="badge badge-success">启用</span></td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewModelDetail('personnel')">查看</button>
                                                <button class="btn btn-warning" onclick="editModel('personnel')">编辑</button>
                                                <button class="btn btn-success" onclick="addField('personnel')">新增字段</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>教学信息</td>
                                            <td>教师教学信息表</td>
                                            <td>teacher_teaching_info</td>
                                            <td>32</td>
                                            <td>teacher_id</td>
                                            <td>2024-01-01</td>
                                            <td><span class="badge badge-success">启用</span></td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewModelDetail('teaching')">查看</button>
                                                <button class="btn btn-warning" onclick="editModel('teaching')">编辑</button>
                                                <button class="btn btn-success" onclick="addField('teaching')">新增字段</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>科研信息</td>
                                            <td>教师科研信息表</td>
                                            <td>teacher_research_info</td>
                                            <td>28</td>
                                            <td>teacher_id</td>
                                            <td>2024-01-01</td>
                                            <td><span class="badge badge-success">启用</span></td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewModelDetail('research')">查看</button>
                                                <button class="btn btn-warning" onclick="editModel('research')">编辑</button>
                                                <button class="btn btn-success" onclick="addField('research')">新增字段</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 模型权限管理 -->
                    <div id="model-permission" class="content-panel">
                        <div class="alert alert-warning">
                            <i class="fas fa-shield-alt"></i>
                            支持对数据模型进行授权，灵活配置不同用户组对数据内容的增删查改权限，以及数据纠错的方式，实现分级分权管理数据。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">6</div>
                                <div class="stat-label">用户组数量</div>
                                <div class="stat-trend">
                                    <i class="fas fa-users"></i>
                                    权限管理
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">45</div>
                                <div class="stat-label">权限配置项</div>
                                <div class="stat-trend">
                                    <i class="fas fa-key"></i>
                                    精细控制
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">4</div>
                                <div class="stat-label">纠错机制</div>
                                <div class="stat-trend">
                                    <i class="fas fa-edit"></i>
                                    多种方式
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">12</div>
                                <div class="stat-label">审核流程</div>
                                <div class="stat-trend">
                                    <i class="fas fa-check-circle"></i>
                                    流程管理
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-success" onclick="createPermissionGroup()">
                                <i class="fas fa-plus"></i> 新增权限组
                            </button>
                            <button class="btn btn-outline" onclick="copyPermission()">
                                <i class="fas fa-copy"></i> 复制权限
                            </button>
                            <button class="btn btn-outline" onclick="batchPermissionSet()">
                                <i class="fas fa-cogs"></i> 批量设置
                            </button>
                            <button class="btn btn-outline" onclick="auditFlowConfig()">
                                <i class="fas fa-project-diagram"></i> 审核流程配置
                            </button>
                        </div>

                        <div class="tabs">
                            <button class="tab active" onclick="showTab('table-permission')">表权限配置</button>
                            <button class="tab" onclick="showTab('field-permission')">字段权限配置</button>
                            <button class="tab" onclick="showTab('audit-flow')">审核流程设置</button>
                        </div>

                        <div class="tab-content">
                            <div id="table-permission" class="tab-panel active">
                                <div class="data-table">
                                    <div class="table-header">
                                        <div class="table-title">数据模型表权限配置</div>
                                        <div class="filter-group">
                                            <span class="filter-label">用户角色:</span>
                                            <select class="form-control" style="width: 150px;">
                                                <option>管理员</option>
                                                <option>教师</option>
                                                <option>院系管理员</option>
                                                <option>系室管理员</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="table-content">
                                        <table>
                                            <thead>
                                                <tr>
                                                    <th>数据模型表</th>
                                                    <th>管理员</th>
                                                    <th>院系管理员</th>
                                                    <th>系室管理员</th>
                                                    <th>教师</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>教师基本信息表</td>
                                                    <td><span class="badge badge-success">可见</span></td>
                                                    <td><span class="badge badge-success">可见</span></td>
                                                    <td><span class="badge badge-warning">部分可见</span></td>
                                                    <td><span class="badge badge-danger">不可见</span></td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="configTablePermission('basic')">配置</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>教师人事信息表</td>
                                                    <td><span class="badge badge-success">可见</span></td>
                                                    <td><span class="badge badge-warning">部分可见</span></td>
                                                    <td><span class="badge badge-danger">不可见</span></td>
                                                    <td><span class="badge badge-danger">不可见</span></td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="configTablePermission('personnel')">配置</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>教师教学信息表</td>
                                                    <td><span class="badge badge-success">可见</span></td>
                                                    <td><span class="badge badge-success">可见</span></td>
                                                    <td><span class="badge badge-success">可见</span></td>
                                                    <td><span class="badge badge-warning">部分可见</span></td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="configTablePermission('teaching')">配置</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>教师科研信息表</td>
                                                    <td><span class="badge badge-success">可见</span></td>
                                                    <td><span class="badge badge-success">可见</span></td>
                                                    <td><span class="badge badge-success">可见</span></td>
                                                    <td><span class="badge badge-success">可见</span></td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="configTablePermission('research')">配置</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div id="field-permission" class="tab-panel">
                                <div class="data-table">
                                    <div class="table-header">
                                        <div class="table-title">字段权限配置</div>
                                        <div class="filter-group">
                                            <span class="filter-label">数据表:</span>
                                            <select class="form-control" style="width: 200px;">
                                                <option>教师基本信息表</option>
                                                <option>教师人事信息表</option>
                                                <option>教师教学信息表</option>
                                                <option>教师科研信息表</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="table-content">
                                        <table>
                                            <thead>
                                                <tr>
                                                    <th>字段名称</th>
                                                    <th>字段类型</th>
                                                    <th>纠错机制</th>
                                                    <th>管理员权限</th>
                                                    <th>教师权限</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>工号</td>
                                                    <td>varchar(20)</td>
                                                    <td><span class="badge badge-info">源头纠错</span></td>
                                                    <td><span class="badge badge-success">读写</span></td>
                                                    <td><span class="badge badge-warning">只读</span></td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="configFieldPermission('teacher_id')">配置</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>姓名</td>
                                                    <td>varchar(50)</td>
                                                    <td><span class="badge badge-success">直接纠错</span></td>
                                                    <td><span class="badge badge-success">读写</span></td>
                                                    <td><span class="badge badge-success">读写</span></td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="configFieldPermission('name')">配置</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>身份证号</td>
                                                    <td>varchar(18)</td>
                                                    <td><span class="badge badge-warning">审核纠错</span></td>
                                                    <td><span class="badge badge-success">读写</span></td>
                                                    <td><span class="badge badge-danger">隐藏</span></td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="configFieldPermission('id_card')">配置</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>联系电话</td>
                                                    <td>varchar(20)</td>
                                                    <td><span class="badge badge-success">直接纠错</span></td>
                                                    <td><span class="badge badge-success">读写</span></td>
                                                    <td><span class="badge badge-success">读写</span></td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="configFieldPermission('phone')">配置</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 用户授权管理 -->
                    <div id="user-auth-mgmt" class="content-panel">
                        <div class="alert alert-info">
                            <i class="fas fa-users-cog"></i>
                            支持针对不同类型人员可查询人员类型范围内的数据项。普通教职工能够查询个人数据，管理人员根据管理范围，可查询管理范围内的数据。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">6</div>
                                <div class="stat-label">用户组数量</div>
                                <div class="stat-trend">
                                    <i class="fas fa-users"></i>
                                    分级管理
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">186</div>
                                <div class="stat-label">用户总数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-user"></i>
                                    权限分配
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">15</div>
                                <div class="stat-label">功能权限项</div>
                                <div class="stat-trend">
                                    <i class="fas fa-key"></i>
                                    差异化授权
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">98.5%</div>
                                <div class="stat-label">权限配置完整率</div>
                                <div class="stat-trend">
                                    <i class="fas fa-check"></i>
                                    配置完善
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-success" onclick="createUserGroup()">
                                <i class="fas fa-plus"></i> 创建用户组
                            </button>
                            <button class="btn btn-outline" onclick="batchAddUsers()">
                                <i class="fas fa-user-plus"></i> 批量添加用户
                            </button>
                            <button class="btn btn-outline" onclick="exportUserAuth()">
                                <i class="fas fa-download"></i> 导出权限配置
                            </button>
                        </div>

                        <div class="data-table">
                            <div class="table-header">
                                <div class="table-title">用户组权限管理</div>
                                <input type="text" class="search-box" placeholder="搜索用户组...">
                            </div>
                            <div class="table-content">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>用户组名称</th>
                                            <th>用户组描述</th>
                                            <th>成员数量</th>
                                            <th>功能权限</th>
                                            <th>数据范围</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>系统管理员</td>
                                            <td>拥有系统所有权限的管理员</td>
                                            <td>3</td>
                                            <td>
                                                <span class="badge badge-success">全部权限</span>
                                            </td>
                                            <td>全校数据</td>
                                            <td>2024-01-01</td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewUserGroup('admin')">查看</button>
                                                <button class="btn btn-warning" onclick="editUserGroup('admin')">编辑</button>
                                                <button class="btn btn-success" onclick="manageMembers('admin')">成员管理</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>院系管理员</td>
                                            <td>管理本院系教师档案和权限</td>
                                            <td>8</td>
                                            <td>
                                                <span class="badge badge-warning">部分权限</span>
                                            </td>
                                            <td>本院系数据</td>
                                            <td>2024-01-01</td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewUserGroup('college')">查看</button>
                                                <button class="btn btn-warning" onclick="editUserGroup('college')">编辑</button>
                                                <button class="btn btn-success" onclick="manageMembers('college')">成员管理</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>系室管理员</td>
                                            <td>管理本系室教师基本信息</td>
                                            <td>15</td>
                                            <td>
                                                <span class="badge badge-info">基础权限</span>
                                            </td>
                                            <td>本系室数据</td>
                                            <td>2024-01-01</td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewUserGroup('department')">查看</button>
                                                <button class="btn btn-warning" onclick="editUserGroup('department')">编辑</button>
                                                <button class="btn btn-success" onclick="manageMembers('department')">成员管理</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>普通教师</td>
                                            <td>只能查看个人信息档案</td>
                                            <td>160</td>
                                            <td>
                                                <span class="badge badge-warning">查看权限</span>
                                            </td>
                                            <td>个人数据</td>
                                            <td>2024-01-01</td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewUserGroup('teacher')">查看</button>
                                                <button class="btn btn-warning" onclick="editUserGroup('teacher')">编辑</button>
                                                <button class="btn btn-success" onclick="manageMembers('teacher')">成员管理</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 教师个人数字画像 -->
                    <div id="teacher-portrait" class="content-panel">
                        <div class="alert alert-success">
                            <i class="fas fa-chart-line"></i>
                            支持查询教师列表，按工号、姓名或画像模型条件检索教师。通过柱状图等图表呈现教师个人成长过程中的业绩表现以及在院、系、室所处的水平。
                        </div>

                        <div class="filter-bar">
                            <div class="filter-group">
                                <span class="filter-label">搜索教师:</span>
                                <input type="text" class="form-control" placeholder="工号/姓名" style="width: 200px;">
                            </div>
                            <div class="filter-group">
                                <span class="filter-label">画像模型:</span>
                                <select class="form-control" style="width: 150px;">
                                    <option>全部模型</option>
                                    <option>教学能力模型</option>
                                    <option>科研能力模型</option>
                                    <option>综合素质模型</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <span class="filter-label">时间范围:</span>
                                <input type="date" class="form-control" style="width: 150px;">
                            </div>
                            <button class="btn btn-outline">
                                <i class="fas fa-search"></i> 查询
                            </button>
                            <button class="btn btn-success" onclick="saveAsGroup()">
                                <i class="fas fa-save"></i> 保存为群组
                            </button>
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">186</div>
                                <div class="stat-label">教师总数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-users"></i>
                                    可查询
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">12</div>
                                <div class="stat-label">画像维度</div>
                                <div class="stat-trend">
                                    <i class="fas fa-chart-pie"></i>
                                    多维分析
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">5</div>
                                <div class="stat-label">保存的群组</div>
                                <div class="stat-trend">
                                    <i class="fas fa-bookmark"></i>
                                    快速分析
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">3</div>
                                <div class="stat-label">查询模板</div>
                                <div class="stat-trend">
                                    <i class="fas fa-template"></i>
                                    常用模板
                                </div>
                            </div>
                        </div>

                        <div class="data-table">
                            <div class="table-header">
                                <div class="table-title">教师画像列表</div>
                                <div class="action-buttons">
                                    <button class="btn btn-success" onclick="createQueryTemplate()">
                                        <i class="fas fa-plus"></i> 创建查询模板
                                    </button>
                                </div>
                            </div>
                            <div class="table-content">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>工号</th>
                                            <th>姓名</th>
                                            <th>所属系室</th>
                                            <th>教学能力评分</th>
                                            <th>科研能力评分</th>
                                            <th>综合排名</th>
                                            <th>画像更新时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>T001</td>
                                            <td>张教授</td>
                                            <td>计算机系</td>
                                            <td>
                                                <div style="display: flex; align-items: center; gap: 10px;">
                                                    <div class="progress-bar" style="width: 80px;">
                                                        <div class="progress-fill" style="width: 92%;"></div>
                                                    </div>
                                                    92分
                                                </div>
                                            </td>
                                            <td>
                                                <div style="display: flex; align-items: center; gap: 10px;">
                                                    <div class="progress-bar" style="width: 80px;">
                                                        <div class="progress-fill" style="width: 88%;"></div>
                                                    </div>
                                                    88分
                                                </div>
                                            </td>
                                            <td><span class="badge badge-success">第3名</span></td>
                                            <td>2024-01-15</td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewTeacherPortrait('T001')">查看画像</button>
                                                <button class="btn btn-success" onclick="addToCompare('T001')">加入对比</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>T002</td>
                                            <td>李副教授</td>
                                            <td>数学系</td>
                                            <td>
                                                <div style="display: flex; align-items: center; gap: 10px;">
                                                    <div class="progress-bar" style="width: 80px;">
                                                        <div class="progress-fill" style="width: 85%;"></div>
                                                    </div>
                                                    85分
                                                </div>
                                            </td>
                                            <td>
                                                <div style="display: flex; align-items: center; gap: 10px;">
                                                    <div class="progress-bar" style="width: 80px;">
                                                        <div class="progress-fill" style="width: 90%;"></div>
                                                    </div>
                                                    90分
                                                </div>
                                            </td>
                                            <td><span class="badge badge-success">第5名</span></td>
                                            <td>2024-01-15</td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewTeacherPortrait('T002')">查看画像</button>
                                                <button class="btn btn-success" onclick="addToCompare('T002')">加入对比</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 画像群工具 -->
                    <div id="group-tools" class="content-panel">
                        <div class="alert alert-info">
                            <i class="fas fa-users"></i>
                            支持对群组列表的增删查改。群组支持四种圈群方式：全部教师、条件筛选、直接选择人员、导入人员。支持根据圈群结果查看群组成员，灵活分析群体特征。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">8</div>
                                <div class="stat-label">已创建群组</div>
                                <div class="stat-trend">
                                    <i class="fas fa-layer-group"></i>
                                    群组管理
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">4</div>
                                <div class="stat-label">圈群方式</div>
                                <div class="stat-trend">
                                    <i class="fas fa-filter"></i>
                                    灵活配置
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">156</div>
                                <div class="stat-label">群组成员总数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-users"></i>
                                    分析对象
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">15</div>
                                <div class="stat-label">分析维度</div>
                                <div class="stat-trend">
                                    <i class="fas fa-chart-bar"></i>
                                    多角度分析
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-success" onclick="createGroup()">
                                <i class="fas fa-plus"></i> 创建群组
                            </button>
                            <button class="btn btn-outline" onclick="importGroupMembers()">
                                <i class="fas fa-upload"></i> 导入人员
                            </button>
                            <button class="btn btn-outline" onclick="exportGroupAnalysis()">
                                <i class="fas fa-download"></i> 导出分析
                            </button>
                        </div>

                        <div class="data-table">
                            <div class="table-header">
                                <div class="table-title">教师群组列表</div>
                                <input type="text" class="search-box" placeholder="搜索群组名称...">
                            </div>
                            <div class="table-content">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>群组名称</th>
                                            <th>圈群方式</th>
                                            <th>成员数量</th>
                                            <th>创建时间</th>
                                            <th>最后分析时间</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>高级职称教师群组</td>
                                            <td><span class="badge badge-info">条件筛选</span></td>
                                            <td>45</td>
                                            <td>2024-01-10</td>
                                            <td>2024-01-15</td>
                                            <td><span class="badge badge-success">活跃</span></td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewGroupMembers('senior')">查看成员</button>
                                                <button class="btn btn-success" onclick="analyzeGroup('senior')">群体分析</button>
                                                <button class="btn btn-warning" onclick="editGroup('senior')">编辑</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>青年教师群组</td>
                                            <td><span class="badge badge-info">条件筛选</span></td>
                                            <td>68</td>
                                            <td>2024-01-08</td>
                                            <td>2024-01-14</td>
                                            <td><span class="badge badge-success">活跃</span></td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewGroupMembers('young')">查看成员</button>
                                                <button class="btn btn-success" onclick="analyzeGroup('young')">群体分析</button>
                                                <button class="btn btn-warning" onclick="editGroup('young')">编辑</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>科研骨干群组</td>
                                            <td><span class="badge badge-warning">直接选择</span></td>
                                            <td>23</td>
                                            <td>2024-01-05</td>
                                            <td>2024-01-12</td>
                                            <td><span class="badge badge-success">活跃</span></td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewGroupMembers('research')">查看成员</button>
                                                <button class="btn btn-success" onclick="analyzeGroup('research')">群体分析</button>
                                                <button class="btn btn-warning" onclick="editGroup('research')">编辑</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>新入职教师群组</td>
                                            <td><span class="badge badge-success">导入人员</span></td>
                                            <td>12</td>
                                            <td>2024-01-03</td>
                                            <td>2024-01-10</td>
                                            <td><span class="badge badge-warning">待分析</span></td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewGroupMembers('new')">查看成员</button>
                                                <button class="btn btn-success" onclick="analyzeGroup('new')">群体分析</button>
                                                <button class="btn btn-warning" onclick="editGroup('new')">编辑</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 画像对比分析 -->
                    <div id="portrait-compare" class="content-panel">
                        <div class="alert alert-warning">
                            <i class="fas fa-balance-scale"></i>
                            支持选择教师进行个体画像对比，选择群组进行群组画像对比，选择专业系进行画像对比。可对比师资结构、教育教学、科学研究等水平的差异。
                        </div>

                        <div class="tabs">
                            <button class="tab active" onclick="showTab('individual-compare')">个体对比</button>
                            <button class="tab" onclick="showTab('group-compare')">群组对比</button>
                            <button class="tab" onclick="showTab('department-compare')">专业系对比</button>
                        </div>

                        <div class="tab-content">
                            <div id="individual-compare" class="tab-panel active">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label class="form-label">选择教师A</label>
                                        <select class="form-control">
                                            <option>请选择教师</option>
                                            <option value="T001">T001 - 张教授</option>
                                            <option value="T002">T002 - 李副教授</option>
                                            <option value="T003">T003 - 王讲师</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">选择教师B</label>
                                        <select class="form-control">
                                            <option>请选择教师</option>
                                            <option value="T001">T001 - 张教授</option>
                                            <option value="T002">T002 - 李副教授</option>
                                            <option value="T003">T003 - 王讲师</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="action-buttons">
                                    <button class="btn btn-success" onclick="startIndividualCompare()">
                                        <i class="fas fa-chart-bar"></i> 开始对比分析
                                    </button>
                                    <button class="btn btn-outline" onclick="exportCompareResult()">
                                        <i class="fas fa-download"></i> 导出对比结果
                                    </button>
                                </div>

                                <div class="chart-container">
                                    <div class="chart-title">教师个体画像对比分析</div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                        <div>
                                            <h4>张教授 (T001)</h4>
                                            <div class="form-group">
                                                <label>发表论文数</label>
                                                <div style="display: flex; align-items: center; gap: 10px;">
                                                    <div class="progress-bar" style="width: 200px;">
                                                        <div class="progress-fill" style="width: 85%;"></div>
                                                    </div>
                                                    <span>25篇</span>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label>指导学生数</label>
                                                <div style="display: flex; align-items: center; gap: 10px;">
                                                    <div class="progress-bar" style="width: 200px;">
                                                        <div class="progress-fill" style="width: 90%;"></div>
                                                    </div>
                                                    <span>18人</span>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label>科研项目数</label>
                                                <div style="display: flex; align-items: center; gap: 10px;">
                                                    <div class="progress-bar" style="width: 200px;">
                                                        <div class="progress-fill" style="width: 75%;"></div>
                                                    </div>
                                                    <span>8项</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <h4>李副教授 (T002)</h4>
                                            <div class="form-group">
                                                <label>发表论文数</label>
                                                <div style="display: flex; align-items: center; gap: 10px;">
                                                    <div class="progress-bar" style="width: 200px;">
                                                        <div class="progress-fill" style="width: 70%;"></div>
                                                    </div>
                                                    <span>18篇</span>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label>指导学生数</label>
                                                <div style="display: flex; align-items: center; gap: 10px;">
                                                    <div class="progress-bar" style="width: 200px;">
                                                        <div class="progress-fill" style="width: 80%;"></div>
                                                    </div>
                                                    <span>15人</span>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label>科研项目数</label>
                                                <div style="display: flex; align-items: center; gap: 10px;">
                                                    <div class="progress-bar" style="width: 200px;">
                                                        <div class="progress-fill" style="width: 95%;"></div>
                                                    </div>
                                                    <span>12项</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="group-compare" class="tab-panel">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label class="form-label">选择群组A</label>
                                        <select class="form-control">
                                            <option>请选择群组</option>
                                            <option>高级职称教师群组</option>
                                            <option>青年教师群组</option>
                                            <option>科研骨干群组</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">选择群组B</label>
                                        <select class="form-control">
                                            <option>请选择群组</option>
                                            <option>高级职称教师群组</option>
                                            <option>青年教师群组</option>
                                            <option>科研骨干群组</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="action-buttons">
                                    <button class="btn btn-success" onclick="startGroupCompare()">
                                        <i class="fas fa-chart-bar"></i> 开始群组对比
                                    </button>
                                </div>

                                <div class="chart-container">
                                    <div class="chart-title">群组画像对比分析</div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                        <div>
                                            <h4>高级职称教师群组 (45人)</h4>
                                            <div class="form-group">
                                                <label>平均科研项目经费</label>
                                                <div style="display: flex; align-items: center; gap: 10px;">
                                                    <div class="progress-bar" style="width: 200px;">
                                                        <div class="progress-fill" style="width: 90%;"></div>
                                                    </div>
                                                    <span>¥85万</span>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label>发表科研论文总数</label>
                                                <div style="display: flex; align-items: center; gap: 10px;">
                                                    <div class="progress-bar" style="width: 200px;">
                                                        <div class="progress-fill" style="width: 85%;"></div>
                                                    </div>
                                                    <span>856篇</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <h4>青年教师群组 (68人)</h4>
                                            <div class="form-group">
                                                <label>平均科研项目经费</label>
                                                <div style="display: flex; align-items: center; gap: 10px;">
                                                    <div class="progress-bar" style="width: 200px;">
                                                        <div class="progress-fill" style="width: 45%;"></div>
                                                    </div>
                                                    <span>¥32万</span>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label>发表科研论文总数</label>
                                                <div style="display: flex; align-items: center; gap: 10px;">
                                                    <div class="progress-bar" style="width: 200px;">
                                                        <div class="progress-fill" style="width: 60%;"></div>
                                                    </div>
                                                    <span>425篇</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 学生成长数智评价 -->
                    <div id="growth-evaluation" class="content-panel">
                        <div class="alert alert-success">
                            <i class="fas fa-chart-area"></i>
                            支持展示学生近两年的学习情况、成长经历和综合素质等数据信息。通过多维度数据分析详细展示学生的学习能力、实践能力和创新能力等方面的提升情况。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">3,256</div>
                                <div class="stat-label">评价学生总数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-users"></i>
                                    全覆盖评价
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">15</div>
                                <div class="stat-label">评价维度</div>
                                <div class="stat-trend">
                                    <i class="fas fa-chart-pie"></i>
                                    多维分析
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">85.6</div>
                                <div class="stat-label">平均综合评分</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上年 +2.3
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">234</div>
                                <div class="stat-label">优秀学生数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-star"></i>
                                    评分≥90分
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-success" onclick="generateEvaluationReport()">
                                <i class="fas fa-file-alt"></i> 生成评价报告
                            </button>
                            <button class="btn btn-outline" onclick="exportEvaluationData()">
                                <i class="fas fa-download"></i> 导出评价数据
                            </button>
                            <button class="btn btn-outline" onclick="evaluationSettings()">
                                <i class="fas fa-cog"></i> 评价设置
                            </button>
                        </div>

                        <div class="tabs">
                            <button class="tab active" onclick="showTab('learning-situation')">学习情况</button>
                            <button class="tab" onclick="showTab('growth-experience')">成长经历</button>
                            <button class="tab" onclick="showTab('comprehensive-quality')">综合素质</button>
                            <button class="tab" onclick="showTab('ability-analysis')">能力分析</button>
                        </div>

                        <div class="tab-content">
                            <div id="learning-situation" class="tab-panel active">
                                <div class="data-table">
                                    <div class="table-header">
                                        <div class="table-title">学生学习情况评价</div>
                                        <input type="text" class="search-box" placeholder="搜索学生...">
                                    </div>
                                    <div class="table-content">
                                        <table>
                                            <thead>
                                                <tr>
                                                    <th>学号</th>
                                                    <th>姓名</th>
                                                    <th>专业班级</th>
                                                    <th>课程选修情况</th>
                                                    <th>学习成绩</th>
                                                    <th>学分获取</th>
                                                    <th>学科竞赛</th>
                                                    <th>实习实践</th>
                                                    <th>综合评分</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>S2024001</td>
                                                    <td>张同学</td>
                                                    <td>计算机1班</td>
                                                    <td>
                                                        <div class="progress-bar">
                                                            <div class="progress-fill" style="width: 92%;"></div>
                                                        </div>
                                                        92%
                                                    </td>
                                                    <td>
                                                        <div class="progress-bar">
                                                            <div class="progress-fill" style="width: 88%;"></div>
                                                        </div>
                                                        88分
                                                    </td>
                                                    <td>
                                                        <div class="progress-bar">
                                                            <div class="progress-fill" style="width: 95%;"></div>
                                                        </div>
                                                        95%
                                                    </td>
                                                    <td><span class="badge badge-success">3项</span></td>
                                                    <td><span class="badge badge-success">2次</span></td>
                                                    <td><span class="badge badge-success">91.5分</span></td>
                                                </tr>
                                                <tr>
                                                    <td>S2024002</td>
                                                    <td>李同学</td>
                                                    <td>数学1班</td>
                                                    <td>
                                                        <div class="progress-bar">
                                                            <div class="progress-fill" style="width: 85%;"></div>
                                                        </div>
                                                        85%
                                                    </td>
                                                    <td>
                                                        <div class="progress-bar">
                                                            <div class="progress-fill" style="width: 90%;"></div>
                                                        </div>
                                                        90分
                                                    </td>
                                                    <td>
                                                        <div class="progress-bar">
                                                            <div class="progress-fill" style="width: 88%;"></div>
                                                        </div>
                                                        88%
                                                    </td>
                                                    <td><span class="badge badge-warning">1项</span></td>
                                                    <td><span class="badge badge-success">3次</span></td>
                                                    <td><span class="badge badge-success">87.8分</span></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 学生个人数字画像 -->
                    <div id="student-portrait" class="content-panel">
                        <div class="alert alert-info">
                            <i class="fas fa-user-chart"></i>
                            支持基于学生的个人评价结果生成学生的数字画像，将学生个人评价结果以各指标项为维度进行展示，支持学生数据横向对比和历年评价结果纵向对比。
                        </div>

                        <div class="filter-bar">
                            <div class="filter-group">
                                <span class="filter-label">选择学生:</span>
                                <input type="text" class="form-control" placeholder="学号/姓名" style="width: 200px;">
                            </div>
                            <div class="filter-group">
                                <span class="filter-label">学年:</span>
                                <select class="form-control" style="width: 120px;">
                                    <option>2023-2024</option>
                                    <option>2022-2023</option>
                                    <option>2021-2022</option>
                                </select>
                            </div>
                            <button class="btn btn-outline">
                                <i class="fas fa-search"></i> 查询画像
                            </button>
                        </div>

                        <div class="chart-container">
                            <div class="chart-title">学生个人数字画像 - 张同学 (S2024001)</div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                                <div>
                                    <h4>各指标项得分情况</h4>
                                    <div class="form-group">
                                        <label>学习能力</label>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <div class="progress-bar" style="width: 200px;">
                                                <div class="progress-fill" style="width: 91%;"></div>
                                            </div>
                                            <span>91分</span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label>实践能力</label>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <div class="progress-bar" style="width: 200px;">
                                                <div class="progress-fill" style="width: 85%;"></div>
                                            </div>
                                            <span>85分</span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label>创新能力</label>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <div class="progress-bar" style="width: 200px;">
                                                <div class="progress-fill" style="width: 88%;"></div>
                                            </div>
                                            <span>88分</span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label>综合素质</label>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <div class="progress-bar" style="width: 200px;">
                                                <div class="progress-fill" style="width: 92%;"></div>
                                            </div>
                                            <span>92分</span>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <h4>与平均分对比分析</h4>
                                    <div class="form-group">
                                        <label>学习能力 (班级平均: 78分)</label>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <span class="badge badge-success">+13分</span>
                                            <span>超出平均水平</span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label>实践能力 (班级平均: 82分)</label>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <span class="badge badge-success">+3分</span>
                                            <span>略高于平均水平</span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label>创新能力 (班级平均: 75分)</label>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <span class="badge badge-success">+13分</span>
                                            <span>明显高于平均水平</span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label>综合素质 (班级平均: 80分)</label>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <span class="badge badge-success">+12分</span>
                                            <span>显著高于平均水平</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="chart-container">
                            <div class="chart-title">历年评价结果纵向对比</div>
                            <div class="alert alert-info">
                                <i class="fas fa-chart-line"></i>
                                该学生各项能力指标呈现稳步上升趋势，学习能力从2022年的85分提升至2024年的91分，实践能力和创新能力也有显著提升。
                            </div>
                        </div>
                    </div>

                    <!-- 训练活动异常 -->
                    <div id="training-analysis" class="content-panel">
                        <div class="alert alert-warning">
                            <i class="fas fa-running"></i>
                            支持通过教学训练活动和学生队日常训练活动以及学生自主主动的训练活动数据分析，判断学生训练活动情况是否存在异常，并将预警学生数据推送相关干部和教师。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">8</div>
                                <div class="stat-label">训练异常学生</div>
                                <div class="stat-trend">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    需要关注
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">156</div>
                                <div class="stat-label">训练频率低学生</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-down"></i>
                                    较上月 -12
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">23</div>
                                <div class="stat-label">活动不规律学生</div>
                                <div class="stat-trend">
                                    <i class="fas fa-clock"></i>
                                    时间异常
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">5</div>
                                <div class="stat-label">已推送预警</div>
                                <div class="stat-trend">
                                    <i class="fas fa-bell"></i>
                                    今日推送
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-success" onclick="setTrainingRules()">
                                <i class="fas fa-cog"></i> 设置异常规则
                            </button>
                            <button class="btn btn-outline" onclick="sendWarningMessage()">
                                <i class="fas fa-paper-plane"></i> 发送预警消息
                            </button>
                            <button class="btn btn-outline" onclick="exportTrainingData()">
                                <i class="fas fa-download"></i> 导出数据
                            </button>
                        </div>

                        <div class="data-table">
                            <div class="table-header">
                                <div class="table-title">训练活动异常学生列表</div>
                                <div class="filter-group">
                                    <span class="filter-label">异常类型:</span>
                                    <select class="form-control" style="width: 150px;">
                                        <option>全部</option>
                                        <option>训练频率低</option>
                                        <option>活动不规律</option>
                                        <option>训练异常</option>
                                    </select>
                                </div>
                            </div>
                            <div class="table-content">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>学号</th>
                                            <th>姓名</th>
                                            <th>学院</th>
                                            <th>异常类型</th>
                                            <th>训练频率</th>
                                            <th>最后训练时间</th>
                                            <th>预警等级</th>
                                            <th>推送状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>S2023101</td>
                                            <td>王同学</td>
                                            <td>计算机学院</td>
                                            <td><span class="badge badge-danger">训练频率低</span></td>
                                            <td>2次/周</td>
                                            <td>2024-01-10</td>
                                            <td><span class="badge badge-danger">高</span></td>
                                            <td><span class="badge badge-success">已推送</span></td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewTrainingDetail('S2023101')">查看详情</button>
                                                <button class="btn btn-success" onclick="processTrainingWarning('S2023101')">处理</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>S2023102</td>
                                            <td>赵同学</td>
                                            <td>数学学院</td>
                                            <td><span class="badge badge-warning">活动不规律</span></td>
                                            <td>4次/周</td>
                                            <td>2024-01-14</td>
                                            <td><span class="badge badge-warning">中</span></td>
                                            <td><span class="badge badge-warning">待推送</span></td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewTrainingDetail('S2023102')">查看详情</button>
                                                <button class="btn btn-success" onclick="processTrainingWarning('S2023102')">处理</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 日常活动异常 -->
                    <div id="daily-analysis" class="content-panel">
                        <div class="alert alert-danger">
                            <i class="fas fa-calendar-day"></i>
                            支持通过记录学生的出入门禁、上网、作息等数据结合学生的心理测试或性格测试数据等在科学分析基础上建立学生异常活动预警分析模型，对可能发生的学生异常活动风险进行预警。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">15</div>
                                <div class="stat-label">今日预警学生</div>
                                <div class="stat-trend">
                                    <i class="fas fa-exclamation-circle"></i>
                                    需要关注
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">3</div>
                                <div class="stat-label">高风险预警</div>
                                <div class="stat-trend">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    紧急处理
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">8</div>
                                <div class="stat-label">中风险预警</div>
                                <div class="stat-trend">
                                    <i class="fas fa-exclamation"></i>
                                    重点关注
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">4</div>
                                <div class="stat-label">低风险预警</div>
                                <div class="stat-trend">
                                    <i class="fas fa-info-circle"></i>
                                    一般关注
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-success" onclick="setDailyRules()">
                                <i class="fas fa-cog"></i> 设置预警规则
                            </button>
                            <button class="btn btn-outline" onclick="whitelistManagement()">
                                <i class="fas fa-list"></i> 白名单管理
                            </button>
                            <button class="btn btn-outline" onclick="exportDailyData()">
                                <i class="fas fa-download"></i> 导出预警数据
                            </button>
                            <button class="btn btn-outline" onclick="warningStatisticsReport()">
                                <i class="fas fa-chart-bar"></i> 统计报告
                            </button>
                        </div>

                        <div class="tabs">
                            <button class="tab active" onclick="showTab('today-warning')">今日预警</button>
                            <button class="tab" onclick="showTab('pending-process')">待处理预警</button>
                            <button class="tab" onclick="showTab('history-warning')">历史预警</button>
                            <button class="tab" onclick="showTab('warning-statistics')">预警统计</button>
                        </div>

                        <div class="tab-content">
                            <div id="today-warning" class="tab-panel active">
                                <div class="data-table">
                                    <div class="table-header">
                                        <div class="table-title">今日预警记录管理</div>
                                        <div class="filter-group">
                                            <span class="filter-label">预警等级:</span>
                                            <select class="form-control" style="width: 120px;">
                                                <option>全部</option>
                                                <option>高风险</option>
                                                <option>中风险</option>
                                                <option>低风险</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="table-content">
                                        <table>
                                            <thead>
                                                <tr>
                                                    <th>学号</th>
                                                    <th>姓名</th>
                                                    <th>学院</th>
                                                    <th>异常原因</th>
                                                    <th>预警等级</th>
                                                    <th>预警时间</th>
                                                    <th>处理状态</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>S2023201</td>
                                                    <td>陈同学</td>
                                                    <td>计算机学院</td>
                                                    <td>
                                                        <span class="badge badge-danger">门禁异常</span>
                                                        <span class="badge badge-warning">作息异常</span>
                                                    </td>
                                                    <td><span class="badge badge-danger">高风险</span></td>
                                                    <td>2024-01-15 08:30</td>
                                                    <td><span class="badge badge-warning">处理中</span></td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="viewDailyDetail('S2023201')">查看详情</button>
                                                        <button class="btn btn-success" onclick="processDailyWarning('S2023201')">处理</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>S2023202</td>
                                                    <td>刘同学</td>
                                                    <td>数学学院</td>
                                                    <td>
                                                        <span class="badge badge-warning">上网异常</span>
                                                        <span class="badge badge-info">心理关注</span>
                                                    </td>
                                                    <td><span class="badge badge-warning">中风险</span></td>
                                                    <td>2024-01-15 09:15</td>
                                                    <td><span class="badge badge-info">待处理</span></td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="viewDailyDetail('S2023202')">查看详情</button>
                                                        <button class="btn btn-success" onclick="processDailyWarning('S2023202')">处理</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>S2023203</td>
                                                    <td>周同学</td>
                                                    <td>物理学院</td>
                                                    <td>
                                                        <span class="badge badge-info">学业情况</span>
                                                    </td>
                                                    <td><span class="badge badge-info">低风险</span></td>
                                                    <td>2024-01-15 10:20</td>
                                                    <td><span class="badge badge-info">待处理</span></td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="viewDailyDetail('S2023203')">查看详情</button>
                                                        <button class="btn btn-success" onclick="processDailyWarning('S2023203')">处理</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div id="warning-statistics" class="tab-panel">
                                <div class="chart-container">
                                    <div class="chart-title">预警等级人数占比</div>
                                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-bottom: 20px;">
                                        <div style="text-align: center; padding: 20px; background: #fee2e2; border-radius: 8px;">
                                            <div style="font-size: 24px; font-weight: bold; color: #dc2626;">3</div>
                                            <div style="color: #991b1b;">高风险预警</div>
                                            <div style="font-size: 12px; color: #7f1d1d;">占比 20%</div>
                                        </div>
                                        <div style="text-align: center; padding: 20px; background: #fef3c7; border-radius: 8px;">
                                            <div style="font-size: 24px; font-weight: bold; color: #d97706;">8</div>
                                            <div style="color: #92400e;">中风险预警</div>
                                            <div style="font-size: 12px; color: #78350f;">占比 53%</div>
                                        </div>
                                        <div style="text-align: center; padding: 20px; background: #dbeafe; border-radius: 8px;">
                                            <div style="font-size: 24px; font-weight: bold; color: #2563eb;">4</div>
                                            <div style="color: #1d4ed8;">低风险预警</div>
                                            <div style="font-size: 12px; color: #1e40af;">占比 27%</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="chart-container">
                                    <div class="chart-title">各学院预警情况统计</div>
                                    <div class="data-table">
                                        <div class="table-content">
                                            <table>
                                                <thead>
                                                    <tr>
                                                        <th>学院</th>
                                                        <th>学生总数</th>
                                                        <th>预警学生数</th>
                                                        <th>预警率</th>
                                                        <th>处理完成数</th>
                                                        <th>处理率</th>
                                                        <th>趋势</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>计算机学院</td>
                                                        <td>856</td>
                                                        <td>8</td>
                                                        <td>0.93%</td>
                                                        <td>5</td>
                                                        <td>62.5%</td>
                                                        <td><span class="badge badge-success">↓</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td>数学学院</td>
                                                        <td>623</td>
                                                        <td>4</td>
                                                        <td>0.64%</td>
                                                        <td>3</td>
                                                        <td>75%</td>
                                                        <td><span class="badge badge-success">↓</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td>物理学院</td>
                                                        <td>445</td>
                                                        <td>2</td>
                                                        <td>0.45%</td>
                                                        <td>2</td>
                                                        <td>100%</td>
                                                        <td><span class="badge badge-success">→</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td>化学学院</td>
                                                        <td>378</td>
                                                        <td>1</td>
                                                        <td>0.26%</td>
                                                        <td>1</td>
                                                        <td>100%</td>
                                                        <td><span class="badge badge-success">↓</span></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 消费活动异常 -->
                    <div id="consumption-analysis" class="content-panel">
                        <div class="alert alert-info">
                            <i class="fas fa-credit-card"></i>
                            通过深度剖析学生在校一卡通消费数据，提供精准资助辅助分析。辅助探索科学合理的隐形资助落地方案，为确保资助的精准度提供数据依据。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">45</div>
                                <div class="stat-label">消费过低学生</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-down"></i>
                                    较上月 -8
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">23</div>
                                <div class="stat-label">消费过高学生</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上月 +3
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">156</div>
                                <div class="stat-label">拟资助学生</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-down"></i>
                                    较上月 -12
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">¥28,500</div>
                                <div class="stat-label">本月补贴金额</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-down"></i>
                                    较上月 -¥3,200
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-success" onclick="createAssistanceScheme()">
                                <i class="fas fa-plus"></i> 创建资助方案
                            </button>
                            <button class="btn btn-outline" onclick="setConsumptionThreshold()">
                                <i class="fas fa-cog"></i> 设置消费阈值
                            </button>
                            <button class="btn btn-outline" onclick="exportConsumptionReport()">
                                <i class="fas fa-download"></i> 导出报告
                            </button>
                        </div>

                        <div class="tabs">
                            <button class="tab active" onclick="showTab('low-consumption')">消费过低</button>
                            <button class="tab" onclick="showTab('high-consumption')">消费过高</button>
                            <button class="tab" onclick="showTab('assistance-analysis')">资助分析</button>
                        </div>

                        <div class="tab-content">
                            <div id="low-consumption" class="tab-panel active">
                                <div class="data-table">
                                    <div class="table-header">
                                        <div class="table-title">消费过低学生列表</div>
                                        <input type="text" class="search-box" placeholder="搜索学生信息...">
                                    </div>
                                    <div class="table-content">
                                        <table>
                                            <thead>
                                                <tr>
                                                    <th>学号</th>
                                                    <th>姓名</th>
                                                    <th>困难生类型</th>
                                                    <th>院系</th>
                                                    <th>专业班级</th>
                                                    <th>月消费额</th>
                                                    <th>消费次数</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>S2023101</td>
                                                    <td>陈同学</td>
                                                    <td><span class="badge badge-danger">特困生</span></td>
                                                    <td>计算机学院</td>
                                                    <td>计算机1班</td>
                                                    <td>¥285</td>
                                                    <td>42次</td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="viewConsumptionDetail('S2023101')">查看详情</button>
                                                        <button class="btn btn-success" onclick="addToAssistance('S2023101')">加入资助</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>S2023102</td>
                                                    <td>刘同学</td>
                                                    <td><span class="badge badge-warning">一般困难</span></td>
                                                    <td>数学学院</td>
                                                    <td>数学1班</td>
                                                    <td>¥320</td>
                                                    <td>38次</td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="viewConsumptionDetail('S2023102')">查看详情</button>
                                                        <button class="btn btn-success" onclick="addToAssistance('S2023102')">加入资助</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="detailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">详细信息</h3>
                <span class="close" onclick="closeModal('detailModal')">&times;</span>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 模态框内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSystem = 'teacher';
        let currentContent = 'teacher-profile';

        // 系统切换
        function switchSystem(system) {
            // 更新导航菜单状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            // 隐藏所有菜单系统
            document.querySelectorAll('.menu-system').forEach(menu => {
                menu.style.display = 'none';
            });

            // 显示对应的菜单系统
            currentSystem = system;
            if (system === 'teacher') {
                document.getElementById('teacherMenu').style.display = 'block';
                showContent('teacher-profile');
            } else if (system === 'student') {
                document.getElementById('studentMenu').style.display = 'block';
                showContent('student-profile');
            } else if (system === 'analysis') {
                document.getElementById('analysisMenu').style.display = 'block';
                showContent('academic-warning');
            }
        }

        // 内容切换
        function showContent(contentId) {
            // 隐藏所有内容面板
            document.querySelectorAll('.content-panel').forEach(panel => {
                panel.classList.remove('active');
            });

            // 移除所有菜单项的active状态
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });

            // 显示选中的内容面板
            const targetPanel = document.getElementById(contentId);
            if (targetPanel) {
                targetPanel.classList.add('active');
            }

            // 激活对应的菜单项
            event.target.classList.add('active');

            // 更新页面标题和图标
            updateContentHeader(contentId);
            currentContent = contentId;
        }

        // 更新内容头部
        function updateContentHeader(contentId) {
            const titleMap = {
                'teacher-profile': { icon: 'fas fa-user', title: '教师个人信息档案' },
                'teacher-data-mgmt': { icon: 'fas fa-database', title: '教师数据管理' },
                'data-model-mgmt': { icon: 'fas fa-cogs', title: '数据模型管理' },
                'model-permission': { icon: 'fas fa-shield-alt', title: '模型权限管理' },
                'user-auth-mgmt': { icon: 'fas fa-users-cog', title: '用户授权管理' },
                'teacher-portrait': { icon: 'fas fa-chart-line', title: '教师个人数字画像' },
                'group-tools': { icon: 'fas fa-users', title: '画像群工具' },
                'portrait-compare': { icon: 'fas fa-balance-scale', title: '画像对比分析' },
                'student-profile': { icon: 'fas fa-user-graduate', title: '学生个人信息档案' },
                'student-data-mgmt': { icon: 'fas fa-database', title: '学生数据管理' },
                'growth-evaluation': { icon: 'fas fa-chart-area', title: '学生成长数智评价' },
                'student-portrait': { icon: 'fas fa-user-chart', title: '学生个人数字画像' },
                'academic-warning': { icon: 'fas fa-exclamation-triangle', title: '学业成绩异常预警' },
                'consumption-analysis': { icon: 'fas fa-credit-card', title: '学生消费活动异常' },
                'training-analysis': { icon: 'fas fa-running', title: '学生训练活动异常' },
                'daily-analysis': { icon: 'fas fa-calendar-day', title: '学生日常活动异常' }
            };

            const info = titleMap[contentId];
            if (info) {
                document.getElementById('contentIcon').className = info.icon;
                document.getElementById('contentTitle').textContent = info.title;
            }
        }

        // 标签切换
        function showTab(tabId) {
            // 移除所有标签的active状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 隐藏所有标签面板
            document.querySelectorAll('.tab-panel').forEach(panel => {
                panel.classList.remove('active');
            });

            // 激活当前标签
            event.target.classList.add('active');

            // 显示对应的标签面板
            const targetPanel = document.getElementById(tabId);
            if (targetPanel) {
                targetPanel.classList.add('active');
            }
        }

        // 功能函数
        function exportData() {
            alert('导出数据功能');
        }

        function importData() {
            alert('导入数据功能');
        }

        function addRecord() {
            alert('新增记录功能');
        }

        function customizeColumns() {
            alert('自定义列显示功能');
        }

        function viewDetail(id) {
            document.getElementById('modalTitle').textContent = '教师详细信息';
            document.getElementById('modalBody').innerHTML = `
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">工号</label>
                        <div class="form-control">${id}</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">姓名</label>
                        <div class="form-control">张教授</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">职称</label>
                        <div class="form-control">教授</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">学历</label>
                        <div class="form-control">博士</div>
                    </div>
                </div>
            `;
            document.getElementById('detailModal').style.display = 'block';
        }

        function editRecord(id) {
            alert(`编辑记录: ${id}`);
        }

        function viewStudentDetail(id) {
            document.getElementById('modalTitle').textContent = '学生详细信息';
            document.getElementById('modalBody').innerHTML = `
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">学号</label>
                        <div class="form-control">${id}</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">姓名</label>
                        <div class="form-control">张同学</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">专业</label>
                        <div class="form-control">计算机科学与技术</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">班级</label>
                        <div class="form-control">计算机1班</div>
                    </div>
                </div>
            `;
            document.getElementById('detailModal').style.display = 'block';
        }

        function setWarningConditions() {
            alert('设置预警条件功能');
        }

        function processWarning(id) {
            alert(`处理预警学生: ${id}`);
        }

        function createAssistanceScheme() {
            alert('创建隐形资助方案功能');
        }

        function viewConsumptionDetail(id) {
            alert(`查看消费详情: ${id}`);
        }

        function addToAssistance(id) {
            alert(`加入资助名单: ${id}`);
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 教师数据管理相关函数
        function batchImport() {
            alert('批量导入教师数据功能');
        }

        function dataCorrection() {
            alert('数据纠错功能');
        }

        function dataSync() {
            alert('数据同步功能');
        }

        function exportTeacherData() {
            alert('导出教师数据功能');
        }

        function manageTeacherData(id) {
            alert(`管理教师数据: ${id}`);
        }

        function correctData(id) {
            alert(`纠错教师数据: ${id}`);
        }

        // 数据模型管理相关函数
        function createDataModel() {
            alert('创建数据模型功能');
        }

        function importModel() {
            alert('导入数据模型功能');
        }

        function exportModel() {
            alert('导出数据模型功能');
        }

        function modelMaintenance() {
            alert('模型维护功能');
        }

        function viewModelDetail(modelId) {
            alert(`查看模型详情: ${modelId}`);
        }

        function editModel(modelId) {
            alert(`编辑模型: ${modelId}`);
        }

        function addField(modelId) {
            alert(`为模型 ${modelId} 新增字段`);
        }

        // 权限管理相关函数
        function createPermissionGroup() {
            alert('创建权限组功能');
        }

        function copyPermission() {
            alert('复制权限功能');
        }

        function batchPermissionSet() {
            alert('批量权限设置功能');
        }

        function auditFlowConfig() {
            alert('审核流程配置功能');
        }

        function configTablePermission(tableId) {
            alert(`配置表权限: ${tableId}`);
        }

        function configFieldPermission(fieldId) {
            alert(`配置字段权限: ${fieldId}`);
        }

        // 用户授权管理相关函数
        function createUserGroup() {
            alert('创建用户组功能');
        }

        function batchAddUsers() {
            alert('批量添加用户功能');
        }

        function exportUserAuth() {
            alert('导出权限配置功能');
        }

        function viewUserGroup(groupId) {
            alert(`查看用户组: ${groupId}`);
        }

        function editUserGroup(groupId) {
            alert(`编辑用户组: ${groupId}`);
        }

        function manageMembers(groupId) {
            alert(`管理用户组成员: ${groupId}`);
        }

        // 教师画像相关函数
        function saveAsGroup() {
            alert('保存为群组功能');
        }

        function createQueryTemplate() {
            alert('创建查询模板功能');
        }

        function viewTeacherPortrait(teacherId) {
            document.getElementById('modalTitle').textContent = '教师数字画像';
            document.getElementById('modalBody').innerHTML = `
                <div class="chart-container">
                    <h4>教师画像 - ${teacherId}</h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label>教学能力评分</label>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div class="progress-bar" style="width: 200px;">
                                    <div class="progress-fill" style="width: 92%;"></div>
                                </div>
                                <span>92分</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>科研能力评分</label>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div class="progress-bar" style="width: 200px;">
                                    <div class="progress-fill" style="width: 88%;"></div>
                                </div>
                                <span>88分</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>综合排名</label>
                            <div>第3名 / 186人</div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('detailModal').style.display = 'block';
        }

        function addToCompare(teacherId) {
            alert(`将教师 ${teacherId} 加入对比列表`);
        }

        // 群组工具相关函数
        function createGroup() {
            alert('创建教师群组功能');
        }

        function importGroupMembers() {
            alert('导入群组成员功能');
        }

        function exportGroupAnalysis() {
            alert('导出群组分析功能');
        }

        function viewGroupMembers(groupId) {
            alert(`查看群组成员: ${groupId}`);
        }

        function analyzeGroup(groupId) {
            alert(`群体分析: ${groupId}`);
        }

        function editGroup(groupId) {
            alert(`编辑群组: ${groupId}`);
        }

        // 画像对比相关函数
        function startIndividualCompare() {
            alert('开始个体画像对比分析');
        }

        function exportCompareResult() {
            alert('导出对比结果功能');
        }

        function startGroupCompare() {
            alert('开始群组画像对比分析');
        }

        // 学生相关函数
        function customizeStudentColumns() {
            alert('自定义学生列显示功能');
        }

        function editStudentRecord(id) {
            alert(`编辑学生记录: ${id}`);
        }

        // 学生成长评价相关函数
        function generateEvaluationReport() {
            alert('生成学生成长评价报告功能');
        }

        function exportEvaluationData() {
            alert('导出评价数据功能');
        }

        function evaluationSettings() {
            alert('评价设置功能');
        }

        // 预警相关函数
        function exportWarningData() {
            alert('导出预警数据功能');
        }

        function warningStatistics() {
            alert('预警统计分析功能');
        }

        function viewWarningDetail(studentId) {
            document.getElementById('modalTitle').textContent = '学业预警详情';
            document.getElementById('modalBody').innerHTML = `
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">学号</label>
                        <div class="form-control">${studentId}</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">预警类型</label>
                        <div class="form-control">学分不足</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">预警等级</label>
                        <div class="form-control"><span class="badge badge-danger">高风险</span></div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">预警原因</label>
                        <div class="form-control">当前学分仅获得45学分，距离毕业要求还差35学分</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">建议措施</label>
                        <div class="form-control">建议加强学习，及时补修相关课程</div>
                    </div>
                </div>
            `;
            document.getElementById('detailModal').style.display = 'block';
        }

        // 消费异常相关函数
        function createAssistanceScheme() {
            alert('创建隐形资助方案功能');
        }

        function setConsumptionThreshold() {
            alert('设置消费阈值功能');
        }

        function exportConsumptionReport() {
            alert('导出消费报告功能');
        }

        function viewConsumptionDetail(studentId) {
            document.getElementById('modalTitle').textContent = '学生消费详情';
            document.getElementById('modalBody').innerHTML = `
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">学号</label>
                        <div class="form-control">${studentId}</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">月消费总额</label>
                        <div class="form-control">¥285</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">消费次数</label>
                        <div class="form-control">42次</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">平均每餐消费</label>
                        <div class="form-control">¥6.8</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">消费趋势</label>
                        <div class="form-control">近6个月消费呈下降趋势</div>
                    </div>
                </div>
                <div class="chart-container">
                    <div class="chart-title">最近6个月消费趋势</div>
                    <div class="alert alert-info">
                        消费金额从9月的¥420逐步下降至当前的¥285，建议关注学生经济状况。
                    </div>
                </div>
            `;
            document.getElementById('detailModal').style.display = 'block';
        }

        // 训练异常相关函数
        function setTrainingRules() {
            alert('设置训练异常规则功能');
        }

        function sendWarningMessage() {
            alert('发送预警消息功能');
        }

        function exportTrainingData() {
            alert('导出训练数据功能');
        }

        function viewTrainingDetail(studentId) {
            document.getElementById('modalTitle').textContent = '训练活动详情';
            document.getElementById('modalBody').innerHTML = `
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">学号</label>
                        <div class="form-control">${studentId}</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">训练频率</label>
                        <div class="form-control">2次/周 (标准: 5次/周)</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">最后训练时间</label>
                        <div class="form-control">2024-01-10 16:30</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">训练时长</label>
                        <div class="form-control">平均45分钟/次</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">异常原因</label>
                        <div class="form-control">训练频率明显低于标准要求，可能影响身体素质</div>
                    </div>
                </div>
            `;
            document.getElementById('detailModal').style.display = 'block';
        }

        function processTrainingWarning(studentId) {
            alert(`处理训练预警: ${studentId}`);
        }

        // 日常活动异常相关函数
        function setDailyRules() {
            alert('设置日常活动异常规则功能');
        }

        function whitelistManagement() {
            alert('白名单管理功能');
        }

        function exportDailyData() {
            alert('导出日常活动数据功能');
        }

        function warningStatisticsReport() {
            alert('预警统计报告功能');
        }

        function viewDailyDetail(studentId) {
            document.getElementById('modalTitle').textContent = '日常活动异常详情';
            document.getElementById('modalBody').innerHTML = `
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">学号</label>
                        <div class="form-control">${studentId}</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">异常类型</label>
                        <div class="form-control">
                            <span class="badge badge-danger">门禁异常</span>
                            <span class="badge badge-warning">作息异常</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">门禁记录</label>
                        <div class="form-control">连续3天凌晨2点后刷卡进入宿舍</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">上网记录</label>
                        <div class="form-control">每日上网时长超过12小时</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">学业情况</label>
                        <div class="form-control">本学期已有2门课程不及格</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">心理状态</label>
                        <div class="form-control">心理测试显示轻度焦虑倾向</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">风险评估</label>
                        <div class="form-control"><span class="badge badge-danger">高风险</span> - 需要立即关注和干预</div>
                    </div>
                </div>
                <div class="alert alert-warning">
                    <strong>处理建议：</strong>
                    <ul style="margin: 10px 0 0 20px;">
                        <li>联系辅导员进行谈话了解情况</li>
                        <li>建议心理咨询师介入评估</li>
                        <li>关注学业辅导需求</li>
                        <li>加强宿舍管理和作息指导</li>
                    </ul>
                </div>
            `;
            document.getElementById('detailModal').style.display = 'block';
        }

        function processDailyWarning(studentId) {
            alert(`处理日常活动预警: ${studentId}`);
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('师资发展与学生成长数智建设系统已加载');

            // 点击模态框外部关闭
            window.addEventListener('click', function(e) {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    if (e.target === modal) {
                        modal.style.display = 'none';
                    }
                });
            });
        });
    </script>
</body>
</html>
