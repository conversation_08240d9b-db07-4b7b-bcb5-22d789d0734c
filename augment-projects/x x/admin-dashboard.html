<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>出租车管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .layout {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #34495e;
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-size: 18px;
            font-weight: bold;
        }
        
        .logo-icon {
            margin-right: 10px;
            font-size: 24px;
        }
        
        .nav-menu {
            padding: 20px 0;
        }
        
        .nav-item {
            display: block;
            padding: 12px 20px;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .nav-item:hover,
        .nav-item.active {
            background: #34495e;
            color: white;
            border-left-color: #3498db;
        }
        
        .nav-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #3498db;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--accent-color, #3498db);
        }
        
        .stat-card.orders::before { --accent-color: #3498db; }
        .stat-card.drivers::before { --accent-color: #2ecc71; }
        .stat-card.revenue::before { --accent-color: #f39c12; }
        .stat-card.vehicles::before { --accent-color: #e74c3c; }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .stat-title {
            font-size: 14px;
            color: #7f8c8d;
            font-weight: 500;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .stat-change {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .stat-change.positive {
            color: #2ecc71;
        }
        
        .stat-change.negative {
            color: #e74c3c;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
        }
        
        .chart-card,
        .activity-card {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .card-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .chart-placeholder {
            height: 300px;
            background: #f8f9fa;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
            font-size: 16px;
        }
        
        .activity-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 14px;
            color: white;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-size: 14px;
            color: #2c3e50;
            margin-bottom: 3px;
        }
        
        .activity-time {
            font-size: 12px;
            color: #7f8c8d;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #2ecc71;
            color: white;
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="layout">
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <span class="logo-icon">🚕</span>
                    出租车管理系统
                </div>
            </div>
            
            <div class="nav-menu">
                <a href="#" class="nav-item active" onclick="showDashboard()">
                    <i>📊</i> 数据概览
                </a>
                <a href="#" class="nav-item" onclick="showSystemManagement()">
                    <i>⚙️</i> 系统管理
                </a>
                <a href="#" class="nav-item" onclick="showFleetManagement()">
                    <i>🚗</i> 车队管理
                </a>
                <a href="#" class="nav-item" onclick="showVehicleManagement()">
                    <i>🚙</i> 车辆管理
                </a>
                <a href="#" class="nav-item" onclick="showOrderManagement()">
                    <i>📋</i> 订单管理
                </a>
                <a href="#" class="nav-item" onclick="showRuleManagement()">
                    <i>📏</i> 规则管理
                </a>
                <a href="#" class="nav-item" onclick="showUsageStats()">
                    <i>📈</i> 用量统计
                </a>
                <a href="#" class="nav-item" onclick="showLogs()">
                    <i>📝</i> 系统日志
                </a>
            </div>
        </nav>
        
        <main class="main-content">
            <div class="header">
                <h1 class="page-title">数据概览</h1>
                <div class="user-info">
                    <span>管理员</span>
                    <div class="user-avatar">A</div>
                    <button class="btn btn-primary" onclick="logout()">退出</button>
                </div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card orders">
                    <div class="stat-header">
                        <span class="stat-title">今日订单</span>
                        <div class="stat-icon" style="background: #3498db;">📋</div>
                    </div>
                    <div class="stat-value">1,234</div>
                    <div class="stat-change positive">
                        ↗ +12.5% 较昨日
                    </div>
                </div>
                
                <div class="stat-card drivers">
                    <div class="stat-header">
                        <span class="stat-title">在线司机</span>
                        <div class="stat-icon" style="background: #2ecc71;">👨‍💼</div>
                    </div>
                    <div class="stat-value">456</div>
                    <div class="stat-change positive">
                        ↗ +8.3% 较昨日
                    </div>
                </div>
                
                <div class="stat-card revenue">
                    <div class="stat-header">
                        <span class="stat-title">今日收入</span>
                        <div class="stat-icon" style="background: #f39c12;">💰</div>
                    </div>
                    <div class="stat-value">¥89,567</div>
                    <div class="stat-change positive">
                        ↗ +15.2% 较昨日
                    </div>
                </div>
                
                <div class="stat-card vehicles">
                    <div class="stat-header">
                        <span class="stat-title">运营车辆</span>
                        <div class="stat-icon" style="background: #e74c3c;">🚗</div>
                    </div>
                    <div class="stat-value">789</div>
                    <div class="stat-change negative">
                        ↘ -2.1% 较昨日
                    </div>
                </div>
            </div>
            
            <div class="content-grid">
                <div class="chart-card">
                    <h3 class="card-title">订单趋势图</h3>
                    <div class="chart-placeholder">
                        📈 订单数据图表区域
                        <br>
                        <small>集成 ECharts 或 Chart.js 显示实时数据</small>
                    </div>
                </div>
                
                <div class="activity-card">
                    <h3 class="card-title">最近活动</h3>
                    <div class="activity-list">
                        <div class="activity-item">
                            <div class="activity-icon" style="background: #3498db;">📋</div>
                            <div class="activity-content">
                                <div class="activity-title">新订单创建</div>
                                <div class="activity-time">2分钟前</div>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon" style="background: #2ecc71;">👨‍💼</div>
                            <div class="activity-content">
                                <div class="activity-title">司机上线</div>
                                <div class="activity-time">5分钟前</div>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon" style="background: #f39c12;">🚗</div>
                            <div class="activity-content">
                                <div class="activity-title">车辆状态更新</div>
                                <div class="activity-time">10分钟前</div>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon" style="background: #e74c3c;">⚠️</div>
                            <div class="activity-content">
                                <div class="activity-title">系统异常警告</div>
                                <div class="activity-time">15分钟前</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function showDashboard() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '数据概览';
            // 这里可以加载仪表板内容
        }
        
        function showSystemManagement() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '系统管理';
            window.location.href = 'admin-system.html';
        }
        
        function showFleetManagement() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '车队管理';
            window.location.href = 'admin-fleet.html';
        }
        
        function showVehicleManagement() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '车辆管理';
            window.location.href = 'admin-vehicles.html';
        }
        
        function showOrderManagement() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '订单管理';
            window.location.href = 'admin-orders.html';
        }
        
        function showRuleManagement() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '规则管理';
            window.location.href = 'admin-rules.html';
        }
        
        function showUsageStats() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '用量统计';
            window.location.href = 'admin-usage.html';
        }
        
        function showLogs() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '系统日志';
            window.location.href = 'admin-logs.html';
        }
        
        function updateActiveNav(target) {
            document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
            target.classList.add('active');
        }
        
        function logout() {
            if (confirm('确认退出登录？')) {
                window.location.href = 'admin-login.html';
            }
        }
        
        // 模拟实时数据更新
        function updateStats() {
            // 这里可以通过 WebSocket 或定时器更新统计数据
            console.log('更新统计数据');
        }
        
        // 页面加载时初始化
        window.addEventListener('load', () => {
            // 每30秒更新一次数据
            setInterval(updateStats, 30000);
        });
    </script>
</body>
</html>
