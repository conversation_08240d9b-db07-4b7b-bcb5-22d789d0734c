<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据目录管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            flex-wrap: wrap;
        }
        
        .nav-tab {
            flex: 1;
            min-width: 140px;
            padding: 15px 10px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 13px;
            font-weight: 500;
            text-align: center;
        }
        
        .nav-tab.active {
            background: #667eea;
            color: white;
        }
        
        .nav-tab:hover {
            background: #5a6fd8;
            color: white;
        }
        
        .content-panel {
            display: none;
            background: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .content-panel.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 13px;
        }
        
        .data-table th,
        .data-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            position: sticky;
            top: 0;
        }
        
        .data-table tr:hover {
            background: #f8f9ff;
        }
        
        .row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .col {
            flex: 1;
        }
        
        .col-2 {
            flex: 2;
        }
        
        .col-3 {
            flex: 3;
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-deleted {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-approved {
            background: #cce5ff;
            color: #004085;
        }
        
        .catalog-tree {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            background: #fafafa;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .tree-node {
            margin: 5px 0;
            padding: 8px;
            cursor: pointer;
            border-radius: 4px;
            transition: background 0.3s;
        }
        
        .tree-node:hover {
            background: #e9ecef;
        }
        
        .tree-node.selected {
            background: #e3f2fd;
            color: #1976d2;
            font-weight: 500;
        }
        
        .tree-level-1 {
            font-weight: bold;
            color: #495057;
        }
        
        .tree-level-2 {
            margin-left: 20px;
            color: #6c757d;
        }
        
        .tree-level-3 {
            margin-left: 40px;
            color: #868e96;
        }
        
        .search-box {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .search-box input {
            flex: 1;
        }
        
        .sub-tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        
        .sub-tab {
            padding: 10px 20px;
            background: none;
            border: none;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .sub-tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: 500;
        }
        
        .resource-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s;
        }
        
        .resource-card:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
        }
        
        .resource-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .resource-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .resource-code {
            font-size: 12px;
            color: #666;
            background: #f8f9fa;
            padding: 2px 8px;
            border-radius: 4px;
        }
        
        .resource-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .meta-item {
            font-size: 13px;
        }
        
        .meta-label {
            color: #666;
            margin-right: 5px;
        }
        
        .meta-value {
            color: #333;
            font-weight: 500;
        }
        
        .data-items {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 10px;
            margin-top: 10px;
        }
        
        .data-items h5 {
            margin-bottom: 8px;
            color: #667eea;
        }
        
        .item-list {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        
        .item-tag {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 2px 8px;
            font-size: 12px;
            color: #666;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 30px;
            border-radius: 8px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
        
        .policy-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .policy-section h3 {
            color: #667eea;
            margin-bottom: 15px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }
        
        .flow-step {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }
        
        .step-number {
            background: #667eea;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }
        
        .step-content {
            flex: 1;
        }
        
        .step-title {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .step-desc {
            color: #666;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>数据目录管理系统</h1>
        <p>数据资源目录构建、数据资源管理、分类维护、申请流程的一体化平台</p>
    </div>

    <div class="container">
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showPanel('catalog')">数据资源目录</button>
            <button class="nav-tab" onclick="showPanel('classification')">分类维度管理</button>
            <button class="nav-tab" onclick="showPanel('resources')">数据资源管理</button>
            <button class="nav-tab" onclick="showPanel('authorization')">授权管理</button>
            <button class="nav-tab" onclick="showPanel('application')">申请流程</button>
            <button class="nav-tab" onclick="showPanel('policies')">制度规范</button>
        </div>

        <!-- 数据资源目录面板 -->
        <div id="catalog" class="content-panel active">
            <h2>数据资源目录管理</h2>
            <div class="alert alert-info">
                支持根据数据范围构建不同的数据资源目录，完成数据资源目录的新增和删除，支持自定义的管理数据资源的组织和展示结构
            </div>

            <div class="row">
                <div class="col">
                    <h3>目录结构管理</h3>
                    <div class="form-group">
                        <label>目录名称</label>
                        <input type="text" class="form-control" placeholder="请输入目录名称">
                    </div>
                    <div class="form-group">
                        <label>数据范围</label>
                        <select class="form-control">
                            <option>全校数据</option>
                            <option>学院数据</option>
                            <option>部门数据</option>
                            <option>专业数据</option>
                            <option>项目数据</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>目录描述</label>
                        <textarea class="form-control" rows="3" placeholder="请输入目录描述"></textarea>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-primary">新增目录</button>
                        <button class="btn btn-danger">删除目录</button>
                        <button class="btn btn-info">导出目录</button>
                    </div>
                </div>

                <div class="col">
                    <h3>目录树结构</h3>
                    <div class="catalog-tree">
                        <div class="tree-node tree-level-1 selected" onclick="selectCatalog(this, 'all')">
                            📁 全校数据资源目录
                        </div>
                        <div class="tree-node tree-level-2" onclick="selectCatalog(this, 'student')">
                            📂 学生管理数据目录
                        </div>
                        <div class="tree-node tree-level-3" onclick="selectCatalog(this, 'student_basic')">
                            📄 学生基本信息
                        </div>
                        <div class="tree-node tree-level-3" onclick="selectCatalog(this, 'student_score')">
                            📄 学生成绩信息
                        </div>
                        <div class="tree-node tree-level-2" onclick="selectCatalog(this, 'teacher')">
                            📂 教师管理数据目录
                        </div>
                        <div class="tree-node tree-level-3" onclick="selectCatalog(this, 'teacher_basic')">
                            📄 教师基本信息
                        </div>
                        <div class="tree-node tree-level-3" onclick="selectCatalog(this, 'teacher_course')">
                            📄 教师授课信息
                        </div>
                        <div class="tree-node tree-level-2" onclick="selectCatalog(this, 'course')">
                            📂 教学管理数据目录
                        </div>
                        <div class="tree-node tree-level-3" onclick="selectCatalog(this, 'course_basic')">
                            📄 课程基本信息
                        </div>
                        <div class="tree-node tree-level-3" onclick="selectCatalog(this, 'course_schedule')">
                            📄 课程安排信息
                        </div>
                        <div class="tree-node tree-level-2" onclick="selectCatalog(this, 'research')">
                            📂 科研管理数据目录
                        </div>
                        <div class="tree-node tree-level-3" onclick="selectCatalog(this, 'research_project')">
                            📄 科研项目信息
                        </div>
                        <div class="tree-node tree-level-3" onclick="selectCatalog(this, 'research_result')">
                            📄 科研成果信息
                        </div>
                    </div>
                </div>
            </div>

            <h3>目录列表</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>目录名称</th>
                        <th>数据范围</th>
                        <th>包含资源数</th>
                        <th>创建时间</th>
                        <th>最后更新</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>全校数据资源目录</td>
                        <td>全校数据</td>
                        <td>156</td>
                        <td>2024-01-01</td>
                        <td>2024-01-15</td>
                        <td><span class="status-badge status-active">启用</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看</button>
                        </td>
                    </tr>
                    <tr>
                        <td>学院数据资源目录</td>
                        <td>学院数据</td>
                        <td>89</td>
                        <td>2024-01-05</td>
                        <td>2024-01-14</td>
                        <td><span class="status-badge status-active">启用</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看</button>
                        </td>
                    </tr>
                    <tr>
                        <td>项目数据资源目录</td>
                        <td>项目数据</td>
                        <td>23</td>
                        <td>2024-01-10</td>
                        <td>2024-01-12</td>
                        <td><span class="status-badge status-deleted">已删除</span></td>
                        <td>
                            <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">恢复</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分类维度管理面板 -->
        <div id="classification" class="content-panel">
            <h2>分类维度管理</h2>
            <div class="alert alert-info">
                支持数据资源分类维度和层级的新增和删除，自定义分类体系结构
            </div>

            <div class="sub-tabs">
                <button class="sub-tab active" onclick="showSubPanel('dimensions')">分类维度</button>
                <button class="sub-tab" onclick="showSubPanel('categories')">分类层级</button>
            </div>

            <div id="dimensions" class="tab-content">
                <h3>分类维度配置</h3>
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>维度名称</label>
                            <input type="text" class="form-control" placeholder="请输入分类维度名称">
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>维度编码</label>
                            <input type="text" class="form-control" placeholder="请输入维度编码">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>维度描述</label>
                    <textarea class="form-control" rows="3" placeholder="请输入维度描述"></textarea>
                </div>

                <div class="form-group">
                    <button class="btn btn-primary">新增维度</button>
                    <button class="btn btn-success">保存配置</button>
                </div>

                <h4>已配置维度</h4>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>维度名称</th>
                            <th>维度编码</th>
                            <th>分类数量</th>
                            <th>创建时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>业务域分类</td>
                            <td>BUSINESS_DOMAIN</td>
                            <td>8</td>
                            <td>2024-01-01</td>
                            <td><span class="status-badge status-active">启用</span></td>
                            <td>
                                <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                                <button class="btn btn-danger" style="padding: 6px 12px; font-size: 12px;">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td>数据类型分类</td>
                            <td>DATA_TYPE</td>
                            <td>5</td>
                            <td>2024-01-01</td>
                            <td><span class="status-badge status-active">启用</span></td>
                            <td>
                                <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                                <button class="btn btn-danger" style="padding: 6px 12px; font-size: 12px;">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td>共享级别分类</td>
                            <td>SHARE_LEVEL</td>
                            <td>4</td>
                            <td>2024-01-01</td>
                            <td><span class="status-badge status-active">启用</span></td>
                            <td>
                                <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                                <button class="btn btn-danger" style="padding: 6px 12px; font-size: 12px;">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div id="categories" class="tab-content" style="display: none;">
                <h3>分类层级配置</h3>
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>所属维度</label>
                            <select class="form-control">
                                <option>业务域分类</option>
                                <option>数据类型分类</option>
                                <option>共享级别分类</option>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>父级分类</label>
                            <select class="form-control">
                                <option>无（顶级分类）</option>
                                <option>学生管理域</option>
                                <option>教师管理域</option>
                                <option>教学管理域</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>分类名称</label>
                            <input type="text" class="form-control" placeholder="请输入分类名称">
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>分类编码</label>
                            <input type="text" class="form-control" placeholder="请输入分类编码">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>分类描述</label>
                    <textarea class="form-control" rows="3" placeholder="请输入分类描述"></textarea>
                </div>

                <div class="form-group">
                    <button class="btn btn-primary">新增分类</button>
                    <button class="btn btn-success">保存配置</button>
                </div>

                <h4>业务域分类层级</h4>
                <div class="catalog-tree">
                    <div class="tree-node tree-level-1">
                        📁 学生管理域
                        <div class="tree-node tree-level-2">
                            📂 学籍管理
                            <div class="tree-node tree-level-3">📄 基本信息</div>
                            <div class="tree-node tree-level-3">📄 学籍变动</div>
                        </div>
                        <div class="tree-node tree-level-2">
                            📂 成绩管理
                            <div class="tree-node tree-level-3">📄 考试成绩</div>
                            <div class="tree-node tree-level-3">📄 平时成绩</div>
                        </div>
                    </div>
                    <div class="tree-node tree-level-1">
                        📁 教师管理域
                        <div class="tree-node tree-level-2">
                            📂 人事档案
                            <div class="tree-node tree-level-3">📄 基本信息</div>
                            <div class="tree-node tree-level-3">📄 职业发展</div>
                        </div>
                    </div>
                    <div class="tree-node tree-level-1">
                        📁 教学管理域
                        <div class="tree-node tree-level-2">
                            📂 课程管理
                            <div class="tree-node tree-level-3">📄 课程信息</div>
                            <div class="tree-node tree-level-3">📄 课程安排</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据资源管理面板 -->
        <div id="resources" class="content-panel">
            <h2>数据资源管理</h2>
            <div class="alert alert-info">
                支持按照不同的数据资源目录展示数据资源，新增数据资源时需支持数据资源属性、分类和数据项的维护工作
            </div>

            <div class="search-box">
                <input type="text" class="form-control" placeholder="搜索数据资源...">
                <select class="form-control" style="flex: 0 0 150px;">
                    <option>全校数据资源目录</option>
                    <option>学院数据资源目录</option>
                    <option>部门数据资源目录</option>
                </select>
                <select class="form-control" style="flex: 0 0 120px;">
                    <option>全部状态</option>
                    <option>正常</option>
                    <option>已删除</option>
                </select>
                <button class="btn btn-primary">搜索</button>
                <button class="btn btn-success" onclick="openResourceModal()">新增资源</button>
            </div>

            <div class="resource-card">
                <div class="resource-header">
                    <div class="resource-title">学生基本信息数据资源</div>
                    <div class="resource-code">STU_BASIC_001</div>
                </div>
                <div class="resource-meta">
                    <div class="meta-item">
                        <span class="meta-label">责任单位：</span>
                        <span class="meta-value">学生处</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">共享类型：</span>
                        <span class="meta-value">有条件共享</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">使用方式：</span>
                        <span class="meta-value">API接口</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">分类：</span>
                        <span class="meta-value">学生管理域 > 学籍管理 > 基本信息</span>
                    </div>
                </div>
                <div class="meta-item">
                    <span class="meta-label">说明：</span>
                    <span class="meta-value">包含学生的基本个人信息，如姓名、性别、出生日期、身份证号等核心字段</span>
                </div>
                <div class="data-items">
                    <h5>数据项 (15个)</h5>
                    <div class="item-list">
                        <span class="item-tag">student_id - 学生ID</span>
                        <span class="item-tag">student_name - 学生姓名</span>
                        <span class="item-tag">gender - 性别</span>
                        <span class="item-tag">birth_date - 出生日期</span>
                        <span class="item-tag">id_card - 身份证号</span>
                        <span class="item-tag">phone - 联系电话</span>
                        <span class="item-tag">email - 邮箱地址</span>
                        <span class="item-tag">enrollment_date - 入学日期</span>
                        <span class="item-tag">major - 专业</span>
                        <span class="item-tag">class_name - 班级</span>
                    </div>
                </div>
                <div style="margin-top: 15px;">
                    <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;" onclick="editResource('STU_BASIC_001')">编辑</button>
                    <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看详情</button>
                    <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">申请权限</button>
                    <button class="btn btn-danger" style="padding: 6px 12px; font-size: 12px;" onclick="deleteResource('STU_BASIC_001')">删除</button>
                </div>
            </div>

            <div class="resource-card">
                <div class="resource-header">
                    <div class="resource-title">教师基本信息数据资源</div>
                    <div class="resource-code">TEA_BASIC_001</div>
                </div>
                <div class="resource-meta">
                    <div class="meta-item">
                        <span class="meta-label">责任单位：</span>
                        <span class="meta-value">人事处</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">共享类型：</span>
                        <span class="meta-value">无条件共享</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">使用方式：</span>
                        <span class="meta-value">数据库查询</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">分类：</span>
                        <span class="meta-value">教师管理域 > 人事档案 > 基本信息</span>
                    </div>
                </div>
                <div class="meta-item">
                    <span class="meta-label">说明：</span>
                    <span class="meta-value">包含教师的基本个人信息和职业信息，支持教学管理和人事管理业务</span>
                </div>
                <div class="data-items">
                    <h5>数据项 (12个)</h5>
                    <div class="item-list">
                        <span class="item-tag">teacher_id - 教师ID</span>
                        <span class="item-tag">teacher_name - 教师姓名</span>
                        <span class="item-tag">department - 所属部门</span>
                        <span class="item-tag">title - 职称</span>
                        <span class="item-tag">degree - 学历</span>
                        <span class="item-tag">hire_date - 入职日期</span>
                        <span class="item-tag">research_area - 研究方向</span>
                        <span class="item-tag">office_location - 办公地点</span>
                    </div>
                </div>
                <div style="margin-top: 15px;">
                    <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;" onclick="editResource('TEA_BASIC_001')">编辑</button>
                    <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看详情</button>
                    <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">申请权限</button>
                    <button class="btn btn-danger" style="padding: 6px 12px; font-size: 12px;" onclick="deleteResource('TEA_BASIC_001')">删除</button>
                </div>
            </div>

            <div class="resource-card" style="border-color: #dc3545; background: #fff5f5;">
                <div class="resource-header">
                    <div class="resource-title" style="color: #dc3545;">课程安排信息数据资源 [已删除]</div>
                    <div class="resource-code">COU_SCHEDULE_001</div>
                </div>
                <div class="resource-meta">
                    <div class="meta-item">
                        <span class="meta-label">责任单位：</span>
                        <span class="meta-value">教务处</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">删除时间：</span>
                        <span class="meta-value">2024-01-10 15:30</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">授权记录：</span>
                        <span class="meta-value">5条（不受影响）</span>
                    </div>
                </div>
                <div style="margin-top: 15px;">
                    <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">恢复资源</button>
                    <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看授权记录</button>
                </div>
            </div>
        </div>

        <!-- 授权管理面板 -->
        <div id="authorization" class="content-panel">
            <h2>授权管理</h2>
            <div class="alert alert-info">
                管理数据资源的授权记录，支持查看已删除数据资源的授权记录，新增的数据项不会出现在原有的授权和现在待申请清单中
            </div>

            <div class="search-box">
                <input type="text" class="form-control" placeholder="搜索授权记录...">
                <select class="form-control" style="flex: 0 0 150px;">
                    <option>全部资源</option>
                    <option>正常资源</option>
                    <option>已删除资源</option>
                </select>
                <select class="form-control" style="flex: 0 0 120px;">
                    <option>全部状态</option>
                    <option>有效</option>
                    <option>过期</option>
                    <option>暂停</option>
                </select>
                <button class="btn btn-primary">搜索</button>
                <button class="btn btn-success">批量授权</button>
            </div>

            <table class="data-table">
                <thead>
                    <tr>
                        <th>资源名称</th>
                        <th>资源编码</th>
                        <th>申请单位</th>
                        <th>授权范围</th>
                        <th>授权时间</th>
                        <th>有效期至</th>
                        <th>使用次数</th>
                        <th>资源状态</th>
                        <th>授权状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>学生基本信息数据资源</td>
                        <td>STU_BASIC_001</td>
                        <td>教务处</td>
                        <td>查询、统计</td>
                        <td>2024-01-01</td>
                        <td>2024-12-31</td>
                        <td>1,234</td>
                        <td><span class="status-badge status-active">正常</span></td>
                        <td><span class="status-badge status-approved">有效</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">暂停</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">详情</button>
                        </td>
                    </tr>
                    <tr>
                        <td>教师基本信息数据资源</td>
                        <td>TEA_BASIC_001</td>
                        <td>科研处</td>
                        <td>查询</td>
                        <td>2024-01-05</td>
                        <td>2024-06-30</td>
                        <td>567</td>
                        <td><span class="status-badge status-active">正常</span></td>
                        <td><span class="status-badge status-approved">有效</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">续期</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">详情</button>
                        </td>
                    </tr>
                    <tr style="background: #fff5f5;">
                        <td>课程安排信息数据资源 [已删除]</td>
                        <td>COU_SCHEDULE_001</td>
                        <td>学院办公室</td>
                        <td>查询、导出</td>
                        <td>2023-12-01</td>
                        <td>2024-11-30</td>
                        <td>89</td>
                        <td><span class="status-badge status-deleted">已删除</span></td>
                        <td><span class="status-badge status-approved">有效</span></td>
                        <td>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看记录</button>
                            <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">转移授权</button>
                        </td>
                    </tr>
                    <tr>
                        <td>学生成绩信息数据资源</td>
                        <td>STU_SCORE_001</td>
                        <td>招生办</td>
                        <td>统计分析</td>
                        <td>2024-01-10</td>
                        <td>2024-03-31</td>
                        <td>23</td>
                        <td><span class="status-badge status-active">正常</span></td>
                        <td><span class="status-badge status-pending">待审核</span></td>
                        <td>
                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">审批</button>
                            <button class="btn btn-danger" style="padding: 6px 12px; font-size: 12px;">拒绝</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">详情</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 申请流程面板 -->
        <div id="application" class="content-panel">
            <h2>数据使用申请流程</h2>
            <div class="alert alert-info">
                结合数据开放使用原则，建立完善数据申请、使用及共享线上执行的全流程管理
            </div>

            <div class="sub-tabs">
                <button class="sub-tab active" onclick="showSubPanel('apply')">申请数据</button>
                <button class="sub-tab" onclick="showSubPanel('process')">申请流程</button>
                <button class="sub-tab" onclick="showSubPanel('records')">申请记录</button>
            </div>

            <div id="apply" class="tab-content">
                <h3>数据使用申请</h3>
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>申请单位</label>
                            <input type="text" class="form-control" placeholder="请输入申请单位">
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>申请人</label>
                            <input type="text" class="form-control" placeholder="请输入申请人姓名">
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>联系方式</label>
                            <input type="text" class="form-control" placeholder="请输入联系方式">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>申请的数据资源</label>
                    <select class="form-control" multiple>
                        <option>学生基本信息数据资源</option>
                        <option>教师基本信息数据资源</option>
                        <option>课程基本信息数据资源</option>
                        <option>成绩信息数据资源</option>
                        <option>科研项目信息数据资源</option>
                    </select>
                </div>

                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>使用目的</label>
                            <select class="form-control">
                                <option>教学管理</option>
                                <option>科研分析</option>
                                <option>统计报告</option>
                                <option>决策支持</option>
                                <option>其他</option>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>使用期限</label>
                            <select class="form-control">
                                <option>3个月</option>
                                <option>6个月</option>
                                <option>1年</option>
                                <option>长期</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>申请理由</label>
                    <textarea class="form-control" rows="4" placeholder="请详细说明申请理由和使用场景"></textarea>
                </div>

                <div class="form-group">
                    <label>数据安全承诺</label>
                    <div>
                        <label><input type="checkbox" required> 承诺严格按照数据管理办法使用数据</label><br>
                        <label><input type="checkbox" required> 承诺不将数据用于商业用途</label><br>
                        <label><input type="checkbox" required> 承诺不泄露、传播敏感数据</label><br>
                        <label><input type="checkbox" required> 承诺使用完毕后及时删除数据</label>
                    </div>
                </div>

                <div class="form-group">
                    <button class="btn btn-primary">提交申请</button>
                    <button class="btn btn-success">保存草稿</button>
                    <button class="btn btn-info">预览申请</button>
                </div>
            </div>

            <div id="process" class="tab-content" style="display: none;">
                <h3>申请审批流程</h3>
                <div class="flow-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <div class="step-title">提交申请</div>
                        <div class="step-desc">申请人填写申请表单，选择需要的数据资源，说明使用目的和期限</div>
                    </div>
                </div>

                <div class="flow-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <div class="step-title">初步审核</div>
                        <div class="step-desc">数据管理员对申请进行初步审核，检查申请信息的完整性和合规性</div>
                    </div>
                </div>

                <div class="flow-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <div class="step-title">业务审核</div>
                        <div class="step-desc">数据资源责任单位对申请进行业务审核，评估数据使用的必要性和合理性</div>
                    </div>
                </div>

                <div class="flow-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <div class="step-title">安全评估</div>
                        <div class="step-desc">信息安全部门对数据使用进行安全风险评估，确保数据安全</div>
                    </div>
                </div>

                <div class="flow-step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <div class="step-title">最终审批</div>
                        <div class="step-desc">分管领导进行最终审批，决定是否授权数据使用</div>
                    </div>
                </div>

                <div class="flow-step">
                    <div class="step-number">6</div>
                    <div class="step-content">
                        <div class="step-title">授权开通</div>
                        <div class="step-desc">系统自动开通数据访问权限，申请人可以开始使用数据</div>
                    </div>
                </div>
            </div>

            <div id="records" class="tab-content" style="display: none;">
                <h3>申请记录查询</h3>
                <div class="search-box">
                    <input type="text" class="form-control" placeholder="搜索申请记录...">
                    <select class="form-control" style="flex: 0 0 120px;">
                        <option>全部状态</option>
                        <option>待审核</option>
                        <option>已通过</option>
                        <option>已拒绝</option>
                    </select>
                    <button class="btn btn-primary">搜索</button>
                </div>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>申请编号</th>
                            <th>申请单位</th>
                            <th>申请人</th>
                            <th>申请资源</th>
                            <th>使用目的</th>
                            <th>申请时间</th>
                            <th>当前状态</th>
                            <th>审批进度</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>APP20240115001</td>
                            <td>教务处</td>
                            <td>张三</td>
                            <td>学生基本信息数据资源</td>
                            <td>教学管理</td>
                            <td>2024-01-15 09:30</td>
                            <td><span class="status-badge status-pending">待审核</span></td>
                            <td>2/6</td>
                            <td>
                                <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看详情</button>
                                <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">催办</button>
                            </td>
                        </tr>
                        <tr>
                            <td>APP20240114002</td>
                            <td>科研处</td>
                            <td>李四</td>
                            <td>教师基本信息数据资源</td>
                            <td>科研分析</td>
                            <td>2024-01-14 14:20</td>
                            <td><span class="status-badge status-approved">已通过</span></td>
                            <td>6/6</td>
                            <td>
                                <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">开始使用</button>
                                <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看详情</button>
                            </td>
                        </tr>
                        <tr>
                            <td>APP20240113003</td>
                            <td>招生办</td>
                            <td>王五</td>
                            <td>学生成绩信息数据资源</td>
                            <td>统计报告</td>
                            <td>2024-01-13 16:45</td>
                            <td><span class="status-badge status-deleted">已拒绝</span></td>
                            <td>3/6</td>
                            <td>
                                <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看原因</button>
                                <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">重新申请</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 制度规范面板 -->
        <div id="policies" class="content-panel">
            <h2>制度规范</h2>
            <div class="alert alert-info">
                结合数据开放使用原则，完成《数据管理办法》、《数据资源分类分级》、《数据使用申请流程》等制度规范
            </div>

            <div class="policy-section">
                <h3>数据管理办法</h3>
                <div class="row">
                    <div class="col-2">
                        <h4>第一章 总则</h4>
                        <p><strong>第一条</strong> 为规范学校数据资源管理，保障数据安全，促进数据共享和应用，根据国家相关法律法规，结合学校实际，制定本办法。</p>
                        <p><strong>第二条</strong> 本办法适用于学校各部门、各单位的数据资源管理活动。</p>
                        <p><strong>第三条</strong> 数据管理遵循"统一规划、分级管理、安全可控、开放共享"的原则。</p>

                        <h4>第二章 数据分类分级</h4>
                        <p><strong>第四条</strong> 数据按敏感程度分为公开、内部、敏感、机密四个级别。</p>
                        <p><strong>第五条</strong> 数据按业务领域分为学生管理、教师管理、教学管理、科研管理、财务管理、资产管理、行政管理、公共服务八大类。</p>

                        <h4>第三章 数据共享</h4>
                        <p><strong>第六条</strong> 数据共享分为无条件共享、有条件共享、不予共享三种类型。</p>
                        <p><strong>第七条</strong> 各部门应积极推进数据开放共享，提高数据利用效率。</p>
                    </div>
                    <div class="col">
                        <h4>第四章 数据安全</h4>
                        <p><strong>第八条</strong> 建立数据安全管理制度，明确数据安全责任。</p>
                        <p><strong>第九条</strong> 对敏感数据实施加密存储和传输。</p>
                        <p><strong>第十条</strong> 定期开展数据安全检查和风险评估。</p>

                        <h4>第五章 监督管理</h4>
                        <p><strong>第十一条</strong> 建立数据质量监控机制，定期评估数据质量。</p>
                        <p><strong>第十二条</strong> 对违反本办法的行为，依据相关规定进行处理。</p>

                        <h4>第六章 附则</h4>
                        <p><strong>第十三条</strong> 本办法由信息化建设与管理处负责解释。</p>
                        <p><strong>第十四条</strong> 本办法自发布之日起施行。</p>
                    </div>
                </div>
            </div>

            <div class="policy-section">
                <h3>数据资源分类分级标准</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>分类维度</th>
                            <th>分类标准</th>
                            <th>级别</th>
                            <th>描述</th>
                            <th>示例</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td rowspan="4">敏感程度</td>
                            <td>公开</td>
                            <td>Level 0</td>
                            <td>可以公开发布的数据</td>
                            <td>学校简介、专业设置</td>
                        </tr>
                        <tr>
                            <td>内部</td>
                            <td>Level 1</td>
                            <td>仅限校内使用的数据</td>
                            <td>课程安排、教室分配</td>
                        </tr>
                        <tr>
                            <td>敏感</td>
                            <td>Level 2</td>
                            <td>涉及个人隐私的数据</td>
                            <td>学生成绩、教师薪酬</td>
                        </tr>
                        <tr>
                            <td>机密</td>
                            <td>Level 3</td>
                            <td>涉及学校核心机密的数据</td>
                            <td>财务明细、人事档案</td>
                        </tr>
                        <tr>
                            <td rowspan="3">共享类型</td>
                            <td>无条件共享</td>
                            <td>Type A</td>
                            <td>可直接共享使用</td>
                            <td>公开课程信息</td>
                        </tr>
                        <tr>
                            <td>有条件共享</td>
                            <td>Type B</td>
                            <td>需申请审批后共享</td>
                            <td>学生基本信息</td>
                        </tr>
                        <tr>
                            <td>不予共享</td>
                            <td>Type C</td>
                            <td>不对外共享</td>
                            <td>个人身份证号</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="policy-section">
                <h3>数据使用申请流程规范</h3>
                <div class="row">
                    <div class="col">
                        <h4>申请条件</h4>
                        <ul>
                            <li>申请人必须是学校正式员工或授权人员</li>
                            <li>申请用途必须符合学校业务需要</li>
                            <li>申请人必须签署数据安全承诺书</li>
                            <li>申请的数据范围应当最小化原则</li>
                        </ul>

                        <h4>审批权限</h4>
                        <ul>
                            <li>公开数据：数据管理员审批</li>
                            <li>内部数据：部门负责人审批</li>
                            <li>敏感数据：分管领导审批</li>
                            <li>机密数据：校领导审批</li>
                        </ul>
                    </div>
                    <div class="col">
                        <h4>使用规范</h4>
                        <ul>
                            <li>严格按照申请用途使用数据</li>
                            <li>不得将数据提供给第三方</li>
                            <li>不得用于商业用途</li>
                            <li>使用完毕后及时删除数据</li>
                        </ul>

                        <h4>监督检查</h4>
                        <ul>
                            <li>定期检查数据使用情况</li>
                            <li>记录数据访问日志</li>
                            <li>发现违规行为及时处理</li>
                            <li>建立数据使用评估机制</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button class="btn btn-primary">下载完整制度文件</button>
                <button class="btn btn-success">制度培训</button>
                <button class="btn btn-info">制度解读</button>
                <button class="btn btn-warning">意见反馈</button>
            </div>
        </div>
    </div>

    <!-- 数据资源编辑模态框 -->
    <div id="resourceModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeResourceModal()">&times;</span>
            <h3 id="modalTitle">新增数据资源</h3>

            <div class="sub-tabs">
                <button class="sub-tab active" onclick="showModalTab('attributes')">资源属性</button>
                <button class="sub-tab" onclick="showModalTab('classification')">资源分类</button>
                <button class="sub-tab" onclick="showModalTab('items')">数据项</button>
            </div>

            <div id="attributes" class="tab-content">
                <h4>数据资源属性</h4>
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>数据资源编码</label>
                            <input type="text" class="form-control" placeholder="请输入资源编码">
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>数据资源名称</label>
                            <input type="text" class="form-control" placeholder="请输入资源名称">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>责任单位</label>
                            <select class="form-control">
                                <option>学生处</option>
                                <option>教务处</option>
                                <option>人事处</option>
                                <option>科研处</option>
                                <option>财务处</option>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>共享类型</label>
                            <select class="form-control">
                                <option>无条件共享</option>
                                <option>有条件共享</option>
                                <option>不予共享</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>使用方式</label>
                    <select class="form-control">
                        <option>API接口</option>
                        <option>数据库查询</option>
                        <option>文件导出</option>
                        <option>在线查看</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>数据资源说明</label>
                    <textarea class="form-control" rows="4" placeholder="请输入资源说明"></textarea>
                </div>
            </div>

            <div id="classification" class="tab-content" style="display: none;">
                <h4>数据资源分类</h4>
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>分类维度</label>
                            <select class="form-control">
                                <option>业务域分类</option>
                                <option>数据类型分类</option>
                                <option>共享级别分类</option>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>一级分类</label>
                            <select class="form-control">
                                <option>学生管理域</option>
                                <option>教师管理域</option>
                                <option>教学管理域</option>
                                <option>科研管理域</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>二级分类</label>
                            <select class="form-control">
                                <option>学籍管理</option>
                                <option>成绩管理</option>
                                <option>奖惩管理</option>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>三级分类</label>
                            <select class="form-control">
                                <option>基本信息</option>
                                <option>学籍变动</option>
                                <option>毕业信息</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div id="items" class="tab-content" style="display: none;">
                <h4>数据项配置</h4>
                <div style="margin-bottom: 15px;">
                    <button class="btn btn-success" onclick="addDataItem()">添加数据项</button>
                    <button class="btn btn-info">从模板导入</button>
                </div>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>SQL字段</th>
                            <th>数据项名称</th>
                            <th>数据项别名</th>
                            <th>业务标识</th>
                            <th>责任单位</th>
                            <th>元数据关联</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="dataItemsTable">
                        <tr>
                            <td><input type="text" class="form-control" value="student_id"></td>
                            <td><input type="text" class="form-control" value="学生ID"></td>
                            <td><input type="text" class="form-control" value="学号"></td>
                            <td><input type="text" class="form-control" value="STU_ID"></td>
                            <td>
                                <select class="form-control">
                                    <option>学生处</option>
                                    <option>教务处</option>
                                </select>
                            </td>
                            <td><input type="text" class="form-control" value="MD_STUDENT_ID"></td>
                            <td>
                                <button class="btn btn-danger" style="padding: 4px 8px; font-size: 11px;" onclick="removeDataItem(this)">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div style="text-align: center; margin-top: 20px;">
                <button class="btn btn-primary" onclick="saveResource()">保存资源</button>
                <button class="btn btn-success">保存并发布</button>
                <button class="btn btn-info">预览</button>
                <button class="btn btn-warning" onclick="closeResourceModal()">取消</button>
            </div>
        </div>
    </div>

    <script>
        let currentCatalog = 'all';
        let currentModalTab = 'attributes';

        // 切换面板显示
        function showPanel(panelId) {
            // 隐藏所有面板
            const panels = document.querySelectorAll('.content-panel');
            panels.forEach(panel => panel.classList.remove('active'));

            // 移除所有标签的active状态
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的面板
            document.getElementById(panelId).classList.add('active');

            // 激活对应的标签
            event.target.classList.add('active');
        }

        // 切换子面板
        function showSubPanel(subPanelId) {
            // 隐藏当前面板下的所有子内容
            const currentPanel = document.querySelector('.content-panel.active');
            const subContents = currentPanel.querySelectorAll('.tab-content');
            subContents.forEach(content => content.style.display = 'none');

            // 移除所有子标签的active状态
            const subTabs = currentPanel.querySelectorAll('.sub-tab');
            subTabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的子内容
            const targetContent = currentPanel.querySelector(`#${subPanelId}`);
            if (targetContent) {
                targetContent.style.display = 'block';
            }

            // 激活对应的子标签
            event.target.classList.add('active');
        }

        // 选择目录
        function selectCatalog(element, catalogId) {
            // 移除其他选中状态
            document.querySelectorAll('.tree-node').forEach(node => {
                node.classList.remove('selected');
            });

            // 添加选中状态
            element.classList.add('selected');
            currentCatalog = catalogId;

            console.log('选择目录:', catalogId);
            // 这里可以根据选择的目录加载对应的数据资源
        }

        // 打开资源编辑模态框
        function openResourceModal() {
            document.getElementById('modalTitle').textContent = '新增数据资源';
            document.getElementById('resourceModal').style.display = 'block';
        }

        // 关闭资源编辑模态框
        function closeResourceModal() {
            document.getElementById('resourceModal').style.display = 'none';
        }

        // 切换模态框标签
        function showModalTab(tabId) {
            // 隐藏所有标签内容
            const modal = document.getElementById('resourceModal');
            const tabContents = modal.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.style.display = 'none');

            // 移除所有标签的active状态
            const tabs = modal.querySelectorAll('.sub-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的标签内容
            document.getElementById(tabId).style.display = 'block';

            // 激活对应的标签
            event.target.classList.add('active');
            currentModalTab = tabId;
        }

        // 编辑资源
        function editResource(resourceId) {
            document.getElementById('modalTitle').textContent = '编辑数据资源';
            document.getElementById('resourceModal').style.display = 'block';
            console.log('编辑资源:', resourceId);
        }

        // 删除资源
        function deleteResource(resourceId) {
            if (confirm('确定要删除此数据资源吗？删除后授权记录不受影响。')) {
                alert('数据资源已删除，授权记录保持不变');
                console.log('删除资源:', resourceId);
            }
        }

        // 添加数据项
        function addDataItem() {
            const table = document.getElementById('dataItemsTable');
            const newRow = table.insertRow();
            newRow.innerHTML = `
                <td><input type="text" class="form-control" placeholder="字段名"></td>
                <td><input type="text" class="form-control" placeholder="数据项名称"></td>
                <td><input type="text" class="form-control" placeholder="别名"></td>
                <td><input type="text" class="form-control" placeholder="业务标识"></td>
                <td>
                    <select class="form-control">
                        <option>学生处</option>
                        <option>教务处</option>
                        <option>人事处</option>
                    </select>
                </td>
                <td><input type="text" class="form-control" placeholder="元数据关联"></td>
                <td>
                    <button class="btn btn-danger" style="padding: 4px 8px; font-size: 11px;" onclick="removeDataItem(this)">删除</button>
                </td>
            `;
        }

        // 删除数据项
        function removeDataItem(button) {
            const row = button.closest('tr');
            row.remove();
        }

        // 保存资源
        function saveResource() {
            alert('数据资源保存成功！新增的数据项不会出现在原有的授权和现在待申请清单中。');
            closeResourceModal();
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('resourceModal');
            if (event.target === modal) {
                closeResourceModal();
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('数据目录管理系统已加载');
        });
    </script>
</body>
</html>
