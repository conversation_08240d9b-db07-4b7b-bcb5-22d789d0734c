<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据共享子系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            flex-wrap: wrap;
        }
        
        .nav-tab {
            flex: 1;
            min-width: 120px;
            padding: 15px 10px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 13px;
            font-weight: 500;
            text-align: center;
        }
        
        .nav-tab.active {
            background: #667eea;
            color: white;
        }
        
        .nav-tab:hover {
            background: #5a6fd8;
            color: white;
        }
        
        .content-panel {
            display: none;
            background: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .content-panel.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 13px;
        }
        
        .data-table th,
        .data-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            position: sticky;
            top: 0;
        }
        
        .data-table tr:hover {
            background: #f8f9ff;
        }
        
        .row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .col {
            flex: 1;
        }
        
        .col-2 {
            flex: 2;
        }
        
        .col-3 {
            flex: 3;
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-approved {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }
        
        .search-box {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .search-box input {
            flex: 1;
        }
        
        .sub-tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        
        .sub-tab {
            padding: 10px 20px;
            background: none;
            border: none;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .sub-tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: 500;
        }
        
        .resource-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s;
        }
        
        .resource-card:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
        }
        
        .resource-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .resource-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .resource-code {
            font-size: 12px;
            color: #666;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .resource-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .meta-item {
            font-size: 14px;
        }
        
        .meta-label {
            color: #666;
            margin-right: 8px;
        }
        
        .meta-value {
            color: #333;
            font-weight: 500;
        }
        
        .sql-builder {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
        }
        
        .sql-editor {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            min-height: 200px;
            overflow: auto;
        }
        
        .field-selector {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .field-item {
            padding: 8px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .field-item:hover {
            background: #f8f9fa;
        }
        
        .field-item.selected {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 30px;
            border-radius: 8px;
            width: 90%;
            max-width: 1000px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
        
        .todo-item {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 0 6px 6px 0;
        }
        
        .todo-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .todo-title {
            font-weight: 600;
            color: #333;
        }
        
        .todo-priority {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .priority-high {
            background: #f8d7da;
            color: #721c24;
        }
        
        .priority-medium {
            background: #fff3cd;
            color: #856404;
        }
        
        .priority-low {
            background: #d4edda;
            color: #155724;
        }
        
        .approval-form {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .approval-mode {
            margin-bottom: 20px;
        }
        
        .approval-mode label {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            cursor: pointer;
        }
        
        .approval-mode input[type="radio"] {
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>数据共享子系统</h1>
        <p>API管理、SQL构建器、数据开放门户、申请审核、工单配置的一体化平台</p>
    </div>

    <div class="container">
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showPanel('api')">API管理</button>
            <button class="nav-tab" onclick="showPanel('sql-builder')">SQL构建器</button>
            <button class="nav-tab" onclick="showPanel('portal')">数据开放门户</button>
            <button class="nav-tab" onclick="showPanel('applications')">申请管理</button>
            <button class="nav-tab" onclick="showPanel('approval')">审核中心</button>
            <button class="nav-tab" onclick="showPanel('todos')">待办事项</button>
            <button class="nav-tab" onclick="showPanel('workorders')">工单配置</button>
        </div>

        <!-- API管理面板 -->
        <div id="api" class="content-panel active">
            <h2>数据集成API管理</h2>
            <div class="alert alert-info">
                具备对数据集成API管理的功能，包括增加、编辑和删除等功能
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>API名称</label>
                        <input type="text" class="form-control" placeholder="请输入API名称">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>API版本</label>
                        <input type="text" class="form-control" placeholder="如：v1.0" value="v1.0">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>请求方法</label>
                        <select class="form-control">
                            <option>GET</option>
                            <option>POST</option>
                            <option>PUT</option>
                            <option>DELETE</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>API路径</label>
                        <input type="text" class="form-control" placeholder="如：/api/v1/students">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>数据源</label>
                        <select class="form-control">
                            <option>学生管理系统</option>
                            <option>教务管理系统</option>
                            <option>人事管理系统</option>
                            <option>数据中心</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>API描述</label>
                <textarea class="form-control" rows="3" placeholder="请输入API功能描述"></textarea>
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>认证方式</label>
                        <select class="form-control">
                            <option>API Key</option>
                            <option>OAuth 2.0</option>
                            <option>JWT Token</option>
                            <option>Basic Auth</option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>访问频率限制</label>
                        <input type="number" class="form-control" placeholder="每分钟请求次数" value="100">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <button class="btn btn-primary">新增API</button>
                <button class="btn btn-success">测试API</button>
                <button class="btn btn-info">生成文档</button>
            </div>

            <h3>API列表</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>API名称</th>
                        <th>版本</th>
                        <th>请求方法</th>
                        <th>API路径</th>
                        <th>数据源</th>
                        <th>状态</th>
                        <th>调用次数</th>
                        <th>最后更新</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>学生信息查询API</td>
                        <td>v1.0</td>
                        <td>GET</td>
                        <td>/api/v1/students</td>
                        <td>学生管理系统</td>
                        <td><span class="status-badge status-active">启用</span></td>
                        <td>1,234</td>
                        <td>2024-01-15</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;" onclick="editAPI('api001')">编辑</button>
                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">测试</button>
                            <button class="btn btn-danger" style="padding: 6px 12px; font-size: 12px;" onclick="deleteAPI('api001')">删除</button>
                        </td>
                    </tr>
                    <tr>
                        <td>教师信息查询API</td>
                        <td>v1.1</td>
                        <td>GET</td>
                        <td>/api/v1/teachers</td>
                        <td>人事管理系统</td>
                        <td><span class="status-badge status-active">启用</span></td>
                        <td>567</td>
                        <td>2024-01-14</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;" onclick="editAPI('api002')">编辑</button>
                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">测试</button>
                            <button class="btn btn-danger" style="padding: 6px 12px; font-size: 12px;" onclick="deleteAPI('api002')">删除</button>
                        </td>
                    </tr>
                    <tr>
                        <td>课程信息API</td>
                        <td>v2.0</td>
                        <td>POST</td>
                        <td>/api/v2/courses</td>
                        <td>教务管理系统</td>
                        <td><span class="status-badge status-pending">测试中</span></td>
                        <td>89</td>
                        <td>2024-01-13</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;" onclick="editAPI('api003')">编辑</button>
                            <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">发布</button>
                            <button class="btn btn-danger" style="padding: 6px 12px; font-size: 12px;" onclick="deleteAPI('api003')">删除</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- SQL构建器面板 -->
        <div id="sql-builder" class="content-panel">
            <h2>SQL构建器</h2>
            <div class="alert alert-info">
                支持用户通过SQL语句从外部数据源或数据中心选择所需字段
            </div>

            <div class="row">
                <div class="col">
                    <h3>数据源选择</h3>
                    <div class="form-group">
                        <label>数据源</label>
                        <select class="form-control" onchange="loadTables(this.value)">
                            <option>请选择数据源</option>
                            <option value="student_db">学生管理数据库</option>
                            <option value="teacher_db">教师管理数据库</option>
                            <option value="course_db">教务管理数据库</option>
                            <option value="data_center">数据中心</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>数据表</label>
                        <select class="form-control" onchange="loadFields(this.value)">
                            <option>请选择数据表</option>
                            <option value="students">学生信息表</option>
                            <option value="teachers">教师信息表</option>
                            <option value="courses">课程信息表</option>
                            <option value="scores">成绩信息表</option>
                        </select>
                    </div>

                    <h4>可选字段</h4>
                    <div class="field-selector">
                        <div class="field-item" onclick="selectField(this, 'student_id')">
                            <strong>student_id</strong> - 学生ID (VARCHAR)
                        </div>
                        <div class="field-item" onclick="selectField(this, 'student_name')">
                            <strong>student_name</strong> - 学生姓名 (VARCHAR)
                        </div>
                        <div class="field-item" onclick="selectField(this, 'gender')">
                            <strong>gender</strong> - 性别 (CHAR)
                        </div>
                        <div class="field-item" onclick="selectField(this, 'birth_date')">
                            <strong>birth_date</strong> - 出生日期 (DATE)
                        </div>
                        <div class="field-item" onclick="selectField(this, 'major')">
                            <strong>major</strong> - 专业 (VARCHAR)
                        </div>
                        <div class="field-item" onclick="selectField(this, 'class_name')">
                            <strong>class_name</strong> - 班级 (VARCHAR)
                        </div>
                        <div class="field-item" onclick="selectField(this, 'enrollment_date')">
                            <strong>enrollment_date</strong> - 入学日期 (DATE)
                        </div>
                        <div class="field-item" onclick="selectField(this, 'status')">
                            <strong>status</strong> - 状态 (VARCHAR)
                        </div>
                    </div>
                </div>

                <div class="col-2">
                    <h3>SQL编辑器</h3>
                    <div class="sql-builder">
                        <div class="form-group">
                            <button class="btn btn-success" onclick="generateSQL()">生成SQL</button>
                            <button class="btn btn-info" onclick="executeSQL()">执行查询</button>
                            <button class="btn btn-warning" onclick="clearSQL()">清空</button>
                            <button class="btn btn-primary" onclick="saveSQL()">保存查询</button>
                        </div>

                        <div class="sql-editor" contenteditable="true" id="sqlEditor">
SELECT student_id, student_name, gender, major, class_name
FROM students
WHERE status = 'active'
    AND enrollment_date >= '2020-01-01'
ORDER BY enrollment_date DESC
LIMIT 100;
                        </div>
                    </div>

                    <h4>查询结果预览</h4>
                    <div style="background: white; border: 1px solid #ddd; border-radius: 6px; padding: 15px; max-height: 300px; overflow: auto;">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>student_id</th>
                                    <th>student_name</th>
                                    <th>gender</th>
                                    <th>major</th>
                                    <th>class_name</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>STU001234</td>
                                    <td>张三</td>
                                    <td>男</td>
                                    <td>计算机科学与技术</td>
                                    <td>计科2021-1班</td>
                                </tr>
                                <tr>
                                    <td>STU001235</td>
                                    <td>李四</td>
                                    <td>女</td>
                                    <td>软件工程</td>
                                    <td>软工2021-1班</td>
                                </tr>
                                <tr>
                                    <td>STU001236</td>
                                    <td>王五</td>
                                    <td>男</td>
                                    <td>信息安全</td>
                                    <td>信安2021-1班</td>
                                </tr>
                            </tbody>
                        </table>
                        <p style="margin-top: 10px; color: #666; font-size: 12px;">
                            查询结果：3条记录，执行时间：0.05秒
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据开放门户面板 -->
        <div id="portal" class="content-panel">
            <h2>统一数据开放服务门户</h2>
            <div class="alert alert-info">
                具备全文检索和分类搜索数据资源的功能，支持API接口、数据库推送、文件导出等多种数据开放方式
            </div>

            <div class="search-box">
                <input type="text" class="form-control" placeholder="输入数据资源名称或数据项名称进行全局检索..." id="globalSearch">
                <select class="form-control" style="flex: 0 0 150px;">
                    <option>全部分类</option>
                    <option>学生管理域</option>
                    <option>教师管理域</option>
                    <option>教学管理域</option>
                    <option>科研管理域</option>
                </select>
                <select class="form-control" style="flex: 0 0 120px;">
                    <option>全部方式</option>
                    <option>API接口</option>
                    <option>数据库推送</option>
                    <option>文件导出</option>
                    <option>在线查询</option>
                </select>
                <select class="form-control" style="flex: 0 0 120px;">
                    <option>全部单位</option>
                    <option>学生处</option>
                    <option>教务处</option>
                    <option>人事处</option>
                    <option>科研处</option>
                </select>
                <button class="btn btn-primary" onclick="searchResources()">搜索</button>
            </div>

            <div class="resource-card">
                <div class="resource-header">
                    <div class="resource-title">学生基本信息数据资源</div>
                    <div class="resource-code">STU_BASIC_001</div>
                </div>
                <div class="resource-meta">
                    <div class="meta-item">
                        <span class="meta-label">责任单位：</span>
                        <span class="meta-value">学生处</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">共享类型：</span>
                        <span class="meta-value">有条件共享</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">使用方式：</span>
                        <span class="meta-value">API接口、在线查询</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">数据项数量：</span>
                        <span class="meta-value">15个</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">数据量：</span>
                        <span class="meta-value">15,200条</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">更新频率：</span>
                        <span class="meta-value">实时</span>
                    </div>
                </div>
                <div class="meta-item">
                    <span class="meta-label">描述：</span>
                    <span class="meta-value">包含学生的基本个人信息，如姓名、性别、出生日期、身份证号等核心字段，支持学生管理相关业务</span>
                </div>
                <div style="margin-top: 15px;">
                    <button class="btn btn-info" onclick="viewResourceDetail('STU_BASIC_001')">查看详情</button>
                    <button class="btn btn-primary" onclick="applyResource('STU_BASIC_001')">直接申请</button>
                    <button class="btn btn-warning" onclick="addToCart('STU_BASIC_001')">加入申请清单</button>
                    <button class="btn btn-success" onclick="requestExchange('STU_BASIC_001')">发起数据交换</button>
                </div>
            </div>

            <div class="resource-card">
                <div class="resource-header">
                    <div class="resource-title">教师基本信息数据资源</div>
                    <div class="resource-code">TEA_BASIC_001</div>
                </div>
                <div class="resource-meta">
                    <div class="meta-item">
                        <span class="meta-label">责任单位：</span>
                        <span class="meta-value">人事处</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">共享类型：</span>
                        <span class="meta-value">无条件共享</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">使用方式：</span>
                        <span class="meta-value">数据库推送、文件导出</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">数据项数量：</span>
                        <span class="meta-value">12个</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">数据量：</span>
                        <span class="meta-value">1,230条</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">更新频率：</span>
                        <span class="meta-value">每日</span>
                    </div>
                </div>
                <div class="meta-item">
                    <span class="meta-label">描述：</span>
                    <span class="meta-value">包含教师的基本个人信息和职业信息，支持教学管理和人事管理业务</span>
                </div>
                <div style="margin-top: 15px;">
                    <button class="btn btn-info" onclick="viewResourceDetail('TEA_BASIC_001')">查看详情</button>
                    <button class="btn btn-primary" onclick="applyResource('TEA_BASIC_001')">直接申请</button>
                    <button class="btn btn-warning" onclick="addToCart('TEA_BASIC_001')">加入申请清单</button>
                    <button class="btn btn-success" onclick="requestExchange('TEA_BASIC_001')">发起数据交换</button>
                </div>
            </div>

            <div class="resource-card">
                <div class="resource-header">
                    <div class="resource-title">学生成绩信息数据资源</div>
                    <div class="resource-code">STU_SCORE_001</div>
                </div>
                <div class="resource-meta">
                    <div class="meta-item">
                        <span class="meta-label">责任单位：</span>
                        <span class="meta-value">教务处</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">共享类型：</span>
                        <span class="meta-value">有条件共享</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">使用方式：</span>
                        <span class="meta-value">Excel下载、OData接口</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">数据项数量：</span>
                        <span class="meta-value">18个</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">数据量：</span>
                        <span class="meta-value">45,678条</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">更新频率：</span>
                        <span class="meta-value">每学期</span>
                    </div>
                </div>
                <div class="meta-item">
                    <span class="meta-label">描述：</span>
                    <span class="meta-value">包含学生的各科成绩信息，支持成绩分析和教学质量评估</span>
                </div>
                <div style="margin-top: 15px;">
                    <button class="btn btn-info" onclick="viewResourceDetail('STU_SCORE_001')">查看详情</button>
                    <button class="btn btn-primary" onclick="applyResource('STU_SCORE_001')">直接申请</button>
                    <button class="btn btn-warning" onclick="addToCart('STU_SCORE_001')">加入申请清单</button>
                    <button class="btn btn-success" onclick="requestExchange('STU_SCORE_001')">发起数据交换</button>
                </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button class="btn btn-warning" onclick="viewCart()">查看申请清单 (3)</button>
                <button class="btn btn-success" onclick="batchApply()">批量申请</button>
            </div>
        </div>

        <!-- 申请管理面板 -->
        <div id="applications" class="content-panel">
            <h2>数据资源申请管理</h2>
            <div class="alert alert-info">
                支持单位用户对数据资源的申请使用，申请方式支持直接提交申请，或加入到待申请清单中
            </div>

            <div class="sub-tabs">
                <button class="sub-tab active" onclick="showSubPanel('my-applications')">我的申请</button>
                <button class="sub-tab" onclick="showSubPanel('application-cart')">申请清单</button>
                <button class="sub-tab" onclick="showSubPanel('new-application')">新建申请</button>
            </div>

            <div id="my-applications" class="tab-content">
                <h3>我的申请记录</h3>
                <div class="search-box">
                    <input type="text" class="form-control" placeholder="搜索申请记录...">
                    <select class="form-control" style="flex: 0 0 120px;">
                        <option>全部状态</option>
                        <option>待审核</option>
                        <option>已通过</option>
                        <option>已拒绝</option>
                    </select>
                    <button class="btn btn-primary">搜索</button>
                </div>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>申请编号</th>
                            <th>申请资源</th>
                            <th>使用方式</th>
                            <th>申请时间</th>
                            <th>审核状态</th>
                            <th>审核进度</th>
                            <th>有效期至</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>APP20240115001</td>
                            <td>学生基本信息数据资源</td>
                            <td>在线查询、Excel下载</td>
                            <td>2024-01-15 09:30</td>
                            <td><span class="status-badge status-pending">待审核</span></td>
                            <td>2/4</td>
                            <td>2024-12-31</td>
                            <td>
                                <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;" onclick="viewApplicationDetail('APP20240115001')">查看详情</button>
                                <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">撤回</button>
                            </td>
                        </tr>
                        <tr>
                            <td>APP20240114002</td>
                            <td>教师基本信息数据资源</td>
                            <td>OData接口</td>
                            <td>2024-01-14 14:20</td>
                            <td><span class="status-badge status-approved">已通过</span></td>
                            <td>4/4</td>
                            <td>2024-06-30</td>
                            <td>
                                <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">开始使用</button>
                                <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;" onclick="viewApplicationDetail('APP20240114002')">查看详情</button>
                            </td>
                        </tr>
                        <tr>
                            <td>APP20240113003</td>
                            <td>学生成绩信息数据资源</td>
                            <td>ETL获取</td>
                            <td>2024-01-13 16:45</td>
                            <td><span class="status-badge status-rejected">已拒绝</span></td>
                            <td>3/4</td>
                            <td>-</td>
                            <td>
                                <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;" onclick="viewApplicationDetail('APP20240113003')">查看原因</button>
                                <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">重新申请</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div id="application-cart" class="tab-content" style="display: none;">
                <h3>待申请清单</h3>
                <div class="alert alert-warning">
                    您的申请清单中有 3 个数据资源，可以批量提交申请
                </div>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>资源名称</th>
                            <th>资源编码</th>
                            <th>责任单位</th>
                            <th>预期使用方式</th>
                            <th>加入时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>学生基本信息数据资源</td>
                            <td>STU_BASIC_001</td>
                            <td>学生处</td>
                            <td>在线查询</td>
                            <td>2024-01-15 10:30</td>
                            <td>
                                <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">配置申请</button>
                                <button class="btn btn-danger" style="padding: 6px 12px; font-size: 12px;">移除</button>
                            </td>
                        </tr>
                        <tr>
                            <td>教师基本信息数据资源</td>
                            <td>TEA_BASIC_001</td>
                            <td>人事处</td>
                            <td>Excel下载</td>
                            <td>2024-01-15 11:15</td>
                            <td>
                                <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">配置申请</button>
                                <button class="btn btn-danger" style="padding: 6px 12px; font-size: 12px;">移除</button>
                            </td>
                        </tr>
                        <tr>
                            <td>学生成绩信息数据资源</td>
                            <td>STU_SCORE_001</td>
                            <td>教务处</td>
                            <td>OData接口</td>
                            <td>2024-01-15 12:00</td>
                            <td>
                                <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">配置申请</button>
                                <button class="btn btn-danger" style="padding: 6px 12px; font-size: 12px;">移除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-success">批量提交申请</button>
                    <button class="btn btn-warning">清空清单</button>
                </div>
            </div>

            <div id="new-application" class="tab-content" style="display: none;">
                <h3>新建数据资源申请</h3>
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>申请资源</label>
                            <select class="form-control">
                                <option>学生基本信息数据资源</option>
                                <option>教师基本信息数据资源</option>
                                <option>学生成绩信息数据资源</option>
                                <option>课程信息数据资源</option>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>使用方式</label>
                            <div>
                                <label><input type="checkbox"> 在线查询</label><br>
                                <label><input type="checkbox"> Excel下载</label><br>
                                <label><input type="checkbox"> OData接口获取</label><br>
                                <label><input type="checkbox"> ETL获取数据</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>使用期限</label>
                            <select class="form-control">
                                <option>3个月</option>
                                <option>6个月</option>
                                <option>1年</option>
                                <option>长期</option>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>使用目的</label>
                            <select class="form-control">
                                <option>教学管理</option>
                                <option>科研分析</option>
                                <option>统计报告</option>
                                <option>决策支持</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>申请理由</label>
                    <textarea class="form-control" rows="4" placeholder="请详细说明申请理由和使用场景"></textarea>
                </div>

                <div class="form-group">
                    <button class="btn btn-primary">提交申请</button>
                    <button class="btn btn-warning">加入申请清单</button>
                    <button class="btn btn-info">保存草稿</button>
                </div>
            </div>
        </div>

        <!-- 审核中心面板 -->
        <div id="approval" class="content-panel">
            <h2>数据申请审核中心</h2>
            <div class="alert alert-info">
                支持多种审核方式：方式一为用户仅能同意或不同意，方式二为支持用户在审核时修改数据申请内容
            </div>

            <div class="search-box">
                <input type="text" class="form-control" placeholder="搜索待审核申请...">
                <select class="form-control" style="flex: 0 0 120px;">
                    <option>全部状态</option>
                    <option>待我审核</option>
                    <option>我已审核</option>
                </select>
                <button class="btn btn-primary">搜索</button>
            </div>

            <div class="resource-card">
                <div class="resource-header">
                    <div class="resource-title">数据申请审核 - APP20240115001</div>
                    <div class="resource-code">待审核</div>
                </div>
                <div class="resource-meta">
                    <div class="meta-item">
                        <span class="meta-label">申请单位：</span>
                        <span class="meta-value">教务处</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">申请人：</span>
                        <span class="meta-value">张三</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">申请资源：</span>
                        <span class="meta-value">学生基本信息数据资源</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">使用方式：</span>
                        <span class="meta-value">在线查询、Excel下载</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">使用期限：</span>
                        <span class="meta-value">1年</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">申请时间：</span>
                        <span class="meta-value">2024-01-15 09:30</span>
                    </div>
                </div>
                <div class="meta-item">
                    <span class="meta-label">申请理由：</span>
                    <span class="meta-value">用于教学管理系统的学生信息查询和统计分析，提高教学管理效率</span>
                </div>

                <div class="approval-form">
                    <h4>审核方式选择</h4>
                    <div class="approval-mode">
                        <label>
                            <input type="radio" name="approvalMode" value="simple" checked onchange="toggleApprovalMode()">
                            方式一：简单审核（仅同意或不同意）
                        </label>
                        <label>
                            <input type="radio" name="approvalMode" value="advanced" onchange="toggleApprovalMode()">
                            方式二：高级审核（可修改申请内容）
                        </label>
                    </div>

                    <div id="simpleApproval">
                        <div class="form-group">
                            <label>审核意见</label>
                            <textarea class="form-control" rows="3" placeholder="请输入审核意见"></textarea>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-success">同意申请</button>
                            <button class="btn btn-danger">拒绝申请</button>
                        </div>
                    </div>

                    <div id="advancedApproval" style="display: none;">
                        <div class="row">
                            <div class="col">
                                <div class="form-group">
                                    <label>修改使用时限</label>
                                    <select class="form-control">
                                        <option>保持原申请（1年）</option>
                                        <option>3个月</option>
                                        <option>6个月</option>
                                        <option>2年</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <label>修改使用方式</label>
                                    <div>
                                        <label><input type="checkbox" checked> 在线查询</label><br>
                                        <label><input type="checkbox" checked> Excel下载</label><br>
                                        <label><input type="checkbox"> OData接口</label><br>
                                        <label><input type="checkbox"> ETL获取</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>修改数据项范围</label>
                            <div style="max-height: 150px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                                <label><input type="checkbox" checked> student_id - 学生ID</label><br>
                                <label><input type="checkbox" checked> student_name - 学生姓名</label><br>
                                <label><input type="checkbox"> id_card - 身份证号</label><br>
                                <label><input type="checkbox" checked> major - 专业</label><br>
                                <label><input type="checkbox" checked> class_name - 班级</label><br>
                                <label><input type="checkbox"> phone - 联系电话</label><br>
                                <label><input type="checkbox"> email - 邮箱地址</label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>安全策略</label>
                            <select class="form-control">
                                <option>标准安全策略</option>
                                <option>高级安全策略</option>
                                <option>自定义安全策略</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>审核意见</label>
                            <textarea class="form-control" rows="3" placeholder="请输入审核意见和修改说明"></textarea>
                        </div>

                        <div class="form-group">
                            <button class="btn btn-success">同意申请（含修改）</button>
                            <button class="btn btn-danger">拒绝申请</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 待办事项面板 -->
        <div id="todos" class="content-panel">
            <h2>待办事项</h2>
            <div class="alert alert-info">
                支持用户查看待办和已办事项，包括审核数据资源申请和配置工单
            </div>

            <div class="sub-tabs">
                <button class="sub-tab active" onclick="showSubPanel('pending-todos')">待办事项</button>
                <button class="sub-tab" onclick="showSubPanel('completed-todos')">已办事项</button>
            </div>

            <div id="pending-todos" class="tab-content">
                <h3>待办事项列表</h3>

                <div class="todo-item">
                    <div class="todo-header">
                        <div class="todo-title">审核数据资源申请 - APP20240115001</div>
                        <div class="todo-priority priority-high">高优先级</div>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">申请人：</span>
                        <span class="meta-value">张三（教务处）</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">申请资源：</span>
                        <span class="meta-value">学生基本信息数据资源</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">提交时间：</span>
                        <span class="meta-value">2024-01-15 09:30</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">截止时间：</span>
                        <span class="meta-value">2024-01-17 18:00</span>
                    </div>
                    <div style="margin-top: 10px;">
                        <button class="btn btn-primary" onclick="handleTodo('APP20240115001')">立即处理</button>
                        <button class="btn btn-info">查看详情</button>
                    </div>
                </div>

                <div class="todo-item">
                    <div class="todo-header">
                        <div class="todo-title">配置ETL工单 - WO20240115002</div>
                        <div class="todo-priority priority-medium">中优先级</div>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">关联申请：</span>
                        <span class="meta-value">APP20240114002（教师基本信息数据资源）</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">工单类型：</span>
                        <span class="meta-value">ETL任务配置</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">生成时间：</span>
                        <span class="meta-value">2024-01-14 16:30</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">截止时间：</span>
                        <span class="meta-value">2024-01-16 18:00</span>
                    </div>
                    <div style="margin-top: 10px;">
                        <button class="btn btn-primary" onclick="configureWorkOrder('WO20240115002')">配置工单</button>
                        <button class="btn btn-info">查看详情</button>
                    </div>
                </div>

                <div class="todo-item">
                    <div class="todo-header">
                        <div class="todo-title">审核数据资源申请 - APP20240115003</div>
                        <div class="todo-priority priority-low">低优先级</div>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">申请人：</span>
                        <span class="meta-value">李四（科研处）</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">申请资源：</span>
                        <span class="meta-value">科研项目信息数据资源</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">提交时间：</span>
                        <span class="meta-value">2024-01-15 14:20</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">截止时间：</span>
                        <span class="meta-value">2024-01-18 18:00</span>
                    </div>
                    <div style="margin-top: 10px;">
                        <button class="btn btn-primary" onclick="handleTodo('APP20240115003')">立即处理</button>
                        <button class="btn btn-info">查看详情</button>
                    </div>
                </div>
            </div>

            <div id="completed-todos" class="tab-content" style="display: none;">
                <h3>已办事项列表</h3>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>事项类型</th>
                            <th>事项标题</th>
                            <th>关联对象</th>
                            <th>处理时间</th>
                            <th>处理结果</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>申请审核</td>
                            <td>审核数据资源申请</td>
                            <td>APP20240114001</td>
                            <td>2024-01-14 15:30</td>
                            <td><span class="status-badge status-approved">已通过</span></td>
                            <td>
                                <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看详情</button>
                            </td>
                        </tr>
                        <tr>
                            <td>工单配置</td>
                            <td>配置ETL工单</td>
                            <td>WO20240114001</td>
                            <td>2024-01-14 16:45</td>
                            <td><span class="status-badge status-approved">已完成</span></td>
                            <td>
                                <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看详情</button>
                            </td>
                        </tr>
                        <tr>
                            <td>申请审核</td>
                            <td>审核数据资源申请</td>
                            <td>APP20240113001</td>
                            <td>2024-01-13 17:20</td>
                            <td><span class="status-badge status-rejected">已拒绝</span></td>
                            <td>
                                <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看详情</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 工单配置面板 -->
        <div id="workorders" class="content-panel">
            <h2>工单配置提醒</h2>
            <div class="alert alert-info">
                在数据资源申请审核完成后，系统将自动生成一个与ETL任务有关的配置工单，提醒用户完成该工单的配置工作
            </div>

            <div class="alert alert-warning">
                <strong>系统提醒：</strong>您有 2 个ETL配置工单需要处理，请及时完成配置以确保数据服务正常运行
            </div>

            <div class="resource-card">
                <div class="resource-header">
                    <div class="resource-title">ETL配置工单 - WO20240115002</div>
                    <div class="resource-code">待配置</div>
                </div>
                <div class="resource-meta">
                    <div class="meta-item">
                        <span class="meta-label">关联申请：</span>
                        <span class="meta-value">APP20240114002</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">申请资源：</span>
                        <span class="meta-value">教师基本信息数据资源</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">申请单位：</span>
                        <span class="meta-value">科研处</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">使用方式：</span>
                        <span class="meta-value">ETL获取数据</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">生成时间：</span>
                        <span class="meta-value">2024-01-14 16:30</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">截止时间：</span>
                        <span class="meta-value">2024-01-16 18:00</span>
                    </div>
                </div>

                <h4>ETL任务配置</h4>
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>源数据库</label>
                            <select class="form-control">
                                <option>人事管理系统数据库</option>
                                <option>数据中心</option>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>目标数据库</label>
                            <select class="form-control">
                                <option>科研处数据库</option>
                                <option>共享数据库</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>同步频率</label>
                            <select class="form-control">
                                <option>实时同步</option>
                                <option>每小时</option>
                                <option>每日</option>
                                <option>每周</option>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>同步时间</label>
                            <input type="time" class="form-control" value="02:00">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>数据转换规则</label>
                    <textarea class="form-control" rows="4" placeholder="请输入数据转换规则">-- 教师信息ETL转换规则
SELECT
    teacher_id,
    teacher_name,
    department,
    title,
    research_area
FROM source_teacher_info
WHERE status = 'active'</textarea>
                </div>

                <div style="margin-top: 15px;">
                    <button class="btn btn-success">完成配置</button>
                    <button class="btn btn-primary">测试连接</button>
                    <button class="btn btn-info">保存草稿</button>
                    <button class="btn btn-warning">延期申请</button>
                </div>
            </div>

            <h3>工单配置历史</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>工单编号</th>
                        <th>关联申请</th>
                        <th>申请资源</th>
                        <th>配置类型</th>
                        <th>生成时间</th>
                        <th>完成时间</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>WO20240115002</td>
                        <td>APP20240114002</td>
                        <td>教师基本信息数据资源</td>
                        <td>ETL任务配置</td>
                        <td>2024-01-14 16:30</td>
                        <td>-</td>
                        <td><span class="status-badge status-pending">待配置</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">立即配置</button>
                        </td>
                    </tr>
                    <tr>
                        <td>WO20240114001</td>
                        <td>APP20240113001</td>
                        <td>学生基本信息数据资源</td>
                        <td>ETL任务配置</td>
                        <td>2024-01-13 15:20</td>
                        <td>2024-01-14 16:45</td>
                        <td><span class="status-badge status-approved">已完成</span></td>
                        <td>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看配置</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 资源详情模态框 -->
    <div id="resourceDetailModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeResourceDetailModal()">&times;</span>
            <h3 id="resourceDetailTitle">数据资源详情</h3>

            <div class="sub-tabs">
                <button class="sub-tab active" onclick="showDetailTab('basic-info')">基本属性</button>
                <button class="sub-tab" onclick="showDetailTab('data-items')">数据项</button>
                <button class="sub-tab" onclick="showDetailTab('data-subset')">数据子集</button>
                <button class="sub-tab" onclick="showDetailTab('sample-data')">样例数据</button>
                <button class="sub-tab" onclick="showDetailTab('security-info')">安全信息</button>
            </div>

            <div id="basic-info" class="tab-content">
                <h4>基本属性信息</h4>
                <div class="resource-meta">
                    <div class="meta-item">
                        <span class="meta-label">资源编码：</span>
                        <span class="meta-value">STU_BASIC_001</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">资源名称：</span>
                        <span class="meta-value">学生基本信息数据资源</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">责任单位：</span>
                        <span class="meta-value">学生处</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">共享类型：</span>
                        <span class="meta-value">有条件共享</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">使用方式：</span>
                        <span class="meta-value">API接口、在线查询、Excel下载</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">数据量：</span>
                        <span class="meta-value">15,200条</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">更新频率：</span>
                        <span class="meta-value">实时</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">创建时间：</span>
                        <span class="meta-value">2024-01-01</span>
                    </div>
                </div>
                <div class="meta-item">
                    <span class="meta-label">资源描述：</span>
                    <span class="meta-value">包含学生的基本个人信息，如姓名、性别、出生日期、身份证号等核心字段，支持学生管理相关业务</span>
                </div>
            </div>

            <div id="data-items" class="tab-content" style="display: none;">
                <h4>数据项列表</h4>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>字段名</th>
                            <th>数据项名称</th>
                            <th>数据类型</th>
                            <th>长度</th>
                            <th>是否必填</th>
                            <th>描述</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>student_id</td>
                            <td>学生ID</td>
                            <td>VARCHAR</td>
                            <td>20</td>
                            <td>是</td>
                            <td>学生唯一标识</td>
                        </tr>
                        <tr>
                            <td>student_name</td>
                            <td>学生姓名</td>
                            <td>VARCHAR</td>
                            <td>50</td>
                            <td>是</td>
                            <td>学生真实姓名</td>
                        </tr>
                        <tr>
                            <td>gender</td>
                            <td>性别</td>
                            <td>CHAR</td>
                            <td>1</td>
                            <td>是</td>
                            <td>M-男，F-女</td>
                        </tr>
                        <tr>
                            <td>birth_date</td>
                            <td>出生日期</td>
                            <td>DATE</td>
                            <td>-</td>
                            <td>是</td>
                            <td>学生出生日期</td>
                        </tr>
                        <tr>
                            <td>major</td>
                            <td>专业</td>
                            <td>VARCHAR</td>
                            <td>100</td>
                            <td>是</td>
                            <td>学生所学专业</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div id="data-subset" class="tab-content" style="display: none;">
                <h4>数据子集</h4>
                <p>根据不同的业务需求，可以配置不同的数据子集：</p>
                <ul>
                    <li><strong>基础信息子集：</strong>包含学生ID、姓名、性别、专业等基本信息</li>
                    <li><strong>联系信息子集：</strong>包含电话、邮箱、地址等联系方式</li>
                    <li><strong>学籍信息子集：</strong>包含入学时间、班级、学籍状态等信息</li>
                </ul>
            </div>

            <div id="sample-data" class="tab-content" style="display: none;">
                <h4>样例数据</h4>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>student_id</th>
                            <th>student_name</th>
                            <th>gender</th>
                            <th>major</th>
                            <th>class_name</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>STU001234</td>
                            <td>张***</td>
                            <td>男</td>
                            <td>计算机科学与技术</td>
                            <td>计科2021-1班</td>
                        </tr>
                        <tr>
                            <td>STU001235</td>
                            <td>李***</td>
                            <td>女</td>
                            <td>软件工程</td>
                            <td>软工2021-1班</td>
                        </tr>
                    </tbody>
                </table>
                <p style="margin-top: 10px; color: #666; font-size: 12px;">
                    注：样例数据已脱敏处理，仅供参考
                </p>
            </div>

            <div id="security-info" class="tab-content" style="display: none;">
                <h4>安全信息</h4>
                <div class="resource-meta">
                    <div class="meta-item">
                        <span class="meta-label">安全级别：</span>
                        <span class="meta-value">敏感</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">访问控制：</span>
                        <span class="meta-value">基于角色的访问控制</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">加密方式：</span>
                        <span class="meta-value">AES-256</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">审计日志：</span>
                        <span class="meta-value">启用</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">数据脱敏：</span>
                        <span class="meta-value">身份证号、电话号码</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">备份策略：</span>
                        <span class="meta-value">每日增量备份</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedFields = [];
        let currentApprovalMode = 'simple';

        // 切换面板显示
        function showPanel(panelId) {
            // 隐藏所有面板
            const panels = document.querySelectorAll('.content-panel');
            panels.forEach(panel => panel.classList.remove('active'));

            // 移除所有标签的active状态
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的面板
            document.getElementById(panelId).classList.add('active');

            // 激活对应的标签
            event.target.classList.add('active');
        }

        // 切换子面板
        function showSubPanel(subPanelId) {
            // 隐藏当前面板下的所有子内容
            const currentPanel = document.querySelector('.content-panel.active');
            const subContents = currentPanel.querySelectorAll('.tab-content');
            subContents.forEach(content => content.style.display = 'none');

            // 移除所有子标签的active状态
            const subTabs = currentPanel.querySelectorAll('.sub-tab');
            subTabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的子内容
            const targetContent = currentPanel.querySelector(`#${subPanelId}`);
            if (targetContent) {
                targetContent.style.display = 'block';
            }

            // 激活对应的子标签
            event.target.classList.add('active');
        }

        // API管理功能
        function editAPI(apiId) {
            alert('编辑API: ' + apiId);
        }

        function deleteAPI(apiId) {
            if (confirm('确定要删除此API吗？')) {
                alert('API已删除: ' + apiId);
            }
        }

        // SQL构建器功能
        function loadTables(dataSource) {
            console.log('加载数据表:', dataSource);
        }

        function loadFields(tableName) {
            console.log('加载字段:', tableName);
        }

        function selectField(element, fieldName) {
            element.classList.toggle('selected');
            if (selectedFields.includes(fieldName)) {
                selectedFields = selectedFields.filter(f => f !== fieldName);
            } else {
                selectedFields.push(fieldName);
            }
            console.log('选中字段:', selectedFields);
        }

        function generateSQL() {
            if (selectedFields.length === 0) {
                alert('请先选择字段');
                return;
            }
            const sql = `SELECT ${selectedFields.join(', ')}\nFROM students\nWHERE status = 'active'\nORDER BY student_id;`;
            document.getElementById('sqlEditor').textContent = sql;
        }

        function executeSQL() {
            alert('执行SQL查询...');
        }

        function clearSQL() {
            document.getElementById('sqlEditor').textContent = '';
        }

        function saveSQL() {
            alert('SQL查询已保存');
        }

        // 数据门户功能
        function searchResources() {
            const searchTerm = document.getElementById('globalSearch').value;
            console.log('搜索资源:', searchTerm);
            alert('搜索功能执行中...');
        }

        function viewResourceDetail(resourceId) {
            document.getElementById('resourceDetailModal').style.display = 'block';
            console.log('查看资源详情:', resourceId);
        }

        function closeResourceDetailModal() {
            document.getElementById('resourceDetailModal').style.display = 'none';
        }

        function showDetailTab(tabId) {
            // 隐藏所有标签内容
            const modal = document.getElementById('resourceDetailModal');
            const tabContents = modal.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.style.display = 'none');

            // 移除所有标签的active状态
            const tabs = modal.querySelectorAll('.sub-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的标签内容
            document.getElementById(tabId).style.display = 'block';

            // 激活对应的标签
            event.target.classList.add('active');
        }

        function applyResource(resourceId) {
            alert('直接申请资源: ' + resourceId);
        }

        function addToCart(resourceId) {
            alert('已加入申请清单: ' + resourceId);
        }

        function requestExchange(resourceId) {
            alert('发起数据交换申请: ' + resourceId);
        }

        function viewCart() {
            alert('查看申请清单');
        }

        function batchApply() {
            alert('批量申请提交');
        }

        // 申请管理功能
        function viewApplicationDetail(appId) {
            alert('查看申请详情: ' + appId);
        }

        // 审核功能
        function toggleApprovalMode() {
            const mode = document.querySelector('input[name="approvalMode"]:checked').value;
            currentApprovalMode = mode;

            if (mode === 'simple') {
                document.getElementById('simpleApproval').style.display = 'block';
                document.getElementById('advancedApproval').style.display = 'none';
            } else {
                document.getElementById('simpleApproval').style.display = 'none';
                document.getElementById('advancedApproval').style.display = 'block';
            }
        }

        // 待办事项功能
        function handleTodo(todoId) {
            alert('处理待办事项: ' + todoId);
        }

        function configureWorkOrder(workOrderId) {
            alert('配置工单: ' + workOrderId);
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('resourceDetailModal');
            if (event.target === modal) {
                closeResourceDetailModal();
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('数据共享子系统已加载');
        });
    </script>
</body>
</html>
