<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>业务驾驶舱测试</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .dashboard-card {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-title {
            text-align: center;
            margin-bottom: 20px;
            font-size: 16px;
            font-weight: bold;
        }
        .chart-small {
            height: 200px;
        }
        canvas {
            max-height: 200px;
        }
        .row {
            display: flex;
            gap: 20px;
        }
        .col {
            flex: 1;
        }
    </style>
</head>
<body>
    <h1>业务驾驶舱图表测试</h1>
    
    <div class="dashboard-card">
        <h3>办公室业务指标</h3>
        <p>重点事项督办: 23项</p>
        <p>信息报送: 156份</p>
        <p>人员在位: 186人</p>
        <p>用印情况: 45次</p>
    </div>
    
    <div class="row">
        <div class="col">
            <div class="chart-container">
                <div class="chart-title">重点事项督办完成趋势</div>
                <div class="chart-small">
                    <canvas id="taskChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="chart-container">
                <div class="chart-title">信息报送类型分布</div>
                <div class="chart-small">
                    <canvas id="reportChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col">
            <div class="chart-container">
                <div class="chart-title">教学成果统计</div>
                <div class="chart-small">
                    <canvas id="academicChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="chart-container">
                <div class="chart-title">科研项目分布</div>
                <div class="chart-small">
                    <canvas id="researchChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 等待页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始初始化图表...');
            
            // 检查Chart.js是否加载
            if (typeof Chart === 'undefined') {
                console.error('Chart.js未加载');
                return;
            }
            
            // 基础配置
            const baseConfig = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            };
            
            // 1. 重点事项督办趋势图
            const taskCtx = document.getElementById('taskChart');
            if (taskCtx) {
                new Chart(taskCtx, {
                    type: 'line',
                    data: {
                        labels: ['周一', '周二', '周三', '周四', '周五'],
                        datasets: [{
                            label: '完成事项',
                            data: [8, 12, 15, 18, 23],
                            borderColor: '#1e3c72',
                            backgroundColor: 'rgba(30, 60, 114, 0.1)',
                            fill: false
                        }, {
                            label: '新增事项',
                            data: [5, 8, 6, 9, 7],
                            borderColor: '#2a5298',
                            backgroundColor: 'rgba(42, 82, 152, 0.1)',
                            fill: false
                        }]
                    },
                    options: baseConfig
                });
                console.log('重点事项督办图表创建成功');
            }
            
            // 2. 信息报送类型分布
            const reportCtx = document.getElementById('reportChart');
            if (reportCtx) {
                new Chart(reportCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['工作简报', '统计报表', '专项报告', '会议纪要', '其他'],
                        datasets: [{
                            data: [45, 32, 28, 25, 26],
                            backgroundColor: [
                                '#1e3c72',
                                '#2a5298',
                                '#4a90e2',
                                '#7bb3f0',
                                '#a8d0f7'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
                console.log('信息报送分布图表创建成功');
            }
            
            // 3. 教学成果统计
            const academicCtx = document.getElementById('academicChart');
            if (academicCtx) {
                new Chart(academicCtx, {
                    type: 'bar',
                    data: {
                        labels: ['国家级', '省部级', '校级', '院级'],
                        datasets: [{
                            label: '教学成果奖',
                            data: [3, 8, 15, 23],
                            backgroundColor: '#1e3c72'
                        }, {
                            label: '教学竞赛奖',
                            data: [2, 12, 18, 28],
                            backgroundColor: '#2a5298'
                        }]
                    },
                    options: baseConfig
                });
                console.log('教学成果图表创建成功');
            }
            
            // 4. 科研项目分布
            const researchCtx = document.getElementById('researchChart');
            if (researchCtx) {
                new Chart(researchCtx, {
                    type: 'pie',
                    data: {
                        labels: ['国家级', '省部级', '军队级', '企业合作', '其他'],
                        datasets: [{
                            data: [26, 35, 28, 18, 8],
                            backgroundColor: [
                                '#1e3c72',
                                '#2a5298',
                                '#4a90e2',
                                '#7bb3f0',
                                '#a8d0f7'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
                console.log('科研项目分布图表创建成功');
            }
            
            console.log('所有图表初始化完成');
        });
    </script>
</body>
</html>
