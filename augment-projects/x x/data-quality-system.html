<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据质量管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            flex-wrap: wrap;
        }
        
        .nav-tab {
            flex: 1;
            min-width: 140px;
            padding: 15px 10px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 13px;
            font-weight: 500;
            text-align: center;
        }
        
        .nav-tab.active {
            background: #667eea;
            color: white;
        }
        
        .nav-tab:hover {
            background: #5a6fd8;
            color: white;
        }
        
        .content-panel {
            display: none;
            background: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .content-panel.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 13px;
        }
        
        .data-table th,
        .data-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            position: sticky;
            top: 0;
        }
        
        .data-table tr:hover {
            background: #f8f9ff;
        }
        
        .row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .col {
            flex: 1;
        }
        
        .col-2 {
            flex: 2;
        }
        
        .col-3 {
            flex: 3;
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .status-running {
            background: #d4edda;
            color: #155724;
        }
        
        .status-completed {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .quality-score {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .score-excellent {
            background: #d4edda;
            color: #155724;
        }
        
        .score-good {
            background: #cce5ff;
            color: #004085;
        }
        
        .score-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .score-danger {
            background: #f8d7da;
            color: #721c24;
        }
        
        .rule-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .rule-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .rule-card:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
        }
        
        .rule-card.selected {
            border-color: #667eea;
            background: #e3f2fd;
        }
        
        .rule-card h4 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .threshold-config {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .threshold-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        
        .threshold-high {
            border-color: #dc3545;
            background: #f8d7da;
        }
        
        .threshold-medium {
            border-color: #ffc107;
            background: #fff3cd;
        }
        
        .threshold-low {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .report-config {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .condition-builder {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .search-box {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .search-box input {
            flex: 1;
        }
        
        .sub-tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        
        .sub-tab {
            padding: 10px 20px;
            background: none;
            border: none;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .sub-tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: 500;
        }
        
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            transition: width 0.3s;
        }
        
        .progress-excellent {
            background: #28a745;
        }
        
        .progress-good {
            background: #17a2b8;
        }
        
        .progress-warning {
            background: #ffc107;
        }
        
        .progress-danger {
            background: #dc3545;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .report-section {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .report-section h3 {
            color: #667eea;
            margin-bottom: 15px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>数据质量管理系统</h1>
        <p>预配置质量检测规则、自定义质检方案、智能预警阈值、自动化报告生成的一体化平台</p>
    </div>

    <div class="container">
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showPanel('rules')">质量检测规则</button>
            <button class="nav-tab" onclick="showPanel('schemes')">质检方案管理</button>
            <button class="nav-tab" onclick="showPanel('thresholds')">预警阈值设置</button>
            <button class="nav-tab" onclick="showPanel('reports')">报告发送策略</button>
            <button class="nav-tab" onclick="showPanel('examples')">典型检测规则</button>
            <button class="nav-tab" onclick="showPanel('dashboard')">质量监控面板</button>
            <button class="nav-tab" onclick="showPanel('report-view')">质量检查报告</button>
        </div>

        <!-- 质量检测规则面板 -->
        <div id="rules" class="content-panel active">
            <h2>数据质量检测规则配置</h2>
            <div class="alert alert-info">
                支持预先配置数据质量检测规则，用于管理、检测、追踪和处理各类数据质量问题
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>规则名称</label>
                        <input type="text" class="form-control" placeholder="请输入质量检测规则名称">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>规则类型</label>
                        <select class="form-control">
                            <option>完整性检测</option>
                            <option>准确性检测</option>
                            <option>一致性检测</option>
                            <option>有效性检测</option>
                            <option>唯一性检测</option>
                            <option>及时性检测</option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>应用表</label>
                        <select class="form-control">
                            <option>dwd_student_info</option>
                            <option>dwd_teacher_info</option>
                            <option>dwd_course_info</option>
                            <option>dwd_score_info</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>检测字段</label>
                        <select class="form-control" multiple>
                            <option>student_id</option>
                            <option>student_name</option>
                            <option>age</option>
                            <option>phone</option>
                            <option>email</option>
                            <option>enrollment_date</option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>严重程度</label>
                        <select class="form-control">
                            <option value="high">高 - 严重影响数据使用</option>
                            <option value="medium">中 - 影响数据质量</option>
                            <option value="low">低 - 轻微影响</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>检测规则表达式</label>
                <textarea class="form-control" rows="4" placeholder="请输入检测规则的SQL表达式或条件">-- 完整性检测示例
SELECT COUNT(*) as null_count
FROM dwd_student_info
WHERE student_name IS NULL OR student_name = ''

-- 准确性检测示例
SELECT COUNT(*) as invalid_age_count
FROM dwd_student_info
WHERE age < 16 OR age > 30</textarea>
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>期望结果</label>
                        <input type="text" class="form-control" placeholder="如：0（表示期望没有问题数据）">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>容忍阈值</label>
                        <input type="number" class="form-control" placeholder="允许的最大问题数量" min="0">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>规则描述</label>
                <textarea class="form-control" rows="3" placeholder="请描述该规则的检测目的和预期效果"></textarea>
            </div>

            <div class="form-group">
                <button class="btn btn-primary">保存规则</button>
                <button class="btn btn-success">测试规则</button>
                <button class="btn btn-info">导入规则模板</button>
            </div>

            <h3>已配置的检测规则</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>规则名称</th>
                        <th>规则类型</th>
                        <th>应用表</th>
                        <th>检测字段</th>
                        <th>严重程度</th>
                        <th>最后检测</th>
                        <th>检测结果</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>学生姓名完整性检测</td>
                        <td>完整性检测</td>
                        <td>dwd_student_info</td>
                        <td>student_name</td>
                        <td><span class="status-badge" style="background: #f8d7da; color: #721c24;">高</span></td>
                        <td>2024-01-15 10:30</td>
                        <td>0/15200</td>
                        <td><span class="status-badge status-completed">正常</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">执行</button>
                        </td>
                    </tr>
                    <tr>
                        <td>学生年龄有效性检测</td>
                        <td>有效性检测</td>
                        <td>dwd_student_info</td>
                        <td>age</td>
                        <td><span class="status-badge" style="background: #fff3cd; color: #856404;">中</span></td>
                        <td>2024-01-15 10:30</td>
                        <td>5/15200</td>
                        <td><span class="status-badge status-failed">异常</span></td>
                        <td>
                            <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">查看问题</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">处理</button>
                        </td>
                    </tr>
                    <tr>
                        <td>手机号格式检测</td>
                        <td>准确性检测</td>
                        <td>dwd_student_info</td>
                        <td>phone</td>
                        <td><span class="status-badge" style="background: #d4edda; color: #155724;">低</span></td>
                        <td>2024-01-15 10:30</td>
                        <td>12/15200</td>
                        <td><span class="status-badge status-failed">异常</span></td>
                        <td>
                            <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">查看问题</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">处理</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 质检方案管理面板 -->
        <div id="schemes" class="content-panel">
            <h2>质检方案管理</h2>
            <div class="alert alert-info">
                支持用户自定义质检方案，支持按日、周、月、年进行任务调度
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>方案名称</label>
                        <input type="text" class="form-control" placeholder="请输入质检方案名称">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>方案类型</label>
                        <select class="form-control">
                            <option>全面质检</option>
                            <option>重点质检</option>
                            <option>专项质检</option>
                            <option>快速质检</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>包含的检测规则</label>
                <div style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                    <label><input type="checkbox" checked> 学生姓名完整性检测</label><br>
                    <label><input type="checkbox" checked> 学生年龄有效性检测</label><br>
                    <label><input type="checkbox"> 手机号格式检测</label><br>
                    <label><input type="checkbox"> 邮箱格式检测</label><br>
                    <label><input type="checkbox"> 学号唯一性检测</label><br>
                    <label><input type="checkbox"> 入学日期一致性检测</label><br>
                    <label><input type="checkbox"> 成绩数据完整性检测</label><br>
                    <label><input type="checkbox"> 课程信息准确性检测</label>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>调度频率</label>
                        <select class="form-control" onchange="updateScheduleConfig(this.value)">
                            <option value="daily">按日执行</option>
                            <option value="weekly">按周执行</option>
                            <option value="monthly">按月执行</option>
                            <option value="yearly">按年执行</option>
                            <option value="custom">自定义</option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>执行时间</label>
                        <input type="time" class="form-control" value="02:00">
                    </div>
                </div>
                <div class="col" id="scheduleDetail">
                    <div class="form-group">
                        <label>执行日期</label>
                        <select class="form-control">
                            <option>每天</option>
                            <option>工作日</option>
                            <option>周末</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>超时设置（分钟）</label>
                        <input type="number" class="form-control" value="60" min="1">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>失败重试次数</label>
                        <input type="number" class="form-control" value="3" min="0" max="10">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>方案描述</label>
                <textarea class="form-control" rows="3" placeholder="请描述该质检方案的目的和适用场景"></textarea>
            </div>

            <div class="form-group">
                <button class="btn btn-primary">保存方案</button>
                <button class="btn btn-success">立即执行</button>
                <button class="btn btn-info">复制方案</button>
            </div>

            <h3>质检方案列表</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>方案名称</th>
                        <th>方案类型</th>
                        <th>包含规则数</th>
                        <th>调度频率</th>
                        <th>下次执行时间</th>
                        <th>最后执行</th>
                        <th>执行状态</th>
                        <th>质量得分</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>学生信息全面质检</td>
                        <td>全面质检</td>
                        <td>8</td>
                        <td>每日</td>
                        <td>2024-01-16 02:00</td>
                        <td>2024-01-15 02:00</td>
                        <td><span class="status-badge status-completed">已完成</span></td>
                        <td><span style="color: #28a745; font-weight: bold;">95.2</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">执行</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">报告</button>
                        </td>
                    </tr>
                    <tr>
                        <td>教师信息重点质检</td>
                        <td>重点质检</td>
                        <td>5</td>
                        <td>每周</td>
                        <td>2024-01-21 02:00</td>
                        <td>2024-01-14 02:00</td>
                        <td><span class="status-badge status-completed">已完成</span></td>
                        <td><span style="color: #17a2b8; font-weight: bold;">88.7</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">执行</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">报告</button>
                        </td>
                    </tr>
                    <tr>
                        <td>成绩数据专项质检</td>
                        <td>专项质检</td>
                        <td>6</td>
                        <td>每月</td>
                        <td>2024-02-01 02:00</td>
                        <td>2024-01-01 02:00</td>
                        <td><span class="status-badge status-running">执行中</span></td>
                        <td><span style="color: #ffc107; font-weight: bold;">76.3</span></td>
                        <td>
                            <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">停止</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">监控</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 预警阈值设置面板 -->
        <div id="thresholds" class="content-panel">
            <h2>预警阈值设置</h2>
            <div class="alert alert-info">
                支持用户根据自身对数据问题的严重性和质检得分情况，设定合理的预警阈值，划分高、中、低三个不同级别的预警
            </div>

            <h3>质量得分阈值配置</h3>
            <div class="threshold-config">
                <div class="threshold-item threshold-high">
                    <h4 style="color: #dc3545;">高级预警</h4>
                    <div class="form-group">
                        <label>得分阈值</label>
                        <input type="number" class="form-control" value="60" min="0" max="100">
                        <small>得分低于此值触发高级预警</small>
                    </div>
                    <div class="form-group">
                        <label>问题数量阈值</label>
                        <input type="number" class="form-control" value="100" min="0">
                        <small>问题数量超过此值触发预警</small>
                    </div>
                </div>

                <div class="threshold-item threshold-medium">
                    <h4 style="color: #ffc107;">中级预警</h4>
                    <div class="form-group">
                        <label>得分阈值</label>
                        <input type="number" class="form-control" value="80" min="0" max="100">
                        <small>得分低于此值触发中级预警</small>
                    </div>
                    <div class="form-group">
                        <label>问题数量阈值</label>
                        <input type="number" class="form-control" value="50" min="0">
                        <small>问题数量超过此值触发预警</small>
                    </div>
                </div>

                <div class="threshold-item threshold-low">
                    <h4 style="color: #28a745;">低级预警</h4>
                    <div class="form-group">
                        <label>得分阈值</label>
                        <input type="number" class="form-control" value="90" min="0" max="100">
                        <small>得分低于此值触发低级预警</small>
                    </div>
                    <div class="form-group">
                        <label>问题数量阈值</label>
                        <input type="number" class="form-control" value="20" min="0">
                        <small>问题数量超过此值触发预警</small>
                    </div>
                </div>
            </div>

            <h3>业务域阈值配置</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>业务域</th>
                        <th>数据表</th>
                        <th>高级预警阈值</th>
                        <th>中级预警阈值</th>
                        <th>低级预警阈值</th>
                        <th>当前得分</th>
                        <th>预警状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>学生管理</td>
                        <td>dwd_student_info</td>
                        <td>60</td>
                        <td>80</td>
                        <td>90</td>
                        <td><span style="color: #28a745; font-weight: bold;">95.2</span></td>
                        <td><span class="status-badge status-completed">正常</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">配置</button>
                        </td>
                    </tr>
                    <tr>
                        <td>教学管理</td>
                        <td>dwd_course_info</td>
                        <td>60</td>
                        <td>80</td>
                        <td>90</td>
                        <td><span style="color: #ffc107; font-weight: bold;">76.3</span></td>
                        <td><span class="status-badge" style="background: #fff3cd; color: #856404;">中级预警</span></td>
                        <td>
                            <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">处理</button>
                        </td>
                    </tr>
                    <tr>
                        <td>教师管理</td>
                        <td>dwd_teacher_info</td>
                        <td>60</td>
                        <td>80</td>
                        <td>90</td>
                        <td><span style="color: #dc3545; font-weight: bold;">58.7</span></td>
                        <td><span class="status-badge" style="background: #f8d7da; color: #721c24;">高级预警</span></td>
                        <td>
                            <button class="btn btn-danger" style="padding: 6px 12px; font-size: 12px;">紧急处理</button>
                        </td>
                    </tr>
                </tbody>
            </table>

            <h3>预警通知配置</h3>
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>通知方式</label>
                        <div>
                            <label><input type="checkbox" checked> 邮件通知</label><br>
                            <label><input type="checkbox"> 短信通知</label><br>
                            <label><input type="checkbox" checked> 系统内通知</label><br>
                            <label><input type="checkbox"> 微信通知</label>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>通知频率</label>
                        <select class="form-control">
                            <option>立即通知</option>
                            <option>每小时汇总</option>
                            <option>每日汇总</option>
                            <option>每周汇总</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <button class="btn btn-primary">保存阈值配置</button>
                <button class="btn btn-success">测试预警</button>
                <button class="btn btn-info">导出配置</button>
            </div>
        </div>

        <!-- 报告发送策略面板 -->
        <div id="reports" class="content-panel">
            <h2>报告发送策略</h2>
            <div class="alert alert-info">
                支持基于质检次数、预警级别或质检分数等单一条件触发报告发送，允许用户组合使用这些条件
            </div>

            <h3>发送条件配置</h3>
            <div class="condition-builder">
                <h4>条件组合 1</h4>
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>触发条件类型</label>
                            <select class="form-control">
                                <option>质检次数</option>
                                <option>预警级别</option>
                                <option>质检分数</option>
                                <option>问题数量</option>
                                <option>时间间隔</option>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>条件操作符</label>
                            <select class="form-control">
                                <option>等于</option>
                                <option>大于</option>
                                <option>小于</option>
                                <option>大于等于</option>
                                <option>小于等于</option>
                                <option>不等于</option>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>条件值</label>
                            <input type="text" class="form-control" placeholder="请输入条件值">
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>逻辑关系</label>
                            <select class="form-control">
                                <option>AND</option>
                                <option>OR</option>
                            </select>
                        </div>
                    </div>
                </div>
                <button class="btn btn-success" onclick="addCondition()">添加条件</button>
                <button class="btn btn-danger" onclick="removeCondition()">删除条件</button>
            </div>

            <h3>报告配置</h3>
            <div class="report-config">
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>报告模板</label>
                            <select class="form-control">
                                <option>标准质量报告</option>
                                <option>简化质量报告</option>
                                <option>详细质量报告</option>
                                <option>高管摘要报告</option>
                                <option>技术详情报告</option>
                            </select>
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>报告格式</label>
                            <select class="form-control">
                                <option>PDF</option>
                                <option>Excel</option>
                                <option>HTML</option>
                                <option>Word</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>报告内容配置</label>
                    <div>
                        <label><input type="checkbox" checked> 数据质量概述</label><br>
                        <label><input type="checkbox" checked> 数据质量统计</label><br>
                        <label><input type="checkbox" checked> 数据质量情况</label><br>
                        <label><input type="checkbox" checked> 数据质量问题与改进</label><br>
                        <label><input type="checkbox" checked> 参考信息</label><br>
                        <label><input type="checkbox"> 趋势分析图表</label><br>
                        <label><input type="checkbox"> 对比分析</label>
                    </div>
                </div>
            </div>

            <h3>发送对象配置</h3>
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label>收件人类型</label>
                        <select class="form-control">
                            <option>按角色发送</option>
                            <option>按部门发送</option>
                            <option>指定人员</option>
                            <option>组合发送</option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label>收件人</label>
                        <select class="form-control" multiple>
                            <option>数据管理员</option>
                            <option>业务负责人</option>
                            <option>技术负责人</option>
                            <option>部门领导</option>
                            <option>质量专员</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>自定义收件人邮箱</label>
                <textarea class="form-control" rows="3" placeholder="请输入邮箱地址，多个邮箱用分号分隔"><EMAIL>;<EMAIL>;<EMAIL></textarea>
            </div>

            <div class="form-group">
                <button class="btn btn-primary">保存发送策略</button>
                <button class="btn btn-success">测试发送</button>
                <button class="btn btn-info">预览报告</button>
            </div>

            <h3>发送策略列表</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>策略名称</th>
                        <th>触发条件</th>
                        <th>报告模板</th>
                        <th>收件人数量</th>
                        <th>最后发送</th>
                        <th>发送次数</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>高级预警报告</td>
                        <td>预警级别=高级</td>
                        <td>详细质量报告</td>
                        <td>5</td>
                        <td>2024-01-15 08:30</td>
                        <td>3</td>
                        <td><span class="status-badge status-completed">启用</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">发送</button>
                        </td>
                    </tr>
                    <tr>
                        <td>每日质量摘要</td>
                        <td>质检次数≥1</td>
                        <td>简化质量报告</td>
                        <td>8</td>
                        <td>2024-01-15 02:00</td>
                        <td>15</td>
                        <td><span class="status-badge status-completed">启用</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">暂停</button>
                        </td>
                    </tr>
                    <tr>
                        <td>低分数预警</td>
                        <td>质检分数<80</td>
                        <td>标准质量报告</td>
                        <td>3</td>
                        <td>2024-01-14 15:20</td>
                        <td>7</td>
                        <td><span class="status-badge status-pending">暂停</span></td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">编辑</button>
                            <button class="btn btn-success" style="padding: 6px 12px; font-size: 12px;">启用</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 典型检测规则面板 -->
        <div id="examples" class="content-panel">
            <h2>典型检测规则示例</h2>
            <div class="alert alert-info">
                针对不同数据类型和业务领域的典型检测规则示例，说明设计依据和预期检测效果
            </div>

            <h3>按数据类型分类</h3>
            <div class="rule-types">
                <div class="rule-card">
                    <h4>数值型数据检测</h4>
                    <p><strong>设计依据：</strong>数值型数据需要检测范围合理性、精度准确性</p>
                    <p><strong>典型规则：</strong></p>
                    <ul>
                        <li>年龄范围检测：16 ≤ age ≤ 30</li>
                        <li>成绩范围检测：0 ≤ score ≤ 100</li>
                        <li>学分检测：credit > 0</li>
                        <li>GPA检测：0 ≤ gpa ≤ 4.0</li>
                    </ul>
                    <p><strong>预期效果：</strong>识别异常数值，确保数据在合理范围内</p>
                    <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 10px;">
                        <code>
                        SELECT COUNT(*) as invalid_count<br>
                        FROM dwd_student_info<br>
                        WHERE age < 16 OR age > 30
                        </code>
                    </div>
                </div>

                <div class="rule-card">
                    <h4>文本型数据检测</h4>
                    <p><strong>设计依据：</strong>文本数据需要检测格式规范性、完整性</p>
                    <p><strong>典型规则：</strong></p>
                    <ul>
                        <li>姓名非空检测：name IS NOT NULL AND name != ''</li>
                        <li>手机号格式：REGEXP '^1[3-9][0-9]{9}$'</li>
                        <li>邮箱格式：REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'</li>
                        <li>学号格式：REGEXP '^[0-9]{10}$'</li>
                    </ul>
                    <p><strong>预期效果：</strong>确保文本数据格式正确，提高数据可用性</p>
                    <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 10px;">
                        <code>
                        SELECT COUNT(*) as invalid_phone<br>
                        FROM dwd_student_info<br>
                        WHERE phone NOT REGEXP '^1[3-9][0-9]{9}$'
                        </code>
                    </div>
                </div>

                <div class="rule-card">
                    <h4>日期型数据检测</h4>
                    <p><strong>设计依据：</strong>日期数据需要检测时间逻辑性、合理性</p>
                    <p><strong>典型规则：</strong></p>
                    <ul>
                        <li>入学日期合理性：enrollment_date >= '2000-01-01'</li>
                        <li>毕业日期逻辑性：graduation_date > enrollment_date</li>
                        <li>出生日期检测：birth_date < enrollment_date</li>
                        <li>考试日期检测：exam_date <= CURDATE()</li>
                    </ul>
                    <p><strong>预期效果：</strong>确保日期逻辑正确，避免时间矛盾</p>
                    <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 10px;">
                        <code>
                        SELECT COUNT(*) as invalid_date<br>
                        FROM dwd_student_info<br>
                        WHERE enrollment_date < '2000-01-01'<br>
                        OR enrollment_date > CURDATE()
                        </code>
                    </div>
                </div>
            </div>

            <h3>按业务领域分类</h3>
            <div class="rule-types">
                <div class="rule-card">
                    <h4>教学业务检测</h4>
                    <p><strong>设计依据：</strong>教学业务数据关联性强，需要检测业务逻辑一致性</p>
                    <p><strong>典型规则：</strong></p>
                    <ul>
                        <li>选课人数检测：选课人数 ≤ 课程容量</li>
                        <li>成绩录入完整性：已结课课程必须有成绩</li>
                        <li>学分统计一致性：总学分 = 各科学分之和</li>
                        <li>课程时间冲突检测：同一学生同一时间不能选多门课</li>
                    </ul>
                    <p><strong>预期效果：</strong>确保教学数据逻辑正确，支持教学管理决策</p>
                </div>

                <div class="rule-card">
                    <h4>学科管理检测</h4>
                    <p><strong>设计依据：</strong>学科数据需要保证层次结构和关联关系正确</p>
                    <p><strong>典型规则：</strong></p>
                    <ul>
                        <li>专业-学院关联检测：专业必须属于有效学院</li>
                        <li>课程-专业关联检测：课程必须属于有效专业</li>
                        <li>学科代码唯一性：学科代码不能重复</li>
                        <li>学科层级完整性：子学科必须有父学科</li>
                    </ul>
                    <p><strong>预期效果：</strong>维护学科体系完整性，支持学科建设分析</p>
                </div>

                <div class="rule-card">
                    <h4>科研关系检测</h4>
                    <p><strong>设计依据：</strong>科研数据涉及多方关系，需要检测关联完整性</p>
                    <p><strong>典型规则：</strong></p>
                    <ul>
                        <li>项目负责人检测：项目必须有有效负责人</li>
                        <li>项目经费检测：经费金额 > 0</li>
                        <li>项目时间检测：结束时间 > 开始时间</li>
                        <li>成果归属检测：成果必须关联到有效项目</li>
                    </ul>
                    <p><strong>预期效果：</strong>确保科研数据准确性，支持科研绩效评估</p>
                </div>
            </div>

            <h3>规则模板库</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>规则模板名称</th>
                        <th>适用数据类型</th>
                        <th>业务领域</th>
                        <th>检测内容</th>
                        <th>使用次数</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>手机号格式检测模板</td>
                        <td>文本型</td>
                        <td>通用</td>
                        <td>11位手机号格式验证</td>
                        <td>15</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">使用</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看</button>
                        </td>
                    </tr>
                    <tr>
                        <td>年龄范围检测模板</td>
                        <td>数值型</td>
                        <td>学生管理</td>
                        <td>学生年龄合理性检测</td>
                        <td>12</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">使用</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看</button>
                        </td>
                    </tr>
                    <tr>
                        <td>课程时间冲突检测模板</td>
                        <td>关联型</td>
                        <td>教学管理</td>
                        <td>学生选课时间冲突检测</td>
                        <td>8</td>
                        <td>
                            <button class="btn btn-primary" style="padding: 6px 12px; font-size: 12px;">使用</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 质量监控面板 -->
        <div id="dashboard" class="content-panel">
            <h2>数据质量监控面板</h2>
            <div class="alert alert-success">
                系统整体数据质量评分：<strong>89.5分</strong> - 良好
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">156</div>
                    <div class="stat-label">配置规则数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">23</div>
                    <div class="stat-label">质检方案数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">1,234</div>
                    <div class="stat-label">今日检测记录</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">45</div>
                    <div class="stat-label">发现问题数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">12</div>
                    <div class="stat-label">待处理问题</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">98.2%</div>
                    <div class="stat-label">规则通过率</div>
                </div>
            </div>

            <h3>各业务域质量得分</h3>
            <div class="row">
                <div class="col">
                    <h4>学生管理域</h4>
                    <div class="quality-score score-excellent">95.2分</div>
                    <div class="progress-bar">
                        <div class="progress-fill progress-excellent" style="width: 95.2%;"></div>
                    </div>
                    <p>检测规则：15个 | 问题数：3个</p>
                </div>
                <div class="col">
                    <h4>教学管理域</h4>
                    <div class="quality-score score-good">88.7分</div>
                    <div class="progress-bar">
                        <div class="progress-fill progress-good" style="width: 88.7%;"></div>
                    </div>
                    <p>检测规则：12个 | 问题数：8个</p>
                </div>
                <div class="col">
                    <h4>科研管理域</h4>
                    <div class="quality-score score-warning">76.3分</div>
                    <div class="progress-bar">
                        <div class="progress-fill progress-warning" style="width: 76.3%;"></div>
                    </div>
                    <p>检测规则：10个 | 问题数：15个</p>
                </div>
                <div class="col">
                    <h4>财务管理域</h4>
                    <div class="quality-score score-danger">65.8分</div>
                    <div class="progress-bar">
                        <div class="progress-fill progress-danger" style="width: 65.8%;"></div>
                    </div>
                    <p>检测规则：8个 | 问题数：19个</p>
                </div>
            </div>

            <h3>实时质检任务监控</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>质检方案</th>
                        <th>执行状态</th>
                        <th>开始时间</th>
                        <th>进度</th>
                        <th>检测记录数</th>
                        <th>发现问题数</th>
                        <th>当前得分</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>学生信息全面质检</td>
                        <td><span class="status-badge status-running">执行中</span></td>
                        <td>2024-01-15 14:30</td>
                        <td>
                            <div class="progress-bar">
                                <div class="progress-fill progress-good" style="width: 75%;"></div>
                            </div>
                            75%
                        </td>
                        <td>11,400/15,200</td>
                        <td>23</td>
                        <td>94.8</td>
                        <td>
                            <button class="btn btn-warning" style="padding: 6px 12px; font-size: 12px;">停止</button>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">监控</button>
                        </td>
                    </tr>
                    <tr>
                        <td>教师信息重点质检</td>
                        <td><span class="status-badge status-completed">已完成</span></td>
                        <td>2024-01-15 14:00</td>
                        <td>
                            <div class="progress-bar">
                                <div class="progress-fill progress-excellent" style="width: 100%;"></div>
                            </div>
                            100%
                        </td>
                        <td>1,230/1,230</td>
                        <td>8</td>
                        <td>88.7</td>
                        <td>
                            <button class="btn btn-info" style="padding: 6px 12px; font-size: 12px;">查看报告</button>
                        </td>
                    </tr>
                </tbody>
            </table>

            <h3>质量趋势分析</h3>
            <div style="background: #f8f9fa; border: 1px solid #ddd; border-radius: 8px; padding: 20px; text-align: center; min-height: 200px;">
                <p style="color: #666; margin-top: 80px;">质量趋势图表区域</p>
                <p style="color: #666;">（此处可集成图表库显示质量得分趋势、问题数量趋势等）</p>
            </div>
        </div>

        <!-- 质量检查报告面板 -->
        <div id="report-view" class="content-panel">
            <h2>数据质量检查报告</h2>
            <div class="alert alert-info">
                详细的数据质量检查报告，包括数据质量概述、统计、情况分析、问题改进建议和参考信息
            </div>

            <div class="search-box">
                <input type="text" class="form-control" placeholder="搜索报告...">
                <select class="form-control" style="flex: 0 0 150px;">
                    <option>全部报告</option>
                    <option>今日报告</option>
                    <option>本周报告</option>
                    <option>本月报告</option>
                </select>
                <button class="btn btn-primary">搜索</button>
                <button class="btn btn-success">生成新报告</button>
            </div>

            <!-- 报告内容 -->
            <div class="report-section">
                <h3>数据质量概述</h3>
                <div class="row">
                    <div class="col-2">
                        <p><strong>报告生成时间：</strong>2024年1月15日 15:30:00</p>
                        <p><strong>检测时间范围：</strong>2024年1月15日 00:00 - 24:00</p>
                        <p><strong>检测数据范围：</strong>全校核心业务数据</p>
                        <p><strong>质检方案：</strong>学生信息全面质检、教师信息重点质检、课程数据专项质检</p>
                        <p><strong>整体质量评分：</strong><span style="color: #28a745; font-weight: bold; font-size: 18px;">89.5分</span></p>
                        <p><strong>质量等级：</strong><span style="color: #28a745; font-weight: bold;">良好</span></p>
                    </div>
                    <div class="col">
                        <div class="alert alert-success">
                            <h4>质量评估结论</h4>
                            <p>本次数据质量检测覆盖了学校核心业务数据，整体质量状况良好。学生管理域和教学管理域数据质量较高，科研管理域和财务管理域存在一定问题需要关注和改进。</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="report-section">
                <h3>数据质量统计</h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">4</div>
                        <div class="stat-label">检测业务域</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">15</div>
                        <div class="stat-label">检测数据表</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">156</div>
                        <div class="stat-label">执行规则数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">1,234,567</div>
                        <div class="stat-label">检测记录总数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">45</div>
                        <div class="stat-label">发现问题数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">99.996%</div>
                        <div class="stat-label">数据准确率</div>
                    </div>
                </div>

                <h4>各质量维度统计</h4>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>质量维度</th>
                            <th>检测规则数</th>
                            <th>检测记录数</th>
                            <th>问题记录数</th>
                            <th>问题率</th>
                            <th>得分</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>完整性</td>
                            <td>45</td>
                            <td>1,234,567</td>
                            <td>12</td>
                            <td>0.001%</td>
                            <td><span style="color: #28a745; font-weight: bold;">98.5</span></td>
                        </tr>
                        <tr>
                            <td>准确性</td>
                            <td>38</td>
                            <td>1,234,567</td>
                            <td>18</td>
                            <td>0.0015%</td>
                            <td><span style="color: #28a745; font-weight: bold;">96.2</span></td>
                        </tr>
                        <tr>
                            <td>一致性</td>
                            <td>32</td>
                            <td>1,234,567</td>
                            <td>8</td>
                            <td>0.0006%</td>
                            <td><span style="color: #28a745; font-weight: bold;">97.8</span></td>
                        </tr>
                        <tr>
                            <td>有效性</td>
                            <td>25</td>
                            <td>1,234,567</td>
                            <td>5</td>
                            <td>0.0004%</td>
                            <td><span style="color: #28a745; font-weight: bold;">98.9</span></td>
                        </tr>
                        <tr>
                            <td>唯一性</td>
                            <td>16</td>
                            <td>1,234,567</td>
                            <td>2</td>
                            <td>0.0002%</td>
                            <td><span style="color: #28a745; font-weight: bold;">99.5</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="report-section">
                <h3>数据质量情况</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>数据表</th>
                            <th>责任单位</th>
                            <th>数据源</th>
                            <th>质检字段数</th>
                            <th>质检规则数</th>
                            <th>数据量</th>
                            <th>检测项</th>
                            <th>问题数</th>
                            <th>质量得分</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>dwd_student_info</td>
                            <td>学生处</td>
                            <td>MySQL-学生管理系统</td>
                            <td>25</td>
                            <td>15</td>
                            <td>15,200</td>
                            <td>完整性、准确性、有效性</td>
                            <td>3</td>
                            <td><span style="color: #28a745; font-weight: bold;">95.2</span></td>
                        </tr>
                        <tr>
                            <td>dwd_teacher_info</td>
                            <td>人事处</td>
                            <td>Oracle-人事系统</td>
                            <td>22</td>
                            <td>12</td>
                            <td>1,230</td>
                            <td>完整性、准确性、一致性</td>
                            <td>8</td>
                            <td><span style="color: #17a2b8; font-weight: bold;">88.7</span></td>
                        </tr>
                        <tr>
                            <td>dwd_course_info</td>
                            <td>教务处</td>
                            <td>PostgreSQL-教务系统</td>
                            <td>18</td>
                            <td>10</td>
                            <td>567</td>
                            <td>完整性、有效性、唯一性</td>
                            <td>15</td>
                            <td><span style="color: #ffc107; font-weight: bold;">76.3</span></td>
                        </tr>
                        <tr>
                            <td>dwd_score_info</td>
                            <td>教务处</td>
                            <td>PostgreSQL-教务系统</td>
                            <td>15</td>
                            <td>8</td>
                            <td>45,678</td>
                            <td>准确性、一致性</td>
                            <td>12</td>
                            <td><span style="color: #17a2b8; font-weight: bold;">87.5</span></td>
                        </tr>
                        <tr>
                            <td>dwd_finance_info</td>
                            <td>财务处</td>
                            <td>MySQL-财务系统</td>
                            <td>20</td>
                            <td>12</td>
                            <td>23,456</td>
                            <td>完整性、准确性、及时性</td>
                            <td>19</td>
                            <td><span style="color: #dc3545; font-weight: bold;">65.8</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="report-section">
                <h3>数据质量问题与改进</h3>
                <div class="alert alert-warning">
                    <h4>主要问题汇总</h4>
                    <ul>
                        <li><strong>财务数据完整性问题：</strong>财务信息表中存在19条记录的关键字段缺失，影响财务分析准确性</li>
                        <li><strong>课程信息有效性问题：</strong>课程信息表中发现15条无效记录，包括课程代码重复和课程时间冲突</li>
                        <li><strong>教师信息一致性问题：</strong>教师信息在不同系统间存在8处不一致，需要进行数据同步</li>
                    </ul>
                </div>

                <h4>改进建议</h4>
                <div class="alert alert-info">
                    <ol>
                        <li><strong>加强数据录入规范：</strong>建议财务处完善数据录入流程，确保关键字段的完整性</li>
                        <li><strong>建立数据同步机制：</strong>建议在不同业务系统间建立定期数据同步机制，确保数据一致性</li>
                        <li><strong>完善数据验证规则：</strong>建议在数据录入环节增加实时验证，避免无效数据进入系统</li>
                        <li><strong>定期数据清理：</strong>建议建立定期数据清理机制，及时发现和处理历史遗留问题</li>
                        <li><strong>加强培训：</strong>建议对相关业务人员进行数据质量意识培训，提高数据录入质量</li>
                    </ol>
                </div>

                <h4>问题处理进度</h4>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>问题类型</th>
                            <th>问题数量</th>
                            <th>已处理</th>
                            <th>处理中</th>
                            <th>待处理</th>
                            <th>处理率</th>
                            <th>责任单位</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>财务数据缺失</td>
                            <td>19</td>
                            <td>7</td>
                            <td>5</td>
                            <td>7</td>
                            <td>63.2%</td>
                            <td>财务处</td>
                        </tr>
                        <tr>
                            <td>课程信息无效</td>
                            <td>15</td>
                            <td>10</td>
                            <td>3</td>
                            <td>2</td>
                            <td>86.7%</td>
                            <td>教务处</td>
                        </tr>
                        <tr>
                            <td>教师信息不一致</td>
                            <td>8</td>
                            <td>5</td>
                            <td>2</td>
                            <td>1</td>
                            <td>87.5%</td>
                            <td>人事处</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="report-section">
                <h3>参考信息</h3>
                <div class="row">
                    <div class="col">
                        <h4>检测标准依据</h4>
                        <ul>
                            <li>《高等学校数据质量管理规范》</li>
                            <li>《教育数据标准体系建设指南》</li>
                            <li>《学校数据治理实施细则》</li>
                            <li>ISO/IEC 25012 数据质量模型</li>
                        </ul>
                    </div>
                    <div class="col">
                        <h4>质量评分标准</h4>
                        <ul>
                            <li>优秀：90-100分</li>
                            <li>良好：80-89分</li>
                            <li>一般：70-79分</li>
                            <li>较差：60-69分</li>
                            <li>很差：60分以下</li>
                        </ul>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <h4>联系信息</h4>
                        <p><strong>数据质量管理员：</strong>张三 (<EMAIL>)</p>
                        <p><strong>技术支持：</strong>李四 (<EMAIL>)</p>
                        <p><strong>业务咨询：</strong>王五 (<EMAIL>)</p>
                    </div>
                    <div class="col">
                        <h4>报告说明</h4>
                        <p>本报告基于系统自动检测生成，检测结果仅供参考。如有疑问请联系数据质量管理员。</p>
                        <p><strong>下次检测时间：</strong>2024年1月16日 02:00</p>
                        <p><strong>报告生成版本：</strong>v2.1.0</p>
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button class="btn btn-primary">导出PDF</button>
                <button class="btn btn-success">导出Excel</button>
                <button class="btn btn-info">发送邮件</button>
                <button class="btn btn-warning">打印报告</button>
            </div>
        </div>
    </div>

    <script>
        // 切换面板显示
        function showPanel(panelId) {
            // 隐藏所有面板
            const panels = document.querySelectorAll('.content-panel');
            panels.forEach(panel => panel.classList.remove('active'));

            // 移除所有标签的active状态
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的面板
            document.getElementById(panelId).classList.add('active');

            // 激活对应的标签
            event.target.classList.add('active');
        }

        // 更新调度配置
        function updateScheduleConfig(frequency) {
            const detailDiv = document.getElementById('scheduleDetail');
            let configHTML = '';

            switch(frequency) {
                case 'daily':
                    configHTML = `
                        <div class="form-group">
                            <label>执行日期</label>
                            <select class="form-control">
                                <option>每天</option>
                                <option>工作日</option>
                                <option>周末</option>
                            </select>
                        </div>
                    `;
                    break;
                case 'weekly':
                    configHTML = `
                        <div class="form-group">
                            <label>执行星期</label>
                            <select class="form-control">
                                <option>星期一</option>
                                <option>星期二</option>
                                <option>星期三</option>
                                <option>星期四</option>
                                <option>星期五</option>
                                <option>星期六</option>
                                <option>星期日</option>
                            </select>
                        </div>
                    `;
                    break;
                case 'monthly':
                    configHTML = `
                        <div class="form-group">
                            <label>执行日期</label>
                            <select class="form-control">
                                <option>每月1日</option>
                                <option>每月15日</option>
                                <option>月末最后一天</option>
                            </select>
                        </div>
                    `;
                    break;
                case 'yearly':
                    configHTML = `
                        <div class="form-group">
                            <label>执行月份</label>
                            <select class="form-control">
                                <option>1月</option>
                                <option>6月</option>
                                <option>12月</option>
                            </select>
                        </div>
                    `;
                    break;
                case 'custom':
                    configHTML = `
                        <div class="form-group">
                            <label>Cron表达式</label>
                            <input type="text" class="form-control" placeholder="0 0 2 * * ?">
                        </div>
                    `;
                    break;
            }

            detailDiv.innerHTML = configHTML;
        }

        // 添加条件
        function addCondition() {
            alert('添加新的触发条件');
            // 这里可以动态添加新的条件配置行
        }

        // 删除条件
        function removeCondition() {
            alert('删除最后一个条件');
            // 这里可以删除最后一个条件配置行
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('数据质量管理系统已加载');

            // 模拟实时数据更新
            setInterval(function() {
                // 这里可以添加实时数据更新逻辑
                console.log('更新质量监控数据...');
            }, 30000); // 每30秒更新一次
        });
    </script>
</body>
</html>
