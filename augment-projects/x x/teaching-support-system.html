<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教学支撑数智分析系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .header-content {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .nav-menu {
            display: flex;
            gap: 30px;
        }
        
        .nav-item {
            color: #333;
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s;
        }
        
        .nav-item:hover, .nav-item.active {
            background: #667eea;
            color: white;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            color: #333;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .main-layout {
            display: flex;
            gap: 20px;
            min-height: calc(100vh - 120px);
        }
        
        .sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            height: fit-content;
            position: sticky;
            top: 100px;
        }
        
        .main-content {
            flex: 1;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 10px;
            padding-bottom: 15px;
            border-bottom: 2px solid #667eea;
        }
        
        .menu-group {
            margin-bottom: 25px;
        }
        
        .menu-group-title {
            font-size: 14px;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            margin-bottom: 5px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
            color: #666;
            text-decoration: none;
        }
        
        .menu-item:hover {
            background: #f0f4ff;
            color: #667eea;
            transform: translateX(5px);
        }
        
        .menu-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .menu-item i {
            width: 20px;
            margin-right: 12px;
            text-align: center;
        }
        
        .content-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .content-title {
            font-size: 24px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .content-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }
        
        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-primary:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-warning {
            background: #f59e0b;
            color: white;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .btn-outline {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
        }
        
        .btn-outline:hover {
            background: #667eea;
            color: white;
        }
        
        .content-body {
            padding: 30px;
        }
        
        .content-panel {
            display: none;
        }
        
        .content-panel.active {
            display: block;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border-left: 4px solid #667eea;
            transition: all 0.3s;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .stat-trend {
            font-size: 12px;
            color: #10b981;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .data-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .table-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .search-box {
            padding: 8px 15px;
            border: 1px solid #e9ecef;
            border-radius: 20px;
            font-size: 14px;
            width: 250px;
        }
        
        .table-content {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }
        
        .alert-info {
            background: #dbeafe;
            color: #1e40af;
            border-left-color: #3b82f6;
        }
        
        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border-left-color: #10b981;
        }
        
        .alert-warning {
            background: #fef3c7;
            color: #92400e;
            border-left-color: #f59e0b;
        }
        
        .alert-danger {
            background: #fee2e2;
            color: #991b1b;
            border-left-color: #ef4444;
        }
        
        .tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 8px 8px 0 0;
            margin-bottom: 0;
        }
        
        .tab {
            flex: 1;
            padding: 15px 20px;
            background: transparent;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s;
        }
        
        .tab.active {
            background: white;
            color: #667eea;
            border-bottom: 2px solid #667eea;
        }
        
        .tab-content {
            background: white;
            padding: 20px;
            border-radius: 0 0 8px 8px;
        }
        
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            margin: 8px 0;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 10px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
        }
        
        .filter-bar {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .filter-label {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        
        .form-control {
            padding: 8px 12px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 2000;
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }
        
        .modal-header {
            background: #667eea;
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-body {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .close {
            font-size: 24px;
            cursor: pointer;
            color: white;
            opacity: 0.8;
        }
        
        .close:hover {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-chalkboard-teacher"></i>
                教学支撑数智分析系统
            </div>
            <nav class="nav-menu">
                <a href="#" class="nav-item active" onclick="switchSystem('course')">课程体系分析</a>
                <a href="#" class="nav-item" onclick="switchSystem('academic')">学业水平分析</a>
                <a href="#" class="nav-item" onclick="switchSystem('team')">教学团队分析</a>
                <a href="#" class="nav-item" onclick="switchSystem('evaluation')">专业建设评价</a>
            </nav>
            <div class="user-info">
                <span>教学管理员</span>
                <i class="fas fa-user-circle" style="font-size: 24px;"></i>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="main-layout">
            <div class="sidebar">
                <div class="sidebar-title">
                    <i class="fas fa-list"></i>
                    功能导航
                </div>

                <!-- 课程体系分析菜单 -->
                <div id="courseMenu" class="menu-system">
                    <div class="menu-group">
                        <div class="menu-group-title">课程内容分析</div>
                        <a href="#" class="menu-item active" onclick="showContent('course-content')">
                            <i class="fas fa-book"></i>
                            课程内容指标分析
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('course-materials')">
                            <i class="fas fa-book-open"></i>
                            课程教材分析
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('course-evaluation')">
                            <i class="fas fa-star"></i>
                            课程评价分析
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('course-achievement')">
                            <i class="fas fa-trophy"></i>
                            课程建设成效
                        </a>
                    </div>
                </div>

                <!-- 学业水平分析菜单 -->
                <div id="academicMenu" class="menu-system" style="display: none;">
                    <div class="menu-group">
                        <div class="menu-group-title">学业水平分析</div>
                        <a href="#" class="menu-item" onclick="showContent('course-performance')">
                            <i class="fas fa-chart-line"></i>
                            课程学习分析
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('practice-teaching')">
                            <i class="fas fa-hands-helping"></i>
                            实践教学分析
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('graduation-analysis')">
                            <i class="fas fa-graduation-cap"></i>
                            毕业升学分析
                        </a>
                    </div>
                </div>

                <!-- 教学团队分析菜单 -->
                <div id="teamMenu" class="menu-system" style="display: none;">
                    <div class="menu-group">
                        <div class="menu-group-title">教学团队分析</div>
                        <a href="#" class="menu-item" onclick="showContent('team-structure')">
                            <i class="fas fa-users"></i>
                            团队结构分析
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('teaching-performance')">
                            <i class="fas fa-chalkboard"></i>
                            授课情况分析
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('guidance-analysis')">
                            <i class="fas fa-user-graduate"></i>
                            指导情况分析
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('research-analysis')">
                            <i class="fas fa-flask"></i>
                            科研情况分析
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('service-analysis')">
                            <i class="fas fa-handshake"></i>
                            服务情况分析
                        </a>
                    </div>
                </div>

                <!-- 专业建设评价菜单 -->
                <div id="evaluationMenu" class="menu-system" style="display: none;">
                    <div class="menu-group">
                        <div class="menu-group-title">专业建设评价</div>
                        <a href="#" class="menu-item" onclick="showContent('risk-warning')">
                            <i class="fas fa-exclamation-triangle"></i>
                            风险预警识别
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('horizontal-comparison')">
                            <i class="fas fa-balance-scale"></i>
                            专业横向对比
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('risk-disposal')">
                            <i class="fas fa-tools"></i>
                            风险处置管理
                        </a>
                        <a href="#" class="menu-item" onclick="showContent('quality-report')">
                            <i class="fas fa-file-alt"></i>
                            质量分析报告
                        </a>
                    </div>
                </div>
            </div>

            <div class="main-content">
                <div class="content-header">
                    <div class="content-title">
                        <i class="fas fa-book" id="contentIcon"></i>
                        <span id="contentTitle">课程内容指标分析</span>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-primary" onclick="exportAnalysisData()">
                            <i class="fas fa-download"></i> 导出分析数据
                        </button>
                        <button class="btn btn-primary" onclick="generateReport()">
                            <i class="fas fa-file-alt"></i> 生成报告
                        </button>
                        <button class="btn btn-primary" onclick="refreshData()">
                            <i class="fas fa-sync"></i> 刷新数据
                        </button>
                    </div>
                </div>

                <div class="content-body">
                    <!-- 课程内容指标分析 -->
                    <div id="course-content" class="content-panel active">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            支持根据本科专业人才培养方案，细化形成课程内容分析指标模型，指标覆盖教学设计理念、教改项目、课程思政、前沿技术、课程内容更新等方面。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">156</div>
                                <div class="stat-label">课程总数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上学期 +8
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">92.5%</div>
                                <div class="stat-label">教学设计理念覆盖率</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上学期 +3.2%
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">78</div>
                                <div class="stat-label">教改项目数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上学期 +12
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">85.6%</div>
                                <div class="stat-label">课程思政融入率</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上学期 +5.1%
                                </div>
                            </div>
                        </div>

                        <div class="filter-bar">
                            <div class="filter-group">
                                <span class="filter-label">专业:</span>
                                <select class="form-control" style="width: 200px;">
                                    <option>全部专业</option>
                                    <option>计算机科学与技术</option>
                                    <option>软件工程</option>
                                    <option>数据科学与大数据技术</option>
                                    <option>人工智能</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <span class="filter-label">课程类型:</span>
                                <select class="form-control" style="width: 150px;">
                                    <option>全部</option>
                                    <option>必修课</option>
                                    <option>选修课</option>
                                    <option>实践课</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <span class="filter-label">学期:</span>
                                <select class="form-control" style="width: 120px;">
                                    <option>2024春</option>
                                    <option>2023秋</option>
                                    <option>2023春</option>
                                </select>
                            </div>
                            <button class="btn btn-outline">
                                <i class="fas fa-search"></i> 筛选
                            </button>
                        </div>

                        <div class="tabs">
                            <button class="tab active" onclick="showTab('design-concept')">教学设计理念</button>
                            <button class="tab" onclick="showTab('reform-project')">教改项目</button>
                            <button class="tab" onclick="showTab('ideological-politics')">课程思政</button>
                            <button class="tab" onclick="showTab('frontier-tech')">前沿技术</button>
                            <button class="tab" onclick="showTab('content-update')">内容更新</button>
                        </div>

                        <div class="tab-content">
                            <div id="design-concept" class="tab-panel active">
                                <div class="chart-container">
                                    <div class="chart-title">教学设计理念分析</div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                        <div>
                                            <h4>理念覆盖情况</h4>
                                            <div style="margin: 15px 0;">
                                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                                    <span>以学生为中心</span>
                                                    <span>95.2%</span>
                                                </div>
                                                <div class="progress-bar">
                                                    <div class="progress-fill" style="width: 95.2%;"></div>
                                                </div>
                                            </div>
                                            <div style="margin: 15px 0;">
                                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                                    <span>成果导向教育</span>
                                                    <span>88.7%</span>
                                                </div>
                                                <div class="progress-bar">
                                                    <div class="progress-fill" style="width: 88.7%;"></div>
                                                </div>
                                            </div>
                                            <div style="margin: 15px 0;">
                                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                                    <span>持续改进</span>
                                                    <span>92.1%</span>
                                                </div>
                                                <div class="progress-bar">
                                                    <div class="progress-fill" style="width: 92.1%;"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <h4>专业分布情况</h4>
                                            <div class="data-table">
                                                <div class="table-content">
                                                    <table>
                                                        <thead>
                                                            <tr>
                                                                <th>专业</th>
                                                                <th>课程数</th>
                                                                <th>覆盖率</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td>计算机科学与技术</td>
                                                                <td>45</td>
                                                                <td><span class="badge badge-success">96.8%</span></td>
                                                            </tr>
                                                            <tr>
                                                                <td>软件工程</td>
                                                                <td>38</td>
                                                                <td><span class="badge badge-success">94.2%</span></td>
                                                            </tr>
                                                            <tr>
                                                                <td>数据科学与大数据</td>
                                                                <td>32</td>
                                                                <td><span class="badge badge-warning">87.5%</span></td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="data-table">
                                    <div class="table-header">
                                        <div class="table-title">课程教学设计理念详情</div>
                                        <input type="text" class="search-box" placeholder="搜索课程名称...">
                                    </div>
                                    <div class="table-content">
                                        <table>
                                            <thead>
                                                <tr>
                                                    <th>课程名称</th>
                                                    <th>专业</th>
                                                    <th>学分</th>
                                                    <th>教学设计理念</th>
                                                    <th>理念融入度</th>
                                                    <th>更新时间</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>数据结构与算法</td>
                                                    <td>计算机科学与技术</td>
                                                    <td>4</td>
                                                    <td>
                                                        <span class="badge badge-success">以学生为中心</span>
                                                        <span class="badge badge-info">成果导向</span>
                                                    </td>
                                                    <td>
                                                        <div class="progress-bar" style="width: 100px;">
                                                            <div class="progress-fill" style="width: 95%;"></div>
                                                        </div>
                                                        95%
                                                    </td>
                                                    <td>2024-01-15</td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="viewCourseDetail('CS001')">查看详情</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>软件工程</td>
                                                    <td>软件工程</td>
                                                    <td>3</td>
                                                    <td>
                                                        <span class="badge badge-success">以学生为中心</span>
                                                        <span class="badge badge-warning">持续改进</span>
                                                    </td>
                                                    <td>
                                                        <div class="progress-bar" style="width: 100px;">
                                                            <div class="progress-fill" style="width: 88%;"></div>
                                                        </div>
                                                        88%
                                                    </td>
                                                    <td>2024-01-12</td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="viewCourseDetail('SE001')">查看详情</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 课程教材分析 -->
                    <div id="course-materials" class="content-panel">
                        <div class="alert alert-success">
                            <i class="fas fa-book-open"></i>
                            支持根据本科专业人才培养方案，细化形成课程教材分析指标模型，指标覆盖公开出版教材和自编教材2个方面。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">234</div>
                                <div class="stat-label">教材总数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-book"></i>
                                    全专业统计
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">78.5%</div>
                                <div class="stat-label">公开出版教材比例</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上学期 +2.3%
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">45</div>
                                <div class="stat-label">获奖教材数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-trophy"></i>
                                    各级获奖
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">32</div>
                                <div class="stat-label">项目支撑教材</div>
                                <div class="stat-trend">
                                    <i class="fas fa-project-diagram"></i>
                                    有项目支撑
                                </div>
                            </div>
                        </div>

                        <div class="tabs">
                            <button class="tab active" onclick="showTab('published-materials')">公开出版教材</button>
                            <button class="tab" onclick="showTab('self-compiled')">自编教材</button>
                            <button class="tab" onclick="showTab('material-quality')">教材质量分析</button>
                        </div>

                        <div class="tab-content">
                            <div id="published-materials" class="tab-panel active">
                                <div class="data-table">
                                    <div class="table-header">
                                        <div class="table-title">公开出版教材列表</div>
                                        <input type="text" class="search-box" placeholder="搜索教材名称...">
                                    </div>
                                    <div class="table-content">
                                        <table>
                                            <thead>
                                                <tr>
                                                    <th>教材名称</th>
                                                    <th>课程名称</th>
                                                    <th>出版社</th>
                                                    <th>出版年份</th>
                                                    <th>项目支撑</th>
                                                    <th>获奖情况</th>
                                                    <th>获奖级别</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>数据结构与算法分析</td>
                                                    <td>数据结构与算法</td>
                                                    <td>清华大学出版社</td>
                                                    <td>2023</td>
                                                    <td><span class="badge badge-success">有</span></td>
                                                    <td><span class="badge badge-success">已获奖</span></td>
                                                    <td><span class="badge badge-warning">省级</span></td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="viewMaterialDetail('M001')">查看详情</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>软件工程导论</td>
                                                    <td>软件工程</td>
                                                    <td>机械工业出版社</td>
                                                    <td>2022</td>
                                                    <td><span class="badge badge-danger">无</span></td>
                                                    <td><span class="badge badge-info">未获奖</span></td>
                                                    <td>-</td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="viewMaterialDetail('M002')">查看详情</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 课程学习分析 -->
                    <div id="course-performance" class="content-panel">
                        <div class="alert alert-info">
                            <i class="fas fa-chart-line"></i>
                            支持采集本科生各课程学习过程数据和考核结果数据，进行细化指标模型构建，展示课程成绩及格率、课程成绩优秀率、课程通过率、课程重修率等情况。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">94.2%</div>
                                <div class="stat-label">课程平均及格率</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上学期 +1.5%
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">32.8%</div>
                                <div class="stat-label">课程平均优秀率</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上学期 +2.1%
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">96.7%</div>
                                <div class="stat-label">课程平均通过率</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上学期 +0.8%
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">3.3%</div>
                                <div class="stat-label">课程平均重修率</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-down"></i>
                                    较上学期 -0.8%
                                </div>
                            </div>
                        </div>

                        <div class="chart-container">
                            <div class="chart-title">各专业课程学习情况对比</div>
                            <div class="data-table">
                                <div class="table-content">
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>专业</th>
                                                <th>课程数</th>
                                                <th>及格率</th>
                                                <th>优秀率</th>
                                                <th>通过率</th>
                                                <th>重修率</th>
                                                <th>趋势</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>计算机科学与技术</td>
                                                <td>45</td>
                                                <td>
                                                    <div class="progress-bar" style="width: 80px;">
                                                        <div class="progress-fill" style="width: 95.2%;"></div>
                                                    </div>
                                                    95.2%
                                                </td>
                                                <td>
                                                    <div class="progress-bar" style="width: 80px;">
                                                        <div class="progress-fill" style="width: 35.6%;"></div>
                                                    </div>
                                                    35.6%
                                                </td>
                                                <td>
                                                    <div class="progress-bar" style="width: 80px;">
                                                        <div class="progress-fill" style="width: 97.1%;"></div>
                                                    </div>
                                                    97.1%
                                                </td>
                                                <td>2.9%</td>
                                                <td><span class="badge badge-success">↑</span></td>
                                            </tr>
                                            <tr>
                                                <td>软件工程</td>
                                                <td>38</td>
                                                <td>
                                                    <div class="progress-bar" style="width: 80px;">
                                                        <div class="progress-fill" style="width: 93.8%;"></div>
                                                    </div>
                                                    93.8%
                                                </td>
                                                <td>
                                                    <div class="progress-bar" style="width: 80px;">
                                                        <div class="progress-fill" style="width: 31.2%;"></div>
                                                    </div>
                                                    31.2%
                                                </td>
                                                <td>
                                                    <div class="progress-bar" style="width: 80px;">
                                                        <div class="progress-fill" style="width: 96.5%;"></div>
                                                    </div>
                                                    96.5%
                                                </td>
                                                <td>3.5%</td>
                                                <td><span class="badge badge-success">↑</span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 课程评价分析 -->
                    <div id="course-evaluation" class="content-panel">
                        <div class="alert alert-success">
                            <i class="fas fa-star"></i>
                            支持采集各本科专业课程的听查课、督导评价和学生评价等数据，根据学院需求构建评价指标模型，呈现督导专家课程评价、学生评教、院系领导听查课、队干部跟班跟训等数据。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">4.6</div>
                                <div class="stat-label">督导评价平均分</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上学期 +0.2
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">4.4</div>
                                <div class="stat-label">学生评教平均分</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上学期 +0.1
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">4.5</div>
                                <div class="stat-label">领导听查课平均分</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上学期 +0.3
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">156</div>
                                <div class="stat-label">评价课程总数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-book"></i>
                                    全覆盖评价
                                </div>
                            </div>
                        </div>

                        <div class="tabs">
                            <button class="tab active" onclick="showTab('supervisor-evaluation')">督导评价</button>
                            <button class="tab" onclick="showTab('student-evaluation')">学生评教</button>
                            <button class="tab" onclick="showTab('leader-inspection')">领导听查课</button>
                            <button class="tab" onclick="showTab('cadre-follow')">队干部跟班跟训</button>
                        </div>

                        <div class="tab-content">
                            <div id="supervisor-evaluation" class="tab-panel active">
                                <div class="data-table">
                                    <div class="table-header">
                                        <div class="table-title">督导专家课程评价</div>
                                        <input type="text" class="search-box" placeholder="搜索课程或教师...">
                                    </div>
                                    <div class="table-content">
                                        <table>
                                            <thead>
                                                <tr>
                                                    <th>课程名称</th>
                                                    <th>授课教师</th>
                                                    <th>专业</th>
                                                    <th>督导专家</th>
                                                    <th>评价分数</th>
                                                    <th>评价等级</th>
                                                    <th>评价时间</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>数据结构与算法</td>
                                                    <td>张教授</td>
                                                    <td>计算机科学与技术</td>
                                                    <td>李督导</td>
                                                    <td>4.8</td>
                                                    <td><span class="badge badge-success">优秀</span></td>
                                                    <td>2024-01-15</td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="viewEvaluationDetail('E001')">查看详情</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>软件工程</td>
                                                    <td>王副教授</td>
                                                    <td>软件工程</td>
                                                    <td>赵督导</td>
                                                    <td>4.5</td>
                                                    <td><span class="badge badge-success">良好</span></td>
                                                    <td>2024-01-12</td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="viewEvaluationDetail('E002')">查看详情</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>数据库原理</td>
                                                    <td>刘讲师</td>
                                                    <td>计算机科学与技术</td>
                                                    <td>陈督导</td>
                                                    <td>4.2</td>
                                                    <td><span class="badge badge-warning">中等</span></td>
                                                    <td>2024-01-10</td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="viewEvaluationDetail('E003')">查看详情</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 课程建设成效 -->
                    <div id="course-achievement" class="content-panel">
                        <div class="alert alert-warning">
                            <i class="fas fa-trophy"></i>
                            支持采集各本科专业课程的成绩和获奖数据，根据学院需求构建评价指标模型。支持呈现各课程建设成效情况，维度包括课程成绩分析和课程建设获奖情况。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">45</div>
                                <div class="stat-label">获奖课程数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-trophy"></i>
                                    各级获奖
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">12</div>
                                <div class="stat-label">国家级获奖</div>
                                <div class="stat-trend">
                                    <i class="fas fa-medal"></i>
                                    最高级别
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">18</div>
                                <div class="stat-label">省级获奖</div>
                                <div class="stat-trend">
                                    <i class="fas fa-award"></i>
                                    省级认可
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">15</div>
                                <div class="stat-label">校级获奖</div>
                                <div class="stat-trend">
                                    <i class="fas fa-star"></i>
                                    校内优秀
                                </div>
                            </div>
                        </div>

                        <div class="tabs">
                            <button class="tab active" onclick="showTab('course-grades')">课程成绩分析</button>
                            <button class="tab" onclick="showTab('course-awards')">课程建设获奖</button>
                        </div>

                        <div class="tab-content">
                            <div id="course-grades" class="tab-panel active">
                                <div class="chart-container">
                                    <div class="chart-title">课程成绩分析统计</div>
                                    <div class="data-table">
                                        <div class="table-content">
                                            <table>
                                                <thead>
                                                    <tr>
                                                        <th>课程名称</th>
                                                        <th>授课教师</th>
                                                        <th>选课人数</th>
                                                        <th>平均分</th>
                                                        <th>及格率</th>
                                                        <th>优秀率</th>
                                                        <th>成绩分布</th>
                                                        <th>趋势</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>数据结构与算法</td>
                                                        <td>张教授</td>
                                                        <td>156</td>
                                                        <td>85.6</td>
                                                        <td>96.8%</td>
                                                        <td>38.5%</td>
                                                        <td>
                                                            <div style="display: flex; gap: 5px;">
                                                                <span class="badge badge-success">优秀: 60</span>
                                                                <span class="badge badge-info">良好: 68</span>
                                                                <span class="badge badge-warning">中等: 23</span>
                                                                <span class="badge badge-danger">不及格: 5</span>
                                                            </div>
                                                        </td>
                                                        <td><span class="badge badge-success">↑</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td>软件工程</td>
                                                        <td>王副教授</td>
                                                        <td>128</td>
                                                        <td>82.3</td>
                                                        <td>94.5%</td>
                                                        <td>32.8%</td>
                                                        <td>
                                                            <div style="display: flex; gap: 5px;">
                                                                <span class="badge badge-success">优秀: 42</span>
                                                                <span class="badge badge-info">良好: 58</span>
                                                                <span class="badge badge-warning">中等: 21</span>
                                                                <span class="badge badge-danger">不及格: 7</span>
                                                            </div>
                                                        </td>
                                                        <td><span class="badge badge-success">↑</span></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="course-awards" class="tab-panel">
                                <div class="data-table">
                                    <div class="table-header">
                                        <div class="table-title">课程建设获奖情况</div>
                                        <input type="text" class="search-box" placeholder="搜索获奖课程...">
                                    </div>
                                    <div class="table-content">
                                        <table>
                                            <thead>
                                                <tr>
                                                    <th>课程名称</th>
                                                    <th>负责人</th>
                                                    <th>获奖名称</th>
                                                    <th>获奖级别</th>
                                                    <th>获奖时间</th>
                                                    <th>颁奖单位</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>数据结构与算法</td>
                                                    <td>张教授</td>
                                                    <td>国家级一流本科课程</td>
                                                    <td><span class="badge badge-danger">国家级</span></td>
                                                    <td>2023-12</td>
                                                    <td>教育部</td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="viewAwardDetail('A001')">查看详情</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>软件工程</td>
                                                    <td>王副教授</td>
                                                    <td>省级精品在线开放课程</td>
                                                    <td><span class="badge badge-warning">省级</span></td>
                                                    <td>2023-10</td>
                                                    <td>省教育厅</td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="viewAwardDetail('A002')">查看详情</button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>计算机网络</td>
                                                    <td>李教授</td>
                                                    <td>校级优秀课程</td>
                                                    <td><span class="badge badge-info">校级</span></td>
                                                    <td>2023-09</td>
                                                    <td>学校</td>
                                                    <td>
                                                        <button class="btn btn-outline" onclick="viewAwardDetail('A003')">查看详情</button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 实践教学分析 -->
                    <div id="practice-teaching" class="content-panel">
                        <div class="alert alert-info">
                            <i class="fas fa-hands-helping"></i>
                            支持采集本科生必修实践教学和选修实践教学过程和结果数据，进行细化指标模型构建，展示学生思政政治教育、实践实习、专业实践、毕业论文、学科竞赛和科技创新等活动学习过程和成绩情况。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">3,256</div>
                                <div class="stat-label">参与实践教学学生数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-users"></i>
                                    全员参与
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">95.8%</div>
                                <div class="stat-label">实践教学完成率</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上学期 +1.2%
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">234</div>
                                <div class="stat-label">学科竞赛获奖</div>
                                <div class="stat-trend">
                                    <i class="fas fa-trophy"></i>
                                    各级获奖
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">156</div>
                                <div class="stat-label">科技创新项目</div>
                                <div class="stat-trend">
                                    <i class="fas fa-lightbulb"></i>
                                    创新实践
                                </div>
                            </div>
                        </div>

                        <div class="tabs">
                            <button class="tab active" onclick="showTab('ideological-education')">思政教育</button>
                            <button class="tab" onclick="showTab('practical-internship')">实践实习</button>
                            <button class="tab" onclick="showTab('professional-practice')">专业实践</button>
                            <button class="tab" onclick="showTab('graduation-thesis')">毕业论文</button>
                            <button class="tab" onclick="showTab('competition-innovation')">竞赛创新</button>
                        </div>

                        <div class="tab-content">
                            <div id="ideological-education" class="tab-panel active">
                                <div class="chart-container">
                                    <div class="chart-title">思政政治教育实践情况</div>
                                    <div class="data-table">
                                        <div class="table-content">
                                            <table>
                                                <thead>
                                                    <tr>
                                                        <th>实践项目</th>
                                                        <th>参与人数</th>
                                                        <th>完成率</th>
                                                        <th>优秀率</th>
                                                        <th>平均分</th>
                                                        <th>实践时长</th>
                                                        <th>效果评价</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>社会实践调研</td>
                                                        <td>856</td>
                                                        <td>98.2%</td>
                                                        <td>42.5%</td>
                                                        <td>87.6</td>
                                                        <td>2周</td>
                                                        <td><span class="badge badge-success">优秀</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td>志愿服务活动</td>
                                                        <td>1,245</td>
                                                        <td>96.8%</td>
                                                        <td>38.9%</td>
                                                        <td>85.3</td>
                                                        <td>40小时</td>
                                                        <td><span class="badge badge-success">良好</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td>红色教育基地参观</td>
                                                        <td>2,156</td>
                                                        <td>99.5%</td>
                                                        <td>45.2%</td>
                                                        <td>89.1</td>
                                                        <td>1天</td>
                                                        <td><span class="badge badge-success">优秀</span></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="graduation-thesis" class="tab-panel">
                                <div class="data-table">
                                    <div class="table-header">
                                        <div class="table-title">毕业论文完成情况</div>
                                        <input type="text" class="search-box" placeholder="搜索学生或指导教师...">
                                    </div>
                                    <div class="table-content">
                                        <table>
                                            <thead>
                                                <tr>
                                                    <th>学生姓名</th>
                                                    <th>学号</th>
                                                    <th>专业</th>
                                                    <th>论文题目</th>
                                                    <th>指导教师</th>
                                                    <th>完成进度</th>
                                                    <th>预期成绩</th>
                                                    <th>状态</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>张同学</td>
                                                    <td>2020001</td>
                                                    <td>计算机科学与技术</td>
                                                    <td>基于深度学习的图像识别系统设计</td>
                                                    <td>李教授</td>
                                                    <td>
                                                        <div class="progress-bar" style="width: 100px;">
                                                            <div class="progress-fill" style="width: 85%;"></div>
                                                        </div>
                                                        85%
                                                    </td>
                                                    <td><span class="badge badge-success">优秀</span></td>
                                                    <td><span class="badge badge-warning">进行中</span></td>
                                                </tr>
                                                <tr>
                                                    <td>王同学</td>
                                                    <td>2020002</td>
                                                    <td>软件工程</td>
                                                    <td>微服务架构下的电商系统设计与实现</td>
                                                    <td>赵副教授</td>
                                                    <td>
                                                        <div class="progress-bar" style="width: 100px;">
                                                            <div class="progress-fill" style="width: 92%;"></div>
                                                        </div>
                                                        92%
                                                    </td>
                                                    <td><span class="badge badge-success">良好</span></td>
                                                    <td><span class="badge badge-warning">进行中</span></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 毕业升学分析 -->
                    <div id="graduation-analysis" class="content-panel">
                        <div class="alert alert-success">
                            <i class="fas fa-graduation-cap"></i>
                            支持采集本科生毕业和升学数据，进行细化指标模型构建，展示学生毕业学位授予、毕业去向和升学读研等维度的数据分析情况。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">98.5%</div>
                                <div class="stat-label">毕业率</div>
                                <div class="stat-trend">
                                    <i class="fas fa-graduation-cap"></i>
                                    较上年 +0.8%
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">96.2%</div>
                                <div class="stat-label">学位授予率</div>
                                <div class="stat-trend">
                                    <i class="fas fa-medal"></i>
                                    较上年 +1.2%
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">45.8%</div>
                                <div class="stat-label">升学率</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上年 +3.5%
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">92.3%</div>
                                <div class="stat-label">就业率</div>
                                <div class="stat-trend">
                                    <i class="fas fa-briefcase"></i>
                                    较上年 +1.8%
                                </div>
                            </div>
                        </div>

                        <div class="tabs">
                            <button class="tab active" onclick="showTab('degree-award')">学位授予</button>
                            <button class="tab" onclick="showTab('graduation-destination')">毕业去向</button>
                            <button class="tab" onclick="showTab('further-study')">升学读研</button>
                        </div>

                        <div class="tab-content">
                            <div id="degree-award" class="tab-panel active">
                                <div class="chart-container">
                                    <div class="chart-title">各专业学位授予情况</div>
                                    <div class="data-table">
                                        <div class="table-content">
                                            <table>
                                                <thead>
                                                    <tr>
                                                        <th>专业</th>
                                                        <th>毕业生总数</th>
                                                        <th>获得学位人数</th>
                                                        <th>学位授予率</th>
                                                        <th>优秀毕业生</th>
                                                        <th>优秀率</th>
                                                        <th>趋势</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>计算机科学与技术</td>
                                                        <td>156</td>
                                                        <td>152</td>
                                                        <td>
                                                            <div class="progress-bar" style="width: 80px;">
                                                                <div class="progress-fill" style="width: 97.4%;"></div>
                                                            </div>
                                                            97.4%
                                                        </td>
                                                        <td>23</td>
                                                        <td>14.7%</td>
                                                        <td><span class="badge badge-success">↑</span></td>
                                                    </tr>
                                                    <tr>
                                                        <td>软件工程</td>
                                                        <td>128</td>
                                                        <td>124</td>
                                                        <td>
                                                            <div class="progress-bar" style="width: 80px;">
                                                                <div class="progress-fill" style="width: 96.9%;"></div>
                                                            </div>
                                                            96.9%
                                                        </td>
                                                        <td>18</td>
                                                        <td>14.1%</td>
                                                        <td><span class="badge badge-success">↑</span></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 教学团队结构分析 -->
                    <div id="team-structure" class="content-panel">
                        <div class="alert alert-info">
                            <i class="fas fa-users"></i>
                            支持采集本科专业教学团队中全校教师、专任教师、授课教师、实验技术人员、青年教师信息，进行细化指标模型构建，展示不同职称、年龄、最高学位和外聘情况。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">186</div>
                                <div class="stat-label">教学团队总人数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-users"></i>
                                    全校教师
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">156</div>
                                <div class="stat-label">专任教师</div>
                                <div class="stat-trend">
                                    <i class="fas fa-chalkboard-teacher"></i>
                                    占比 83.9%
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">142</div>
                                <div class="stat-label">授课教师</div>
                                <div class="stat-trend">
                                    <i class="fas fa-graduation-cap"></i>
                                    占比 76.3%
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">68</div>
                                <div class="stat-label">青年教师</div>
                                <div class="stat-trend">
                                    <i class="fas fa-user-graduate"></i>
                                    35岁以下
                                </div>
                            </div>
                        </div>

                        <div class="tabs">
                            <button class="tab active" onclick="showTab('title-structure')">职称结构</button>
                            <button class="tab" onclick="showTab('age-structure')">年龄结构</button>
                            <button class="tab" onclick="showTab('degree-structure')">学位结构</button>
                            <button class="tab" onclick="showTab('external-hire')">外聘情况</button>
                        </div>

                        <div class="tab-content">
                            <div id="title-structure" class="tab-panel active">
                                <div class="chart-container">
                                    <div class="chart-title">教学团队职称结构分析</div>
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                        <div>
                                            <h4>职称分布</h4>
                                            <div style="margin: 15px 0;">
                                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                                    <span>教授</span>
                                                    <span>45人 (24.2%)</span>
                                                </div>
                                                <div class="progress-bar">
                                                    <div class="progress-fill" style="width: 24.2%;"></div>
                                                </div>
                                            </div>
                                            <div style="margin: 15px 0;">
                                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                                    <span>副教授</span>
                                                    <span>68人 (36.6%)</span>
                                                </div>
                                                <div class="progress-bar">
                                                    <div class="progress-fill" style="width: 36.6%;"></div>
                                                </div>
                                            </div>
                                            <div style="margin: 15px 0;">
                                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                                    <span>讲师</span>
                                                    <span>58人 (31.2%)</span>
                                                </div>
                                                <div class="progress-bar">
                                                    <div class="progress-fill" style="width: 31.2%;"></div>
                                                </div>
                                            </div>
                                            <div style="margin: 15px 0;">
                                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                                    <span>助教</span>
                                                    <span>15人 (8.0%)</span>
                                                </div>
                                                <div class="progress-bar">
                                                    <div class="progress-fill" style="width: 8.0%;"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <h4>各专业职称分布</h4>
                                            <div class="data-table">
                                                <div class="table-content">
                                                    <table>
                                                        <thead>
                                                            <tr>
                                                                <th>专业</th>
                                                                <th>教授</th>
                                                                <th>副教授</th>
                                                                <th>讲师</th>
                                                                <th>助教</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td>计算机科学与技术</td>
                                                                <td>18</td>
                                                                <td>25</td>
                                                                <td>20</td>
                                                                <td>5</td>
                                                            </tr>
                                                            <tr>
                                                                <td>软件工程</td>
                                                                <td>12</td>
                                                                <td>18</td>
                                                                <td>15</td>
                                                                <td>4</td>
                                                            </tr>
                                                            <tr>
                                                                <td>数据科学与大数据</td>
                                                                <td>8</td>
                                                                <td>12</td>
                                                                <td>10</td>
                                                                <td>3</td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 授课情况分析 -->
                    <div id="teaching-performance" class="content-panel">
                        <div class="alert alert-info">
                            <i class="fas fa-chalkboard"></i>
                            支持采集本科专业教学团队中教师授课情况数据信息，进行细化指标模型构建，展示不同职称、不同类别、不同年龄段等维度的督导评教成绩、领导干部评教成绩、同行评教成绩、学生评教成绩、课程成绩及格率、课程成绩优秀率情况。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">4.6</div>
                                <div class="stat-label">督导评教平均分</div>
                                <div class="stat-trend">
                                    <i class="fas fa-star"></i>
                                    满分5.0
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">4.4</div>
                                <div class="stat-label">学生评教平均分</div>
                                <div class="stat-trend">
                                    <i class="fas fa-users"></i>
                                    学生评价
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">4.5</div>
                                <div class="stat-label">同行评教平均分</div>
                                <div class="stat-trend">
                                    <i class="fas fa-user-friends"></i>
                                    同行认可
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">94.2%</div>
                                <div class="stat-label">课程平均及格率</div>
                                <div class="stat-trend">
                                    <i class="fas fa-chart-line"></i>
                                    教学效果
                                </div>
                            </div>
                        </div>

                        <div class="data-table">
                            <div class="table-header">
                                <div class="table-title">教师评教成绩统计</div>
                                <input type="text" class="search-box" placeholder="搜索教师姓名...">
                            </div>
                            <div class="table-content">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>教师姓名</th>
                                            <th>职称</th>
                                            <th>年龄段</th>
                                            <th>督导评教</th>
                                            <th>领导评教</th>
                                            <th>同行评教</th>
                                            <th>学生评教</th>
                                            <th>综合评分</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>张教授</td>
                                            <td>教授</td>
                                            <td>45-55岁</td>
                                            <td>4.8</td>
                                            <td>4.7</td>
                                            <td>4.6</td>
                                            <td>4.5</td>
                                            <td><span class="badge badge-success">4.65</span></td>
                                        </tr>
                                        <tr>
                                            <td>李副教授</td>
                                            <td>副教授</td>
                                            <td>35-45岁</td>
                                            <td>4.5</td>
                                            <td>4.4</td>
                                            <td>4.3</td>
                                            <td>4.2</td>
                                            <td><span class="badge badge-success">4.35</span></td>
                                        </tr>
                                        <tr>
                                            <td>王讲师</td>
                                            <td>讲师</td>
                                            <td>25-35岁</td>
                                            <td>4.3</td>
                                            <td>4.2</td>
                                            <td>4.1</td>
                                            <td>4.0</td>
                                            <td><span class="badge badge-warning">4.15</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 指导情况分析 -->
                    <div id="guidance-analysis" class="content-panel">
                        <div class="alert alert-success">
                            <i class="fas fa-user-graduate"></i>
                            支持采集本科专业教学团队中教师指导学科竞赛、科技创新、毕业设计和部队实践等维度数据信息，进行细化指标模型构建，展示不同职称、不同类别、不同年龄段等维度的指导情况。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">456</div>
                                <div class="stat-label">指导毕业论文总数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-file-alt"></i>
                                    本年度
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">234</div>
                                <div class="stat-label">指导竞赛获奖</div>
                                <div class="stat-trend">
                                    <i class="fas fa-trophy"></i>
                                    各级获奖
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">156</div>
                                <div class="stat-label">指导大创项目</div>
                                <div class="stat-trend">
                                    <i class="fas fa-lightbulb"></i>
                                    创新项目
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">89</div>
                                <div class="stat-label">指导实践活动</div>
                                <div class="stat-trend">
                                    <i class="fas fa-hands-helping"></i>
                                    实践指导
                                </div>
                            </div>
                        </div>

                        <div class="data-table">
                            <div class="table-header">
                                <div class="table-title">教师指导毕业论文情况</div>
                                <input type="text" class="search-box" placeholder="搜索指导教师...">
                            </div>
                            <div class="table-content">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>指导教师</th>
                                            <th>职称</th>
                                            <th>年龄段</th>
                                            <th>指导学生数</th>
                                            <th>优秀论文数</th>
                                            <th>优秀率</th>
                                            <th>平均分</th>
                                            <th>指导质量</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>张教授</td>
                                            <td>教授</td>
                                            <td>45-55岁</td>
                                            <td>12</td>
                                            <td>5</td>
                                            <td>41.7%</td>
                                            <td>87.5</td>
                                            <td><span class="badge badge-success">优秀</span></td>
                                        </tr>
                                        <tr>
                                            <td>李副教授</td>
                                            <td>副教授</td>
                                            <td>35-45岁</td>
                                            <td>10</td>
                                            <td>3</td>
                                            <td>30.0%</td>
                                            <td>84.2</td>
                                            <td><span class="badge badge-success">良好</span></td>
                                        </tr>
                                        <tr>
                                            <td>王讲师</td>
                                            <td>讲师</td>
                                            <td>25-35岁</td>
                                            <td>8</td>
                                            <td>2</td>
                                            <td>25.0%</td>
                                            <td>82.1</td>
                                            <td><span class="badge badge-warning">中等</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 科研情况分析 -->
                    <div id="research-analysis" class="content-panel">
                        <div class="alert alert-warning">
                            <i class="fas fa-flask"></i>
                            支持采集本科专业教学团队中教师参与或主持科研项目、获得的科研成果等数据信息，进行细化指标模型构建，展示各专业教师发表教改论文情况、科研项目立项情况、发表科研论文情况。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">156</div>
                                <div class="stat-label">科研项目总数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-project-diagram"></i>
                                    在研项目
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">234</div>
                                <div class="stat-label">发表论文总数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-file-alt"></i>
                                    本年度
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">45</div>
                                <div class="stat-label">教改论文数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-edit"></i>
                                    教学改革
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">¥2,850万</div>
                                <div class="stat-label">科研经费总额</div>
                                <div class="stat-trend">
                                    <i class="fas fa-money-bill"></i>
                                    项目经费
                                </div>
                            </div>
                        </div>

                        <div class="data-table">
                            <div class="table-header">
                                <div class="table-title">教师科研项目立项情况</div>
                                <input type="text" class="search-box" placeholder="搜索项目或教师...">
                            </div>
                            <div class="table-content">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>项目名称</th>
                                            <th>项目负责人</th>
                                            <th>项目级别</th>
                                            <th>项目经费</th>
                                            <th>立项时间</th>
                                            <th>项目状态</th>
                                            <th>完成进度</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>基于深度学习的智能教学系统研究</td>
                                            <td>张教授</td>
                                            <td><span class="badge badge-danger">国家级</span></td>
                                            <td>¥50万</td>
                                            <td>2023-01</td>
                                            <td><span class="badge badge-warning">在研</span></td>
                                            <td>
                                                <div class="progress-bar" style="width: 100px;">
                                                    <div class="progress-fill" style="width: 65%;"></div>
                                                </div>
                                                65%
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>软件工程教学方法改革研究</td>
                                            <td>李副教授</td>
                                            <td><span class="badge badge-warning">省级</span></td>
                                            <td>¥15万</td>
                                            <td>2023-03</td>
                                            <td><span class="badge badge-warning">在研</span></td>
                                            <td>
                                                <div class="progress-bar" style="width: 100px;">
                                                    <div class="progress-fill" style="width: 45%;"></div>
                                                </div>
                                                45%
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 服务情况分析 -->
                    <div id="service-analysis" class="content-panel">
                        <div class="alert alert-info">
                            <i class="fas fa-handshake"></i>
                            支持采集本科专业教学团队中教师参与大项活动和外单位授课等数据信息，进行细化指标模型构建，展示各专业教师服务一线情况。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">89</div>
                                <div class="stat-label">参与大项活动</div>
                                <div class="stat-trend">
                                    <i class="fas fa-calendar-alt"></i>
                                    本年度
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">45</div>
                                <div class="stat-label">外单位授课</div>
                                <div class="stat-trend">
                                    <i class="fas fa-chalkboard-teacher"></i>
                                    对外服务
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">156</div>
                                <div class="stat-label">技术服务项目</div>
                                <div class="stat-trend">
                                    <i class="fas fa-cogs"></i>
                                    技术支持
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">234</div>
                                <div class="stat-label">服务时长(小时)</div>
                                <div class="stat-trend">
                                    <i class="fas fa-clock"></i>
                                    累计时长
                                </div>
                            </div>
                        </div>

                        <div class="data-table">
                            <div class="table-header">
                                <div class="table-title">教师服务一线情况统计</div>
                                <input type="text" class="search-box" placeholder="搜索教师或服务项目...">
                            </div>
                            <div class="table-content">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>教师姓名</th>
                                            <th>职称</th>
                                            <th>服务类型</th>
                                            <th>服务单位</th>
                                            <th>服务内容</th>
                                            <th>服务时长</th>
                                            <th>服务评价</th>
                                            <th>服务时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>张教授</td>
                                            <td>教授</td>
                                            <td>技术咨询</td>
                                            <td>某科技公司</td>
                                            <td>人工智能系统设计咨询</td>
                                            <td>40小时</td>
                                            <td><span class="badge badge-success">优秀</span></td>
                                            <td>2024-01</td>
                                        </tr>
                                        <tr>
                                            <td>李副教授</td>
                                            <td>副教授</td>
                                            <td>外单位授课</td>
                                            <td>某培训机构</td>
                                            <td>软件工程培训课程</td>
                                            <td>24小时</td>
                                            <td><span class="badge badge-success">良好</span></td>
                                            <td>2023-12</td>
                                        </tr>
                                        <tr>
                                            <td>王讲师</td>
                                            <td>讲师</td>
                                            <td>大项活动</td>
                                            <td>学校</td>
                                            <td>计算机技能大赛组织</td>
                                            <td>16小时</td>
                                            <td><span class="badge badge-success">优秀</span></td>
                                            <td>2023-11</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 风险预警识别 -->
                    <div id="risk-warning" class="content-panel">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            支持从本科专业人才培养目标达成度、课程和毕业要求方面，通过细化人才培养目标、课程和毕业要求指标要求，系统自动识别出预警风险。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">8</div>
                                <div class="stat-label">高风险预警</div>
                                <div class="stat-trend">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    需要立即处理
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">15</div>
                                <div class="stat-label">中风险预警</div>
                                <div class="stat-trend">
                                    <i class="fas fa-exclamation"></i>
                                    需要关注
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">23</div>
                                <div class="stat-label">低风险预警</div>
                                <div class="stat-trend">
                                    <i class="fas fa-info-circle"></i>
                                    一般关注
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">12</div>
                                <div class="stat-label">已处置风险</div>
                                <div class="stat-trend">
                                    <i class="fas fa-check-circle"></i>
                                    本月处置
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-success" onclick="createRiskRule()">
                                <i class="fas fa-plus"></i> 创建预警规则
                            </button>
                            <button class="btn btn-outline" onclick="exportRiskData()">
                                <i class="fas fa-download"></i> 导出预警数据
                            </button>
                            <button class="btn btn-outline" onclick="riskStatistics()">
                                <i class="fas fa-chart-bar"></i> 风险统计
                            </button>
                        </div>

                        <div class="data-table">
                            <div class="table-header">
                                <div class="table-title">专业建设风险预警列表</div>
                                <div class="filter-group">
                                    <span class="filter-label">风险等级:</span>
                                    <select class="form-control" style="width: 120px;">
                                        <option>全部</option>
                                        <option>高风险</option>
                                        <option>中风险</option>
                                        <option>低风险</option>
                                    </select>
                                </div>
                            </div>
                            <div class="table-content">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>预警单位</th>
                                            <th>预警类型</th>
                                            <th>风险等级</th>
                                            <th>预警内容</th>
                                            <th>预警时间</th>
                                            <th>责任人</th>
                                            <th>处理状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>计算机科学与技术专业</td>
                                            <td>培养目标达成度</td>
                                            <td><span class="badge badge-danger">高风险</span></td>
                                            <td>部分培养目标达成度低于80%</td>
                                            <td>2024-01-15</td>
                                            <td>张教授</td>
                                            <td><span class="badge badge-warning">处理中</span></td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewRiskDetail('R001')">查看详情</button>
                                                <button class="btn btn-success" onclick="processRisk('R001')">处理</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>软件工程专业</td>
                                            <td>课程建设</td>
                                            <td><span class="badge badge-warning">中风险</span></td>
                                            <td>核心课程更新频率偏低</td>
                                            <td>2024-01-12</td>
                                            <td>李副教授</td>
                                            <td><span class="badge badge-info">待处理</span></td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewRiskDetail('R002')">查看详情</button>
                                                <button class="btn btn-success" onclick="processRisk('R002')">处理</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 专业横向对比 -->
                    <div id="horizontal-comparison" class="content-panel">
                        <div class="alert alert-info">
                            <i class="fas fa-balance-scale"></i>
                            支持呈现专业之间横向对比分析本科专业的课程体系、学业水平和教学团队的建设运行情况，以及从不同专业取得的系列成绩成果情况。
                        </div>

                        <div class="filter-bar">
                            <div class="filter-group">
                                <span class="filter-label">对比专业:</span>
                                <select class="form-control" style="width: 200px;" multiple>
                                    <option selected>计算机科学与技术</option>
                                    <option selected>软件工程</option>
                                    <option>数据科学与大数据技术</option>
                                    <option>人工智能</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <span class="filter-label">对比维度:</span>
                                <select class="form-control" style="width: 150px;">
                                    <option>全部维度</option>
                                    <option>课程体系</option>
                                    <option>学业水平</option>
                                    <option>教学团队</option>
                                    <option>成绩成果</option>
                                </select>
                            </div>
                            <button class="btn btn-outline">
                                <i class="fas fa-chart-bar"></i> 开始对比
                            </button>
                        </div>

                        <div class="chart-container">
                            <div class="chart-title">专业综合对比分析</div>
                            <div class="data-table">
                                <div class="table-content">
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>对比维度</th>
                                                <th>计算机科学与技术</th>
                                                <th>软件工程</th>
                                                <th>数据科学与大数据</th>
                                                <th>人工智能</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><strong>课程体系建设</strong></td>
                                                <td>
                                                    <div class="progress-bar" style="width: 100px;">
                                                        <div class="progress-fill" style="width: 92%;"></div>
                                                    </div>
                                                    92分
                                                </td>
                                                <td>
                                                    <div class="progress-bar" style="width: 100px;">
                                                        <div class="progress-fill" style="width: 88%;"></div>
                                                    </div>
                                                    88分
                                                </td>
                                                <td>
                                                    <div class="progress-bar" style="width: 100px;">
                                                        <div class="progress-fill" style="width: 85%;"></div>
                                                    </div>
                                                    85分
                                                </td>
                                                <td>
                                                    <div class="progress-bar" style="width: 100px;">
                                                        <div class="progress-fill" style="width: 82%;"></div>
                                                    </div>
                                                    82分
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>学业水平</strong></td>
                                                <td>
                                                    <div class="progress-bar" style="width: 100px;">
                                                        <div class="progress-fill" style="width: 95%;"></div>
                                                    </div>
                                                    95.2%
                                                </td>
                                                <td>
                                                    <div class="progress-bar" style="width: 100px;">
                                                        <div class="progress-fill" style="width: 93%;"></div>
                                                    </div>
                                                    93.8%
                                                </td>
                                                <td>
                                                    <div class="progress-bar" style="width: 100px;">
                                                        <div class="progress-fill" style="width: 91%;"></div>
                                                    </div>
                                                    91.5%
                                                </td>
                                                <td>
                                                    <div class="progress-bar" style="width: 100px;">
                                                        <div class="progress-fill" style="width: 89%;"></div>
                                                    </div>
                                                    89.3%
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>教学团队</strong></td>
                                                <td>68人</td>
                                                <td>49人</td>
                                                <td>33人</td>
                                                <td>28人</td>
                                            </tr>
                                            <tr>
                                                <td><strong>教改项目</strong></td>
                                                <td>25项</td>
                                                <td>18项</td>
                                                <td>12项</td>
                                                <td>8项</td>
                                            </tr>
                                            <tr>
                                                <td><strong>获奖成果</strong></td>
                                                <td>15项</td>
                                                <td>12项</td>
                                                <td>8项</td>
                                                <td>5项</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 风险处置管理 -->
                    <div id="risk-disposal" class="content-panel">
                        <div class="alert alert-success">
                            <i class="fas fa-tools"></i>
                            支持风险责任人通过形成处置措施，结合处置手段，如：处置标记、处置记录和处置对比等，对预警风险进行处理，并可以发送消息提醒预警部门和预警主体。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">46</div>
                                <div class="stat-label">总风险数</div>
                                <div class="stat-trend">
                                    <i class="fas fa-list"></i>
                                    全部风险
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">12</div>
                                <div class="stat-label">已处置完成</div>
                                <div class="stat-trend">
                                    <i class="fas fa-check-circle"></i>
                                    完成率 26.1%
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">18</div>
                                <div class="stat-label">处置中</div>
                                <div class="stat-trend">
                                    <i class="fas fa-clock"></i>
                                    进行中
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">16</div>
                                <div class="stat-label">待处置</div>
                                <div class="stat-trend">
                                    <i class="fas fa-exclamation"></i>
                                    需要处理
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-success" onclick="batchDisposal()">
                                <i class="fas fa-tasks"></i> 批量处置
                            </button>
                            <button class="btn btn-outline" onclick="sendReminder()">
                                <i class="fas fa-bell"></i> 发送提醒
                            </button>
                            <button class="btn btn-outline" onclick="disposalReport()">
                                <i class="fas fa-file-alt"></i> 处置报告
                            </button>
                        </div>

                        <div class="data-table">
                            <div class="table-header">
                                <div class="table-title">风险处置管理列表</div>
                                <input type="text" class="search-box" placeholder="搜索风险内容...">
                            </div>
                            <div class="table-content">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>风险编号</th>
                                            <th>风险内容</th>
                                            <th>责任人</th>
                                            <th>处置措施</th>
                                            <th>处置进度</th>
                                            <th>预计完成时间</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>R001</td>
                                            <td>培养目标达成度偏低</td>
                                            <td>张教授</td>
                                            <td>修订培养方案，加强实践环节</td>
                                            <td>
                                                <div class="progress-bar" style="width: 100px;">
                                                    <div class="progress-fill" style="width: 75%;"></div>
                                                </div>
                                                75%
                                            </td>
                                            <td>2024-03-15</td>
                                            <td><span class="badge badge-warning">处置中</span></td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewDisposalDetail('R001')">查看详情</button>
                                                <button class="btn btn-success" onclick="updateDisposal('R001')">更新进度</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>R002</td>
                                            <td>课程内容更新滞后</td>
                                            <td>李副教授</td>
                                            <td>引入前沿技术，更新教学内容</td>
                                            <td>
                                                <div class="progress-bar" style="width: 100px;">
                                                    <div class="progress-fill" style="width: 100%;"></div>
                                                </div>
                                                100%
                                            </td>
                                            <td>2024-02-28</td>
                                            <td><span class="badge badge-success">已完成</span></td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewDisposalDetail('R002')">查看详情</button>
                                                <button class="btn btn-info" onclick="evaluateDisposal('R002')">评价</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 质量分析报告 -->
                    <div id="quality-report" class="content-panel">
                        <div class="alert alert-info">
                            <i class="fas fa-file-alt"></i>
                            支持评价分析结果，输出本科专业建设质量分析报告。可以支持将处置评价打分与人事系统相结合，用于质量的持续跟踪反馈与提升。
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">12</div>
                                <div class="stat-label">已生成报告</div>
                                <div class="stat-trend">
                                    <i class="fas fa-file-alt"></i>
                                    本学期
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">88.5</div>
                                <div class="stat-label">平均质量评分</div>
                                <div class="stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    较上学期 +2.3
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">95.2%</div>
                                <div class="stat-label">改进措施完成率</div>
                                <div class="stat-trend">
                                    <i class="fas fa-check-circle"></i>
                                    持续改进
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">4</div>
                                <div class="stat-label">专业数量</div>
                                <div class="stat-trend">
                                    <i class="fas fa-graduation-cap"></i>
                                    本科专业
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-success" onclick="generateQualityReport()">
                                <i class="fas fa-plus"></i> 生成新报告
                            </button>
                            <button class="btn btn-outline" onclick="exportAllReports()">
                                <i class="fas fa-download"></i> 导出所有报告
                            </button>
                            <button class="btn btn-outline" onclick="reportTemplate()">
                                <i class="fas fa-file-template"></i> 报告模板
                            </button>
                        </div>

                        <div class="data-table">
                            <div class="table-header">
                                <div class="table-title">专业建设质量分析报告列表</div>
                                <input type="text" class="search-box" placeholder="搜索报告名称...">
                            </div>
                            <div class="table-content">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>报告名称</th>
                                            <th>专业</th>
                                            <th>报告周期</th>
                                            <th>质量评分</th>
                                            <th>改进建议数</th>
                                            <th>生成时间</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>2024春季学期专业建设质量报告</td>
                                            <td>计算机科学与技术</td>
                                            <td>2024春季</td>
                                            <td>
                                                <div class="progress-bar" style="width: 80px;">
                                                    <div class="progress-fill" style="width: 92%;"></div>
                                                </div>
                                                92分
                                            </td>
                                            <td>8项</td>
                                            <td>2024-01-15</td>
                                            <td><span class="badge badge-success">已完成</span></td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewReport('RPT001')">查看报告</button>
                                                <button class="btn btn-success" onclick="downloadReport('RPT001')">下载</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>2024春季学期专业建设质量报告</td>
                                            <td>软件工程</td>
                                            <td>2024春季</td>
                                            <td>
                                                <div class="progress-bar" style="width: 80px;">
                                                    <div class="progress-fill" style="width: 88%;"></div>
                                                </div>
                                                88分
                                            </td>
                                            <td>12项</td>
                                            <td>2024-01-12</td>
                                            <td><span class="badge badge-success">已完成</span></td>
                                            <td>
                                                <button class="btn btn-outline" onclick="viewReport('RPT002')">查看报告</button>
                                                <button class="btn btn-success" onclick="downloadReport('RPT002')">下载</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="detailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">详细信息</h3>
                <span class="close" onclick="closeModal('detailModal')">&times;</span>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 模态框内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSystem = 'course';
        let currentContent = 'course-content';

        // 系统切换
        function switchSystem(system) {
            // 更新导航菜单状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            // 隐藏所有菜单系统
            document.querySelectorAll('.menu-system').forEach(menu => {
                menu.style.display = 'none';
            });

            // 显示对应的菜单系统
            currentSystem = system;
            if (system === 'course') {
                document.getElementById('courseMenu').style.display = 'block';
                showContent('course-content');
            } else if (system === 'academic') {
                document.getElementById('academicMenu').style.display = 'block';
                showContent('course-performance');
            } else if (system === 'team') {
                document.getElementById('teamMenu').style.display = 'block';
                showContent('team-structure');
            } else if (system === 'evaluation') {
                document.getElementById('evaluationMenu').style.display = 'block';
                showContent('risk-warning');
            }
        }

        // 内容切换
        function showContent(contentId) {
            // 隐藏所有内容面板
            document.querySelectorAll('.content-panel').forEach(panel => {
                panel.classList.remove('active');
            });

            // 移除所有菜单项的active状态
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
            });

            // 显示选中的内容面板
            const targetPanel = document.getElementById(contentId);
            if (targetPanel) {
                targetPanel.classList.add('active');
            }

            // 激活对应的菜单项
            event.target.classList.add('active');

            // 更新页面标题和图标
            updateContentHeader(contentId);
            currentContent = contentId;
        }

        // 更新内容头部
        function updateContentHeader(contentId) {
            const titleMap = {
                'course-content': { icon: 'fas fa-book', title: '课程内容指标分析' },
                'course-materials': { icon: 'fas fa-book-open', title: '课程教材分析' },
                'course-evaluation': { icon: 'fas fa-star', title: '课程评价分析' },
                'course-achievement': { icon: 'fas fa-trophy', title: '课程建设成效' },
                'course-performance': { icon: 'fas fa-chart-line', title: '课程学习分析' },
                'practice-teaching': { icon: 'fas fa-hands-helping', title: '实践教学分析' },
                'graduation-analysis': { icon: 'fas fa-graduation-cap', title: '毕业升学分析' },
                'team-structure': { icon: 'fas fa-users', title: '教学团队结构分析' },
                'teaching-performance': { icon: 'fas fa-chalkboard', title: '授课情况分析' },
                'guidance-analysis': { icon: 'fas fa-user-graduate', title: '指导情况分析' },
                'research-analysis': { icon: 'fas fa-flask', title: '科研情况分析' },
                'service-analysis': { icon: 'fas fa-handshake', title: '服务情况分析' },
                'risk-warning': { icon: 'fas fa-exclamation-triangle', title: '风险预警识别' },
                'horizontal-comparison': { icon: 'fas fa-balance-scale', title: '专业横向对比' },
                'risk-disposal': { icon: 'fas fa-tools', title: '风险处置管理' },
                'quality-report': { icon: 'fas fa-file-alt', title: '质量分析报告' }
            };

            const info = titleMap[contentId];
            if (info) {
                document.getElementById('contentIcon').className = info.icon;
                document.getElementById('contentTitle').textContent = info.title;
            }
        }

        // 标签切换
        function showTab(tabId) {
            // 移除所有标签的active状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 隐藏所有标签面板
            document.querySelectorAll('.tab-panel').forEach(panel => {
                panel.classList.remove('active');
            });

            // 激活当前标签
            event.target.classList.add('active');

            // 显示对应的标签面板
            const targetPanel = document.getElementById(tabId);
            if (targetPanel) {
                targetPanel.classList.add('active');
            }
        }

        // 通用功能函数
        function exportAnalysisData() {
            alert('导出分析数据功能');
        }

        function generateReport() {
            alert('生成报告功能');
        }

        function refreshData() {
            alert('刷新数据功能');
        }

        // 课程相关函数
        function viewCourseDetail(courseId) {
            document.getElementById('modalTitle').textContent = '课程详细信息';
            document.getElementById('modalBody').innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h4>基本信息</h4>
                        <p><strong>课程编号:</strong> ${courseId}</p>
                        <p><strong>课程名称:</strong> 数据结构与算法</p>
                        <p><strong>学分:</strong> 4学分</p>
                        <p><strong>课时:</strong> 64课时</p>
                        <p><strong>开课学期:</strong> 第3学期</p>
                    </div>
                    <div>
                        <h4>教学设计理念</h4>
                        <p><strong>以学生为中心:</strong> 95%融入度</p>
                        <p><strong>成果导向教育:</strong> 90%融入度</p>
                        <p><strong>持续改进:</strong> 88%融入度</p>
                        <p><strong>最后更新:</strong> 2024-01-15</p>
                    </div>
                </div>
                <div style="margin-top: 20px;">
                    <h4>课程思政融入情况</h4>
                    <p>该课程通过算法设计培养学生的逻辑思维能力，通过团队项目培养协作精神，体现了计算机专业的工匠精神和创新意识。</p>
                </div>
            `;
            document.getElementById('detailModal').style.display = 'block';
        }

        function viewMaterialDetail(materialId) {
            document.getElementById('modalTitle').textContent = '教材详细信息';
            document.getElementById('modalBody').innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h4>教材基本信息</h4>
                        <p><strong>教材编号:</strong> ${materialId}</p>
                        <p><strong>教材名称:</strong> 数据结构与算法分析</p>
                        <p><strong>作者:</strong> 张三, 李四</p>
                        <p><strong>出版社:</strong> 清华大学出版社</p>
                        <p><strong>出版年份:</strong> 2023年</p>
                        <p><strong>ISBN:</strong> 978-7-302-12345-6</p>
                    </div>
                    <div>
                        <h4>质量评价</h4>
                        <p><strong>项目支撑:</strong> 国家级教改项目</p>
                        <p><strong>获奖情况:</strong> 省级优秀教材</p>
                        <p><strong>获奖级别:</strong> 省级</p>
                        <p><strong>使用评价:</strong> 4.8/5.0</p>
                        <p><strong>更新频率:</strong> 每2年更新</p>
                    </div>
                </div>
            `;
            document.getElementById('detailModal').style.display = 'block';
        }

        // 风险管理相关函数
        function createRiskRule() {
            alert('创建预警规则功能');
        }

        function exportRiskData() {
            alert('导出预警数据功能');
        }

        function riskStatistics() {
            alert('风险统计功能');
        }

        function viewRiskDetail(riskId) {
            document.getElementById('modalTitle').textContent = '风险详细信息';
            document.getElementById('modalBody').innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h4>风险基本信息</h4>
                        <p><strong>风险编号:</strong> ${riskId}</p>
                        <p><strong>预警单位:</strong> 计算机科学与技术专业</p>
                        <p><strong>预警类型:</strong> 培养目标达成度</p>
                        <p><strong>风险等级:</strong> <span class="badge badge-danger">高风险</span></p>
                        <p><strong>预警时间:</strong> 2024-01-15</p>
                        <p><strong>责任人:</strong> 张教授</p>
                    </div>
                    <div>
                        <h4>风险详情</h4>
                        <p><strong>预警内容:</strong> 部分培养目标达成度低于80%</p>
                        <p><strong>影响范围:</strong> 2023级学生</p>
                        <p><strong>预期影响:</strong> 可能影响专业认证</p>
                        <p><strong>建议措施:</strong> 修订培养方案，加强实践环节</p>
                    </div>
                </div>
                <div style="margin-top: 20px;">
                    <h4>处理记录</h4>
                    <div class="alert alert-info">
                        已启动培养方案修订工作，预计2024年3月完成。
                    </div>
                </div>
            `;
            document.getElementById('detailModal').style.display = 'block';
        }

        function processRisk(riskId) {
            alert(`处理风险: ${riskId}`);
        }

        // 风险处置相关函数
        function batchDisposal() {
            alert('批量处置功能');
        }

        function sendReminder() {
            alert('发送提醒功能');
        }

        function disposalReport() {
            alert('处置报告功能');
        }

        function viewDisposalDetail(riskId) {
            document.getElementById('modalTitle').textContent = '风险处置详情';
            document.getElementById('modalBody').innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h4>处置基本信息</h4>
                        <p><strong>风险编号:</strong> ${riskId}</p>
                        <p><strong>风险内容:</strong> 培养目标达成度偏低</p>
                        <p><strong>责任人:</strong> 张教授</p>
                        <p><strong>开始时间:</strong> 2024-01-15</p>
                        <p><strong>预计完成:</strong> 2024-03-15</p>
                    </div>
                    <div>
                        <h4>处置进展</h4>
                        <p><strong>当前进度:</strong> 75%</p>
                        <p><strong>处置状态:</strong> <span class="badge badge-warning">处置中</span></p>
                        <p><strong>最后更新:</strong> 2024-01-20</p>
                        <p><strong>下一步计划:</strong> 完成方案评审</p>
                    </div>
                </div>
                <div style="margin-top: 20px;">
                    <h4>处置措施</h4>
                    <p>1. 修订培养方案，增加实践环节比重</p>
                    <p>2. 加强校企合作，提升实践教学质量</p>
                    <p>3. 建立培养目标达成度评价机制</p>
                    <p>4. 定期跟踪评估改进效果</p>
                </div>
            `;
            document.getElementById('detailModal').style.display = 'block';
        }

        function updateDisposal(riskId) {
            alert(`更新处置进度: ${riskId}`);
        }

        function evaluateDisposal(riskId) {
            alert(`评价处置效果: ${riskId}`);
        }

        // 质量报告相关函数
        function generateQualityReport() {
            alert('生成质量分析报告功能');
        }

        function exportAllReports() {
            alert('导出所有报告功能');
        }

        function reportTemplate() {
            alert('报告模板管理功能');
        }

        function viewReport(reportId) {
            document.getElementById('modalTitle').textContent = '质量分析报告';
            document.getElementById('modalBody').innerHTML = `
                <div style="max-height: 400px; overflow-y: auto;">
                    <h4>2024春季学期专业建设质量报告</h4>
                    <p><strong>专业:</strong> 计算机科学与技术</p>
                    <p><strong>报告周期:</strong> 2024春季学期</p>
                    <p><strong>生成时间:</strong> 2024-01-15</p>

                    <h5>一、专业建设概况</h5>
                    <p>本专业本学期在课程体系建设、教学团队建设、学生培养质量等方面取得了显著进展。</p>

                    <h5>二、主要成绩</h5>
                    <ul>
                        <li>课程体系建设评分92分，较上学期提升3分</li>
                        <li>教学团队结构进一步优化，博士比例达到85%</li>
                        <li>学生学业水平稳步提升，及格率达到95.2%</li>
                        <li>获得省级教学成果奖2项</li>
                    </ul>

                    <h5>三、存在问题</h5>
                    <ul>
                        <li>部分课程内容更新滞后</li>
                        <li>实践教学环节需要加强</li>
                        <li>国际化程度有待提升</li>
                    </ul>

                    <h5>四、改进建议</h5>
                    <ul>
                        <li>加快课程内容更新，引入前沿技术</li>
                        <li>加强校企合作，提升实践教学质量</li>
                        <li>推进国际化建设，引入国外优质资源</li>
                    </ul>

                    <h5>五、质量评分</h5>
                    <p><strong>综合评分:</strong> 92分</p>
                    <div class="progress-bar" style="width: 200px;">
                        <div class="progress-fill" style="width: 92%;"></div>
                    </div>
                </div>
            `;
            document.getElementById('detailModal').style.display = 'block';
        }

        function downloadReport(reportId) {
            alert(`下载报告: ${reportId}`);
        }

        // 课程评价相关函数
        function viewEvaluationDetail(evaluationId) {
            document.getElementById('modalTitle').textContent = '课程评价详情';
            document.getElementById('modalBody').innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h4>评价基本信息</h4>
                        <p><strong>评价编号:</strong> ${evaluationId}</p>
                        <p><strong>课程名称:</strong> 数据结构与算法</p>
                        <p><strong>授课教师:</strong> 张教授</p>
                        <p><strong>督导专家:</strong> 李督导</p>
                        <p><strong>评价时间:</strong> 2024-01-15</p>
                    </div>
                    <div>
                        <h4>评价结果</h4>
                        <p><strong>评价分数:</strong> 4.8分</p>
                        <p><strong>评价等级:</strong> <span class="badge badge-success">优秀</span></p>
                        <p><strong>课堂表现:</strong> 教学内容丰富，方法得当</p>
                        <p><strong>学生反馈:</strong> 课堂氛围活跃，学生参与度高</p>
                    </div>
                </div>
                <div style="margin-top: 20px;">
                    <h4>评价意见</h4>
                    <p>该教师教学准备充分，教学方法灵活多样，能够很好地调动学生的学习积极性。建议在实践环节进一步加强，增加更多的案例分析。</p>
                </div>
            `;
            document.getElementById('detailModal').style.display = 'block';
        }

        function viewAwardDetail(awardId) {
            document.getElementById('modalTitle').textContent = '获奖详情';
            document.getElementById('modalBody').innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h4>获奖基本信息</h4>
                        <p><strong>获奖编号:</strong> ${awardId}</p>
                        <p><strong>课程名称:</strong> 数据结构与算法</p>
                        <p><strong>负责人:</strong> 张教授</p>
                        <p><strong>获奖名称:</strong> 国家级一流本科课程</p>
                        <p><strong>获奖时间:</strong> 2023-12</p>
                    </div>
                    <div>
                        <h4>获奖详情</h4>
                        <p><strong>获奖级别:</strong> <span class="badge badge-danger">国家级</span></p>
                        <p><strong>颁奖单位:</strong> 教育部</p>
                        <p><strong>证书编号:</strong> YLKC2023001</p>
                        <p><strong>获奖理由:</strong> 课程建设成效显著</p>
                    </div>
                </div>
                <div style="margin-top: 20px;">
                    <h4>课程特色</h4>
                    <p>该课程采用线上线下混合式教学模式，注重理论与实践相结合，教学效果显著，学生评价良好，在同类课程中具有示范作用。</p>
                </div>
            `;
            document.getElementById('detailModal').style.display = 'block';
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('教学支撑数智分析系统已加载');

            // 点击模态框外部关闭
            window.addEventListener('click', function(e) {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    if (e.target === modal) {
                        modal.style.display = 'none';
                    }
                });
            });
        });
    </script>
</body>
</html>
