<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>派单管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .layout {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #34495e;
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #2c3e50;
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-size: 18px;
            font-weight: bold;
        }
        
        .nav-menu {
            padding: 20px 0;
        }
        
        .nav-item {
            display: block;
            padding: 12px 20px;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .nav-item:hover,
        .nav-item.active {
            background: #2c3e50;
            color: white;
            border-left-color: #e74c3c;
        }
        
        .main-content {
            flex: 1;
            margin-left: 250px;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: white;
            padding: 15px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .content-area {
            flex: 1;
            display: flex;
            padding: 20px;
            gap: 20px;
        }
        
        .map-section {
            flex: 2;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .control-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .panel-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .map-container {
            height: 600px;
            background: #f8f9fa;
            border-radius: 8px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
            font-size: 16px;
            background-image: 
                radial-gradient(circle at 25% 25%, #e3f2fd 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, #f3e5f5 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, #e8f5e8 0%, transparent 50%);
        }
        
        .grid-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            pointer-events: none;
        }
        
        .vehicle-marker {
            position: absolute;
            width: 30px;
            height: 30px;
            background: #2ecc71;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        
        .vehicle-marker:hover {
            transform: scale(1.2);
        }
        
        .vehicle-marker.busy {
            background: #e74c3c;
        }
        
        .vehicle-marker.offline {
            background: #95a5a6;
        }
        
        .order-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .form-label {
            font-weight: 500;
            color: #2c3e50;
        }
        
        .form-input,
        .form-select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-input:focus,
        .form-select:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .location-picker {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .location-input {
            flex: 1;
        }
        
        .pick-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #2ecc71;
            color: white;
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .vehicle-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .vehicle-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #ecf0f1;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .vehicle-item:hover {
            background: #f8f9fa;
        }
        
        .vehicle-item.selected {
            background: #e3f2fd;
            border-left: 3px solid #3498db;
        }
        
        .vehicle-info {
            display: flex;
            flex-direction: column;
            gap: 3px;
        }
        
        .vehicle-plate {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .vehicle-driver {
            font-size: 12px;
            color: #7f8c8d;
        }
        
        .vehicle-status {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .status-available {
            background: #d5f4e6;
            color: #27ae60;
        }
        
        .status-busy {
            background: #fadbd8;
            color: #e74c3c;
        }
        
        .status-offline {
            background: #f4f6f6;
            color: #95a5a6;
        }
        
        .dispatch-controls {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .stats-bar {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
            flex: 1;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .stat-label {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="layout">
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    🚕 派单管理系统
                </div>
            </div>
            
            <div class="nav-menu">
                <a href="#" class="nav-item active" onclick="showDispatchMap()">
                    🗺️ 派单地图
                </a>
                <a href="#" class="nav-item" onclick="showOrderCreate()">
                    📞 话务员订单
                </a>
                <a href="#" class="nav-item" onclick="showDispatchHistory()">
                    📋 派单历史
                </a>
                <a href="#" class="nav-item" onclick="showVehicleMonitor()">
                    🚗 车辆监控
                </a>
                <a href="#" class="nav-item" onclick="showSettings()">
                    ⚙️ 系统设置
                </a>
            </div>
        </nav>
        
        <main class="main-content">
            <div class="header">
                <h1 class="page-title">派单地图</h1>
                <div class="user-info">
                    <span>派单员：张三</span>
                    <button class="btn btn-danger" onclick="logout()">退出</button>
                </div>
            </div>
            
            <div class="stats-bar">
                <div class="stat-item">
                    <div class="stat-value">45</div>
                    <div class="stat-label">在线车辆</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">12</div>
                    <div class="stat-label">待派订单</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">8</div>
                    <div class="stat-label">进行中</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">156</div>
                    <div class="stat-label">今日完成</div>
                </div>
            </div>
            
            <div class="content-area">
                <div class="map-section">
                    <div class="card-title">网格地图 - 实时车辆分布</div>
                    <div class="map-container" id="mapContainer">
                        <div class="grid-overlay"></div>
                        🗺️ 网格地图区域
                        <br>
                        <small>显示车辆实时位置和网格分布</small>
                        
                        <!-- 模拟车辆标记 -->
                        <div class="vehicle-marker" style="top: 20%; left: 30%;" onclick="selectVehicle('京A12345')" title="京A12345 - 张师傅">🚗</div>
                        <div class="vehicle-marker busy" style="top: 60%; left: 70%;" onclick="selectVehicle('京B67890')" title="京B67890 - 李师傅">🚗</div>
                        <div class="vehicle-marker" style="top: 40%; left: 50%;" onclick="selectVehicle('京C11111')" title="京C11111 - 王师傅">🚗</div>
                        <div class="vehicle-marker offline" style="top: 80%; left: 20%;" onclick="selectVehicle('京D22222')" title="京D22222 - 赵师傅">🚗</div>
                    </div>
                </div>
                
                <div class="control-panel">
                    <div class="panel-card">
                        <div class="card-title">创建订单</div>
                        <form class="order-form" onsubmit="createOrder(event)">
                            <div class="form-group">
                                <label class="form-label">乘客姓名</label>
                                <input type="text" class="form-input" placeholder="请输入乘客姓名" required>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">联系电话</label>
                                <input type="tel" class="form-input" placeholder="请输入联系电话" required>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">出发地</label>
                                <div class="location-picker">
                                    <input type="text" class="form-input location-input" id="startLocation" placeholder="请输入出发地址" required>
                                    <button type="button" class="pick-btn" onclick="pickLocation('start')">📍</button>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">目的地</label>
                                <div class="location-picker">
                                    <input type="text" class="form-input location-input" id="endLocation" placeholder="请输入目的地址" required>
                                    <button type="button" class="pick-btn" onclick="pickLocation('end')">📍</button>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">预约时间</label>
                                <select class="form-select">
                                    <option value="now">立即用车</option>
                                    <option value="schedule">预约用车</option>
                                </select>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">创建订单</button>
                        </form>
                    </div>
                    
                    <div class="panel-card">
                        <div class="card-title">附近车辆</div>
                        <div class="vehicle-list" id="vehicleList">
                            <div class="vehicle-item" onclick="selectVehicleFromList(this, '京A12345')">
                                <div class="vehicle-info">
                                    <div class="vehicle-plate">京A12345</div>
                                    <div class="vehicle-driver">张师傅 | 距离: 0.8km</div>
                                </div>
                                <span class="vehicle-status status-available">空闲</span>
                            </div>
                            
                            <div class="vehicle-item" onclick="selectVehicleFromList(this, '京C11111')">
                                <div class="vehicle-info">
                                    <div class="vehicle-plate">京C11111</div>
                                    <div class="vehicle-driver">王师傅 | 距离: 1.2km</div>
                                </div>
                                <span class="vehicle-status status-available">空闲</span>
                            </div>
                            
                            <div class="vehicle-item" onclick="selectVehicleFromList(this, '京B67890')">
                                <div class="vehicle-info">
                                    <div class="vehicle-plate">京B67890</div>
                                    <div class="vehicle-driver">李师傅 | 距离: 2.1km</div>
                                </div>
                                <span class="vehicle-status status-busy">忙碌</span>
                            </div>
                        </div>
                        
                        <div class="dispatch-controls">
                            <button class="btn btn-success" onclick="autoDispatch()">自动派单</button>
                            <button class="btn btn-warning" onclick="manualDispatch()">手动派单</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        let selectedVehicle = null;
        let currentOrder = null;
        
        function showDispatchMap() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '派单地图';
        }
        
        function showOrderCreate() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '话务员订单';
        }
        
        function showDispatchHistory() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '派单历史';
        }
        
        function showVehicleMonitor() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '车辆监控';
        }
        
        function showSettings() {
            updateActiveNav(event.target);
            document.querySelector('.page-title').textContent = '系统设置';
        }
        
        function updateActiveNav(target) {
            document.querySelectorAll('.nav-item').forEach(item => item.classList.remove('active'));
            target.classList.add('active');
        }
        
        function selectVehicle(plateNumber) {
            selectedVehicle = plateNumber;
            console.log('选中车辆:', plateNumber);
            
            // 高亮选中的车辆
            document.querySelectorAll('.vehicle-marker').forEach(marker => {
                marker.style.border = 'none';
            });
            event.target.style.border = '3px solid #f39c12';
        }
        
        function selectVehicleFromList(element, plateNumber) {
            selectedVehicle = plateNumber;
            
            // 更新列表选中状态
            document.querySelectorAll('.vehicle-item').forEach(item => item.classList.remove('selected'));
            element.classList.add('selected');
            
            console.log('从列表选中车辆:', plateNumber);
        }
        
        function pickLocation(type) {
            // 模拟地图坐标拾取
            const mockCoordinates = {
                lat: 39.9042 + (Math.random() - 0.5) * 0.1,
                lng: 116.4074 + (Math.random() - 0.5) * 0.1
            };
            
            const address = prompt('请在地图上选择位置，或输入地址：');
            if (address) {
                const inputId = type === 'start' ? 'startLocation' : 'endLocation';
                document.getElementById(inputId).value = address;
                
                // 转换网格编码
                const gridCode = convertToGridCode(mockCoordinates);
                console.log(`${type}位置:`, { address, coordinates: mockCoordinates, gridCode });
            }
        }
        
        function convertToGridCode(coordinates) {
            // 模拟网格编码转换
            const gridX = Math.floor((coordinates.lng - 116.0) * 100);
            const gridY = Math.floor((coordinates.lat - 39.5) * 100);
            return `G${gridX}_${gridY}`;
        }
        
        function createOrder(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const orderData = {
                passengerName: formData.get('passengerName') || event.target.querySelector('input[placeholder="请输入乘客姓名"]').value,
                phone: formData.get('phone') || event.target.querySelector('input[placeholder="请输入联系电话"]').value,
                startLocation: document.getElementById('startLocation').value,
                endLocation: document.getElementById('endLocation').value,
                scheduleType: event.target.querySelector('select').value,
                orderId: 'ORD' + Date.now(),
                timestamp: new Date().toISOString()
            };
            
            if (!orderData.startLocation || !orderData.endLocation) {
                alert('请填写完整的出发地和目的地');
                return;
            }
            
            currentOrder = orderData;
            console.log('创建订单:', orderData);
            alert('订单创建成功！订单号：' + orderData.orderId);
            
            // 计算里程和价格
            calculateDistance(orderData);
        }
        
        function calculateDistance(orderData) {
            // 模拟里程和价格计算
            const mockDistance = (Math.random() * 20 + 5).toFixed(1); // 5-25公里
            const mockPrice = (mockDistance * 2.5 + 10).toFixed(0); // 起步价10元 + 2.5元/公里
            
            orderData.distance = mockDistance + 'km';
            orderData.estimatedPrice = '¥' + mockPrice;
            
            console.log('计算结果:', { distance: orderData.distance, price: orderData.estimatedPrice });
        }
        
        function autoDispatch() {
            if (!currentOrder) {
                alert('请先创建订单');
                return;
            }
            
            // 自动选择最近的空闲车辆
            const availableVehicles = ['京A12345', '京C11111'];
            const selectedVehicle = availableVehicles[0];
            
            console.log('自动派单:', { order: currentOrder, vehicle: selectedVehicle });
            alert(`自动派单成功！\n订单：${currentOrder.orderId}\n车辆：${selectedVehicle}`);
            
            // 扩展周边网格派单逻辑
            expandGridDispatch(currentOrder);
        }
        
        function manualDispatch() {
            if (!currentOrder) {
                alert('请先创建订单');
                return;
            }
            
            if (!selectedVehicle) {
                alert('请先选择车辆');
                return;
            }
            
            console.log('手动派单:', { order: currentOrder, vehicle: selectedVehicle });
            alert(`手动派单成功！\n订单：${currentOrder.orderId}\n车辆：${selectedVehicle}`);
        }
        
        function expandGridDispatch(order) {
            // 模拟周边网格扩展派单
            console.log('扩展周边网格派单:', order);
        }
        
        function logout() {
            if (confirm('确认退出登录？')) {
                window.location.href = 'dispatch-login.html';
            }
        }
        
        // 页面加载时初始化
        window.addEventListener('load', () => {
            console.log('派单系统初始化完成');
        });
    </script>
</body>
</html>
