<script>
export default {
  onLaunch: function() {
    console.log('App Launch');
  },
  onShow: function() {
    console.log('App Show');
  },
  onHide: function() {
    console.log('App Hide');
  }
};
</script>

<style>
/* 全局样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #f8fffe;
  color: #333;
}

.container {
  min-height: 100vh;
  background-color: #f8fffe;
}

/* 通用样式 */
.btn {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 24rpx 48rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  cursor: pointer;
  transition: background 0.3s;
}

.btn:hover {
  background: #388E3C;
}

.btn-secondary {
  background: #ecf0f1;
  color: #2c3e50;
}

.btn-secondary:hover {
  background: #d5dbdb;
}

/* 通知样式 */
.notification {
  background: #27ae60;
  color: white;
  padding: 24rpx 48rpx;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.2);
}

.notification.error {
  background: #e74c3c;
}

.notification.warning {
  background: #f39c12;
}
</style>