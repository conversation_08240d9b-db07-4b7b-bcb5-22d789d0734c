document.addEventListener('DOMContentLoaded', function() {
    // 全局变量
    let currentDate = new Date();
    let currentUser = {
        id: 1,
        name: '张三',
        department: '技术部',
        role: 'user' // 'admin' 或 'user'
    };
    let departments = ['技术部', '市场部', '人事部', '财务部'];
    let allUsers = [
        { id: 1, name: '张三', department: '技术部', role: 'user' },
        { id: 2, name: '李四', department: '技术部', role: 'user' },
        { id: 3, name: '王五', department: '市场部', role: 'user' },
        { id: 4, name: '赵六', department: '人事部', role: 'user' },
        { id: 5, name: '钱七', department: '财务部', role: 'user' },
        { id: 6, name: '孙八', department: '公司', role: 'admin' }
    ];
    let reports = JSON.parse(localStorage.getItem('dailyReports')) || [];

    // DOM元素
    const calendarEl = document.querySelector('.calendar');
    const currentMonthEl = document.getElementById('current-month');
    const prevMonthBtn = document.getElementById('prev-month');
    const nextMonthBtn = document.getElementById('next-month');
    const reportModal = document.getElementById('report-modal');
    const modalTitle = document.getElementById('modal-title');
    const cancelReportBtn = document.getElementById('cancel-report');
    const saveReportBtn = document.getElementById('save-report');
    const noReportMessage = document.getElementById('no-report-message');
    const reportDisplayArea = document.getElementById('report-display-area');
    const workProgressInput = document.getElementById('work-progress');
    const reportContentInput = document.getElementById('report-content');
    const reportRemarkInput = document.getElementById('report-remark');
    const departmentSelect = document.getElementById('department');
    const searchBtn = document.getElementById('search-btn');
    const resetBtn = document.getElementById('reset-btn');
    const reportTableBody = document.getElementById('report-table-body');
    const tabs = document.querySelectorAll('.tab');
    const tabContents = document.querySelectorAll('.tab-content');

    // 初始化
    init();

    function init() {
        renderCalendar();
        initTabs();
        populateDepartments();
        setupEventListeners();
    }

    // 初始化标签页切换
    function initTabs() {
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabId = tab.getAttribute('data-tab');
                tabs.forEach(t => t.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));
                tab.classList.add('active');
                document.getElementById(tabId).classList.add('active');
            });
        });
    }

    // 渲染日历
    function renderCalendar() {
        const year = currentDate.getFullYear();
        const month = currentDate.getMonth();
        currentMonthEl.textContent = `${year}年${month + 1}月`;

        // 清空日历
        calendarEl.innerHTML = '';

        // 添加星期标题
        const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
        weekdays.forEach(day => {
            const dayHeader = document.createElement('div');
            dayHeader.className = 'calendar-day-header';
            dayHeader.textContent = day;
            calendarEl.appendChild(dayHeader);
        });

        // 获取当月第一天
        const firstDay = new Date(year, month, 1);
        // 获取当月最后一天
        const lastDay = new Date(year, month + 1, 0);

        // 获取当月第一天是星期几
        const firstDayOfWeek = firstDay.getDay();

        // 添加空白格子
        for (let i = 0; i < firstDayOfWeek; i++) {
            const emptyDay = document.createElement('div');
            emptyDay.className = 'calendar-day empty';
            calendarEl.appendChild(emptyDay);
        }

        // 添加日期格子
        for (let i = 1; i <= lastDay.getDate(); i++) {
            const dayEl = document.createElement('div');
            dayEl.className = 'calendar-day';
            dayEl.textContent = i;
            dayEl.dataset.date = `${year}-${String(month + 1).padStart(2, '0')}-${String(i).padStart(2, '0')}`;

            // 检查是否是今天
            const today = new Date();
            if (i === today.getDate() && month === today.getMonth() && year === today.getFullYear()) {
                dayEl.classList.add('today');
            }

            // 检查是否有日报
            const hasReport = reports.some(report => {
                return report.date === dayEl.dataset.date &&
                       (currentUser.role === 'admin' || report.userId === currentUser.id || report.department === currentUser.department);
            });

            if (hasReport) {
                dayEl.classList.add('has-report');
            }

            // 添加点击事件
            dayEl.addEventListener('click', () => openReportModal(dayEl.dataset.date));

            calendarEl.appendChild(dayEl);
        }
    }

    // 打开日报编辑弹窗
    function openReportModal(date) {
        modalTitle.textContent = `编写日报 - ${formatDateForDisplay(date)}`;
        reportModal.style.display = 'flex';

        // 填充已有数据
        const existingReport = reports.find(report => report.date === date && report.userId === currentUser.id);
        if (existingReport) {
            workProgressInput.value = existingReport.workProgress || '';
            reportContentInput.value = existingReport.content || '';
            reportRemarkInput.value = existingReport.remark || '';
        } else {
            workProgressInput.value = '';
            reportContentInput.value = '';
            reportRemarkInput.value = '';
        }

        // 存储当前编辑的日期
        reportModal.dataset.currentDate = date;
    }

    // 关闭日报编辑弹窗
    function closeReportModal() {
        reportModal.style.display = 'none';
    }

    // 保存日报
    function saveReport() {
        const date = reportModal.dataset.currentDate;
        if (!date) return;

        const workProgress = workProgressInput.value.trim();
        const content = reportContentInput.value.trim();
        const remark = reportRemarkInput.value.trim();

        if (!content) {
            alert('请输入日报内容');
            return;
        }

        // 查找是否已有该日期的日报
        const existingReportIndex = reports.findIndex(report => report.date === date && report.userId === currentUser.id);

        const reportData = {
            id: existingReportIndex >= 0 ? reports[existingReportIndex].id : Date.now(),
            date: date,
            userId: currentUser.id,
            userName: currentUser.name,
            department: currentUser.department,
            workProgress: workProgress,
            content: content,
            remark: remark,
            createdAt: new Date().toISOString()
        };

        if (existingReportIndex >= 0) {
            reports[existingReportIndex] = reportData;
        } else {
            reports.push(reportData);
        }

        // 保存到本地存储
        localStorage.setItem('dailyReports', JSON.stringify(reports));

        // 关闭弹窗
        closeReportModal();

        // 更新日历和日报显示
        renderCalendar();
        displayReportsForDate(date);

        showNotification('日报保存成功！', 'success');
    }

    // 显示指定日期的日报
    function displayReportsForDate(date) {
        // 根据权限筛选可见的日报
        let filteredReports = [];

        if (currentUser.role === 'admin') {
            // 管理员可以看到所有部门的日报
            filteredReports = reports.filter(report => report.date === date);
        } else {
            // 普通用户只能看到本部门的日报
            filteredReports = reports.filter(report => 
                report.date === date && report.department === currentUser.department
            );
        }

        if (filteredReports.length === 0) {
            noReportMessage.style.display = 'block';
            reportDisplayArea.style.display = 'none';
            document.getElementById('content-title').textContent = `日报内容 - ${formatDateForDisplay(date)}`;
            return;
        }

        noReportMessage.style.display = 'none';
        reportDisplayArea.style.display = 'block';
        document.getElementById('content-title').textContent = `日报内容 - ${formatDateForDisplay(date)}`;

        let reportsHTML = '';
        filteredReports.forEach(report => {
            reportsHTML += `
                <div class="report-item">
                    <div class="report-date">${report.userName} (${report.department})</div>
                    <div class="report-content"><strong>工作进展：</strong>${report.workProgress || '无'}</div>
                    <div class="report-content"><strong>日报内容：</strong>${report.content}</div>
                    <div class="report-content"><strong>备注：</strong>${report.remark || '无'}</div>
                    <div class="report-meta">提交时间：${formatDateTime(report.createdAt)}</div>
                </div>
            `;
        });

        reportDisplayArea.innerHTML = reportsHTML;
    }

    // 填充部门下拉框
    function populateDepartments() {
        departments.forEach(dept => {
            const option = document.createElement('option');
            option.value = dept;
            option.textContent = dept;
            departmentSelect.appendChild(option);
        });
    }

    // 搜索日报
    function searchReports() {
        const startDate = document.getElementById('start-date').value;
        const endDate = document.getElementById('end-date').value;
        const department = document.getElementById('department').value;
        const name = document.getElementById('name').value.trim();

        // 按条件筛选
        let filteredReports = [...reports];

        // 按日期范围筛选
        if (startDate || endDate) {
            filteredReports = filteredReports.filter(report => {
                if (startDate && new Date(report.date) < new Date(startDate)) return false;
                if (endDate && new Date(report.date) > new Date(endDate)) return false;
                return true;
            });
        }

        // 按部门筛选
        if (department && department !== 'all') {
            filteredReports = filteredReports.filter(report => report.department === department);
        }

        // 按姓名筛选
        if (name) {
            filteredReports = filteredReports.filter(report => 
                report.userName.includes(name)
            );
        }

        // 按权限筛选
        if (currentUser.role !== 'admin') {
            filteredReports = filteredReports.filter(report => 
                report.department === currentUser.department
            );
        }

        // 统计每个人的日报次数
        const userReportCounts = {};
        filteredReports.forEach(report => {
            if (!userReportCounts[report.userId]) {
                userReportCounts[report.userId] = {
                    userName: report.userName,
                    department: report.department,
                    count: 0
                };
            }
            userReportCounts[report.userId].count++;
        });

        // 渲染表格
        renderReportTable(userReportCounts);
    }

    // 渲染日报统计表格
    function renderReportTable(userReportCounts) {
        reportTableBody.innerHTML = '';

        if (Object.keys(userReportCounts).length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = '<td colspan="5" style="text-align: center;">没有找到符合条件的日报记录</td>';
            reportTableBody.appendChild(row);
            return;
        }

        let index = 1;
        Object.values(userReportCounts).forEach(user => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${index++}</td>
                <td>${user.department}</td>
                <td>${user.userName}</td>
                <td>${user.count}</td>
                <td><button class="action-btn view-user-reports" data-user-id="${getUserIdByName(user.userName)}">查看</button></td>
            `;
            reportTableBody.appendChild(row);
        });

        // 添加查看按钮事件
        document.querySelectorAll('.view-user-reports').forEach(btn => {
            btn.addEventListener('click', function() {
                const userId = parseInt(this.getAttribute('data-user-id'));
                viewUserReports(userId);
            });
        });
    }

    // 查看用户的所有日报
    function viewUserReports(userId) {
        const user = allUsers.find(u => u.id === userId);
        if (!user) return;

        // 获取该用户的所有日报并按日期排序
        const userReports = reports
            .filter(report => report.userId === userId)
            .sort((a, b) => new Date(b.date) - new Date(a.date));

        if (userReports.length === 0) {
            noReportMessage.style.display = 'block';
            reportDisplayArea.style.display = 'none';
            document.getElementById('content-title').textContent = `${user.name}的日报`;
            return;
        }

        noReportMessage.style.display = 'none';
        reportDisplayArea.style.display = 'block';
        document.getElementById('content-title').textContent = `${user.name}的日报 (共${userReports.length}篇)`;

        let reportsHTML = '';
        userReports.forEach(report => {
            reportsHTML += `
                <div class="report-item">
                    <div class="report-date">${formatDateForDisplay(report.date)}</div>
                    <div class="report-content"><strong>工作进展：</strong>${report.workProgress || '无'}</div>
                    <div class="report-content"><strong>日报内容：</strong>${report.content}</div>
                    <div class="report-content"><strong>备注：</strong>${report.remark || '无'}</div>
                    <div class="report-meta">提交时间：${formatDateTime(report.createdAt)}</div>
                </div>
            `;
        });

        reportDisplayArea.innerHTML = reportsHTML;
    }

    // 获取用户ID by姓名
    function getUserIdByName(userName) {
        const user = allUsers.find(u => u.userName === userName);
        return user ? user.id : null;
    }

    // 设置事件监听器
    function setupEventListeners() {
        // 月份导航按钮
        prevMonthBtn.addEventListener('click', () => {
            currentDate.setMonth(currentDate.getMonth() - 1);
            renderCalendar();
        });

        nextMonthBtn.addEventListener('click', () => {
            currentDate.setMonth(currentDate.getMonth() + 1);
            renderCalendar();
        });

        // 弹窗按钮
        cancelReportBtn.addEventListener('click', closeReportModal);
        saveReportBtn.addEventListener('click', saveReport);

        // 搜索按钮
        searchBtn.addEventListener('click', searchReports);
        resetBtn.addEventListener('click', resetSearch);

        // 点击日历空白处关闭弹窗
        reportModal.addEventListener('click', (e) => {
            if (e.target === reportModal) {
                closeReportModal();
            }
        });

        // 日历日期点击事件在渲染日历时动态添加
    }

    // 重置搜索条件
    function resetSearch() {
        document.getElementById('start-date').value = '';
        document.getElementById('end-date').value = '';
        document.getElementById('department').value = 'all';
        document.getElementById('name').value = '';
        reportTableBody.innerHTML = '';
    }

    // 格式化日期显示
    function formatDateForDisplay(dateString) {
        const date = new Date(dateString);
        return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
    }

    // 格式化日期时间显示
    function formatDateTime(dateString) {
        const date = new Date(dateString);
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    }

    // 显示通知
    function showNotification(message, type = 'info') {
        // 检查是否已有通知元素
        let notification = document.querySelector('.notification');
        if (!notification) {
            notification = document.createElement('div');
            notification.className = 'notification';
            notification.style.position = 'fixed';
            notification.style.bottom = '20px';
            notification.style.right = '20px';
            notification.style.padding = '10px 20px';
            notification.style.borderRadius = '4px';
            notification.style.color = 'white';
            notification.style.zIndex = '1000';
            notification.style.transition = 'opacity 0.3s';
            document.body.appendChild(notification);
        }

        // 设置通知样式
        notification.textContent = message;
        notification.style.backgroundColor = type === 'success' ? '#4CAF50' : '#2196F3';
        notification.style.opacity = '1';

        // 3秒后隐藏
        setTimeout(() => {
            notification.style.opacity = '0';
        }, 3000);
    }
});