<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游记生成 - 骆岗公园</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #2c3e50;
        }

        .app-container {
            max-width: 414px;
            margin: 0 auto;
            background: rgba(255,255,255,0.95);
            min-height: 100vh;
            position: relative;
            backdrop-filter: blur(10px);
        }

        .header {
            background: linear-gradient(135deg, #3498db, #2ecc71);
            color: white;
            padding: 20px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }

        .header-title {
            font-size: 20px;
            font-weight: bold;
        }

        .content {
            padding: 20px 16px;
        }

        .section {
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 12px;
            color: #2c3e50;
        }

        .upload-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .upload-area {
            width: 100%;
            height: 150px;
            border: 2px dashed #3498db;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            cursor: pointer;
            transition: all 0.3s;
            background: #f8f9fa;
        }

        .upload-area:hover {
            border-color: #2980b9;
            background: #e3f2fd;
        }

        .upload-placeholder {
            text-align: center;
            color: #7f8c8d;
        }

        .upload-icon {
            font-size: 32px;
            margin-bottom: 8px;
            color: #3498db;
        }

        .upload-text {
            font-size: 14px;
            margin-bottom: 4px;
        }

        .upload-hint {
            font-size: 12px;
            color: #95a5a6;
        }

        .image-preview {
            display: flex;
            gap: 8px;
            margin-top: 12px;
            flex-wrap: wrap;
        }

        .image-preview-item {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            background: #f0f0f0;
            background-size: cover;
            background-position: center;
        }

        .image-remove {
            position: absolute;
            top: 4px;
            right: 4px;
            width: 20px;
            height: 20px;
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .description-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #2c3e50;
        }

        .form-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            outline: none;
            resize: vertical;
            min-height: 120px;
            line-height: 1.5;
        }

        .form-textarea:focus {
            border-color: #3498db;
        }

        .char-count {
            text-align: right;
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 4px;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.3s;
            width: 100%;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #ecf0f1;
            color: #2c3e50;
        }

        .btn-secondary:hover {
            background: #d5dbdb;
        }

        .generated-log {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-top: 20px;
            display: none;
        }

        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #ecf0f1;
        }

        .log-title {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
        }

        .log-meta {
            font-size: 12px;
            color: #7f8c8d;
        }

        .log-content {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 16px;
            line-height: 1.6;
        }

        .log-content h3 {
            color: #3498db;
            margin-bottom: 12px;
        }

        .log-content p {
            margin-bottom: 12px;
            text-align: justify;
        }

        .log-actions {
            display: flex;
            gap: 12px;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #ecf0f1;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 12px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #27ae60;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 3000;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .notification.show {
            opacity: 1;
        }

        .notification.error {
            background: #e74c3c;
        }

        .notification.warning {
            background: #f39c12;
        }

        .edit-mode {
            display: none;
        }

        .edit-textarea {
            width: 100%;
            min-height: 200px;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            outline: none;
            resize: vertical;
            line-height: 1.6;
        }

        .edit-textarea:focus {
            border-color: #3498db;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">←</button>
            <div class="header-title">📝 游记生成</div>
        </div>

        <div class="content">
            <!-- Photo Upload Section -->
            <div class="section">
                <div class="section-title">📸 上传照片</div>
                <div class="upload-section">
                    <div class="upload-area" onclick="selectImages()" id="uploadArea">
                        <div class="upload-placeholder">
                            <div class="upload-icon">📸</div>
                            <div class="upload-text">点击上传照片</div>
                            <div class="upload-hint">支持多张图片，JPG/PNG格式</div>
                        </div>
                    </div>
                    <input type="file" id="imageInput" accept="image/*" multiple style="display: none;" onchange="processImages()">
                    <div class="image-preview" id="imagePreview">
                        <!-- Preview images will be shown here -->
                    </div>
                </div>
            </div>

            <!-- Description Section -->
            <div class="section">
                <div class="section-title">✍️ 游玩描述</div>
                <div class="description-section">
                    <label class="form-label">请描述您的游玩体验、感受和见闻</label>
                    <textarea class="form-textarea" id="travelDescription" placeholder="今天来到骆岗公园，天气很好...&#10;&#10;可以描述：&#10;• 游览的景点和体验&#10;• 印象深刻的瞬间&#10;• 与朋友家人的互动&#10;• 对公园的整体感受&#10;• 推荐给其他游客的建议" oninput="updateCharCount()"></textarea>
                    <div class="char-count" id="charCount">0/500字</div>
                </div>
            </div>

            <!-- Generate Button -->
            <div class="section">
                <button class="btn" onclick="generateTravelLog()">🤖 AI生成游记</button>
            </div>

            <!-- Loading -->
            <div class="loading" id="loadingSection">
                <div class="loading-spinner"></div>
                <div>AI正在生成您的专属游记...</div>
            </div>

            <!-- Generated Travel Log -->
            <div class="generated-log" id="generatedLog">
                <div class="log-header">
                    <div>
                        <div class="log-title" id="logTitle">我的骆岗公园游记</div>
                        <div class="log-meta" id="logMeta">生成时间：2024-03-20 14:30</div>
                    </div>
                </div>

                <!-- Display Mode -->
                <div id="displayMode">
                    <div class="log-content" id="logContent">
                        <!-- Generated content will be inserted here -->
                    </div>
                    <div class="log-actions">
                        <button class="btn btn-secondary" onclick="editTravelLog()" style="flex: 1;">✏️ 编辑</button>
                        <button class="btn" onclick="publishTravelLog()" style="flex: 1;">📤 发布</button>
                        <button class="btn btn-secondary" onclick="shareTravelLog()">🔗 分享</button>
                    </div>
                </div>

                <!-- Edit Mode -->
                <div class="edit-mode" id="editMode">
                    <textarea class="edit-textarea" id="editTextarea"></textarea>
                    <div style="display: flex; gap: 12px; margin-top: 12px;">
                        <button class="btn btn-secondary" onclick="cancelEdit()" style="flex: 1;">取消</button>
                        <button class="btn" onclick="saveEdit()" style="flex: 1;">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let uploadedImages = [];
        let generatedContent = '';

        function goBack() {
            window.history.back();
        }

        function selectImages() {
            document.getElementById('imageInput').click();
        }

        function processImages() {
            const input = document.getElementById('imageInput');
            const files = Array.from(input.files);
            
            files.forEach(file => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        uploadedImages.push({
                            file: file,
                            url: e.target.result,
                            name: file.name
                        });
                        updateImagePreview();
                    };
                    reader.readAsDataURL(file);
                }
            });
        }

        function updateImagePreview() {
            const preview = document.getElementById('imagePreview');
            preview.innerHTML = uploadedImages.map((image, index) => `
                <div class="image-preview-item" style="background-image: url('${image.url}')">
                    <button class="image-remove" onclick="removeImage(${index})">×</button>
                </div>
            `).join('');

            if (uploadedImages.length > 0) {
                showNotification(`已上传${uploadedImages.length}张照片`, 'success');
            }
        }

        function removeImage(index) {
            uploadedImages.splice(index, 1);
            updateImagePreview();
        }

        function updateCharCount() {
            const textarea = document.getElementById('travelDescription');
            const charCount = document.getElementById('charCount');
            const count = textarea.value.length;
            charCount.textContent = `${count}/500字`;
            
            if (count > 500) {
                charCount.style.color = '#e74c3c';
            } else {
                charCount.style.color = '#7f8c8d';
            }
        }

        function generateTravelLog() {
            const description = document.getElementById('travelDescription').value.trim();
            
            if (!description) {
                showNotification('请先输入游玩描述', 'warning');
                return;
            }

            if (description.length > 500) {
                showNotification('描述内容不能超过500字', 'warning');
                return;
            }

            // Show loading
            document.getElementById('loadingSection').style.display = 'block';
            document.getElementById('generatedLog').style.display = 'none';

            // Simulate AI processing
            setTimeout(() => {
                const logContent = generateLogContent(description);
                displayGeneratedLog(logContent);
                
                document.getElementById('loadingSection').style.display = 'none';
                document.getElementById('generatedLog').style.display = 'block';
                
                showNotification('游记生成完成！', 'success');
                
                // Scroll to result
                document.getElementById('generatedLog').scrollIntoView({ behavior: 'smooth' });
            }, 3000);
        }

        function generateLogContent(description) {
            const templates = [
                {
                    title: '骆岗公园春日游记',
                    content: `
                        <h3>🌸 春日骆岗，不负好时光</h3>
                        <p>今天来到了合肥的骆岗公园，${description}</p>
                        <p>信标台作为公园的标志性建筑，登顶后可以俯瞰整个公园的美景。春天的骆岗公园生机勃勃，梦想大草坪上绿草如茵，是野餐和放松的绝佳场所。</p>
                        <p>航空科普馆让我了解了很多航空知识，特别是合肥机场的历史变迁。从繁忙的机场到如今的城市公园，这种转变真的很有意义。</p>
                        <p>总的来说，骆岗公园是一个集休闲、科普、娱乐于一体的好地方，强烈推荐大家来体验！</p>
                    `
                },
                {
                    title: '我的骆岗公园探索之旅',
                    content: `
                        <h3>🗺️ 探索骆岗，发现惊喜</h3>
                        <p>${description} 这次的骆岗公园之行真的收获满满。</p>
                        <p>最让我印象深刻的是信标台的设计，既保留了历史的痕迹，又融入了现代的元素。站在塔顶，整个公园的布局一览无余，那种开阔的感觉让人心旷神怡。</p>
                        <p>梦想大草坪真的名副其实，在这里可以尽情地享受阳光和绿意。看到很多家庭在这里野餐、孩子们在草地上奔跑，那种生活的美好让人感动。</p>
                        <p>如果你还没有来过骆岗公园，我真的强烈推荐你来看看。这里不仅有美丽的风景，更有一种独特的历史文化氛围。</p>
                    `
                }
            ];

            const template = templates[Math.floor(Math.random() * templates.length)];
            generatedContent = template.content;
            
            return {
                title: template.title,
                content: template.content,
                meta: `生成时间：${new Date().toLocaleString()}`
            };
        }

        function displayGeneratedLog(logData) {
            document.getElementById('logTitle').textContent = logData.title;
            document.getElementById('logMeta').textContent = logData.meta;
            document.getElementById('logContent').innerHTML = logData.content;
        }

        function editTravelLog() {
            const displayMode = document.getElementById('displayMode');
            const editMode = document.getElementById('editMode');
            const editTextarea = document.getElementById('editTextarea');
            
            // Convert HTML content to plain text for editing
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = generatedContent;
            editTextarea.value = tempDiv.textContent || tempDiv.innerText || '';
            
            displayMode.style.display = 'none';
            editMode.style.display = 'block';
        }

        function cancelEdit() {
            const displayMode = document.getElementById('displayMode');
            const editMode = document.getElementById('editMode');
            
            displayMode.style.display = 'block';
            editMode.style.display = 'none';
        }

        function saveEdit() {
            const editTextarea = document.getElementById('editTextarea');
            const newContent = editTextarea.value.trim();
            
            if (!newContent) {
                showNotification('内容不能为空', 'warning');
                return;
            }
            
            // Convert plain text back to formatted HTML
            const formattedContent = `<p>${newContent.replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>')}</p>`;
            generatedContent = formattedContent;
            
            document.getElementById('logContent').innerHTML = formattedContent;
            
            cancelEdit();
            showNotification('游记已更新', 'success');
        }

        function publishTravelLog() {
            if (!generatedContent) {
                showNotification('请先生成游记', 'warning');
                return;
            }
            
            // Simulate publishing
            showNotification('正在发布游记...', 'info');
            
            setTimeout(() => {
                // Save to localStorage
                const travelLogs = JSON.parse(localStorage.getItem('userTravelLogs') || '[]');
                const newLog = {
                    id: Date.now(),
                    title: document.getElementById('logTitle').textContent,
                    content: generatedContent,
                    images: uploadedImages.length,
                    date: new Date().toISOString().split('T')[0],
                    published: true
                };
                travelLogs.push(newLog);
                localStorage.setItem('userTravelLogs', JSON.stringify(travelLogs));
                
                showNotification('游记发布成功！', 'success');
            }, 1500);
        }

        function shareTravelLog() {
            if (!generatedContent) {
                showNotification('请先生成游记', 'warning');
                return;
            }
            
            // Simulate sharing
            if (navigator.share) {
                navigator.share({
                    title: document.getElementById('logTitle').textContent,
                    text: '我在骆岗公园的游记，快来看看吧！',
                    url: window.location.href
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                showNotification('分享链接已复制到剪贴板', 'success');
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        // Initialize drag and drop
        document.addEventListener('DOMContentLoaded', function() {
            const uploadArea = document.getElementById('uploadArea');
            
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#2980b9';
                uploadArea.style.background = '#e3f2fd';
            });
            
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#3498db';
                uploadArea.style.background = '#f8f9fa';
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#3498db';
                uploadArea.style.background = '#f8f9fa';
                
                const files = Array.from(e.dataTransfer.files);
                files.forEach(file => {
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            uploadedImages.push({
                                file: file,
                                url: e.target.result,
                                name: file.name
                            });
                            updateImagePreview();
                        };
                        reader.readAsDataURL(file);
                    }
                });
            });
        });
    </script>
</body>
</html>
