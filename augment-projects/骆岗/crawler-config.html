<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>爬虫配置工具</title>
    <link rel="stylesheet" href="crawler-styles.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>爬虫配置工具</h1>
            <div class="actions">
                <button id="saveConfig" class="btn primary"><i class="bi bi-save"></i> 保存配置</button>
                <button id="loadConfig" class="btn secondary"><i class="bi bi-folder-open"></i> 加载配置</button>
                <input type="file" id="configFile" accept=".json" style="display: none;">
            </div>
        </header>

        <main>
            <section class="config-section">
                <h2>目标网址 <span class="required">*</span></h2>
                <div class="url-input-container">
                    <div class="url-input-wrapper">
                        <input type="text" id="urlInput" placeholder="输入网址 (例如: https://example.com)" class="full-width">
                        <button id="addUrl" class="btn primary"><i class="bi bi-plus-lg"></i> 添加</button>
                    </div>
                    <div class="url-actions">
                        <button id="validateUrls" class="btn secondary"><i class="bi bi-check-circle"></i> 验证全部</button>
                        <button id="batchImport" class="btn secondary"><i class="bi bi-upload"></i> 批量导入</button>
                        <textarea id="batchUrlInput" class="hidden" placeholder="每行输入一个网址"></textarea>
                    </div>
                </div>
                <div class="url-list-container">
                    <h3>已添加网址 <span id="urlCount">(0)</span></h3>
                    <ul id="urlList" class="url-list"></ul>
                </div>
            </section>

            <section class="config-section">
                <h2>提示词设置</h2>
                <div class="prompt-container">
                    <div class="prompt-header">
                        <div class="language-selector">
                            <label for="promptLanguage">语言:</label>
                            <select id="promptLanguage">
                                <option value="zh-CN">中文</option>
                                <option value="en-US">英文</option>
                                <option value="ja-JP">日语</option>
                                <option value="ko-KR">韩语</option>
                            </select>
                        </div>
                        <div class="template-selector">
                            <label for="promptTemplate">模板:</label>
                            <select id="promptTemplate">
                                <option value="default">默认</option>
                                <option value="detailed">详细</option>
                                <option value="concise">简洁</option>
                                <option value="custom">自定义</option>
                            </select>
                        </div>
                    </div>
                    <textarea id="promptInput" class="full-width" placeholder="输入提示词，指导爬虫如何提取内容..."></textarea>
                </div>
            </section>

            <section class="config-section">
                <h2>输出格式</h2>
                <div class="output-format-container">
                    <div class="format-selector">
                        <label for="outputFormat">选择格式:</label>
                        <select id="outputFormat">
                            <option value="csv">CSV</option>
                            <option value="excel">Excel</option>
                            <option value="json">JSON</option>
                            <option value="markdown">Markdown</option>
                            <option value="txt">纯文本</option>
                        </select>
                    </div>
                    
                    <div id="formatOptions" class="format-options">
                        <div id="csvOptions" class="format-specific-options">
                            <label for="delimiter">分隔符:</label>
                            <select id="delimiter">
                                <option value=",">逗号 (,)</option>
                                <option value=";">分号 (;)</option>
                                <option value="\t">制表符 (Tab)</option>
                                <option value="|">竖线 (|)</option>
                                <option value="custom">自定义</option>
                            </select>
                            <input type="text" id="customDelimiter" class="hidden" placeholder="输入自定义分隔符">
                        </div>
                        
                        <div id="jsonOptions" class="format-specific-options hidden">
                            <label>
                                <input type="checkbox" id="prettyPrint" checked>
                                美化输出
                            </label>
                        </div>
                        
                        <div id="markdownOptions" class="format-specific-options hidden">
                            <label>
                                <input type="checkbox" id="includeHeader" checked>
                                包含表头
                            </label>
                        </div>
                    </div>
                    
                    <div class="field-mapping">
                        <h3>字段映射</h3>
                        <div class="field-list">
                            <div class="field-item">
                                <input type="text" placeholder="字段名称" value="标题">
                                <input type="text" placeholder="CSS选择器" value=".title, h1">
                                <button class="btn icon-btn remove-field"><i class="bi bi-trash"></i></button>
                            </div>
                            <div class="field-item">
                                <input type="text" placeholder="字段名称" value="内容">
                                <input type="text" placeholder="CSS选择器" value=".content, article">
                                <button class="btn icon-btn remove-field"><i class="bi bi-trash"></i></button>
                            </div>
                            <div class="field-item">
                                <input type="text" placeholder="字段名称" value="日期">
                                <input type="text" placeholder="CSS选择器" value=".date, time">
                                <button class="btn icon-btn remove-field"><i class="bi bi-trash"></i></button>
                            </div>
                        </div>
                        <button id="addField" class="btn secondary"><i class="bi bi-plus-lg"></i> 添加字段</button>
                    </div>
                </div>
            </section>
        </main>

        <section class="preview-section">
            <h2>实时预览</h2>
            <div class="preview-container">
                <div class="preview-header">
                    <div class="preview-tabs">
                        <button class="preview-tab active" data-tab="config">配置摘要</button>
                        <button class="preview-tab" data-tab="result">结果示例</button>
                    </div>
                    <button id="runTest" class="btn primary"><i class="bi bi-play-fill"></i> 运行测试</button>
                </div>
                <div class="preview-content">
                    <div id="configPreview" class="preview-panel active">
                        <div class="config-summary">
                            <div class="summary-item">
                                <strong>目标网址:</strong> <span id="previewUrlCount">0个网址</span>
                            </div>
                            <div class="summary-item">
                                <strong>提示词:</strong> <span id="previewPrompt">未设置</span>
                            </div>
                            <div class="summary-item">
                                <strong>输出格式:</strong> <span id="previewFormat">CSV</span>
                            </div>
                            <div class="summary-item">
                                <strong>字段数量:</strong> <span id="previewFieldCount">3</span>
                            </div>
                        </div>
                    </div>
                    <div id="resultPreview" class="preview-panel">
                        <div class="result-loading hidden">
                            <div class="spinner"></div>
                            <p>正在获取数据...</p>
                        </div>
                        <div class="result-content">
                            <p class="placeholder-text">点击"运行测试"按钮查看结果示例</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <footer>
            <p>爬虫配置工具 &copy; 2025 | <a href="#" id="helpLink">帮助文档</a></p>
        </footer>
    </div>

    <div id="notification" class="notification hidden">
        <div class="notification-content">
            <i class="bi bi-info-circle notification-icon"></i>
            <span id="notificationMessage"></span>
        </div>
        <button id="closeNotification" class="notification-close"><i class="bi bi-x"></i></button>
    </div>

    <div id="modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">标题</h3>
                <button id="closeModal" class="modal-close"><i class="bi bi-x"></i></button>
            </div>
            <div id="modalBody" class="modal-body">
                内容
            </div>
            <div class="modal-footer">
                <button id="modalCancel" class="btn secondary">取消</button>
                <button id="modalConfirm" class="btn primary">确认</button>
            </div>
        </div>
    </div>

    <script src="crawler-script.js"></script>
</body>
</html>