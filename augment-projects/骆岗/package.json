{"name": "luogang-park", "version": "1.0.0", "description": "骆岗公园 uniapp 项目", "main": "main.js", "scripts": {"dev": "uni -p h5", "build": "uni build -p h5", "serve": "npx serve -s ./dist -p 5173"}, "keywords": ["uniapp", "vue", "park"], "author": "", "license": "ISC", "dependencies": {"@dcloudio/uni-app": "^2.0.0", "@dcloudio/uni-h5": "^2.0.0", "@dcloudio/uni-helper-json": "^1.0.0", "@dcloudio/uni-i18n": "^2.0.0", "@dcloudio/uni-mp-alipay": "^2.0.0", "@dcloudio/uni-mp-baidu": "^2.0.0", "@dcloudio/uni-mp-qq": "^2.0.0", "@dcloudio/uni-mp-toutiao": "^2.0.0", "@dcloudio/uni-mp-weixin": "^2.0.0", "@dcloudio/uni-stat": "^2.0.0", "vue": "^2.6.11"}, "devDependencies": {"@dcloudio/uni-cli-shared": "^2.0.0", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.0", "@dcloudio/vue-cli-plugin-uni": "^2.0.0", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.0", "@dcloudio/webpack-uni-mp-loader": "^2.0.0", "@dcloudio/webpack-uni-pages-loader": "^2.0.0", "@vue/cli-plugin-babel": "^4.5.0", "@vue/cli-service": "^4.5.0", "babel-plugin-import": "^1.11.0", "cross-env": "^7.0.2", "node-sass": "^4.14.1", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11"}, "browserslist": ["Android >= 4", "ios >= 8"]}