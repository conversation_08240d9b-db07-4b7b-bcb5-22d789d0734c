/* 移动端基础样式 - 乐游合肥 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #2196F3 0%, #4CAF50 50%, #FFA726 100%);
    color: #333;
    line-height: 1.6;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
}

/* 合肥蓝 + 生态绿 + 活力橙 主题色 */
:root {
    --hefei-blue: #2196F3;
    --eco-green: #4CAF50;
    --vibrant-orange: #FFA726;
    --light-blue: #E3F2FD;
    --light-green: #F1F8E9;
    --light-orange: #FFF8E1;
}

.app-container {
    max-width: 414px;
    margin: 0 auto;
    background: #fff;
    min-height: 100vh;
    position: relative;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* 顶部搜索栏 */
.top-header {
    position: fixed;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 414px;
    background: linear-gradient(135deg, #2196F3 0%, #4CAF50 50%, #FFA726 100%);
    padding: 10px 16px;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-container {
    padding-top: env(safe-area-inset-top, 20px);
}

.search-box {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    padding: 8px 16px;
    backdrop-filter: blur(10px);
}

.search-box i {
    color: #666;
    margin-right: 8px;
}

.search-box input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: 16px;
    color: #333;
}

.search-box input::placeholder {
    color: #999;
}

.voice-btn {
    position: relative;
    background: none;
    border: none;
    color: var(--vibrant-orange);
    font-size: 18px;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.voice-btn:active {
    background: rgba(255, 167, 38, 0.1);
    transform: scale(0.95);
}

.voice-indicator {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 8px;
    height: 8px;
    background: #4CAF50;
    border-radius: 50%;
    border: 2px solid white;
}

/* 搜索建议 */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-radius: 0 0 12px 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: none;
    z-index: 1001;
}

.search-suggestions.active {
    display: block;
}

.suggestion-item {
    padding: 12px 16px;
    border-bottom: 1px solid #F0F0F0;
    color: #666;
    font-size: 14px;
    transition: background 0.3s ease;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item:active {
    background: #F8F9FA;
}

/* 主要内容区域 */
.main-content {
    padding-top: 80px;
    padding-bottom: 80px;
    min-height: 100vh;
}

.page {
    display: none;
    padding: 16px;
    animation: fadeIn 0.3s ease;
}

.page.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 首页样式 */
.hero-section {
    margin-bottom: 24px;
}

.smart-planning-card {
    background: linear-gradient(135deg, var(--vibrant-orange) 0%, #FF8F00 100%);
    border-radius: 16px;
    padding: 20px;
    color: white;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: 0 8px 24px rgba(255, 167, 38, 0.3);
    position: relative;
    overflow: hidden;
}

.smart-planning-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.planning-icon {
    position: relative;
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.ai-pulse {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 12px;
    height: 12px;
    background: #4CAF50;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.planning-tags {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.planning-tags .tag {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
}

.planning-content {
    flex: 1;
}

.planning-content h2 {
    font-size: 18px;
    margin-bottom: 4px;
}

.planning-content p {
    font-size: 14px;
    opacity: 0.9;
}

.planning-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: bold;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.planning-btn:active {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0.95);
}

/* 功能卡片 */
.feature-cards {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 24px;
}

.feature-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.card-header h3 {
    font-size: 16px;
    color: #333;
}

.more-btn {
    color: #FFA726;
    font-size: 14px;
}

/* 活动滑块 */
.activity-slider {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 280px;
}

.activity-item img {
    width: 60px;
    height: 40px;
    border-radius: 8px;
    object-fit: cover;
}

.activity-info h4 {
    font-size: 14px;
    color: #333;
    margin-bottom: 2px;
}

.activity-info p {
    font-size: 12px;
    color: #666;
}

/* 活动项目增强样式 */
.activity-item {
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 300px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    margin-right: 12px;
}

.activity-image {
    position: relative;
    flex-shrink: 0;
}

.activity-image img {
    width: 80px;
    height: 60px;
    border-radius: 8px;
    object-fit: cover;
}

.live-badge {
    position: absolute;
    top: 4px;
    left: 4px;
    background: #F44336;
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: bold;
}

.activity-stats {
    display: flex;
    gap: 12px;
    margin-top: 4px;
    font-size: 11px;
}

.participants {
    color: #666;
}

.rating {
    color: var(--vibrant-orange);
    font-weight: bold;
}

/* AI推荐样式 */
.ai-recommendation {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    padding: 12px;
    background: linear-gradient(135deg, #E3F2FD, #F1F8E9);
    border-radius: 12px;
    border-left: 4px solid var(--hefei-blue);
}

.ai-avatar {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--hefei-blue), var(--eco-green));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.ai-message p {
    font-size: 13px;
    color: #333;
    margin: 0;
}

.recommend-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.recommend-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.recommend-item.priority {
    border: 2px solid var(--vibrant-orange);
    box-shadow: 0 4px 12px rgba(255, 167, 38, 0.2);
}

.ai-tag {
    background: linear-gradient(135deg, var(--hefei-blue), var(--eco-green));
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    margin-left: 8px;
}

.recommend-stats {
    display: flex;
    gap: 12px;
    margin-top: 4px;
    font-size: 11px;
}

.quick-nav-btn {
    background: var(--vibrant-orange);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: bold;
}

/* AI工具样式 */
.ai-tools {
    background: linear-gradient(135deg, #F8F9FA, #E3F2FD);
    border: 2px solid var(--hefei-blue);
}

.ai-status {
    background: #4CAF50;
    color: white;
    padding: 2px 8px;
    border-radius: 8px;
    font-size: 11px;
    font-weight: bold;
}

.ai-tools-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.ai-tool-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: white;
    border-radius: 12px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.ai-tool-item.featured {
    border-color: var(--vibrant-orange);
    box-shadow: 0 4px 12px rgba(255, 167, 38, 0.2);
}

.ai-tool-item .tool-icon {
    position: relative;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--hefei-blue), var(--eco-green));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.ai-indicator {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 12px;
    height: 12px;
    background: #4CAF50;
    border-radius: 50%;
    border: 2px solid white;
    animation: pulse 2s infinite;
}

.voice-wave {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 12px;
    height: 12px;
    background: #F44336;
    border-radius: 50%;
    animation: voiceWave 1s infinite;
}

@keyframes voiceWave {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
}

.tool-stats {
    margin-top: 4px;
}

.accuracy {
    font-size: 11px;
    color: #4CAF50;
    font-weight: bold;
}

/* 积分活动样式 */
.points-activities {
    background: linear-gradient(135deg, #FFF8E1, #FFE0B2);
    border: 2px solid var(--vibrant-orange);
}

.points-balance {
    background: var(--vibrant-orange);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.points-carousel {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.points-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: white;
    border-radius: 12px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.points-item.hot {
    border-color: #F44336;
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.2);
}

.points-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--vibrant-orange), #FF8F00);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.points-content {
    flex: 1;
}

.points-content h4 {
    font-size: 14px;
    color: #333;
    margin-bottom: 4px;
}

.points-content p {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.points-progress {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.points-progress .progress-bar {
    background: #E0E0E0;
    height: 4px;
    border-radius: 2px;
    overflow: hidden;
}

.points-progress .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--vibrant-orange), #FF8F00);
}

.progress-text {
    font-size: 10px;
    color: #666;
}

.exchange-btn, .task-btn {
    background: var(--vibrant-orange);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: bold;
    white-space: nowrap;
}

/* 增强地图样式 */
.city-map-section {
    margin-bottom: 24px;
}

.map-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.map-header h3 {
    font-size: 16px;
    color: #333;
}

.map-controls {
    display: flex;
    gap: 8px;
}

.map-control-btn {
    background: white;
    border: 1px solid #E0E0E0;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 11px;
    color: #666;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.3s ease;
}

.map-control-btn.active {
    background: var(--hefei-blue);
    color: white;
    border-color: var(--hefei-blue);
}

.map-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #E3F2FD, #F1F8E9);
}

.map-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(33, 150, 243, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(33, 150, 243, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

.map-rivers {
    position: absolute;
    top: 60%;
    left: 0;
    right: 0;
    height: 8px;
    background: linear-gradient(90deg, transparent, var(--hefei-blue), transparent);
    border-radius: 4px;
}

/* 热力图层 */
.heatmap-layer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: none;
}

.heatmap-layer.active {
    display: block;
}

.heat-zone {
    position: absolute;
    border-radius: 50%;
    opacity: 0.6;
}

.heat-zone.high {
    background: radial-gradient(circle, rgba(244, 67, 54, 0.4), transparent);
}

.heat-zone.medium {
    background: radial-gradient(circle, rgba(255, 152, 0, 0.4), transparent);
}

.heat-zone.low {
    background: radial-gradient(circle, rgba(76, 175, 80, 0.4), transparent);
}

.heat-pulse {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: inherit;
    border-radius: 50%;
    animation: heatPulse 2s infinite;
}

@keyframes heatPulse {
    0% { transform: translate(-50%, -50%) scale(0.8); opacity: 1; }
    100% { transform: translate(-50%, -50%) scale(2); opacity: 0; }
}

/* 今日推荐 */
.weather-info {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #FFA726;
    font-size: 14px;
}

.recommend-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.recommend-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #4CAF50, #8BC34A);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.recommend-content h4 {
    font-size: 14px;
    color: #333;
    margin-bottom: 2px;
}

.recommend-content p {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.crowd-level {
    font-size: 11px;
    padding: 2px 8px;
    border-radius: 10px;
    font-weight: bold;
}

.crowd-level.low {
    background: #E8F5E8;
    color: #4CAF50;
}

.crowd-level.medium {
    background: #FFF3E0;
    color: #FF9800;
}

.crowd-level.high {
    background: #FFEBEE;
    color: #F44336;
}

/* 快捷工具 */
.tools-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
}

.tool-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 12px 8px;
    border-radius: 8px;
    background: #F8F9FA;
    transition: all 0.3s ease;
}

.tool-item:active {
    background: #E3F2FD;
    transform: scale(0.95);
}

.tool-item i {
    font-size: 20px;
    color: #FFA726;
}

.tool-item span {
    font-size: 12px;
    color: #666;
}

/* 城市地图 */
.city-map-section {
    margin-bottom: 24px;
}

.city-map-section h3 {
    font-size: 16px;
    color: #333;
    margin-bottom: 12px;
}

.map-container {
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.city-map {
    position: relative;
    height: 200px;
    background: linear-gradient(135deg, #E3F2FD, #F1F8E9);
    border-radius: 8px;
    overflow: hidden;
}

.map-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #666;
}

.map-placeholder i {
    font-size: 32px;
    margin-bottom: 8px;
    color: #2196F3;
}

.map-marker {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.map-marker:active {
    transform: scale(1.1);
}

.map-marker.luogang {
    top: 30%;
    left: 60%;
}

.map-marker.baogong {
    top: 60%;
    left: 40%;
}

.map-marker.binhu {
    top: 45%;
    left: 80%;
}

.marker-dot {
    width: 12px;
    height: 12px;
    background: #FFA726;
    border: 2px solid white;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(255, 167, 38, 0.4);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 167, 38, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 167, 38, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 167, 38, 0); }
}

/* 增强地图标记 */
.map-marker {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
}

.map-marker:active {
    transform: scale(1.1);
}

.map-marker.featured {
    z-index: 20;
}

.map-marker .marker-dot {
    position: relative;
    width: 32px;
    height: 32px;
    background: var(--vibrant-orange);
    border: 3px solid white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(255, 167, 38, 0.4);
}

.map-marker.luogang .marker-dot {
    background: linear-gradient(135deg, var(--eco-green), #8BC34A);
    animation: featuredPulse 3s infinite;
}

.map-marker.baogong .marker-dot {
    background: linear-gradient(135deg, var(--hefei-blue), #64B5F6);
}

.map-marker.binhu .marker-dot {
    background: linear-gradient(135deg, #00BCD4, #4DD0E1);
}

.map-marker.kexuedao .marker-dot {
    background: linear-gradient(135deg, #9C27B0, #BA68C8);
}

@keyframes featuredPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.marker-pulse {
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border: 2px solid var(--eco-green);
    border-radius: 50%;
    animation: markerPulse 2s infinite;
}

@keyframes markerPulse {
    0% { transform: scale(1); opacity: 1; }
    100% { transform: scale(1.5); opacity: 0; }
}

.marker-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 8px;
    background: white;
    padding: 8px 12px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-width: 80px;
}

.marker-name {
    font-size: 11px;
    color: #333;
    font-weight: bold;
    margin-bottom: 2px;
}

.marker-status {
    font-size: 9px;
    margin-bottom: 2px;
}

.marker-temp {
    font-size: 9px;
    color: var(--vibrant-orange);
    font-weight: bold;
}

/* 地图图例 */
.map-legend {
    position: absolute;
    bottom: 12px;
    left: 12px;
    background: rgba(255, 255, 255, 0.9);
    padding: 8px 12px;
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 4px;
    font-size: 10px;
    color: #666;
}

.legend-item:last-child {
    margin-bottom: 0;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.legend-color.high {
    background: #F44336;
}

.legend-color.medium {
    background: #FF9800;
}

.legend-color.low {
    background: #4CAF50;
}

/* 增强景点卡片 */
.content-card.featured {
    border: 2px solid var(--vibrant-orange);
    box-shadow: 0 8px 24px rgba(255, 167, 38, 0.2);
    position: relative;
    overflow: hidden;
}

.content-card.featured::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

.live-status {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.status-dot {
    width: 6px;
    height: 6px;
    background: #4CAF50;
    border-radius: 50%;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

.card-title-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.sample-tag {
    background: linear-gradient(135deg, #F44336, #E91E63);
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    margin-left: 8px;
}

.smart-features {
    display: flex;
    gap: 4px;
}

.smart-features i {
    width: 20px;
    height: 20px;
    background: var(--hefei-blue);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
}

.rating-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.review-count {
    font-size: 11px;
    color: #999;
    margin-left: 4px;
}

.crowd-indicator {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 2px;
}

.crowd-bar {
    width: 60px;
    height: 4px;
    background: #E0E0E0;
    border-radius: 2px;
    overflow: hidden;
}

.crowd-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #FF9800, #F44336);
    transition: width 0.3s ease;
}

.crowd-text {
    font-size: 9px;
    color: #666;
}

.real-time-info {
    display: flex;
    gap: 12px;
    margin: 8px 0;
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 10px;
    color: #666;
}

.info-item i {
    color: var(--vibrant-orange);
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.distance-info {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: #666;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.quick-btn {
    background: var(--hefei-blue);
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 快捷入口 */
.quick-access {
    margin-bottom: 24px;
}

.access-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    background: white;
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.access-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 12px 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.access-item:active {
    background: #F8F9FA;
    transform: scale(0.95);
}

.access-item i {
    font-size: 24px;
    color: #FFA726;
}

.access-item span {
    font-size: 12px;
    color: #666;
    text-align: center;
}

/* 探索页样式 */
.explore-tabs {
    display: flex;
    background: white;
    border-radius: 12px;
    padding: 4px;
    margin-bottom: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.explore-tabs .tab-item {
    flex: 1;
    text-align: center;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 14px;
    color: #666;
    transition: all 0.3s ease;
}

.explore-tabs .tab-item.active {
    background: linear-gradient(135deg, #FFA726, #FF8F00);
    color: white;
}

/* 筛选器 */
.filter-section {
    margin-bottom: 16px;
}

.filter-chips {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    padding-bottom: 4px;
}

.filter-chip {
    background: white;
    border: 1px solid #E0E0E0;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    color: #666;
    white-space: nowrap;
    transition: all 0.3s ease;
}

.filter-chip.active {
    background: #FFA726;
    color: white;
    border-color: #FFA726;
}

/* 内容卡片 */
.content-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.content-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.content-card:active {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.card-image {
    position: relative;
    height: 120px;
    overflow: hidden;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card-badges {
    position: absolute;
    top: 8px;
    left: 8px;
    display: flex;
    gap: 4px;
}

.badge {
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: bold;
}

.badge.free {
    background: #4CAF50;
    color: white;
}

.badge.paid {
    background: #FF9800;
    color: white;
}

.badge.family {
    background: #E91E63;
    color: white;
}

.badge.culture {
    background: #9C27B0;
    color: white;
}

.card-content {
    padding: 12px;
}

.card-content h3 {
    font-size: 16px;
    color: #333;
    margin-bottom: 8px;
}

.rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.stars {
    font-size: 12px;
}

.score {
    font-size: 12px;
    color: #FFA726;
    font-weight: bold;
}

.description {
    font-size: 12px;
    color: #666;
    margin-bottom: 12px;
    line-height: 1.4;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.distance {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #666;
}

.distance i {
    color: #FFA726;
}

.detail-btn {
    background: linear-gradient(135deg, #FFA726, #FF8F00);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.detail-btn:active {
    transform: scale(0.95);
}

/* 底部导航栏 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 414px;
    background: white;
    display: flex;
    padding: 8px 0;
    padding-bottom: calc(8px + env(safe-area-inset-bottom, 0px));
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 8px 4px;
    color: #999;
    transition: all 0.3s ease;
}

.nav-item.active {
    color: #FFA726;
}

.nav-item i {
    font-size: 20px;
}

.nav-item span {
    font-size: 10px;
}

/* 悬浮助手 */
.floating-assistant {
    position: fixed;
    bottom: 100px;
    right: 16px;
    background: linear-gradient(135deg, #FFA726, #FF8F00);
    color: white;
    padding: 12px 16px;
    border-radius: 25px;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 16px rgba(255, 167, 38, 0.4);
    z-index: 999;
    transition: all 0.3s ease;
}

.floating-assistant:active {
    transform: scale(0.95);
}

.floating-assistant i {
    font-size: 18px;
}

.floating-assistant span {
    font-size: 14px;
    font-weight: bold;
}

/* 响应式适配 */
@media (max-width: 375px) {
    .app-container {
        max-width: 375px;
    }
    
    .top-header {
        max-width: 375px;
    }
    
    .bottom-nav {
        max-width: 375px;
    }
    
    .access-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 12px;
    }
    
    .tools-grid {
        gap: 8px;
    }
}

/* 行程页样式 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.page-header h2 {
    font-size: 20px;
    color: #333;
}

.add-itinerary-btn {
    background: linear-gradient(135deg, #FFA726, #FF8F00);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
}

.add-itinerary-btn:active {
    transform: scale(0.95);
}

.current-itinerary {
    margin-bottom: 24px;
}

.itinerary-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.itinerary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.itinerary-header h3 {
    font-size: 16px;
    color: #333;
}

.itinerary-date {
    font-size: 12px;
    color: #666;
    background: #F8F9FA;
    padding: 4px 8px;
    border-radius: 8px;
}

.itinerary-progress {
    margin-bottom: 16px;
}

.progress-bar {
    background: #E0E0E0;
    height: 4px;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #FFA726);
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 12px;
    color: #666;
}

.itinerary-spots {
    margin-bottom: 16px;
}

.spot-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    border-bottom: 1px solid #F0F0F0;
}

.spot-item:last-child {
    border-bottom: none;
}

.spot-time {
    background: #FFA726;
    color: white;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: bold;
    min-width: 50px;
    text-align: center;
}

.spot-item.completed .spot-time {
    background: #4CAF50;
}

.spot-info {
    flex: 1;
}

.spot-info h4 {
    font-size: 14px;
    color: #333;
    margin-bottom: 2px;
}

.spot-info p {
    font-size: 12px;
    color: #666;
}

.spot-status {
    color: #FFA726;
    font-size: 16px;
}

.spot-item.completed .spot-status {
    color: #4CAF50;
}

.itinerary-actions {
    display: flex;
    gap: 12px;
}

.action-btn {
    flex: 1;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    border: 1px solid #E0E0E0;
    background: white;
    color: #666;
    transition: all 0.3s ease;
}

.action-btn.primary {
    background: linear-gradient(135deg, #FFA726, #FF8F00);
    color: white;
    border-color: #FFA726;
}

.action-btn:active {
    transform: scale(0.95);
}

/* 电子票夹 */
.ticket-wallet {
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.wallet-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.wallet-header h3 {
    font-size: 16px;
    color: #333;
}

.ticket-count {
    font-size: 12px;
    color: #FFA726;
    background: rgba(255, 167, 38, 0.1);
    padding: 4px 8px;
    border-radius: 8px;
}

.ticket-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #F8F9FA;
    border-radius: 8px;
}

.ticket-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #FFA726, #FF8F00);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.ticket-info h4 {
    font-size: 14px;
    color: #333;
    margin-bottom: 2px;
}

.ticket-info p {
    font-size: 12px;
    color: #666;
}

/* 发现页样式 */
.discover-tabs {
    display: flex;
    background: white;
    border-radius: 12px;
    padding: 4px;
    margin-bottom: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.discover-tabs .tab-item {
    flex: 1;
    text-align: center;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 14px;
    color: #666;
    transition: all 0.3s ease;
}

.discover-tabs .tab-item.active {
    background: linear-gradient(135deg, #FFA726, #FF8F00);
    color: white;
}

.community-content {
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.content-filters {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.content-filters .filter-chip {
    background: #F8F9FA;
    border: 1px solid #E0E0E0;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    color: #666;
    white-space: nowrap;
}

.content-filters .filter-chip.active {
    background: #FFA726;
    color: white;
    border-color: #FFA726;
}

.community-posts {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.post-card {
    border: 1px solid #F0F0F0;
    border-radius: 8px;
    padding: 12px;
    background: #FAFAFA;
}

.post-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-info {
    flex: 1;
}

.user-info h4 {
    font-size: 14px;
    color: #333;
    margin-bottom: 2px;
}

.post-time {
    font-size: 11px;
    color: #999;
}

.post-tag {
    background: #FFA726;
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: bold;
}

.post-content h3 {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
}

.post-content p {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    margin-bottom: 8px;
}

.post-images {
    margin-bottom: 12px;
}

.post-images img {
    width: 100%;
    max-width: 120px;
    height: 80px;
    object-fit: cover;
    border-radius: 6px;
}

.post-actions {
    display: flex;
    gap: 16px;
}

.post-actions .action-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.post-actions .action-btn:active {
    background: #F0F0F0;
}

.post-actions .action-btn i {
    font-size: 14px;
}

/* 我的页面样式 */
.profile-header {
    background: linear-gradient(135deg, #FFA726, #FF8F00);
    color: white;
    padding: 20px 16px;
    border-radius: 0 0 20px 20px;
    margin: -16px -16px 20px -16px;
}

.profile-header .user-info {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
}

.user-avatar.large {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.user-avatar.large img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details h3 {
    font-size: 18px;
    margin-bottom: 4px;
}

.user-details p {
    font-size: 14px;
    opacity: 0.9;
}

.level-progress .progress-bar {
    background: rgba(255, 255, 255, 0.2);
    height: 6px;
    border-radius: 3px;
    margin-bottom: 8px;
}

.level-progress .progress-fill {
    background: white;
}

.level-progress .progress-text {
    font-size: 12px;
    opacity: 0.8;
}

.profile-menu {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.menu-section {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border-bottom: 1px solid #F0F0F0;
    transition: all 0.3s ease;
}

.menu-item:last-child {
    border-bottom: none;
}

.menu-item:active {
    background: #F8F9FA;
}

.menu-item i:first-child {
    width: 20px;
    color: #FFA726;
    font-size: 16px;
}

.menu-item span:first-of-type {
    flex: 1;
    font-size: 14px;
    color: #333;
}

.menu-value {
    font-size: 14px;
    color: #FFA726;
    font-weight: bold;
}

.menu-item i:last-child {
    color: #CCC;
    font-size: 12px;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    padding: 20px;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 16px;
    width: 100%;
    max-width: 360px;
    max-height: 80vh;
    overflow: hidden;
    animation: modalSlideUp 0.3s ease;
}

@keyframes modalSlideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    background: linear-gradient(135deg, #FFA726, #FF8F00);
    color: white;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 16px;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    padding: 4px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:active {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

/* 规划表单样式 */
.planning-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-size: 14px;
    color: #333;
    font-weight: bold;
}

.day-selector {
    display: flex;
    gap: 8px;
}

.day-btn {
    flex: 1;
    padding: 8px 16px;
    border: 1px solid #E0E0E0;
    background: white;
    color: #666;
    border-radius: 20px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.day-btn.active {
    background: #FFA726;
    color: white;
    border-color: #FFA726;
}

.preference-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag-item {
    padding: 6px 12px;
    border: 1px solid #E0E0E0;
    background: white;
    color: #666;
    border-radius: 16px;
    font-size: 12px;
    transition: all 0.3s ease;
}

.tag-item.active {
    background: #FFA726;
    color: white;
    border-color: #FFA726;
}

.budget-slider {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.budget-slider input[type="range"] {
    width: 100%;
    height: 4px;
    border-radius: 2px;
    background: #E0E0E0;
    outline: none;
    -webkit-appearance: none;
}

.budget-slider input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #FFA726;
    cursor: pointer;
}

.budget-display {
    text-align: center;
    font-size: 16px;
    color: #FFA726;
    font-weight: bold;
}

.generate-btn {
    background: linear-gradient(135deg, #FFA726, #FF8F00);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.generate-btn:active {
    transform: scale(0.95);
}

/* 骆岗公园详情页样式 */
.attraction-detail-page {
    position: fixed;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 414px;
    height: 100vh;
    background: white;
    z-index: 2000;
    overflow-y: auto;
    display: none;
}

.attraction-detail-page.active {
    display: block;
    animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

.detail-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: linear-gradient(135deg, var(--hefei-blue), var(--eco-green));
    color: white;
    position: sticky;
    top: 0;
    z-index: 100;
}

.back-btn {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.back-btn:active {
    background: rgba(255, 255, 255, 0.2);
}

.detail-header h2 {
    font-size: 18px;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.share-btn, .favorite-btn {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.share-btn:active, .favorite-btn:active {
    background: rgba(255, 255, 255, 0.2);
}

/* 头部画廊 */
.hero-gallery {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.gallery-container {
    position: relative;
    height: 100%;
}

.gallery-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.gallery-item.active {
    opacity: 1;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(transparent 60%, rgba(0, 0, 0, 0.3));
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 16px;
}

.vr-entrance-btn {
    align-self: flex-end;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: bold;
    backdrop-filter: blur(10px);
    position: relative;
}

.vr-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #F44336;
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 9px;
}

.gallery-indicators {
    display: flex;
    justify-content: center;
    gap: 8px;
    align-self: center;
}

.indicator {
    width: 8px;
    height: 8px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.indicator.active {
    background: white;
    transform: scale(1.2);
}

/* 核心信息卡片 */
.core-info-card {
    padding: 20px;
    background: white;
    margin: -20px 16px 16px 16px;
    border-radius: 16px;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 10;
}

.info-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.title-section h1 {
    font-size: 24px;
    color: #333;
    margin-bottom: 4px;
}

.subtitle {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.title-tags {
    display: flex;
    gap: 8px;
}

.title-tags .tag {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
}

.tag.sample {
    background: linear-gradient(135deg, #F44336, #E91E63);
    color: white;
}

.tag.ai {
    background: linear-gradient(135deg, var(--hefei-blue), var(--eco-green));
    color: white;
}

.official-rating {
    text-align: right;
}

.official-rating .stars {
    font-size: 16px;
    margin-bottom: 4px;
}

.official-rating .score {
    display: block;
    font-size: 18px;
    font-weight: bold;
    color: var(--vibrant-orange);
    margin-bottom: 2px;
}

.official-rating .label {
    font-size: 11px;
    color: #666;
}

.basic-info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
    margin-bottom: 20px;
}

.basic-info-grid .info-item {
    display: flex;
    gap: 12px;
    padding: 12px;
    background: #F8F9FA;
    border-radius: 12px;
}

.basic-info-grid .info-item i {
    color: var(--vibrant-orange);
    font-size: 18px;
    margin-top: 2px;
}

.info-content .label {
    display: block;
    font-size: 12px;
    color: #666;
    margin-bottom: 2px;
}

.info-content .value {
    display: block;
    font-size: 14px;
    color: #333;
    font-weight: bold;
    margin-bottom: 2px;
}

.info-content .sub-value {
    display: block;
    font-size: 11px;
    color: #999;
}

/* 核心操作按钮 */
.core-actions {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 12px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 16px 12px;
    border-radius: 12px;
    border: none;
    transition: all 0.3s ease;
    text-align: center;
}

.action-btn.primary {
    background: linear-gradient(135deg, var(--vibrant-orange), #FF8F00);
    color: white;
}

.action-btn.secondary {
    background: white;
    color: var(--hefei-blue);
    border: 2px solid var(--hefei-blue);
}

.action-btn i {
    font-size: 20px;
}

.action-btn span {
    font-size: 12px;
    font-weight: bold;
}

.action-btn small {
    font-size: 10px;
    opacity: 0.8;
}

.action-btn:active {
    transform: scale(0.95);
}

/* 信息模块 */
.info-modules {
    padding: 0 16px 100px 16px;
}

.info-module {
    background: white;
    border-radius: 12px;
    margin-bottom: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.module-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: #F8F9FA;
    cursor: pointer;
    transition: background 0.3s ease;
}

.module-header:active {
    background: #E9ECEF;
}

.module-header h3 {
    font-size: 16px;
    color: #333;
    margin: 0;
}

.module-badge {
    background: var(--vibrant-orange);
    color: white;
    padding: 2px 8px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: bold;
}

.toggle-icon {
    color: #666;
    transition: transform 0.3s ease;
}

.info-module.expanded .toggle-icon {
    transform: rotate(180deg);
}

.module-content {
    padding: 16px;
    display: none;
}

.info-module.expanded .module-content {
    display: block;
}

/* 介绍内容 */
.intro-section {
    margin-bottom: 20px;
}

.intro-section h4 {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
}

.zone-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 12px;
}

.zone-tag {
    background: var(--light-blue);
    color: var(--hefei-blue);
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: bold;
}

.highlights-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.highlight-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: #F8F9FA;
    border-radius: 12px;
    text-align: center;
}

.highlight-item i {
    font-size: 24px;
    color: var(--vibrant-orange);
}

.highlight-item span {
    font-size: 12px;
    color: #333;
    font-weight: bold;
}
