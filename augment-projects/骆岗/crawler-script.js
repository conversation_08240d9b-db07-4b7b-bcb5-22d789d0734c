document.addEventListener('DOMContentLoaded', function() {
    // 全局变量
    const state = {
        urls: [],
        prompt: '',
        outputFormat: 'csv',
        fields: [
            { name: '标题', selector: '.title, h1' },
            { name: '内容', selector: '.content, article' },
            { name: '日期', selector: '.date, time' }
        ]
    };

    // DOM元素引用
    const elements = {
        urlInput: document.getElementById('urlInput'),
        addUrl: document.getElementById('addUrl'),
        validateUrls: document.getElementById('validateUrls'),
        batchImport: document.getElementById('batchImport'),
        batchUrlInput: document.getElementById('batchUrlInput'),
        urlList: document.getElementById('urlList'),
        urlCount: document.getElementById('urlCount'),
        promptInput: document.getElementById('promptInput'),
        promptLanguage: document.getElementById('promptLanguage'),
        promptTemplate: document.getElementById('promptTemplate'),
        outputFormat: document.getElementById('outputFormat'),
        formatOptions: document.getElementById('formatOptions'),
        delimiter: document.getElementById('delimiter'),
        customDelimiter: document.getElementById('customDelimiter'),
        addField: document.getElementById('addField'),
        saveConfig: document.getElementById('saveConfig'),
        loadConfig: document.getElementById('loadConfig'),
        configFile: document.getElementById('configFile'),
        previewTabs: document.querySelectorAll('.preview-tab'),
        previewPanels: document.querySelectorAll('.preview-panel'),
        runTest: document.getElementById('runTest'),
        previewUrlCount: document.getElementById('previewUrlCount'),
        previewPrompt: document.getElementById('previewPrompt'),
        previewFormat: document.getElementById('previewFormat'),
        previewFieldCount: document.getElementById('previewFieldCount'),
        resultLoading: document.querySelector('.result-loading'),
        resultContent: document.querySelector('.result-content'),
        notification: document.getElementById('notification'),
        notificationMessage: document.getElementById('notificationMessage'),
        closeNotification: document.getElementById('closeNotification'),
        modal: document.getElementById('modal'),
        modalTitle: document.getElementById('modalTitle'),
        modalBody: document.getElementById('modalBody'),
        closeModal: document.getElementById('closeModal'),
        modalCancel: document.getElementById('modalCancel'),
        modalConfirm: document.getElementById('modalConfirm'),
        helpLink: document.getElementById('helpLink')
    };

    // 初始化
    initEventListeners();
    updatePreview();

    // 事件监听器初始化
    function initEventListeners() {
        // URL相关
        elements.addUrl.addEventListener('click', addUrl);
        elements.urlInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') addUrl();
        });
        elements.validateUrls.addEventListener('click', validateAllUrls);
        elements.batchImport.addEventListener('click', toggleBatchImport);
        elements.batchUrlInput.addEventListener('blur', processBatchUrls);

        // 提示词相关
        elements.promptInput.addEventListener('input', updatePromptState);
        elements.promptLanguage.addEventListener('change', updatePromptTemplate);
        elements.promptTemplate.addEventListener('change', updatePromptTemplate);

        // 输出格式相关
        elements.outputFormat.addEventListener('change', updateFormatOptions);
        elements.delimiter.addEventListener('change', toggleCustomDelimiter);
        elements.addField.addEventListener('click', addField);
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('remove-field')) {
                removeField(e.target.closest('.field-item'));
            }
        });

        // 配置保存/加载
        elements.saveConfig.addEventListener('click', saveConfiguration);
        elements.loadConfig.addEventListener('click', function() {
            elements.configFile.click();
        });
        elements.configFile.addEventListener('change', loadConfiguration);

        // 预览相关
        elements.previewTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                switchPreviewTab(this.dataset.tab);
            });
        });
        elements.runTest.addEventListener('click', runTestCrawl);

        // 通知和模态框
        elements.closeNotification.addEventListener('click', hideNotification);
        elements.closeModal.addEventListener('click', hideModal);
        elements.modalCancel.addEventListener('click', hideModal);
        elements.helpLink.addEventListener('click', showHelp);

        // 实时更新预览
        ['input', 'change'].forEach(eventType => {
            document.querySelectorAll('input, select, textarea').forEach(element => {
                element.addEventListener(eventType, updatePreview);
            });
        });
    }

    // URL管理功能
    function addUrl() {
        const url = elements.urlInput.value.trim();
        if (!url) {
            showNotification('请输入有效的URL', 'warning');
            return;
        }

        if (!isValidUrl(url)) {
            showNotification('URL格式不正确，请检查后重试', 'error');
            return;
        }

        if (state.urls.some(item => item.url === url)) {
            showNotification('该URL已存在', 'warning');
            return;
        }

        state.urls.push({ url, status: 'pending' });
        elements.urlInput.value = '';
        renderUrlList();
        updatePreview();
        showNotification('URL已添加', 'success');
    }

    function isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    function renderUrlList() {
        elements.urlList.innerHTML = '';
        elements.urlCount.textContent = `(${state.urls.length})`;

        state.urls.forEach((item, index) => {
            const li = document.createElement('li');
            
            const urlText = document.createElement('span');
            urlText.textContent = item.url;
            urlText.className = 'url-text';
            li.appendChild(urlText);
            
            const actionsDiv = document.createElement('div');
            actionsDiv.className = 'url-item-actions';
            
            const statusSpan = document.createElement('span');
            statusSpan.className = `url-status ${item.status}`;
            
            switch(item.status) {
                case 'valid':
                    statusSpan.innerHTML = '<i class="bi bi-check-circle"></i>';
                    break;
                case 'invalid':
                    statusSpan.innerHTML = '<i class="bi bi-x-circle"></i>';
                    break;
                default:
                    statusSpan.innerHTML = '<i class="bi bi-question-circle"></i>';
            }
            
            actionsDiv.appendChild(statusSpan);
            
            const validateBtn = document.createElement('button');
            validateBtn.className = 'btn icon-btn secondary';
            validateBtn.innerHTML = '<i class="bi bi-check"></i>';
            validateBtn.title = '验证URL';
            validateBtn.addEventListener('click', () => validateUrl(index));
            actionsDiv.appendChild(validateBtn);
            
            const removeBtn = document.createElement('button');
            removeBtn.className = 'btn icon-btn secondary';
            removeBtn.innerHTML = '<i class="bi bi-trash"></i>';
            removeBtn.title = '删除URL';
            removeBtn.addEventListener('click', () => removeUrl(index));
            actionsDiv.appendChild(removeBtn);
            
            li.appendChild(actionsDiv);
            elements.urlList.appendChild(li);
        });
    }

    function validateUrl(index) {
        const urlObj = state.urls[index];
        
        // 模拟验证过程
        showNotification(`正在验证: ${urlObj.url}`, 'info');
        
        setTimeout(() => {
            // 模拟随机验证结果
            const isValid = Math.random() > 0.2;
            state.urls[index].status = isValid ? 'valid' : 'invalid';
            renderUrlList();
            showNotification(
                isValid 
                    ? `URL验证成功: ${urlObj.url}` 
                    : `URL验证失败: ${urlObj.url}`, 
                isValid ? 'success' : 'error'
            );
        }, 1000);
    }

    function validateAllUrls() {
        if (state.urls.length === 0) {
            showNotification('没有URL可验证', 'warning');
            return;
        }

        showNotification(`正在验证${state.urls.length}个URL...`, 'info');
        
        let validCount = 0;
        let invalidCount = 0;
        
        // 模拟批量验证
        state.urls.forEach((item, index) => {
            setTimeout(() => {
                const isValid = Math.random() > 0.2;
                state.urls[index].status = isValid ? 'valid' : 'invalid';
                
                if (isValid) validCount++;
                else invalidCount++;
                
                renderUrlList();
                
                // 当所有URL都验证完成时显示结果
                if (validCount + invalidCount === state.urls.length) {
                    showNotification(`验证完成: ${validCount}个有效, ${invalidCount}个无效`, 'info');
                }
            }, 300 * index);
        });
    }

    function removeUrl(index) {
        state.urls.splice(index, 1);
        renderUrlList();
        updatePreview();
        showNotification('URL已删除', 'info');
    }

    function toggleBatchImport() {
        if (elements.batchUrlInput.classList.contains('hidden')) {
            elements.batchUrlInput.classList.remove('hidden');
            elements.batchImport.innerHTML = '<i class="bi bi-check-lg"></i> 确认导入';
            elements.batchUrlInput.focus();
        } else {
            processBatchUrls();
            elements.batchUrlInput.classList.add('hidden');
            elements.batchImport.innerHTML = '<i class="bi bi-upload"></i> 批量导入';
        }
    }

    function processBatchUrls() {
        const batchText = elements.batchUrlInput.value.trim();
        if (!batchText) return;
        
        const urlLines = batchText.split('\n');
        let addedCount = 0;
        let invalidCount = 0;
        let duplicateCount = 0;
        
        urlLines.forEach(line => {
            const url = line.trim();
            if (!url) return;
            
            if (!isValidUrl(url)) {
                invalidCount++;
                return;
            }
            
            if (state.urls.some(item => item.url === url)) {
                duplicateCount++;
                return;
            }
            
            state.urls.push({ url, status: 'pending' });
            addedCount++;
        });
        
        elements.batchUrlInput.value = '';
        renderUrlList();
        updatePreview();
        
        showNotification(
            `批量导入完成: 添加${addedCount}个, 无效${invalidCount}个, 重复${duplicateCount}个`, 
            'info'
        );
    }

    // 提示词相关功能
    function updatePromptState() {
        state.prompt = elements.promptInput.value;
        updatePreview();
    }

    function updatePromptTemplate() {
        const language = elements.promptLanguage.value;
        const template = elements.promptTemplate.value;
        
        // 根据语言和模板生成提示词
        let promptText = '';
        
        switch(template) {
            case 'default':
                promptText = language === 'zh-CN' 
                    ? '请提取网页中的关键信息，包括标题、正文内容和日期。' 
                    : 'Please extract key information from the webpage, including title, content and date.';
                break;
            case 'detailed':
                promptText = language === 'zh-CN'
                    ? '请详细分析网页内容，提取以下信息：\n1. 页面标题和副标题\n2. 正文内容（保留段落结构）\n3. 发布日期和作者信息\n4. 相关标签或分类\n5. 图片描述（如有）'
                    : 'Please analyze the webpage in detail and extract the following information:\n1. Page title and subtitle\n2. Main content (preserve paragraph structure)\n3. Publication date and author information\n4. Related tags or categories\n5. Image descriptions (if any)';
                break;
            case 'concise':
                promptText = language === 'zh-CN'
                    ? '简要提取页面核心内容，仅包括标题和主要信息点。'
                    : 'Briefly extract the core content of the page, including only the title and main information points.';
                break;
            case 'custom':
                // 保持当前输入不变
                return;
        }
        
        elements.promptInput.value = promptText;
        state.prompt = promptText;
        updatePreview();
    }

    // 输出格式相关功能
    function updateFormatOptions() {
        state.outputFormat = elements.outputFormat.value;
        
        // 隐藏所有格式特定选项
        document.querySelectorAll('.format-specific-options').forEach(el => {
            el.classList.add('hidden');
        });
        
        // 显示当前格式的选项
        const currentFormatOptions = document.getElementById(`${state.outputFormat}Options`);
        if (currentFormatOptions) {
            currentFormatOptions.classList.remove('hidden');
        }
        
        updatePreview();
    }

    function toggleCustomDelimiter() {
        if (elements.delimiter.value === 'custom') {
            elements.customDelimiter.classList.remove('hidden');
            elements.customDelimiter.focus();
        } else {
            elements.customDelimiter.classList.add('hidden');
        }
    }

    function addField() {
        const fieldList = document.querySelector('.field-list');
        const fieldItem = document.createElement('div');
        fieldItem.className = 'field-item';
        
        const nameInput = document.createElement('input');
        nameInput.type = 'text';
        nameInput.placeholder = '字段名称';
        
        const selectorInput = document.createElement('input');
        selectorInput.type = 'text';
        selectorInput.placeholder = 'CSS选择器';
        
        const removeBtn = document.createElement('button');
        removeBtn.className = 'btn icon-btn remove-field';
        removeBtn.innerHTML = '<i class="bi bi-trash"></i>';
        
        fieldItem.appendChild(nameInput);
        fieldItem.appendChild(selectorInput);
        fieldItem.appendChild(removeBtn);
        
        fieldList.appendChild(fieldItem);
        
        // 更新字段状态
        updateFieldsState();
        
        nameInput.focus();
    }

    function removeField(fieldItem) {
        if (document.querySelectorAll('.field-item').length <= 1) {
            showNotification('至少需要保留一个字段', 'warning');
            return;
        }
        
        fieldItem.remove();
        updateFieldsState();
    }

    function updateFieldsState() {
        state.fields = [];
        document.querySelectorAll('.field-item').forEach(item => {
            const inputs = item.querySelectorAll('input');
            if (inputs.length >= 2) {
                state.fields.push({
                    name: inputs[0].value || '未命名字段',
                    selector: inputs[1].value || ''
                });
            }
        });
        
        updatePreview();
    }

    // 配置保存/加载功能
    function saveConfiguration() {
        // 收集当前字段信息
        updateFieldsState();
        
        const config = {
            urls: state.urls,
            prompt: state.prompt,
            promptLanguage: elements.promptLanguage.value,
            outputFormat: state.outputFormat,
            delimiter: elements.delimiter.value,
            customDelimiter: elements.customDelimiter.value,
            fields: state.fields,
            timestamp: new Date().toISOString()
        };
        
        const configJson = JSON.stringify(config, null, 2);
        const blob = new Blob([configJson], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `crawler-config-${new Date().toISOString().slice(0, 10)}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showNotification('配置已保存', 'success');
    }

    function loadConfiguration(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const config = JSON.parse(e.target.result);
                
                // 恢复配置
                state.urls = config.urls || [];
                state.prompt = config.prompt || '';
                state.outputFormat = config.outputFormat || 'csv';
                state.fields = config.fields || [];
                
                // 更新UI
                elements.promptInput.value = state.prompt;
                elements.promptLanguage.value = config.promptLanguage || 'zh-CN';
                elements.outputFormat.value = state.outputFormat;
                elements.delimiter.value = config.delimiter || ',';
                elements.customDelimiter.value = config.customDelimiter || '';
                
                // 更新字段列表
                const fieldList = document.querySelector('.field-list');
                fieldList.innerHTML = '';
                
                state.fields.forEach(field => {
                    const fieldItem = document.createElement('div');
                    fieldItem.className = 'field-item';
                    
                    const nameInput = document.createElement('input');
                    nameInput.type = 'text';
                    nameInput.placeholder = '字段名称';
                    nameInput.value = field.name;
                    
                    const selectorInput = document.createElement('input');
                    selectorInput.type = 'text';
                    selectorInput.placeholder = 'CSS选择器';
                    selectorInput.value = field.selector;
                    
                    const removeBtn = document.createElement('button');
                    removeBtn.className = 'btn icon-btn remove-field';
                    removeBtn.innerHTML = '<i class="bi bi-trash"></i>';
                    
                    fieldItem.appendChild(nameInput);
                    fieldItem.appendChild(selectorInput);
                    fieldItem.appendChild(removeBtn);
                    
                    fieldList.appendChild(fieldItem);
                });
                
                // 触发格式选项更新
                updateFormatOptions();
                toggleCustomDelimiter();
                
                // 更新URL列表
                renderUrlList();
                
                // 更新预览
                updatePreview();
                
                showNotification('配置已加载', 'success');
            } catch (error) {
                showNotification('配置文件格式错误', 'error');
                console.error('配置加载错误:', error);
            }
        };
        
        reader.readAsText(file);
        
        // 重置文件输入，以便可以重新选择同一文件
        event.target.value = '';
    }

    // 预览相关功能
    function switchPreviewTab(tabId) {
        elements.previewTabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabId);
        });
        
        elements.previewPanels.forEach(panel => {
            panel.classList.toggle('active', panel.id === `${tabId}Preview`);
        });
    }

    function updatePreview() {
        // 更新配置摘要
        elements.previewUrlCount.textContent = `${state.urls.length}个网址`;
        elements.previewPrompt.textContent = state.prompt ? '已设置' : '未设置';
        elements.previewFormat.textContent = getFormatDisplayName(state.outputFormat);
        elements.previewFieldCount.textContent = state.fields.length;
    }

    function getFormatDisplayName(format) {
        const formatNames = {
            'csv': 'CSV',
            'excel': 'Excel',
            'json': 'JSON',
            'markdown': 'Markdown',
            'txt': '纯文本'
        };
        
        return formatNames[format] || format;
    }

    function runTestCrawl() {
        if (state.urls.length === 0) {
            showNotification('请先添加至少一个URL', 'warning');
            return;
        }
        
        // 切换到结果预览标签
        switchPreviewTab('result');
        
        // 显示加载状态
        elements.resultLoading.classList.remove('hidden');
        elements.resultContent.innerHTML = '';
        
        // 模拟爬取过程
        setTimeout(() => {
            elements.resultLoading.classList.add('hidden');
            
            // 生成示例结果
            let resultHtml = '';
            
            switch(state.outputFormat) {
                case 'csv':
                    resultHtml = generateCsvPreview();
                    break;
                case 'json':
                    resultHtml = generateJsonPreview();
                    break;
                case 'markdown':
                    resultHtml = generateMarkdownPreview();
                    break;
                case 'excel':
                    resultHtml = '<div class="preview-message">Excel格式将在下载时生成</div>';
                    resultHtml += generateCsvPreview();
                    break;
                default:
                    resultHtml = generateTextPreview();
            }
            
            elements.resultContent.innerHTML = resultHtml;
            
            showNotification('测试爬取完成', 'success');
        }, 2000);
    }

    function generateCsvPreview() {
        let delimiter = elements.delimiter.value;
        if (delimiter === 'custom') {
            delimiter = elements.customDelimiter.value || ',';
        }
        
        // 转义特殊字符
        switch(delimiter) {
            case '\t': delimiter = '\\t'; break;
            case '\n': delimiter = '\\n'; break;
        }
        
        const headers = state.fields.map(field => field.name).join(delimiter);
        
        let rows = '';
        for (let i = 0; i < Math.min(5, state.urls.length); i++) {
            const row = state.fields.map(field => {
                return `示例数据-${field.name}-${i+1}`;
            }).join(delimiter);
            
            rows += row + '\n';
        }
        
        return `<pre class="result-preview csv-preview">${headers}\n${rows}</pre>`;
    }

    function generateJsonPreview() {
        const results = [];
        
        for (let i = 0; i < Math.min(5, state.urls.length); i++) {
            const item = {};
            state.fields.forEach(field => {
                item[field.name] = `示例数据-${field.name}-${i+1}`;
            });
            item['url'] = state.urls[i].url;
            results.push(item);
        }
        
        const jsonString = JSON.stringify(results, null, 2);
        return `<pre class="result-preview json-preview">${jsonString}</pre>`;
    }

    function generateMarkdownPreview() {
        let markdown = '| ';
        
        // 表头
        state.fields.forEach(field => {
            markdown += field.name + ' | ';
        });
        markdown += '\n|';
        
        // 分隔行
        state.fields.forEach(() => {
            markdown += ' --- |';
        });
        markdown += '\n';
        
        // 数据行
        for (let i = 0; i < Math.min(5, state.urls.length); i++) {
            markdown += '| ';
            state.fields.forEach(field => {
                markdown += `示例数据-${field.name}-${i+1} | `;
            });
            markdown += '\n';
        }
        
        return `<pre class="result-preview markdown-preview">${markdown}</pre>`;
    }

    function generateTextPreview() {
        let text = '';
        
        for (let i = 0; i < Math.min(5, state.urls.length); i++) {
            text += `URL: ${state.urls[i].url}\n`;
            
            state.fields.forEach(field => {
                text += `${field.name}: 示例数据-${field.name}-${i+1}\n`;
            });
            
            text += '\n---\n\n';
        }
        
        return `<pre class="result-preview text-preview">${text}</pre>`;
    }

    // 通知和模态框功能
    function showNotification(message, type = 'info') {
        elements.notificationMessage.textContent = message;
        elements.notification.className = 'notification';
        
        // 根据类型设置样式
        switch(type) {
            case 'success':
                elements.notification.classList.add('success');
                elements.notificationMessage.innerHTML = '<i class="bi bi-check-circle"></i> ' + message;
                break;
            case 'error':
                elements.notification.classList.add('error');
                elements.notificationMessage.innerHTML = '<i class="bi bi-x-circle"></i> ' + message;
                break;
            case 'warning':
                elements.notification.classList.add('warning');
                elements.notificationMessage.innerHTML = '<i class="bi bi-exclamation-triangle"></i> ' + message;
                break;
            default:
                elements.notification.classList.add('info');
                elements.notificationMessage.innerHTML = '<i class="bi bi-info-circle"></i> ' + message;
        }
        
        elements.notification.classList.add('show');
        
        // 3秒后自动隐藏
        setTimeout(hideNotification, 3000);
    }

    function hideNotification() {
        elements.notification.classList.remove('show');
    }

    function showModal(title, content, onConfirm = null) {
        elements.modalTitle.textContent = title;
        elements.modalBody.innerHTML = content;
        elements.modal.classList.add('show');
        
        if (onConfirm) {
            elements.modalConfirm.onclick = onConfirm;
            elements.modalConfirm.style.display = '';
            elements.modalCancel.textContent = '取消';
        } else {
            elements.modalConfirm.style.display = 'none';
            elements.modalCancel.textContent = '关闭';
        }
    }

    function hideModal() {
        elements.modal.classList.remove('show');
    }

    function showHelp() {
        const helpContent = `
            <h4>爬虫配置工具使用指南</h4>
            <p>本工具可帮助您配置网页爬虫，提取所需信息。</p>
            
            <h5>基本步骤：</h5>
            <ol>
                <li>添加目标网址（单个或批量）</li>
                <li>设置提取提示词</li>
                <li>配置输出格式和字段映射</li>
                <li>运行测试并查看结果</li>
                <li>保存配置以便后续使用</li>
            </ol>
            
            <h5>提示：</h5>
            <ul>
                <li>使用CSS选择器精确定位网页元素</li>
                <li>可以保存多个配置文件用于不同的爬取任务</li>
                <li>批量导入URL时，每行输入一个网址</li>
                <li>验证URL可确保目标网址可访问</li>
            </ul>
        `;
        
        showModal('帮助文档', helpContent);
    }
});