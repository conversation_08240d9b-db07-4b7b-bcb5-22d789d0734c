:root {
    --primary-color: #4a6bff;
    --primary-hover: #3a5bef;
    --secondary-color: #6c757d;
    --secondary-hover: #5a6268;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-color: #dee2e6;
    --border-radius: 4px;
    --box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f7fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

h1 {
    font-size: 28px;
    color: var(--dark-color);
}

h2 {
    font-size: 20px;
    margin-bottom: 15px;
    color: var(--dark-color);
    display: flex;
    align-items: center;
}

h3 {
    font-size: 16px;
    margin-bottom: 10px;
    color: var(--secondary-color);
}

.required {
    color: var(--danger-color);
    margin-left: 5px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 14px;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.btn i {
    font-size: 16px;
}

.primary {
    background-color: var(--primary-color);
    color: white;
}

.primary:hover {
    background-color: var(--primary-hover);
}

.secondary {
    background-color: var(--secondary-color);
    color: white;
}

.secondary:hover {
    background-color: var(--secondary-hover);
}

.icon-btn {
    width: 32px;
    height: 32px;
    padding: 0;
    border-radius: 50%;
}

.actions {
    display: flex;
    gap: 10px;
}

.config-section {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--box-shadow);
}

.full-width {
    width: 100%;
}

input[type="text"],
textarea,
select {
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
}

input[type="text"]:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 107, 255, 0.2);
}

.url-input-container {
    margin-bottom: 15px;
}

.url-input-wrapper {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.url-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.url-list {
    list-style: none;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 10px;
    background-color: var(--light-color);
}

.url-list li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid var(--border-color);
}

.url-list li:last-child {
    border-bottom: none;
}

.url-list .url-item-actions {
    display: flex;
    gap: 5px;
}

.url-list .url-status {
    margin-right: 10px;
    font-size: 14px;
}

.url-list .valid {
    color: var(--success-color);
}

.url-list .invalid {
    color: var(--danger-color);
}

.url-list .pending {
    color: var(--warning-color);
}

.prompt-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.prompt-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

textarea {
    min-height: 120px;
    resize: vertical;
}

.output-format-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.format-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.format-options {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    padding: 15px;
    background-color: var(--light-color);
    border-radius: var(--border-radius);
}

.format-specific-options {
    display: flex;
    align-items: center;
    gap: 10px;
}

.field-mapping {
    margin-top: 20px;
}

.field-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.field-item {
    display: flex;
    gap: 10px;
    align-items: center;
}

.field-item input {
    flex: 1;
}

.preview-section {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--box-shadow);
}

.preview-container {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
}

.preview-tabs {
    display: flex;
    gap: 5px;
}

.preview-tab {
    padding: 8px 15px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.preview-tab.active {
    background-color: var(--primary-color);
    color: white;
}

.preview-content {
    min-height: 300px;
    padding: 20px;
}

.preview-panel {
    display: none;
}

.preview-panel.active {
    display: block;
}

.config-summary {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.summary-item {
    display: flex;
    gap: 10px;
}

.result-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    gap: 15px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.placeholder-text {
    color: var(--secondary-color);
    text-align: center;
    margin-top: 50px;
}

footer {
    text-align: center;
    padding: 20px 0;
    color: var(--secondary-color);
    font-size: 14px;
}

footer a {
    color: var(--primary-color);
    text-decoration: none;
}

footer a:hover {
    text-decoration: underline;
}

.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 300px;
    z-index: 1000;
    transition: var(--transition);
    transform: translateY(100px);
    opacity: 0;
}

.notification.show {
    transform: translateY(0);
    opacity: 1;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.notification-icon {
    font-size: 20px;
    color: var(--primary-color);
}

.notification-close {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    color: var(--secondary-color);
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: white;
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.modal-close {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 20px;
    color: var(--secondary-color);
}

.hidden {
    display: none !important;
}

/* 响应式布局 */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .actions {
        width: 100%;
        justify-content: space-between;
    }

    .url-input-wrapper,
    .url-actions {
        flex-direction: column;
    }

    .prompt-header {
        flex-direction: column;
        gap: 10px;
    }

    .field-item {
        flex-direction: column;
    }

    .preview-header {
        flex-direction: column;
        gap: 10px;
    }

    .preview-tabs {
        width: 100%;
        justify-content: space-between;
    }

    .preview-tab {
        flex: 1;
        text-align: center;
    }

    #runTest {
        width: 100%;
    }
}