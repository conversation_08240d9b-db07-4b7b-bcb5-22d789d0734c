<template>
  <view class="header">
    <view class="status-bar"></view>
    <view class="nav-bar">
      <view class="nav-left" @click="goBack">
        <uni-icons type="back" size="22" color="#fff"></uni-icons>
      </view>
      <view class="nav-title">{{title}}</view>
      <view class="nav-right">
        <uni-icons type="more-filled" size="22" color="#fff"></uni-icons>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '骆岗公园'
    }
  },
  methods: {
    goBack() {
      uni.navigateBack({
        delta: 1
      });
    }
  }
}
</script>

<style>
.header {
  width: 100%;
  background-color: #4CAF50;
  position: sticky;
  top: 0;
  z-index: 100;
}

.status-bar {
  height: var(--status-bar-height);
  width: 100%;
}

.nav-bar {
  display: flex;
  height: 88rpx;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.nav-left, .nav-right {
  width: 88rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-title {
  font-size: 36rpx;
  color: #fff;
  font-weight: bold;
}
</style>