<template>
  <view class="attraction-card" @click="onClick">
    <image class="attraction-image" :src="image" mode="aspectFill"></image>
    <view class="attraction-info">
      <view class="attraction-title">{{title}}</view>
      <view class="attraction-description">{{description}}</view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    image: {
      type: String,
      default: ''
    },
    description: {
      type: String,
      default: ''
    }
  },
  methods: {
    onClick() {
      this.$emit('click');
    }
  }
}
</script>

<style>
.attraction-card {
  width: 48%;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 20rpx rgba(0,0,0,0.1);
  margin-bottom: 20rpx;
}

.attraction-image {
  width: 100%;
  height: 200rpx;
}

.attraction-info {
  padding: 20rpx;
}

.attraction-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.attraction-description {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}
</style>