<template>
  <view class="footer">
    <view class="nav-item" v-for="(item, index) in navItems" :key="index" :class="{ active: activeIndex === index }" @click="switchTab(index)">
      <view class="nav-icon">{{item.icon}}</view>
      <view class="nav-label">{{item.label}}</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      activeIndex: 0,
      navItems: [
        { icon: '🏠', label: '首页' },
        { icon: '🔍', label: '探索' },
        { icon: '📅', label: '行程' },
        { icon: '👥', label: '社区' },
        { icon: '👤', label: '我的' }
      ]
    }
  },
  methods: {
    switchTab(index) {
      this.activeIndex = index;
      this.$emit('tab-change', index);
      
      // 根据索引跳转到对应页面
      const pages = ['index', 'explore', 'itinerary', 'community', 'profile'];
      uni.switchTab({
        url: `/pages/${pages[index]}/${pages[index]}`
      });
    }
  }
}
</script>

<style>
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
  border-top: 1rpx solid #eee;
  display: flex;
  padding: 10rpx 0;
  z-index: 100;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
}

.nav-item {
  flex: 1;
  text-align: center;
  padding: 10rpx 0;
  transition: color 0.3s;
  color: #999;
}

.nav-item.active {
  color: #4CAF50;
}

.nav-icon {
  font-size: 40rpx;
  margin-bottom: 6rpx;
}

.nav-label {
  font-size: 20rpx;
}
</style>