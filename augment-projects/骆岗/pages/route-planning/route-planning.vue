<template>
  <view class="app-container">
    <park-header title="🗺️ 路径规划" :show-back="true"></park-header>

    <view class="content">
      <!-- 路线类型选择 -->
      <view class="section">
        <view class="section-title">选择路线类型</view>
        <scroll-view class="route-types" scroll-x="true" show-scrollbar="false">
          <view 
            class="route-type-card" 
            :class="{ active: currentRouteType === 'recommended' }" 
            @click="selectRouteType('recommended')">
            <view class="route-type-icon">⭐</view>
            <view class="route-type-title">推荐路线</view>
            <view class="route-type-desc">研学·文化</view>
          </view>
          <view 
            class="route-type-card" 
            :class="{ active: currentRouteType === 'shuttle' }" 
            @click="selectRouteType('shuttle')">
            <view class="route-type-icon">🚌</view>
            <view class="route-type-title">接驳车</view>
            <view class="route-type-desc">路线1·2·3</view>
          </view>
          <view 
            class="route-type-card" 
            :class="{ active: currentRouteType === 'bus' }" 
            @click="selectRouteType('bus')">
            <view class="route-type-icon">🚐</view>
            <view class="route-type-title">无人巴士</view>
            <view class="route-type-desc">智能循环</view>
          </view>
          <view 
            class="route-type-card" 
            :class="{ active: currentRouteType === 'ai' }" 
            @click="selectRouteType('ai')">
            <view class="route-type-icon">🤖</view>
            <view class="route-type-title">AI规划</view>
            <view class="route-type-desc">个性化</view>
          </view>
        </scroll-view>
      </view>

      <!-- 路线名称 -->
      <view class="section" v-if="['recommended', 'shuttle', 'bus'].includes(currentRouteType)">
        <scroll-view class="route-names" scroll-x="true" show-scrollbar="false">
          <view 
            class="route-name-btn" 
            :class="{ active: currentRouteName === route.id }" 
            v-for="(route, index) in routeNames" 
            :key="route.id"
            @click="selectRouteName(route.id)">
            {{ route.name }}
          </view>
        </scroll-view>
      </view>

      <!-- AI规划 -->
      <view class="section" v-if="currentRouteType === 'ai'">
        <view class="section-title">AI智能规划</view>
        <view class="ai-planning">
          <view class="ai-option-row">
            <view class="ai-option-label">游玩时间</view>
            <view class="ai-option-buttons">
              <view 
                class="ai-option-btn" 
                :class="{ active: aiOptions.duration === '2' }" 
                @click="selectAIOption('duration', '2')">2小时</view>
              <view 
                class="ai-option-btn" 
                :class="{ active: aiOptions.duration === '4' }" 
                @click="selectAIOption('duration', '4')">4小时</view>
              <view 
                class="ai-option-btn" 
                :class="{ active: aiOptions.duration === '6' }" 
                @click="selectAIOption('duration', '6')">6小时</view>
              <view 
                class="ai-option-btn" 
                :class="{ active: aiOptions.duration === '8' }" 
                @click="selectAIOption('duration', '8')">8小时</view>
            </view>
          </view>
          <view class="ai-option-row">
            <view class="ai-option-label">人数</view>
            <view class="ai-option-buttons">
              <view 
                class="ai-option-btn" 
                :class="{ active: aiOptions.size === '1' }" 
                @click="selectAIOption('size', '1')">1人</view>
              <view 
                class="ai-option-btn" 
                :class="{ active: aiOptions.size === '2-4' }" 
                @click="selectAIOption('size', '2-4')">2-4人</view>
              <view 
                class="ai-option-btn" 
                :class="{ active: aiOptions.size === '5-10' }" 
                @click="selectAIOption('size', '5-10')">5-10人</view>
              <view 
                class="ai-option-btn" 
                :class="{ active: aiOptions.size === '10+' }" 
                @click="selectAIOption('size', '10+')">10+人</view>
            </view>
          </view>
          <view class="ai-option-row">
            <view class="ai-option-label">游览类型</view>
            <view class="ai-option-buttons">
              <view 
                class="ai-option-btn" 
                :class="{ active: aiOptions.type === 'leisure' }" 
                @click="selectAIOption('type', 'leisure')">休闲观光</view>
              <view 
                class="ai-option-btn" 
                :class="{ active: aiOptions.type === 'family' }" 
                @click="selectAIOption('type', 'family')">亲子游</view>
              <view 
                class="ai-option-btn" 
                :class="{ active: aiOptions.type === 'photography' }" 
                @click="selectAIOption('type', 'photography')">摄影打卡</view>
              <view 
                class="ai-option-btn" 
                :class="{ active: aiOptions.type === 'education' }" 
                @click="selectAIOption('type', 'education')">科普教育</view>
            </view>
          </view>
          <view class="ai-option-row">
            <view class="ai-option-label">预算范围</view>
            <view class="ai-option-buttons">
              <view 
                class="ai-option-btn" 
                :class="{ active: aiOptions.budget === '0-50' }" 
                @click="selectAIOption('budget', '0-50')">0-50元</view>
              <view 
                class="ai-option-btn" 
                :class="{ active: aiOptions.budget === '50-100' }" 
                @click="selectAIOption('budget', '50-100')">50-100元</view>
              <view 
                class="ai-option-btn" 
                :class="{ active: aiOptions.budget === '100-200' }" 
                @click="selectAIOption('budget', '100-200')">100-200元</view>
              <view 
                class="ai-option-btn" 
                :class="{ active: aiOptions.budget === '200+' }" 
                @click="selectAIOption('budget', '200+')">200元以上</view>
            </view>
          </view>
          <view class="form-group">
            <text class="form-label">其他需求</text>
            <textarea class="form-textarea" v-model="otherRequirements" placeholder="请描述您的特殊需求或偏好..."></textarea>
          </view>
          <button class="btn" @click="generateAIRoute">生成个性化路线</button>
        </view>
      </view>

      <!-- 生成的路线 -->
      <view class="generated-route" v-if="currentRoute">
        <view class="section-title">🎯 推荐路线</view>
        <view>
          <view style="margin-bottom: 32rpx;">
            <text class="route-name">{{ currentRoute.name }}</text>
            <view class="route-meta">
              <text>总时长: {{ currentRoute.totalTime }}</text>
              <text>预计费用: {{ currentRoute.estimatedCost }}</text>
            </view>
          </view>
          <view style="margin-bottom: 32rpx;">
            <view 
              class="route-step" 
              v-for="(step, index) in currentRoute.steps" 
              :key="index">
              <view class="step-number">{{ index + 1 }}</view>
              <view class="step-content">
                <view class="step-title">{{ step.title }}</view>
                <view class="step-desc">{{ step.desc }}</view>
                <view class="step-tags">
                  <text class="step-tag" v-for="(tag, tagIndex) in step.tags" :key="tagIndex">{{ tag }}</text>
                </view>
                <view class="step-meta">
                  <text class="step-time">⏱️ {{ step.time }}</text>
                  <text class="step-cost">💰 {{ step.cost }}</text>
                </view>
                <view class="step-actions">
                  <button class="step-btn" @click="removeStep(index)">删除</button>
                  <button class="step-btn" @click="addStepAfter(index)">新增</button>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="map-view">
          <view style="position: relative; z-index: 2;">手绘地图路线展示</view>
        </view>
        <view class="route-actions">
          <button class="btn" @click="saveRoute" style="flex: 1;">保存行程</button>
          <button class="btn btn-secondary" @click="payForRoute" style="flex: 1;">一键付费</button>
        </view>
      </view>
    </view>

    <!-- 地图路线显示 (底部弹出) -->
    <uni-popup ref="mapRoutePopup" type="bottom">
      <view class="map-route-display">
        <view class="map-route-header">
          <view class="map-route-title">{{ mapRouteTitle }}</view>
          <button class="map-route-close" @click="closeMapRoute">×</button>
        </view>
        <scroll-view class="points-container" scroll-x="true" show-scrollbar="false">
          <view 
            class="point-card" 
            :class="{ active: activePoint === point.name }"
            v-for="(point, index) in mapRoutePoints" 
            :key="index"
            @click="focusOnPoint(point.name)">
            <view class="point-image" v-if="point.image" :style="{ backgroundImage: 'url(' + point.image + ')' }"></view>
            <view class="point-name">{{ point.name }}</view>
            <view class="point-tags">
              <text class="point-tag" v-for="(tag, tagIndex) in point.tags" :key="tagIndex">{{ tag }}</text>
            </view>
            <view class="point-desc">{{ point.desc }}</view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentRouteType: 'recommended',
      currentRouteName: null,
      currentRoute: null,
      otherRequirements: '',
      activePoint: null,
      mapRouteTitle: '',
      mapRoutePoints: [],
      aiOptions: {
        duration: '6',
        size: '2-4',
        type: 'leisure',
        budget: '0-50'
      },
      routeNames: [],
      routes: {
        'study': {
          name: '研学路线',
          totalTime: '4小时',
          estimatedCost: '60元',
          steps: [
            {
              title: '航空科普馆',
              desc: '了解航空发展历史，体验飞行模拟器',
              time: '90分钟',
              cost: '20元',
              tags: ['科普', '体验']
            },
            {
              title: '城市馆',
              desc: '参观合肥城市发展展览',
              time: '60分钟',
              cost: '免费',
              tags: ['文化', '历史']
            },
            {
              title: '昆虫博物馆',
              desc: '观察各类昆虫标本，学习自然知识',
              time: '90分钟',
              cost: '15元',
              tags: ['自然', '教育']
            }
          ]
        },
        'culture': {
          name: '文化路线',
          totalTime: '3小时',
          estimatedCost: '免费',
          steps: [
            {
              title: '信标台',
              desc: '登顶俯瞰全园，了解机场历史',
              time: '45分钟',
              cost: '免费',
              tags: ['地标', '历史']
            },
            {
              title: '航站楼',
              desc: '参观保留的历史建筑',
              time: '60分钟',
              cost: '免费',
              tags: ['建筑', '历史']
            },
            {
              title: '展园区域',
              desc: '欣赏园林景观和花卉展示',
              time: '75分钟',
              cost: '免费',
              tags: ['园林', '摄影']
            }
          ]
        },
        'shuttle1': {
          name: '接驳车路线1',
          cost: '5元',
          points: [
            { name: '东门', desc: '上车点', tags: ['起点'] },
            { name: '信标台', desc: '中途站', tags: ['景点'] },
            { name: '航站楼', desc: '中途站', tags: ['景点'] },
            { name: '西门', desc: '下车点', tags: ['终点'] }
          ]
        },
        'shuttle2': {
          name: '接驳车路线2',
          cost: '5元',
          points: [
            { name: '南门', desc: '上车点', tags: ['起点'] },
            { name: '科普馆', desc: '中途站', tags: ['景点'] },
            { name: '大草坪', desc: '中途站', tags: ['景点'] },
            { name: '北门', desc: '下车点', tags: ['终点'] }
          ]
        },
        'shuttle3': {
          name: '接驳车路线3',
          cost: '8元',
          points: [
            { name: '东门', desc: '上车点', tags: ['起点'] },
            { name: '全园环线', desc: '主要景点', tags: ['环线'] },
            { name: '东门', desc: '下车点', tags: ['终点'] }
          ]
        },
        'bus1': {
          name: '无人巴士智能循环线',
          cost: '免费',
          points: [
            { name: '东门站', desc: '15分钟一班', tags: ['站点'] },
            { name: '信标台站', desc: '15分钟一班', tags: ['站点'] },
            { name: '科普馆站', desc: '15分钟一班', tags: ['站点'] },
            { name: '大草坪站', desc: '15分钟一班', tags: ['站点'] }
          ]
        }
      }
    };
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '路径规划'
    });
    this.selectRouteType('recommended');
  },
  methods: {
    selectRouteType(type) {
      this.currentRouteType = type;
      this.currentRoute = null;
      
      // 显示路线名称
      this.showRouteNames(type);
    },
    showRouteNames(type) {
      const routeMap = {
        'recommended': [
          { id: 'study', name: '研学路线' },
          { id: 'culture', name: '文化路线' }
        ],
        'shuttle': [
          { id: 'shuttle1', name: '路线1' },
          { id: 'shuttle2', name: '路线2' },
          { id: 'shuttle3', name: '路线3' }
        ],
        'bus': [
          { id: 'bus1', name: '智能循环线' }
        ]
      };
      
      this.routeNames = routeMap[type] || [];
      
      // 自动选择第一个路线
      if (this.routeNames.length > 0) {
        setTimeout(() => {
          this.selectRouteName(this.routeNames[0].id);
        }, 100);
      }
    },
    selectRouteName(routeId) {
      this.currentRouteName = routeId;
      
      // 显示路线在地图上
      this.showRouteOnMap(routeId);
      
      // 如果是推荐路线，生成预定义路线
      if (['study', 'culture'].includes(routeId)) {
        this.generatePredefinedRoute(routeId);
      }
    },
    showRouteOnMap(routeId) {
      const route = this.routes[routeId];
      if (route) {
        this.displayMapRoute(route);
      }
    },
    displayMapRoute(route) {
      this.mapRouteTitle = `${route.name} ${route.cost ? `(${route.cost})` : ''}`;
      this.mapRoutePoints = route.points || [];
      this.$refs.mapRoutePopup.open();
      this.showNotification(`${route.name}已在地图上标注`, 'success');
    },
    closeMapRoute() {
      this.$refs.mapRoutePopup.close();
    },
    focusOnPoint(pointName) {
      this.activePoint = pointName;
      this.showNotification(`地图聚焦到${pointName}`, 'info');
    },
    selectAIOption(category, value) {
      this.aiOptions[category] = value;
    },
    generatePredefinedRoute(routeId) {
      const route = this.routes[routeId];
      if (route) {
        this.currentRoute = JSON.parse(JSON.stringify(route));
      }
    },
    generateAIRoute() {
      this.showNotification('AI正在生成个性化路线...', 'info');
      
      // 模拟AI处理
      setTimeout(() => {
        const route = this.generateRouteBasedOnPreferences(
          this.aiOptions.duration,
          this.aiOptions.size,
          this.aiOptions.type,
          this.aiOptions.budget,
          this.otherRequirements
        );
        this.currentRoute = route;
        this.showNotification('路线生成完成！', 'success');
      }, 2000);
    },
    generateRouteBasedOnPreferences(duration, groupSize, tourType, budget, requirements) {
      const routes = {
        'leisure': {
          name: '休闲观光路线',
          totalTime: duration + '小时',
          estimatedCost: budget === '0-50' ? '免费' : '50元以内',
          steps: [
            {
              title: '信标台',
              desc: '登顶俯瞰全景，拍照留念',
              time: '30分钟',
              cost: '免费',
              tags: ['观景', '拍照']
            },
            {
              title: '梦想大草坪',
              desc: '草坪漫步，享受自然',
              time: '45分钟',
              cost: '免费',
              tags: ['休闲', '自然']
            },
            {
              title: '航站楼咖啡厅',
              desc: '品尝咖啡，休息放松',
              time: '30分钟',
              cost: '30元',
              tags: ['休闲', '美食']
            }
          ]
        },
        'family': {
          name: '亲子游路线',
          totalTime: duration + '小时',
          estimatedCost: '80元以内',
          steps: [
            {
              title: '昆虫博物馆',
              desc: '观察昆虫标本，科普教育',
              time: '60分钟',
              cost: '15元',
              tags: ['亲子', '科普']
            },
            {
              title: '航空科普馆',
              desc: '飞行模拟体验',
              time: '60分钟',
              cost: '20元',
              tags: ['体验', '科普']
            },
            {
              title: '儿童游乐区',
              desc: '儿童游乐设施',
              time: '90分钟',
              cost: '25元',
              tags: ['儿童', '游乐']
            }
          ]
        },
        'photography': {
          name: '摄影打卡路线',
          totalTime: duration + '小时',
          estimatedCost: '免费',
          steps: [
            {
              title: '信标台',
              desc: '俯瞰全景，拍摄全园风光',
              time: '45分钟',
              cost: '免费',
              tags: ['观景', '摄影']
            },
            {
              title: '展园花海',
              desc: '拍摄花卉特写和人像',
              time: '60分钟',
              cost: '免费',
              tags: ['花卉', '摄影']
            },
            {
              title: '航站楼',
              desc: '拍摄历史建筑和夕阳',
              time: '45分钟',
              cost: '免费',
              tags: ['建筑', '摄影']
            }
          ]
        },
        'education': {
          name: '科普教育路线',
          totalTime: duration + '小时',
          estimatedCost: '35元',
          steps: [
            {
              title: '航空科普馆',
              desc: '了解航空历史和科技',
              time: '60分钟',
              cost: '20元',
              tags: ['科普', '教育']
            },
            {
              title: '昆虫博物馆',
              desc: '学习昆虫知识',
              time: '45分钟',
              cost: '15元',
              tags: ['科普', '自然']
            },
            {
              title: '城市馆',
              desc: '了解城市发展历史',
              time: '60分钟',
              cost: '免费',
              tags: ['历史', '文化']
            }
          ]
        }
      };
      
      return routes[tourType] || routes['leisure'];
    },
    removeStep(index) {
      if (this.currentRoute && this.currentRoute.steps.length > 1) {
        this.currentRoute.steps.splice(index, 1);
        this.showNotification('景点已删除', 'success');
      } else {
        this.showNotification('至少需要保留一个景点', 'warning');
      }
    },
    addStepAfter(index) {
      const newStep = {
        title: '新景点',
        desc: '请编辑景点信息',
        time: '30分钟',
        cost: '免费',
        tags: ['新增']
      };
      
      if (this.currentRoute) {
        this.currentRoute.steps.splice(index + 1, 0, newStep);
        this.showNotification('新景点已添加', 'success');
      }
    },
    saveRoute() {
      if (this.currentRoute) {
        // 保存到本地存储
        try {
          const savedRoutes = uni.getStorageSync('savedRoutes') || '[]';
          const routesArray = JSON.parse(savedRoutes);
          const newRoute = {
            id: Date.now(),
            name: this.currentRoute.name,
            date: new Date().toISOString().split('T')[0],
            route: this.currentRoute
          };
          routesArray.push(newRoute);
          uni.setStorageSync('savedRoutes', JSON.stringify(routesArray));
          this.showNotification('行程已保存！', 'success');
        } catch (e) {
          this.showNotification('保存失败，请重试', 'error');
          console.error(e);
        }
      }
    },
    payForRoute() {
      if (this.currentRoute) {
        this.showNotification('跳转到支付页面...', 'info');
      }
    },
    showNotification(message, type = 'info') {
      uni.showToast({
        title: message,
        icon: type === 'success' ? 'success' : 'none',
        duration: 2000
      });
    }
  },
  components: {
    'park-header': () => import('@/components/park-header/park-header')
  }
}
</script>

<style>
.app-container {
  max-width: 750rpx;
  margin: 0 auto;
  background: rgba(255,255,255,0.95);
  min-height: 100vh;
  position: relative;
}

.content {
  padding: 40rpx 32rpx;
}

.section {
  margin-bottom: 48rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  color: #2c3e50;
}

/* 路线类型 */
.route-types {
  display: flex;
  white-space: nowrap;
  margin-bottom: 24rpx;
}

.route-type-card {
  background: rgba(255,255,255,0.9);
  border-radius: 12rpx;
  padding: 12rpx 20rpx;
  text-align: center;
  transition: all 0.3s;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  border: 4rpx solid transparent;
  min-width: 140rpx;
  display: inline-block;
  margin-right: 12rpx;
}

.route-type-card.active {
  border-color: #3498db;
  background: rgba(52, 152, 219, 0.1);
}

.route-type-icon {
  font-size: 32rpx;
  margin-bottom: 4rpx;
}

.route-type-title {
  font-weight: bold;
  margin-bottom: 2rpx;
  font-size: 22rpx;
}

.route-type-desc {
  font-size: 18rpx;
  color: #7f8c8d;
}

/* 路线名称 */
.route-names {
  display: flex;
  white-space: nowrap;
  margin-bottom: 24rpx;
}

.route-name-btn {
  background: rgba(255,255,255,0.9);
  border: 2rpx solid #ddd;
  padding: 12rpx 24rpx;
  border-radius: 32rpx;
  font-size: 24rpx;
  transition: all 0.3s;
  display: inline-block;
  margin-right: 16rpx;
}

.route-name-btn.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

/* AI规划 */
.ai-planning {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  margin-bottom: 40rpx;
}

.ai-option-row {
  background: rgba(255,255,255,0.9);
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.ai-option-label {
  font-weight: bold;
  font-size: 24rpx;
  color: #2c3e50;
  min-width: 120rpx;
  flex-shrink: 0;
}

.ai-option-buttons {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
  flex: 1;
}

.ai-option-btn {
  background: white;
  border: 2rpx solid #ddd;
  padding: 8rpx 20rpx;
  border-radius: 24rpx;
  font-size: 22rpx;
  transition: all 0.3s;
  white-space: nowrap;
}

.ai-option-btn.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  margin-bottom: 16rpx;
  font-weight: bold;
  color: #2c3e50;
  font-size: 24rpx;
}

.form-textarea {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #ddd;
  border-radius: 16rpx;
  font-size: 28rpx;
  min-height: 160rpx;
}

.btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 24rpx 48rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  transition: background 0.3s;
  width: 100%;
}

.btn-secondary {
  background: #ecf0f1;
  color: #2c3e50;
}

/* 生成的路线 */
.generated-route {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  margin-bottom: 40rpx;
}

.route-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8rpx;
  display: block;
}

.route-meta {
  display: flex;
  gap: 24rpx;
  font-size: 24rpx;
  color: #7f8c8d;
  margin-bottom: 24rpx;
}

.route-step {
  display: flex;
  margin-bottom: 32rpx;
  background: rgba(255,255,255,0.9);
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.step-number {
  width: 48rpx;
  height: 48rpx;
  background: #3498db;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-weight: bold;
  font-size: 28rpx;
  margin-bottom: 8rpx;
  color: #2c3e50;
}

.step-desc {
  font-size: 24rpx;
  color: #7f8c8d;
  margin-bottom: 16rpx;
}

.step-tags {
  display: flex;
  gap: 12rpx;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.step-tag {
  background: #ecf0f1;
  color: #7f8c8d;
  padding: 4rpx 16rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
}

.step-meta {
  display: flex;
  gap: 24rpx;
  font-size: 22rpx;
  color: #7f8c8d;
  margin-bottom: 16rpx;
}

.step-actions {
  display: flex;
  gap: 16rpx;
}

.step-btn {
  background: #ecf0f1;
  color: #7f8c8d;
  border: none;
  padding: 8rpx 24rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  transition: background 0.3s;
}

.map-view {
  background: #ecf0f1;
  border-radius: 16rpx;
  height: 400rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  position: relative;
  overflow: hidden;
}

.route-actions {
  display: flex;
  gap: 24rpx;
}

/* 地图路线弹窗 */
.map-route-display {
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  padding: 40rpx;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.map-route-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.map-route-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.map-route-close {
  background: none;
  border: none;
  font-size: 48rpx;
  line-height: 1;
  color: #7f8c8d;
  padding: 0;
}

.points-container {
  display: flex;
  white-space: nowrap;
  padding-bottom: 24rpx;
}

.point-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  margin-right: 24rpx;
  min-width: 240rpx;
  display: inline-block;
  border: 2rpx solid transparent;
  transition: all 0.3s;
}

.point-card.active {
  border-color: #3498db;
  background: rgba(52, 152, 219, 0.05);
}

.point-image {
  width: 100%;
  height: 120rpx;
  background-size: cover;
  background-position: center;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
}

.point-name {
  font-weight: bold;
  font-size: 24rpx;
  margin-bottom: 8rpx;
  color: #2c3e50;
}

.point-tags {
  display: flex;
  gap: 8rpx;
  margin-bottom: 8rpx;
  flex-wrap: wrap;
}

.point-tag {
  background: #ecf0f1;
  color: #7f8c8d;
  padding: 2rpx 12rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
}

.point-desc {
  font-size: 20rpx;
  color: #7f8c8d;
}
