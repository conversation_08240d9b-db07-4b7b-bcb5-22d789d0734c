<template>
  <view class="container">
    <!-- 头部导航 -->
    <park-header title="社区"></park-header>
    
    <!-- 标签导航 -->
    <view class="tab-nav">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index" 
        class="tab-nav-item" 
        :class="{ active: currentTab === index }"
        @click="switchTab(index)">
        {{tab}}
      </view>
    </view>
    
    <!-- 筛选选项 -->
    <view class="filter-options">
      <view 
        v-for="(filter, index) in filters" 
        :key="index" 
        class="filter-btn" 
        :class="{ active: currentFilter === index }"
        @click="switchFilter(index)">
        {{filter}}
      </view>
    </view>
    
    <!-- 内容列表 -->
    <view class="list-container">
      <view class="list-item" v-for="(item, index) in filteredItems" :key="index" @click="showDetail(item)">
        <image class="list-item-image" :src="item.image" mode="aspectFill"></image>
        <view class="list-item-content">
          <view class="list-item-header">
            <view class="list-item-title">{{item.title}}</view>
            <view class="list-item-meta">{{item.date}}</view>
          </view>
          <view class="list-item-desc">作者: {{item.author}}</view>
          <view class="list-item-stats">
            <text>👁️ {{item.views}}</text>
            <text>❤️ {{item.likes}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部导航 -->
    <park-footer></park-footer>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentTab: 0,
      currentFilter: 0,
      tabs: ['游记', '攻略'],
      filters: ['全部', '最新发布', '最多点赞', '最多评论'],
      logs: [
        {
          title: "骆岗公园春日游记",
          author: "旅行达人",
          date: "2024-03-18",
          category: "最新发布",
          image: "/static/images/log1.jpg",
          views: 1256,
          likes: 89
        },
        {
          title: "带娃逛骆岗",
          author: "亲子游妈妈",
          date: "2024-03-16",
          category: "最多点赞",
          image: "/static/images/log2.jpg",
          views: 2341,
          likes: 156
        },
        {
          title: "骆岗夜景摄影",
          author: "摄影爱好者",
          date: "2024-03-15",
          category: "最多评论",
          image: "/static/images/log3.jpg",
          views: 1876,
          likes: 134
        }
      ],
      guides: [
        {
          title: "骆岗公园完整游玩攻略",
          author: "官方攻略",
          date: "2024-03-10",
          type: "官方攻略",
          image: "/static/images/guide1.jpg",
          views: 3452,
          likes: 278
        },
        {
          title: "摄影打卡点推荐",
          author: "摄影师小李",
          date: "2024-03-08",
          type: "用户攻略",
          image: "/static/images/guide2.jpg",
          views: 2156,
          likes: 189
        },
        {
          title: "亲子游攻略",
          author: "亲子游专家",
          date: "2024-03-05",
          type: "用户攻略",
          image: "/static/images/guide3.jpg",
          views: 1876,
          likes: 142
        }
      ]
    }
  },
  computed: {
    filteredItems() {
      let items = this.currentTab === 0 ? this.logs : this.guides;
      
      if (this.currentFilter === 0) {
        return items;
      } else {
        const filterValue = this.filters[this.currentFilter];
        if (this.currentTab === 0) {
          return items.filter(item => item.category === filterValue);
        } else {
          if (filterValue === '最新发布') {
            return [...items].sort((a, b) => new Date(b.date) - new Date(a.date));
          } else if (filterValue === '最多点赞') {
            return [...items].sort((a, b) => b.likes - a.likes);
          } else if (filterValue === '最多评论') {
            return [...items].sort((a, b) => b.views - a.views);
          }
          return items;
        }
      }
    }
  },
  components: {
    'park-header': () => import('@/components/park-header/park-header'),
    'park-footer': () => import('@/components/park-footer/park-footer')
  },
  methods: {
    switchTab(index) {
      this.currentTab = index;
      this.currentFilter = 0;
    },
    switchFilter(index) {
      this.currentFilter = index;
    },
    showDetail(item) {
      uni.showToast({
        title: `查看${this.currentTab === 0 ? '游记' : '攻略'}: ${item.title}`,
        icon: 'none'
      });
    }
  }
}
</script>

<style>
.container {
  padding-bottom: 100rpx;
}

.tab-nav {
  display: flex;
  background: white;
  border-radius: 24rpx;
  margin: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  position: relative;
}

.tab-nav-item {
  flex: 1;
  padding: 24rpx 32rpx;
  text-align: center;
  background: none;
  border: none;
  transition: all 0.3s;
  font-size: 28rpx;
  font-weight: 500;
  color: #7f8c8d;
  position: relative;
  z-index: 2;
}

.tab-nav-item.active {
  background: linear-gradient(135deg, #3498db, #2ecc71);
  color: white;
  border-radius: 16rpx;
  margin: 8rpx;
  box-shadow: 0 4rpx 16rpx rgba(52, 152, 219, 0.3);
}

.filter-options {
  display: flex;
  gap: 16rpx;
  margin: 20rpx;
  flex-wrap: wrap;
}

.filter-btn {
  background: white;
  border: 1rpx solid #ddd;
  padding: 12rpx 24rpx;
  border-radius: 32rpx;
  font-size: 24rpx;
  transition: all 0.3s;
  color: #7f8c8d;
}

.filter-btn.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.list-container {
  padding: 0 20rpx;
}

.list-item {
  background: white;
  border-radius: 24rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
}

.list-item-image {
  width: 160rpx;
  height: 120rpx;
  border-radius: 16rpx;
  flex-shrink: 0;
}

.list-item-content {
  flex: 1;
  min-width: 0;
}

.list-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.list-item-title {
  font-weight: bold;
  color: #2c3e50;
  font-size: 28rpx;
}

.list-item-meta {
  font-size: 22rpx;
  color: #7f8c8d;
  flex-shrink: 0;
}

.list-item-desc {
  font-size: 24rpx;
  color: #7f8c8d;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.list-item-stats {
  display: flex;
  gap: 24rpx;
  font-size: 22rpx;
  color: #95a5a6;
}
</style>