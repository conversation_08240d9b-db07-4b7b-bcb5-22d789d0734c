<template>
  <view class="app-container">
    <park-header title="📷 看图识景" :show-back="true"></park-header>

    <view class="content">
      <!-- 识别类型选择 -->
      <view class="recognition-types">
        <view class="section-title">选择识别类型</view>
        <view class="type-cards">
          <view 
            class="type-card" 
            :class="{ active: currentRecognitionType === 'building' }" 
            @click="selectRecognitionType('building')">
            <view class="type-icon" :style="{ backgroundImage: 'url(' + recognitionExamples.building[0].image + ')' }"></view>
            <view class="type-title">特色建筑</view>
            <view class="type-desc">识别园区内的标志性建筑</view>
          </view>
          <view 
            class="type-card" 
            :class="{ active: currentRecognitionType === 'plant' }" 
            @click="selectRecognitionType('plant')">
            <view class="type-icon" :style="{ backgroundImage: 'url(' + recognitionExamples.plant[0].image + ')' }"></view>
            <view class="type-title">植物科普</view>
            <view class="type-desc">识别园区内的花草树木</view>
          </view>
        </view>
        <scroll-view class="type-examples" scroll-x="true" show-scrollbar="false">
          <view 
            class="example-item" 
            v-for="(example, index) in currentExamples" 
            :key="index"
            @click="useExampleImage(example.image, example.name)">
            <view class="example-image" :style="{ backgroundImage: 'url(' + example.image + ')' }"></view>
            <view class="example-name">{{ example.name }}</view>
          </view>
        </scroll-view>
      </view>

      <!-- 相机区域 -->
      <view class="camera-section">
        <view class="camera-area" @click="selectImage" :style="cameraAreaStyle">
          <view class="camera-placeholder" v-if="!currentImage">
            <view class="camera-icon">📷</view>
            <view class="camera-text">点击拍照或上传图片</view>
            <view class="camera-hint">支持JPG、PNG格式</view>
          </view>
          <image v-if="currentImage" class="uploaded-image" :src="currentImage.url || currentImage" mode="aspectFill"></image>
        </view>
        <view class="camera-buttons">
          <button class="btn" @click="selectImage">📷 选择图片</button>
          <button class="btn btn-secondary" @click="clearImage">🗑️ 清除</button>
          <button class="btn" @click="recognizeImage" v-if="currentImage">🔍 开始识别</button>
        </view>
      </view>

      <!-- 加载中 -->
      <view class="loading" v-if="isLoading">
        <view class="loading-spinner"></view>
        <view>AI正在识别图片...</view>
      </view>

      <!-- 识别结果 -->
      <view class="recognition-result" v-if="showResult">
        <view class="result-header">
          <view class="result-title">🎯 识别结果</view>
          <view style="display: flex; align-items: center;">
            <text class="recognition-accuracy">{{ recognitionData.accuracy }}%</text>
            <button class="play-btn" @click="playAudio">
              🔊 语音播报
            </button>
          </view>
        </view>

        <view class="attraction-info">
          <view class="attraction-image" :style="{ backgroundImage: 'url(' + recognitionData.image + ')' }"></view>
          <view class="attraction-details">
            <view class="attraction-name">{{ recognitionData.name }}</view>
            <view class="attraction-tags">
              <text class="attraction-tag" v-for="(tag, index) in recognitionData.tags" :key="index">{{ tag }}</text>
            </view>
            <rich-text class="attraction-meta" :nodes="recognitionData.meta"></rich-text>
          </view>
        </view>

        <view class="attraction-description">
          <view class="description-title">详细介绍</view>
          <view class="description-text">{{ recognitionData.description }}</view>
        </view>

        <view class="language-selector">
          <text class="language-label">语音播报语言</text>
          <picker 
            class="language-select" 
            :value="languageIndex" 
            :range="languages" 
            range-key="name"
            @change="changeLanguage">
            <view class="picker-value">{{ languages[languageIndex].name }}</view>
          </picker>
        </view>

        <view class="feedback-section">
          <view class="feedback-title">识别结果准确吗？</view>
          <view class="feedback-buttons">
            <button 
              class="feedback-btn correct" 
              :class="{ selected: feedbackSelected === 'correct' }" 
              @click="submitFeedback(true)">✓ 正确</button>
            <button 
              class="feedback-btn incorrect" 
              :class="{ selected: feedbackSelected === 'incorrect' }" 
              @click="submitFeedback(false)">✗ 不正确</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentImage: null,
      currentLanguage: 'mandarin',
      languageIndex: 0,
      languages: [
        { id: 'mandarin', name: '普通话' },
        { id: 'hefei', name: '合肥话' },
        { id: 'english', name: 'English' }
      ],
      currentRecognitionType: 'building',
      isLoading: false,
      showResult: false,
      feedbackSelected: '',
      recognitionData: {
        name: '',
        accuracy: 0,
        tags: [],
        meta: '',
        description: '',
        image: ''
      },
      recognitionExamples: {
        'building': [
          { name: '信标台', image: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80' },
          { name: '航站楼', image: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80' },
          { name: '科普馆', image: 'https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80' }
        ],
        'plant': [
          { name: '樱花', image: 'https://images.unsplash.com/photo-1522383225653-ed111181a951?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80' },
          { name: '银杏', image: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80' },
          { name: '梧桐', image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80' }
        ]
      }
    }
  },
  computed: {
    currentExamples() {
      return this.recognitionExamples[this.currentRecognitionType] || [];
    },
    cameraAreaStyle() {
      if (this.currentImage) {
        return {};
      }
      return {
        background: '#f8f9fa'
      };
    }
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '看图识景'
    });
  },
  methods: {
    selectRecognitionType(type) {
      this.currentRecognitionType = type;
    },
    useExampleImage(imageUrl, name) {
      this.currentImage = { url: imageUrl, name: name };
      this.showNotification(`已选择示例图片：${name}`, 'success');
    },
    selectImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.currentImage = res.tempFilePaths[0];
          this.showNotification('图片上传成功，点击识别按钮开始识别', 'success');
        }
      });
    },
    clearImage() {
      this.currentImage = null;
      this.showResult = false;
    },
    recognizeImage() {
      if (!this.currentImage) {
        this.showNotification('请先选择或上传图片', 'warning');
        return;
      }

      this.isLoading = true;
      this.showResult = false;
      this.showNotification('正在识别中...', 'info');

      // 模拟识别过程
      setTimeout(() => {
        const recognitionData = this.simulateRecognition();
        this.displayRecognitionResult(recognitionData);
        
        this.isLoading = false;
        this.showResult = true;
        this.showNotification('图片识别完成！', 'success');
      }, 2000);
    },
    simulateRecognition() {
      // 模拟不同的识别结果
      const attractions = [
        {
          name: '信标台',
          image: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
          tags: ['地标建筑', '观景台', '拍照打卡'],
          meta: '高度：约50米<br>开放时间：全天<br>门票：免费',
          description: '信标台是骆岗公园的标志性建筑，高约50米，原为合肥机场的导航设施。改造后成为公园的观景台，游客可以登顶俯瞰整个公园的美景。建筑设计融合了现代与历史元素，是拍照打卡的热门地点。',
          accuracy: 95
        },
        {
          name: '梦想大草坪',
          image: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
          tags: ['休闲区域', '野餐', '活动场地'],
          meta: '面积：约100亩<br>开放时间：全天<br>门票：免费',
          description: '梦想大草坪是骆岗公园最大的开阔区域，占地约100亩。这里是举办大型活动和市民休闲的理想场所，也是野餐、放风筝、户外运动的热门地点。草坪四季常绿，为游客提供了亲近自然的绝佳空间。',
          accuracy: 92
        },
        {
          name: '航空科普馆',
          image: 'https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
          tags: ['科普教育', '航空主题', '互动体验'],
          meta: '开放时间：9:00-17:00<br>门票：20元<br>适合年龄：全年龄',
          description: '航空科普馆以航空为主题，展示了航空发展历史和现代航空技术。馆内设有飞行模拟器、航空知识互动展示等设施，是进行科普教育和亲子游览的理想场所。',
          accuracy: 88
        }
      ];

      // 随机选择一个景点进行演示
      return attractions[Math.floor(Math.random() * attractions.length)];
    },
    displayRecognitionResult(data) {
      this.recognitionData = data;
    },
    playAudio() {
      const language = this.languages[this.languageIndex].id;
      
      const messages = {
        'mandarin': `正在播放${this.recognitionData.name}的普通话介绍...`,
        'hefei': `正在播放${this.recognitionData.name}的合肥话介绍...`,
        'english': `Playing English introduction for ${this.recognitionData.name}...`
      };
      
      this.showNotification(messages[language], 'info');
      
      // 模拟音频播放
      setTimeout(() => {
        this.showNotification('语音播报完成', 'success');
      }, 3000);
    },
    changeLanguage(e) {
      this.languageIndex = e.detail.value;
      const language = this.languages[this.languageIndex];
      this.showNotification(`已切换到${language.name}模式`, 'info');
    },
    showNotification(message, type = 'info') {
      uni.showToast({
        title: message,
        icon: type === 'success' ? 'success' : 'none',
        duration: 2000
      });
    },
    submitFeedback(isCorrect) {
      this.feedbackSelected = isCorrect ? 'correct' : 'incorrect';
      
      const feedbackText = isCorrect ? '感谢您的反馈！' : '感谢反馈，我们会持续改进识别准确性';
      this.showNotification(feedbackText, 'success');

      // 这里通常会向服务器发送反馈
      console.log('Feedback submitted:', {
        result: this.recognitionData,
        isCorrect: isCorrect,
        timestamp: new Date().toISOString()
      });
    }
  },
  components: {
    'park-header': () => import('@/components/park-header/park-header')
  }
}
</script>

<style>
.app-container {
  max-width: 750rpx;
  margin: 0 auto;
  background: #f8fffe;
  min-height: 100vh;
  position: relative;
}

.content {
  padding: 20rpx 32rpx;
}

/* 识别类型选择 */
.recognition-types {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 24rpx;
}

.type-cards {
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.type-card {
  background: #f8f9fa;
  border: 4rpx solid transparent;
  border-radius: 24rpx;
  padding: 32rpx;
  text-align: center;
  flex: 1;
  transition: all 0.3s;
}

.type-card.active {
  border-color: #3498db;
  background: #e3f2fd;
}

.type-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.1);
}

.type-icon {
  width: 120rpx;
  height: 120rpx;
  background-size: cover;
  background-position: center;
  border-radius: 16rpx;
  margin: 0 auto 16rpx;
}

.type-title {
  font-weight: bold;
  margin-bottom: 8rpx;
  color: #2c3e50;
  font-size: 28rpx;
}

.type-desc {
  font-size: 22rpx;
  color: #7f8c8d;
}

.type-examples {
  display: flex;
  white-space: nowrap;
  padding-bottom: 8rpx;
}

.example-item {
  display: inline-block;
  min-width: 160rpx;
  text-align: center;
  margin-right: 16rpx;
}

.example-image {
  width: 120rpx;
  height: 120rpx;
  background-size: cover;
  background-position: center;
  border-radius: 16rpx;
  border: 4rpx solid transparent;
  margin-bottom: 8rpx;
  transition: all 0.3s;
  display: inline-block;
}

.example-image:active {
  border-color: #3498db;
}

.example-name {
  font-size: 20rpx;
  color: #7f8c8d;
}

/* 相机区域 */
.camera-section {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  margin-bottom: 40rpx;
  text-align: center;
}

.camera-area {
  width: 100%;
  height: 500rpx;
  border: 4rpx dashed #3498db;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  transition: all 0.3s;
  background: #f8f9fa;
  position: relative;
  overflow: hidden;
}

.camera-placeholder {
  text-align: center;
  color: #7f8c8d;
}

.camera-icon {
  font-size: 96rpx;
  margin-bottom: 24rpx;
  color: #3498db;
}

.camera-text {
  font-size: 32rpx;
  margin-bottom: 16rpx;
}

.camera-hint {
  font-size: 24rpx;
  color: #95a5a6;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 20rpx;
}

.camera-buttons {
  display: flex;
  gap: 24rpx;
}

.btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 24rpx 48rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  transition: background 0.3s;
  flex: 1;
}

.btn:active {
  background: #2980b9;
}

.btn-secondary {
  background: #ecf0f1;
  color: #2c3e50;
}

.btn-secondary:active {
  background: #d5dbdb;
}

/* 加载中 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: #7f8c8d;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 8rpx solid #ecf0f1;
  border-top: 8rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 识别结果 */
.recognition-result {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  margin-bottom: 40rpx;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
}

.recognition-accuracy {
  background: #e8f5e8;
  color: #27ae60;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 22rpx;
  font-weight: bold;
  margin-right: 16rpx;
}

.play-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.play-btn:active {
  background: #229954;
}

.attraction-info {
  display: flex;
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.attraction-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 16rpx;
  background-size: cover;
  background-position: center;
  flex-shrink: 0;
}

.attraction-details {
  flex: 1;
}

.attraction-name {
  font-size: 40rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 16rpx;
}

.attraction-tags {
  display: flex;
  gap: 12rpx;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.attraction-tag {
  background: #e3f2fd;
  color: #3498db;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 20rpx;
}

.attraction-meta {
  font-size: 24rpx;
  color: #7f8c8d;
  line-height: 1.4;
}

.attraction-description {
  background: #f8f9fa;
  padding: 32rpx;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
}

.description-title {
  font-weight: bold;
  margin-bottom: 16rpx;
  color: #2c3e50;
  font-size: 28rpx;
}

.description-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #34495e;
}

.language-selector {
  background: #f8f9fa;
  padding: 32rpx;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
}

.language-label {
  display: block;
  margin-bottom: 16rpx;
  font-weight: bold;
  color: #2c3e50;
  font-size: 28rpx;
}

.language-select {
  width: 100%;
  padding: 16rpx 24rpx;
  border: 2rpx solid #ddd;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.picker-value {
  padding: 16rpx 0;
}

/* 反馈部分 */
.feedback-section {
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 16rpx;
}

.feedback-title {
  font-weight: bold;
  margin-bottom: 16rpx;
  color: #2c3e50;
  font-size: 24rpx;
}

.feedback-buttons {
  display: flex;
  gap: 16rpx;
}

.feedback-btn {
  background: white;
  border: 2rpx solid #ddd;
  padding: 12rpx 24rpx;
  border-radius: 32rpx;
  font-size: 22rpx;
  flex: 1;
  text-align: center;
}

.feedback-btn.correct {
  border-color: #27ae60;
  color: #27ae60;
}

.feedback-btn.incorrect {
  border-color: #e74c3c;
  color: #e74c3c;
}

.feedback-btn.selected {
  background: #27ae60;
  color: white;
}

.feedback-btn.selected.incorrect {
  background: #e74c3c;
}
</style>