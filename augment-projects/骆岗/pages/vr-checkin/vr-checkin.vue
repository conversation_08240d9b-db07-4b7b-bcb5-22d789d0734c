<template>
  <view class="app-container">
    <park-header title="🥽 VR打卡体验" :show-back="true"></park-header>

    <view class="content">
      <!-- 场景选择 -->
      <view class="section">
        <view class="section-title">🎮 选择VR场景</view>
        <view class="scene-selection">
          <view class="scene-grid">
            <view 
              class="scene-card" 
              :class="{ completed: isSceneCompleted('runway') }"
              @click="selectScene('runway')">
              <view class="scene-status">{{ isSceneCompleted('runway') ? '✓' : '○' }}</view>
              <view class="scene-icon">🛫</view>
              <view class="scene-title">机场跑道</view>
              <view class="scene-desc">沿跑道VR漫步</view>
            </view>
            <view 
              class="scene-card" 
              :class="{ completed: isSceneCompleted('tower') }"
              @click="selectScene('tower')">
              <view class="scene-status">{{ isSceneCompleted('tower') ? '✓' : '○' }}</view>
              <view class="scene-icon">🗼</view>
              <view class="scene-title">信标台顶部</view>
              <view class="scene-desc">360度全景体验</view>
            </view>
            <view 
              class="scene-card" 
              :class="{ completed: isSceneCompleted('grassland') }"
              @click="selectScene('grassland')">
              <view class="scene-status">{{ isSceneCompleted('grassland') ? '✓' : '○' }}</view>
              <view class="scene-icon">🌱</view>
              <view class="scene-title">梦想大草坪</view>
              <view class="scene-desc">草坪野餐体验</view>
            </view>
            <view 
              class="scene-card" 
              :class="{ completed: isSceneCompleted('museum') }"
              @click="selectScene('museum')">
              <view class="scene-status">{{ isSceneCompleted('museum') ? '✓' : '○' }}</view>
              <view class="scene-icon">✈️</view>
              <view class="scene-title">航空科普馆</view>
              <view class="scene-desc">飞行模拟体验</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 跑道路径（特殊场景） -->
      <view class="runway-path" v-if="showRunwayPath">
        <view class="section-title">🛫 机场跑道打卡路径</view>
        <view class="runway-map">
          <view class="runway-line"></view>
          <view 
            v-for="point in 4" 
            :key="point"
            class="checkin-point" 
            :class="{ completed: isRunwayPointCompleted(point) }"
            :style="{ left: (15 + (point - 1) * 20) + '%' }"
            @click="checkinAtPoint(point)"
            :title="getRunwayPointTitle(point)"></view>
        </view>
        <view class="checkin-info">
          <view class="checkin-title">沿着机场跑道进行VR体验</view>
          <view class="checkin-desc">点击地图上的打卡点开始VR体验，完成所有打卡点可获得额外积分奖励</view>
        </view>
      </view>

      <!-- 进度部分 -->
      <view class="section">
        <view class="section-title">🎯 打卡进度</view>
        <view class="progress-section">
          <view class="progress-header">
            <view class="progress-title">我的积分</view>
            <view class="points-display">{{ userProgress.points }}分</view>
          </view>
          
          <view class="progress-stats">
            <view class="stat-item">
              <view class="stat-value">{{ completedScenesCount }}</view>
              <view class="stat-label">已完成场景</view>
            </view>
            <view class="stat-item">
              <view class="stat-value">{{ userProgress.totalEarned }}</view>
              <view class="stat-label">本次获得积分</view>
            </view>
          </view>

          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: progressPercentage + '%' }"></view>
          </view>
          <view class="progress-text">已完成 {{ completedScenesCount }}/4 个VR场景</view>
        </view>
      </view>

      <!-- VR体验 -->
      <view class="vr-experience" v-if="showVRExperience">
        <view class="section-title">{{ vrTitle }}</view>
        <view class="vr-viewer">
          <view style="position: relative; z-index: 2;">{{ vrViewerContent }}</view>
        </view>
        <view class="vr-controls">
          <button class="btn btn-secondary" @click="exitVR">退出VR</button>
          <button class="btn btn-success" @click="completeCheckin">完成打卡</button>
        </view>
        <view style="text-align: center; font-size: 24rpx; color: #7f8c8d; margin-top: 16rpx;">
          请将手机横屏并使用VR眼镜获得最佳体验
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userProgress: {
        points: 1250,
        completedScenes: [],
        runwayPoints: [],
        totalEarned: 0
      },
      currentScene: null,
      currentRunwayPoint: null,
      showRunwayPath: false,
      showVRExperience: false,
      vrTitle: '🥽 VR体验中',
      vrViewerContent: 'VR场景加载中...'
    };
  },
  computed: {
    completedScenesCount() {
      // 计算唯一场景数量（跑道算作一个场景，无论有多少个点）
      const uniqueScenes = new Set(this.userProgress.completedScenes.map(scene => 
        scene.startsWith('runway') ? 'runway' : scene
      ));
      return uniqueScenes.size;
    },
    progressPercentage() {
      return (this.completedScenesCount / 4) * 100;
    }
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: 'VR打卡体验'
    });
    
    // 加载保存的进度
    this.loadProgress();
    
    // 监听设备方向变化以获得更好的VR体验
    uni.onDeviceMotionChange((res) => {
      if (this.showVRExperience) {
        // 可以根据设备方向调整VR体验
      }
    });
  },
  methods: {
    selectScene(sceneType) {
      this.currentScene = sceneType;
      
      if (sceneType === 'runway') {
        this.showRunwayPath = true;
        
        // 滚动到跑道部分
        this.$nextTick(() => {
          const query = uni.createSelectorQuery().in(this);
          query.select('.runway-path').boundingClientRect(data => {
            uni.pageScrollTo({
              scrollTop: data.top,
              duration: 300
            });
          }).exec();
        });
      } else {
        this.showRunwayPath = false;
        this.startVRExperience(sceneType);
      }
    },
    
    checkinAtPoint(pointIndex) {
      this.currentRunwayPoint = pointIndex;
      this.currentScene = 'runway';
      this.startVRExperience('runway', pointIndex);
    },
    
    startVRExperience(sceneType, pointIndex = null) {
      const sceneNames = {
        'runway': pointIndex ? `机场跑道 - 打卡点${pointIndex}` : '机场跑道VR体验',
        'tower': '信标台360度全景',
        'grassland': '梦想大草坪VR漫步',
        'museum': '航空科普馆虚拟参观'
      };

      const sceneDescriptions = {
        'runway': pointIndex ? `正在体验跑道第${pointIndex}个打卡点...` : '正在体验机场跑道VR漫步...',
        'tower': '正在加载信标台360度全景体验...',
        'grassland': '正在加载梦想大草坪VR体验...',
        'museum': '正在加载航空科普馆VR体验...'
      };

      this.vrTitle = `🥽 ${sceneNames[sceneType]}`;
      this.vrViewerContent = sceneDescriptions[sceneType];
      this.showVRExperience = true;
      
      // 滚动到VR体验部分
      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this);
        query.select('.vr-experience').boundingClientRect(data => {
          uni.pageScrollTo({
            scrollTop: data.top,
            duration: 300
          });
        }).exec();
      });

      // 模拟VR加载
      setTimeout(() => {
        this.vrViewerContent = 'VR体验进行中... 🥽';
        this.showNotification(`${sceneNames[sceneType]}体验开始！`, 'info');
      }, 2000);
    },
    
    completeCheckin() {
      if (!this.currentScene) return;

      let pointsEarned = 50;
      let sceneKey = this.currentScene;

      if (this.currentScene === 'runway' && this.currentRunwayPoint) {
        sceneKey = `runway-${this.currentRunwayPoint}`;
        if (!this.userProgress.runwayPoints.includes(this.currentRunwayPoint)) {
          this.userProgress.runwayPoints.push(this.currentRunwayPoint);
          
          // 检查是否完成所有跑道点
          if (this.userProgress.runwayPoints.length === 4) {
            pointsEarned += 100; // 额外积分奖励
            this.showNotification('🎉 完成所有跑道打卡点，获得额外100积分奖励！', 'success');
          }
        } else {
          this.showNotification('该打卡点已完成', 'warning');
          return;
        }
      } else {
        if (this.userProgress.completedScenes.includes(sceneKey)) {
          this.showNotification('该场景已完成', 'warning');
          return;
        }
        this.userProgress.completedScenes.push(sceneKey);
      }

      // 更新积分
      this.userProgress.points += pointsEarned;
      this.userProgress.totalEarned += pointsEarned;

      this.showNotification(`VR体验完成，获得${pointsEarned}积分！`, 'success');

      // 保存进度
      this.saveProgress();

      // 退出VR
      setTimeout(() => {
        this.exitVR();
      }, 2000);
    },
    
    exitVR() {
      this.showVRExperience = false;
      this.currentScene = null;
      this.currentRunwayPoint = null;
    },
    
    isSceneCompleted(sceneType) {
      if (sceneType === 'runway') {
        // 对于跑道，检查是否有任何跑道点被完成
        return this.userProgress.completedScenes.some(scene => scene.startsWith('runway')) || 
               this.userProgress.runwayPoints.length > 0;
      }
      return this.userProgress.completedScenes.includes(sceneType);
    },
    
    isRunwayPointCompleted(pointIndex) {
      return this.userProgress.runwayPoints.includes(pointIndex);
    },
    
    getRunwayPointTitle(pointIndex) {
      const titles = ['起点', '跑道中段1', '跑道中段2', '终点'];
      return titles[pointIndex - 1] || `打卡点${pointIndex}`;
    },
    
    loadProgress() {
      try {
        const saved = uni.getStorageSync('vrProgress');
        if (saved) {
          const parsedData = JSON.parse(saved);
          this.userProgress = { ...this.userProgress, ...parsedData };
        }
      } catch (e) {
        console.error('加载VR进度失败', e);
      }
    },
    
    saveProgress() {
      try {
        uni.setStorageSync('vrProgress', JSON.stringify(this.userProgress));
      } catch (e) {
        console.error('保存VR进度失败', e);
      }
    },
    
    showNotification(message, type = 'info') {
      const iconMap = {
        'success': 'success',
        'error': 'error',
        'warning': 'none',
        'info': 'none'
      };
      
      uni.showToast({
        title: message,
        icon: iconMap[type],
        duration: 2000
      });
    }
  },
  components: {
    'park-header': () => import('@/components/park-header/park-header')
  }
}
</script>

<style>
.app-container {
  max-width: 750rpx;
  margin: 0 auto;
  background: rgba(255,255,255,0.95);
  min-height: 100vh;
  position: relative;
}

.content {
  padding: 40rpx 32rpx;
}

.section {
  margin-bottom: 48rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  color: #2c3e50;
}

.scene-selection {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  margin-bottom: 40rpx;
}

.scene-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.scene-card {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: white;
  border-radius: 24rpx;
  padding: 40rpx;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.scene-card:nth-child(2) {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.scene-card:nth-child(3) {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.scene-card:nth-child(4) {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.scene-card.completed {
  background: linear-gradient(135deg, #56ab2f, #a8e6cf);
}

.scene-icon {
  font-size: 64rpx;
  margin-bottom: 16rpx;
}

.scene-title {
  font-weight: bold;
  margin-bottom: 8rpx;
}

.scene-desc {
  font-size: 24rpx;
  opacity: 0.9;
}

.scene-status {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(255,255,255,0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.runway-path {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  margin-bottom: 40rpx;
}

.runway-map {
  width: 100%;
  height: 400rpx;
  background: linear-gradient(45deg, #a8edea, #fed6e3);
  border-radius: 24rpx;
  position: relative;
  margin-bottom: 32rpx;
  overflow: hidden;
}

.runway-line {
  position: absolute;
  top: 50%;
  left: 10%;
  right: 10%;
  height: 8rpx;
  background: #2c3e50;
  transform: translateY(-50%);
}

.runway-line::before,
.runway-line::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 4rpx;
  height: 40rpx;
  background: white;
  transform: translateY(-50%);
}

.runway-line::before {
  left: 25%;
}

.runway-line::after {
  right: 25%;
}

.checkin-point {
  position: absolute;
  width: 32rpx;
  height: 32rpx;
  background: #e74c3c;
  border: 4rpx solid white;
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
  animation: pulse 2s infinite;
}

.checkin-point.completed {
  background: #27ae60;
  animation: none;
}

@keyframes pulse {
  0% { transform: translateY(-50%) scale(1); }
  50% { transform: translateY(-50%) scale(1.2); }
  100% { transform: translateY(-50%) scale(1); }
}

.checkin-info {
  text-align: center;
  margin-bottom: 32rpx;
}

.checkin-title {
  font-weight: bold;
  margin-bottom: 16rpx;
  color: #2c3e50;
}

.checkin-desc {
  font-size: 28rpx;
  color: #7f8c8d;
  line-height: 1.4;
}

.progress-section {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  margin-bottom: 40rpx;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.progress-title {
  font-weight: bold;
  color: #2c3e50;
}

.points-display {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
  padding: 12rpx 24rpx;
  border-radius: 32rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.progress-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.stat-item {
  text-align: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.stat-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #3498db;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #7f8c8d;
}

.progress-bar {
  width: 100%;
  height: 24rpx;
  background: #ecf0f1;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  transition: width 0.3s ease;
  border-radius: 12rpx;
}

.progress-text {
  text-align: center;
  font-size: 24rpx;
  color: #7f8c8d;
}

.vr-experience {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  margin-top: 40rpx;
}

.vr-viewer {
  width: 100%;
  height: 500rpx;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 36rpx;
  margin-bottom: 32rpx;
  position: relative;
  overflow: hidden;
}

.vr-viewer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="white" opacity="0.3"/><circle cx="20" cy="30" r="1" fill="white" opacity="0.5"/><circle cx="80" cy="20" r="1.5" fill="white" opacity="0.4"/><circle cx="70" cy="80" r="1" fill="white" opacity="0.6"/></svg>');
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20rpx); }
}

.vr-controls {
  display: flex;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 24rpx 48rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  transition: background 0.3s;
  flex: 1;
}

.btn-secondary {
  background: #ecf0f1;
  color: #2c3e50;
}

.btn-success {
  background: #27ae60;
}
</style>