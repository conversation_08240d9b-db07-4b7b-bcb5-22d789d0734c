<template>
  <view class="app-container">
    <park-header title="📝 游记生成" :show-back="true"></park-header>

    <view class="content">
      <!-- 照片上传区域 -->
      <view class="section">
        <view class="section-title">📸 上传照片</view>
        <view class="upload-section">
          <view class="upload-area" @click="selectImages" id="uploadArea">
            <view class="upload-placeholder">
              <view class="upload-icon">📸</view>
              <view class="upload-text">点击上传照片</view>
              <view class="upload-hint">支持多张图片，JPG/PNG格式</view>
            </view>
          </view>
          <view class="image-preview">
            <view 
              class="image-preview-item" 
              v-for="(image, index) in uploadedImages" 
              :key="index"
              :style="{ backgroundImage: 'url(' + image.url + ')' }">
              <button class="image-remove" @click.stop="removeImage(index)">×</button>
            </view>
          </view>
        </view>
      </view>

      <!-- 描述区域 -->
      <view class="section">
        <view class="section-title">✍️ 游玩描述</view>
        <view class="description-section">
          <text class="form-label">请描述您的游玩体验、感受和见闻</text>
          <textarea 
            class="form-textarea" 
            v-model="travelDescription" 
            placeholder="今天来到骆岗公园，天气很好...

可以描述：
• 游览的景点和体验
• 印象深刻的瞬间
• 与朋友家人的互动
• 对公园的整体感受
• 推荐给其他游客的建议"
            @input="updateCharCount"
          ></textarea>
          <view class="char-count" :style="{ color: charCountColor }">{{ charCount }}/500字</view>
        </view>
      </view>

      <!-- 生成按钮 -->
      <view class="section">
        <button class="btn" @click="generateTravelLog">🤖 AI生成游记</button>
      </view>

      <!-- 加载中 -->
      <view class="loading" v-if="isLoading">
        <view class="loading-spinner"></view>
        <view>AI正在生成您的专属游记...</view>
      </view>

      <!-- 生成的游记 -->
      <view class="generated-log" v-if="showGeneratedLog">
        <view class="log-header">
          <view>
            <view class="log-title">{{ logTitle }}</view>
            <view class="log-meta">{{ logMeta }}</view>
          </view>
        </view>

        <!-- 显示模式 -->
        <view v-if="!isEditMode">
          <view class="log-content" v-html="logContent"></view>
          <view class="log-actions">
            <button class="btn btn-secondary" @click="editTravelLog" style="flex: 1;">✏️ 编辑</button>
            <button class="btn" @click="publishTravelLog" style="flex: 1;">📤 发布</button>
            <button class="btn btn-secondary" @click="shareTravelLog">🔗 分享</button>
          </view>
        </view>

        <!-- 编辑模式 -->
        <view v-else>
          <textarea class="edit-textarea" v-model="editContent"></textarea>
          <view style="display: flex; gap: 24rpx; margin-top: 24rpx;">
            <button class="btn btn-secondary" @click="cancelEdit" style="flex: 1;">取消</button>
            <button class="btn" @click="saveEdit" style="flex: 1;">保存</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      uploadedImages: [],
      travelDescription: '',
      charCount: 0,
      charCountColor: '#7f8c8d',
      isLoading: false,
      showGeneratedLog: false,
      logTitle: '',
      logContent: '',
      logMeta: '',
      generatedContent: '',
      isEditMode: false,
      editContent: ''
    };
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '游记生成'
    });
  },
  methods: {
    selectImages() {
      uni.chooseImage({
        count: 9,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePaths = res.tempFilePaths;
          const tempFiles = res.tempFiles;
          
          tempFilePaths.forEach((path, index) => {
            this.uploadedImages.push({
              url: path,
              name: tempFiles[index].name || `图片${index + 1}`,
              file: tempFiles[index]
            });
          });
          
          if (tempFilePaths.length > 0) {
            this.showNotification(`已上传${tempFilePaths.length}张照片`, 'success');
          }
        }
      });
    },
    
    removeImage(index) {
      this.uploadedImages.splice(index, 1);
    },
    
    updateCharCount() {
      this.charCount = this.travelDescription.length;
      
      if (this.charCount > 500) {
        this.charCountColor = '#e74c3c';
      } else {
        this.charCountColor = '#7f8c8d';
      }
    },
    
    generateTravelLog() {
      const description = this.travelDescription.trim();
      
      if (!description) {
        this.showNotification('请先输入游玩描述', 'warning');
        return;
      }

      if (description.length > 500) {
        this.showNotification('描述内容不能超过500字', 'warning');
        return;
      }

      // 显示加载
      this.isLoading = true;
      this.showGeneratedLog = false;

      // 模拟AI处理
      setTimeout(() => {
        const logData = this.generateLogContent(description);
        this.logTitle = logData.title;
        this.logContent = logData.content;
        this.logMeta = logData.meta;
        
        this.isLoading = false;
        this.showGeneratedLog = true;
        
        this.showNotification('游记生成完成！', 'success');
        
        // 滚动到结果
        this.$nextTick(() => {
          const query = uni.createSelectorQuery().in(this);
          query.select('.generated-log').boundingClientRect(data => {
            uni.pageScrollTo({
              scrollTop: data.top,
              duration: 300
            });
          }).exec();
        });
      }, 3000);
    },
    
    generateLogContent(description) {
      const templates = [
        {
          title: '骆岗公园春日游记',
          content: `
            <h3>🌸 春日骆岗，不负好时光</h3>
            <p>今天来到了合肥的骆岗公园，${description}</p>
            <p>信标台作为公园的标志性建筑，登顶后可以俯瞰整个公园的美景。春天的骆岗公园生机勃勃，梦想大草坪上绿草如茵，是野餐和放松的绝佳场所。</p>
            <p>航空科普馆让我了解了很多航空知识，特别是合肥机场的历史变迁。从繁忙的机场到如今的城市公园，这种转变真的很有意义。</p>
            <p>总的来说，骆岗公园是一个集休闲、科普、娱乐于一体的好地方，强烈推荐大家来体验！</p>
          `
        },
        {
          title: '我的骆岗公园探索之旅',
          content: `
            <h3>🗺️ 探索骆岗，发现惊喜</h3>
            <p>${description} 这次的骆岗公园之行真的收获满满。</p>
            <p>最让我印象深刻的是信标台的设计，既保留了历史的痕迹，又融入了现代的元素。站在塔顶，整个公园的布局一览无余，那种开阔的感觉让人心旷神怡。</p>
            <p>梦想大草坪真的名副其实，在这里可以尽情地享受阳光和绿意。看到很多家庭在这里野餐、孩子们在草地上奔跑，那种生活的美好让人感动。</p>
            <p>如果你还没有来过骆岗公园，我真的强烈推荐你来看看。这里不仅有美丽的风景，更有一种独特的历史文化氛围。</p>
          `
        }
      ];

      const template = templates[Math.floor(Math.random() * templates.length)];
      this.generatedContent = template.content;
      
      return {
        title: template.title,
        content: template.content,
        meta: `生成时间：${new Date().toLocaleString()}`
      };
    },
    
    editTravelLog() {
      // 将HTML内容转换为纯文本进行编辑
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = this.generatedContent;
      this.editContent = tempDiv.textContent || tempDiv.innerText || '';
      
      this.isEditMode = true;
    },
    
    cancelEdit() {
      this.isEditMode = false;
    },
    
    saveEdit() {
      const newContent = this.editContent.trim();
      
      if (!newContent) {
        this.showNotification('内容不能为空', 'warning');
        return;
      }
      
      // 将纯文本转换回格式化的HTML
      const formattedContent = `<p>${newContent.replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>')}</p>`;
      this.generatedContent = formattedContent;
      this.logContent = formattedContent;
      
      this.isEditMode = false;
      this.showNotification('游记已更新', 'success');
    },
    
    publishTravelLog() {
      if (!this.generatedContent) {
        this.showNotification('请先生成游记', 'warning');
        return;
      }
      
      // 模拟发布
      this.showNotification('正在发布游记...', 'info');
      
      setTimeout(() => {
        // 保存到本地存储
        try {
          const travelLogs = uni.getStorageSync('userTravelLogs') || '[]';
          const logsArray = JSON.parse(travelLogs);
          const newLog = {
            id: Date.now(),
            title: this.logTitle,
            content: this.generatedContent,
            images: this.uploadedImages.length,
            date: new Date().toISOString().split('T')[0],
            published: true
          };
          logsArray.push(newLog);
          uni.setStorageSync('userTravelLogs', JSON.stringify(logsArray));
          
          this.showNotification('游记发布成功！', 'success');
        } catch (e) {
          this.showNotification('发布失败，请重试', 'error');
          console.error(e);
        }
      }, 1500);
    },
    
    shareTravelLog() {
      if (!this.generatedContent) {
        this.showNotification('请先生成游记', 'warning');
        return;
      }
      
      // 模拟分享
      this.showNotification('分享链接已复制到剪贴板', 'success');
    },
    
    showNotification(message, type = 'info') {
      uni.showToast({
        title: message,
        icon: type === 'success' ? 'success' : 'none',
        duration: 2000
      });
    }
  },
  components: {
    'park-header': () => import('@/components/park-header/park-header')
  }
}
</script>

<style>
.app-container {
  max-width: 750rpx;
  margin: 0 auto;
  background: rgba(255,255,255,0.95);
  min-height: 100vh;
  position: relative;
}

.content {
  padding: 40rpx 32rpx;
}

.section {
  margin-bottom: 48rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  color: #2c3e50;
}

.upload-section {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  margin-bottom: 40rpx;
}

.upload-area {
  width: 100%;
  height: 300rpx;
  border: 4rpx dashed #3498db;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  background: #f8f9fa;
}

.upload-placeholder {
  text-align: center;
  color: #7f8c8d;
}

.upload-icon {
  font-size: 64rpx;
  margin-bottom: 16rpx;
  color: #3498db;
}

.upload-text {
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.upload-hint {
  font-size: 24rpx;
  color: #95a5a6;
}

.image-preview {
  display: flex;
  gap: 16rpx;
  margin-top: 24rpx;
  flex-wrap: wrap;
}

.image-preview-item {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  overflow: hidden;
  position: relative;
  background: #f0f0f0;
  background-size: cover;
  background-position: center;
}

.image-remove {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0,0,0,0.7);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.description-section {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  margin-bottom: 16rpx;
  font-weight: bold;
  color: #2c3e50;
}

.form-textarea {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #ddd;
  border-radius: 16rpx;
  font-size: 28rpx;
  min-height: 240rpx;
  line-height: 1.5;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #7f8c8d;
  margin-top: 8rpx;
}

.btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 24rpx 48rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  transition: background 0.3s;
  width: 100%;
}

.btn-secondary {
  background: #ecf0f1;
  color: #2c3e50;
}

.loading {
  text-align: center;
  padding: 40rpx;
  color: #7f8c8d;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 8rpx solid #ecf0f1;
  border-top: 8rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.generated-log {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  margin-top: 40rpx;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 2rpx solid #ecf0f1;
}

.log-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #2c3e50;
}

.log-meta {
  font-size: 24rpx;
  color: #7f8c8d;
}

.log-content {
  background: #f8f9fa;
  padding: 32rpx;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  line-height: 1.6;
}

.log-content h3 {
  color: #3498db;
  margin-bottom: 24rpx;
  font-size: 32rpx;
}

.log-content p {
  margin-bottom: 24rpx;
  text-align: justify;
  font-size: 28rpx;
}

.log-actions {
  display: flex;
  gap: 24rpx;
}

.edit-textarea {
  width: 100%;
  min-height: 400rpx;
  padding: 24rpx;
  border: 2rpx solid #ddd;
  border-radius: 16rpx;
  font-size: 28rpx;
  line-height: 1.6;
}
</style>