<template>
  <view class="app-container">
    <view class="header">
      <view class="back-btn" @click="goBack">←</view>
      <view class="header-content">
        <view class="bot-avatar">🦌</view>
        <view class="bot-info">
          <view class="bot-name">小骆助手</view>
          <view class="bot-status">在线 · 随时为您服务</view>
        </view>
      </view>
    </view>

    <view class="chat-container">
      <scroll-view class="chat-messages" scroll-y="true" :scroll-top="scrollTop" id="chatMessages">
        <view class="welcome-message" v-if="messageHistory.length === 0">
          <view class="welcome-avatar">🦌</view>
          <view class="welcome-text">你好！我是小骆助手</view>
          <view class="welcome-desc">我可以帮您了解骆岗公园的各种信息<br>有什么问题尽管问我吧！</view>
        </view>
        
        <view 
          v-for="(message, index) in messageHistory" 
          :key="index" 
          :class="['message', message.sender]">
          <view :class="['message-avatar', message.sender]">
            {{ message.sender === 'bot' ? '🦌' : '👤' }}
          </view>
          <view class="message-content">
            <view class="message-text">{{ message.text }}</view>
            <view class="message-time">{{ message.time }}</view>
          </view>
        </view>
        
        <!-- Typing Indicator -->
        <view class="typing-indicator" v-show="isTyping">
          <view class="message-avatar bot">🦌</view>
          <view class="typing-dots">
            <view class="typing-dot"></view>
            <view class="typing-dot"></view>
            <view class="typing-dot"></view>
          </view>
        </view>
      </scroll-view>

      <!-- Quick Questions -->
      <view class="quick-questions" v-show="showQuickQuestions">
        <view class="quick-title">💡 常见问题</view>
        <view class="question-buttons">
          <view 
            v-for="(question, index) in quickQuestions" 
            :key="index" 
            class="question-btn" 
            @click="askQuestion(question.text)">
            {{ question.icon }} {{ question.text }}
          </view>
        </view>
      </view>

      <!-- Input Area -->
      <view class="input-area">
        <input 
          type="text" 
          class="message-input" 
          v-model="inputMessage" 
          placeholder="输入您的问题..." 
          @keypress="handleKeyPress"
          confirm-type="send"
          @confirm="sendMessage" />
        <view class="send-btn" @click="sendMessage" :disabled="!inputMessage.trim()">
          ➤
        </view>
      </view>
    </view>
    
    <!-- Notification -->
    <view class="notification" v-if="notification.show">
      {{ notification.message }}
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      messageHistory: [],
      inputMessage: '',
      isTyping: false,
      showQuickQuestions: true,
      scrollTop: 0,
      notification: {
        show: false,
        message: ''
      },
      quickQuestions: [
        { icon: '🕐', text: '骆岗公园的开放时间是什么时候？' },
        { icon: '🏛️', text: '有哪些必看的景点推荐？' },
        { icon: '🚌', text: '园区内有哪些交通方式？' },
        { icon: '👶', text: '适合带小朋友游玩吗？' },
        { icon: '🍽️', text: '园区内有餐饮服务吗？' },
        { icon: '🚗', text: '如何到达骆岗公园？' }
      ],
      responses: {
        '开放时间': '骆岗公园全天24小时开放，但建议您在8:00-18:00期间游览，这时各个场馆和设施都正常开放。夜间虽然可以进入，但部分设施会关闭。',
        '景点推荐': '为您推荐几个必看景点：\n🏛️ 信标台 - 园区地标，可俯瞰全景\n✈️ 航空科普馆 - 了解航空历史\n🌿 梦想大草坪 - 适合休闲野餐\n🏢 城市馆 - 合肥城市发展展示\n🦋 昆虫博物馆 - 自然科普教育',
        '交通方式': '园区内提供多种交通方式：\n🚌 无人巴士 - 免费，15分钟一班\n🚐 接驳车 - 5-8元，覆盖主要景点\n🚶 步行 - 推荐，可以慢慢欣赏风景\n🚲 共享单车 - 部分区域可骑行',
        '小朋友': '非常适合带小朋友！我们有：\n👶 儿童游乐区 - 安全有趣的游乐设施\n🦋 昆虫博物馆 - 寓教于乐的科普体验\n✈️ 航空科普馆 - 飞行模拟器体验\n🌿 大草坪 - 可以奔跑玩耍\n还有专门的亲子路线推荐哦！',
        '餐饮服务': '园区内有多种餐饮选择：\n☕ 骆岗咖啡厅 - 精品咖啡和轻食\n🥪 便利店 - 饮料零食\n🧺 野餐区 - 可自带食物\n🍱 临时餐车 - 节假日会有特色小食\n建议您也可以自带一些食物在草坪上野餐！',
        '如何到达': '到达骆岗公园的方式：\n🚇 地铁：乘坐地铁到骆岗站，步行约10分钟\n🚌 公交：多路公交车可达，在骆岗公园站下车\n🚗 自驾：园区周边有停车场\n🚕 打车：直接导航"骆岗公园"即可\n建议使用公共交通，更加环保便捷！'
      },
      defaultResponses: [
        '感谢您的提问！我正在努力学习更多关于骆岗公园的知识。您可以尝试问我关于开放时间、景点推荐、交通方式等问题。',
        '这是个很好的问题！建议您可以查看园区地图或咨询现场工作人员获取更详细的信息。',
        '我会把您的问题记录下来，持续改进我的服务。您还可以尝试问我其他关于骆岗公园的问题。',
        '抱歉我暂时无法准确回答这个问题。您可以拨打园区服务热线或查看官方信息获取帮助。'
      ]
    }
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '小骆助手'
    })
  },
  methods: {
    goBack() {
      uni.navigateBack({
        delta: 1
      })
    },
    askQuestion(question) {
      this.inputMessage = question
      this.sendMessage()
    },
    handleKeyPress(event) {
      if (event.keyCode === 13) { // Enter key
        this.sendMessage()
      }
    },
    sendMessage() {
      const message = this.inputMessage.trim()
      if (!message) return
      
      // Add user message
      this.addMessage(message, 'user')
      this.inputMessage = ''
      
      // Hide quick questions after first message
      if (this.messageHistory.length === 1) {
        this.showQuickQuestions = false
      }
      
      // Show typing indicator
      this.isTyping = true
      
      // Simulate bot response
      setTimeout(() => {
        this.isTyping = false
        const response = this.getBotResponse(message)
        this.addMessage(response, 'bot')
      }, 1500 + Math.random() * 1000)
    },
    addMessage(text, sender) {
      const now = new Date()
      const timeString = now.getHours().toString().padStart(2, '0') + ':' + 
                       now.getMinutes().toString().padStart(2, '0')
      
      this.messageHistory.push({ text, sender, time: timeString })
      
      // Scroll to bottom
      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this)
        query.select('#chatMessages').boundingClientRect(data => {
          if (data) {
            this.scrollTop = data.height * 2 // Ensure scrolling to bottom
          }
        }).exec()
      })
    },
    getBotResponse(message) {
      // Simple keyword matching
      for (const [keyword, response] of Object.entries(this.responses)) {
        if (message.includes(keyword)) {
          return response
        }
      }
      
      // Default responses
      return this.defaultResponses[Math.floor(Math.random() * this.defaultResponses.length)]
    },
    showNotification(message) {
      this.notification.message = message
      this.notification.show = true
      
      setTimeout(() => {
        this.notification.show = false
      }, 3000)
    }
  }
}
</script>

<style>
.app-container {
  max-width: 750rpx;
  margin: 0 auto;
  background: #f8fffe;
  min-height: 100vh;
  position: relative;
}

.header {
  background: linear-gradient(135deg, #3498db, #2ecc71);
  padding: 32rpx;
  display: flex;
  align-items: center;
  color: white;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn {
  background: none;
  border: none;
  font-size: 48rpx;
  cursor: pointer;
  margin-right: 32rpx;
  color: white;
}

.header-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.bot-avatar {
  width: 80rpx;
  height: 80rpx;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
}

.bot-info {
  flex: 1;
}

.bot-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 4rpx;
}

.bot-status {
  font-size: 24rpx;
  opacity: 0.9;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 280rpx);
}

.chat-messages {
  flex: 1;
  padding: 32rpx;
  height: calc(100vh - 400rpx);
}

.message {
  display: flex;
  gap: 16rpx;
  max-width: 85%;
  margin-bottom: 32rpx;
}

.message.user {
  align-self: flex-end;
  flex-direction: row-reverse;
  margin-left: auto;
}

.message-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  flex-shrink: 0;
}

.message-avatar.bot {
  background: linear-gradient(135deg, #3498db, #2ecc71);
  color: white;
}

.message-avatar.user {
  background: #95a5a6;
  color: white;
}

.message-content {
  background: white;
  padding: 24rpx 32rpx;
  border-radius: 36rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  position: relative;
}

.message.user .message-content {
  background: #3498db;
  color: white;
}

.message-text {
  font-size: 28rpx;
  line-height: 1.4;
  white-space: pre-line;
}

.message-time {
  font-size: 20rpx;
  color: #95a5a6;
  margin-top: 8rpx;
}

.message.user .message-time {
  color: rgba(255,255,255,0.8);
}

/* Quick Questions */
.quick-questions {
  padding: 32rpx;
  background: white;
  border-top: 1rpx solid #ecf0f1;
}

.quick-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  color: #2c3e50;
}

.question-buttons {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.question-btn {
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  padding: 24rpx 32rpx;
  border-radius: 24rpx;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 26rpx;
  color: #2c3e50;
}

/* Input Area */
.input-area {
  padding: 32rpx;
  background: white;
  border-top: 1rpx solid #ecf0f1;
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.message-input {
  flex: 1;
  padding: 24rpx 32rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 48rpx;
  font-size: 28rpx;
  outline: none;
  background: #f8f9fa;
}

.send-btn {
  width: 80rpx;
  height: 80rpx;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  transition: all 0.3s;
}

.send-btn[disabled] {
  background: #bdc3c7;
  cursor: not-allowed;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  background: white;
  border-radius: 36rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  max-width: 85%;
  margin-bottom: 32rpx;
}

.typing-dots {
  display: flex;
  gap: 8rpx;
}

.typing-dot {
  width: 12rpx;
  height: 12rpx;
  background: #95a5a6;
  border-radius: 50%;
  animation: typing 1.4s infinite;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-20rpx);
    opacity: 1;
  }
}

/* Welcome Message */
.welcome-message {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #7f8c8d;
  margin-bottom: 32rpx;
}

.welcome-avatar {
  width: 160rpx;
  height: 160rpx;
  background: linear-gradient(135deg, #3498db, #2ecc71);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 80rpx;
  color: white;
  margin: 0 auto 32rpx;
  animation: bounce 2s infinite;
}

.welcome-text {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  color: #2c3e50;
}

.welcome-desc {
  font-size: 28rpx;
  line-height: 1.4;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20rpx);
  }
  60% {
    transform: translateY(-10rpx);
  }
}

/* Notifications */
.notification {
  position: fixed;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  padding: 24rpx 48rpx;
  border-radius: 16rpx;
  background: #3498db;
  color: white;
  font-size: 28rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.15);
  z-index: 1000;
  animation: fadeIn 0.3s;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translate(-50%, 40rpx); }
  to { opacity: 1; transform: translate(-50%, 0); }
}
</style>