<template>
  <view class="container">
    <!-- 头部导航 -->
    <park-header title="骆岗公园"></park-header>
    
    <!-- 轮播图 -->
    <park-swiper :images="bannerImages"></park-swiper>
    
    <!-- 公园简介 -->
    <view class="park-intro">
      <view class="section-title">公园简介</view>
      <view class="intro-content">
        <text>骆岗公园位于合肥市包河区，是一座以自然生态为主题的综合性公园。公园占地面积约1200亩，园内山水相依，林木葱郁，是市民休闲娱乐的理想去处。</text>
      </view>
    </view>
    
    <!-- 景点推荐 -->
    <view class="attractions">
      <view class="section-title">景点推荐</view>
      <view class="attraction-list">
        <attraction-card 
          v-for="(item, index) in attractions" 
          :key="index" 
          :title="item.title" 
          :image="item.image" 
          :description="item.description">
        </attraction-card>
      </view>
    </view>
    
    <!-- 游玩攻略 -->
    <view class="travel-guide">
      <view class="section-title">游玩攻略</view>
      <view class="guide-content">
        <view class="guide-item">
          <view class="guide-title">开放时间</view>
          <view class="guide-detail">全年 06:00-22:00</view>
        </view>
        <view class="guide-item">
          <view class="guide-title">门票信息</view>
          <view class="guide-detail">免费开放</view>
        </view>
        <view class="guide-item">
          <view class="guide-title">交通指南</view>
          <view class="guide-detail">
            <text>公交路线：乘坐118路、129路、158路至骆岗公园站下车</text>
            <text>自驾路线：导航至"合肥市包河区骆岗公园"</text>
          </view>
        </view>
        <view class="guide-item">
          <view class="guide-title">游玩建议</view>
          <view class="guide-detail">
            <text>1. 建议游玩时间：3-4小时</text>
            <text>2. 春季赏花、夏季避暑、秋季观叶、冬季赏雪，四季皆宜</text>
            <text>3. 公园内设有餐饮区，也可自带食物野餐</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部导航 -->
    <park-footer></park-footer>
  </view>
</template>

<script>
export default {
  data() {
    return {
      bannerImages: [
        '/static/images/banner1.jpg',
        '/static/images/banner2.jpg',
        '/static/images/banner3.jpg'
      ],
      attractions: [
        {
          title: '骆岗湖',
          image: '/static/images/attraction1.jpg',
          description: '骆岗湖是公园的核心景观，湖水清澈，环湖步道适合散步和慢跑，湖中小岛景色宜人。'
        },
        {
          title: '樱花园',
          image: '/static/images/attraction2.jpg',
          description: '春季樱花盛开时节，粉色的樱花如云似霞，吸引众多游客前来观赏拍照。'
        },
        {
          title: '生态林区',
          image: '/static/images/attraction3.jpg',
          description: '园内保留了大量原生态林木，空气清新，是亲近自然、森林浴的理想场所。'
        },
        {
          title: '儿童游乐区',
          image: '/static/images/attraction4.jpg',
          description: '专为儿童设计的游乐区，有各种游乐设施，是孩子们的欢乐天地。'
        }
      ]
    }
  },
  components: {
    'park-header': () => import('@/components/park-header/park-header'),
    'park-swiper': () => import('@/components/park-swiper/park-swiper'),
    'attraction-card': () => import('@/components/attraction-card/attraction-card'),
    'park-footer': () => import('@/components/park-footer/park-footer')
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '骆岗公园'
    })
  },
  methods: {
    
  }
}
</script>

<style>
.container {
  padding-bottom: 100rpx;
}

.park-intro, .attractions, .travel-guide {
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  border-left: 8rpx solid #4CAF50;
}

.intro-content {
  line-height: 1.6;
  color: #666;
  font-size: 28rpx;
  text-indent: 2em;
}

.attraction-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.guide-content {
  background-color: #f8f8f8;
  border-radius: 10rpx;
  padding: 20rpx;
}

.guide-item {
  margin-bottom: 20rpx;
}

.guide-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #4CAF50;
  margin-bottom: 10rpx;
}

.guide-detail {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.guide-detail text {
  display: block;
  margin-bottom: 10rpx;
}
</style>