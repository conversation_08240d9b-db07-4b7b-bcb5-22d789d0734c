<template>
  <view class="app-container">
    <park-header title="👤 个人中心" :show-back="true"></park-header>

    <view class="content">
      <!-- 用户信息 -->
      <view class="user-info">
        <view class="user-avatar">👤</view>
        <view class="user-name">游客用户</view>
        <view class="user-stats">
          <view class="stat-item">
            <view class="stat-number">{{ userStats.travelLogs }}</view>
            <view class="stat-label">游记</view>
          </view>
          <view class="stat-item">
            <view class="stat-number">{{ userStats.points }}</view>
            <view class="stat-label">积分</view>
          </view>
          <view class="stat-item">
            <view class="stat-number">{{ userStats.checkins }}</view>
            <view class="stat-label">打卡</view>
          </view>
        </view>
      </view>

      <!-- 我的游记 -->
      <view class="section">
        <view class="section-title">我的游记</view>
        <block v-if="userTravelLogs.length === 0">
          <view class="empty-state">
            <view class="empty-icon">📝</view>
            <view class="empty-text">还没有发布游记<br>快去记录你的美好时光吧！</view>
          </view>
        </block>
        <block v-else>
          <view 
            class="travel-log-item" 
            v-for="(log, index) in userTravelLogs" 
            :key="index"
            @click="viewTravelLog(log.title)">
            <view class="log-image" :style="{ backgroundImage: 'url(' + log.image + ')' }"></view>
            <view class="log-info">
              <view class="log-title">{{ log.title }}</view>
              <view class="log-date">发布于 {{ log.date }}</view>
            </view>
          </view>
        </block>
      </view>

      <!-- 我的订单 -->
      <view class="section">
        <view class="section-title">我的订单</view>
        <block v-if="userOrders.length === 0">
          <view class="empty-state">
            <view class="empty-icon">📋</view>
            <view class="empty-text">暂无订单记录</view>
          </view>
        </block>
        <block v-else>
          <view 
            class="order-item" 
            v-for="(order, index) in userOrders" 
            :key="index"
            @click="handleOrderClick(order.type, order.canShowQR)">
            <view class="order-header">
              <view class="order-type">{{ order.type }}</view>
              <view :class="['order-status', getStatusClass(order.status)]">
                {{ getStatusText(order.status) }}
              </view>
            </view>
            <view class="order-time">{{ order.time }}</view>
          </view>
        </block>
      </view>
    </view>

    <!-- 二维码弹窗 -->
    <uni-popup ref="qrPopup" type="center">
      <view class="qr-modal-content">
        <view class="qr-title">{{ qrInfo.title }}</view>
        <view class="qr-desc">{{ qrInfo.desc }}</view>
        <view class="qr-code">📱</view>
        <button class="close-btn" @click="closeQRModal">关闭</button>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userStats: {
        travelLogs: 3,
        points: 1250,
        checkins: 8
      },
      userTravelLogs: [
        {
          title: "骆岗公园春日游记",
          image: "https://images.unsplash.com/photo-1522383225653-ed111181a951?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
          date: "2024-03-18"
        },
        {
          title: "带娃逛骆岗",
          image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
          date: "2024-03-16"
        },
        {
          title: "骆岗夜景摄影",
          image: "https://images.unsplash.com/photo-1519501025264-65ba15a82390?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
          date: "2024-03-15"
        }
      ],
      userOrders: [
        {
          type: "无人巴士",
          status: "paid",
          time: "2024-04-15 09:30",
          canShowQR: true
        },
        {
          type: "接驳车",
          status: "used",
          time: "2024-04-10 14:20",
          canShowQR: false
        },
        {
          type: "无人巴士",
          status: "paid",
          time: "2024-04-08 16:45",
          canShowQR: true
        }
      ],
      qrInfo: {
        title: "",
        desc: ""
      }
    };
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '个人中心'
    });
  },
  methods: {
    viewTravelLog(title) {
      this.showNotification(`查看游记: ${title}`, 'success');
    },
    handleOrderClick(type, canShowQR) {
      if (type === '无人巴士' && canShowQR) {
        this.showQRCode(type);
      } else {
        this.showNotification(`查看${type}订单详情`, 'success');
      }
    },
    showQRCode(type) {
      this.qrInfo.title = `${type}乘车码`;
      this.qrInfo.desc = '请向司机出示此二维码';
      this.$refs.qrPopup.open();
    },
    closeQRModal() {
      this.$refs.qrPopup.close();
    },
    showNotification(message, type = 'info') {
      uni.showToast({
        title: message,
        icon: type === 'success' ? 'success' : 'none',
        duration: 2000
      });
    },
    getStatusText(status) {
      const statusText = {
        'paid': '已支付',
        'used': '已使用'
      };
      return statusText[status] || '';
    },
    getStatusClass(status) {
      const statusClass = {
        'paid': 'status-paid',
        'used': 'status-used'
      };
      return statusClass[status] || '';
    }
  },
  components: {
    'park-header': () => import('@/components/park-header/park-header')
  }
}
</script>

<style>
.app-container {
  max-width: 750rpx;
  margin: 0 auto;
  background: #f8fffe;
  min-height: 100vh;
  position: relative;
}

.content {
  padding: 32rpx;
}

.section {
  margin-bottom: 48rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  color: #2c3e50;
}

/* 用户信息 */
.user-info {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  text-align: center;
  margin-bottom: 40rpx;
}

.user-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #3498db, #2ecc71);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 64rpx;
  color: white;
  margin: 0 auto 24rpx;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.user-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 32rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 40rpx;
  font-weight: bold;
  color: #3498db;
}

.stat-label {
  font-size: 24rpx;
  color: #7f8c8d;
}

/* 游记 */
.travel-log-item {
  background: white;
  border-radius: 24rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  display: flex;
  gap: 24rpx;
  transition: all 0.3s;
}

.travel-log-item:active {
  transform: translateY(-4rpx);
}

.log-image {
  width: 160rpx;
  height: 120rpx;
  background-size: cover;
  background-position: center;
  border-radius: 16rpx;
  flex-shrink: 0;
}

.log-info {
  flex: 1;
}

.log-title {
  font-weight: bold;
  margin-bottom: 8rpx;
  font-size: 28rpx;
}

.log-date {
  font-size: 24rpx;
  color: #7f8c8d;
}

/* 订单 */
.order-item {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  transition: all 0.3s;
}

.order-item:active {
  transform: translateY(-4rpx);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.order-type {
  font-weight: bold;
  color: #2c3e50;
  font-size: 28rpx;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 20rpx;
  color: white;
}

.status-paid {
  background: #27ae60;
}

.status-used {
  background: #95a5a6;
}

.order-time {
  font-size: 24rpx;
  color: #7f8c8d;
}

/* 二维码弹窗 */
.qr-modal-content {
  background: white;
  border-radius: 24rpx;
  padding: 48rpx;
  text-align: center;
  width: 560rpx;
}

.qr-code {
  width: 400rpx;
  height: 400rpx;
  background: #f8f9fa;
  border: 4rpx dashed #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 32rpx auto;
  border-radius: 16rpx;
  font-size: 96rpx;
}

.qr-title {
  font-weight: bold;
  margin-bottom: 16rpx;
  font-size: 32rpx;
}

.qr-desc {
  font-size: 24rpx;
  color: #7f8c8d;
  margin-bottom: 32rpx;
}

.close-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 16rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #7f8c8d;
}

.empty-icon {
  font-size: 96rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 28rpx;
  line-height: 1.5;
}
</style>