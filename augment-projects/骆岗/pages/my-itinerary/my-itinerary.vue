<template>
  <view class="app-container">
    <park-header title="📅 我的行程" :show-back="true"></park-header>

    <view class="content">
      <view class="section">
        <view class="section-title">我的行程安排</view>
        <view id="itineraryList">
          <view 
            class="itinerary-card" 
            v-for="(itinerary, index) in itineraries" 
            :key="itinerary.id"
            @click="toggleDetails(itinerary.id)">
            <view class="itinerary-header">
              <view class="itinerary-title">{{ itinerary.title }}</view>
              <view :class="['itinerary-status', getStatusClass(itinerary.status)]">
                {{ getStatusText(itinerary.status) }}
              </view>
            </view>
            <view class="itinerary-time">{{ formatDate(itinerary.startTime) }} - {{ formatTime(itinerary.endTime) }}</view>
            <view class="itinerary-route">
              <block v-for="(point, pointIndex) in itinerary.points" :key="pointIndex">
                <text class="route-point">{{ point.name }}</text>
                <text class="route-arrow" v-if="pointIndex < itinerary.points.length - 1">→</text>
              </block>
            </view>
            <view class="itinerary-cost">预计费用: {{ itinerary.cost }}</view>
            <view class="itinerary-details" v-show="activeItinerary === itinerary.id">
              <view class="point-list">
                <view 
                  class="point-item" 
                  v-for="(point, pointIndex) in itinerary.points" 
                  :key="pointIndex"
                  v-show="!point.deleted">
                  <view class="point-image" :style="{ backgroundImage: 'url(' + point.image + ')' }"></view>
                  <view class="point-info">
                    <view class="point-name">{{ point.name }}</view>
                    <view class="point-tags">
                      <text 
                        class="point-tag" 
                        v-for="(tag, tagIndex) in point.tags" 
                        :key="tagIndex">{{ tag }}</text>
                    </view>
                    <view class="point-time">{{ point.time }}</view>
                    <view class="point-cost" v-if="point.cost">费用: {{ point.cost }}</view>
                  </view>
                  <button 
                    class="delete-btn" 
                    @click.stop="deletePoint(itinerary.id, pointIndex, point.name)">×</button>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      activeItinerary: null,
      itineraries: [
        {
          id: 1,
          title: "骆岗公园一日游",
          status: "upcoming",
          startTime: "2024-04-15 09:00",
          endTime: "2024-04-15 17:00",
          cost: "¥35",
          points: [
            {
              name: "信标台",
              image: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
              tags: ["地标", "观景"],
              time: "09:30-10:30",
              cost: "",
              deleted: false
            },
            {
              name: "航空科普馆",
              image: "https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
              tags: ["科普", "体验"],
              time: "11:00-12:30",
              cost: "¥20",
              deleted: false
            },
            {
              name: "骆岗咖啡厅",
              image: "https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
              tags: ["餐饮", "休息"],
              time: "12:30-13:30",
              cost: "¥15",
              deleted: false
            },
            {
              name: "梦想大草坪",
              image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
              tags: ["休闲", "野餐"],
              time: "14:00-16:00",
              cost: "",
              deleted: false
            }
          ]
        },
        {
          id: 2,
          title: "周末亲子游",
          status: "ongoing",
          startTime: "2024-04-10 10:00",
          endTime: "2024-04-10 16:00",
          cost: "¥50",
          points: [
            {
              name: "航空科普馆",
              image: "https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
              tags: ["科普", "体验"],
              time: "10:00-11:30",
              cost: "¥20",
              deleted: false
            },
            {
              name: "儿童游乐区",
              image: "https://images.unsplash.com/photo-1519331379826-f10be5486c6f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
              tags: ["游乐", "亲子"],
              time: "11:30-13:00",
              cost: "¥30",
              deleted: false
            },
            {
              name: "梦想大草坪",
              image: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
              tags: ["休闲", "野餐"],
              time: "13:30-15:30",
              cost: "",
              deleted: false
            }
          ]
        },
        {
          id: 3,
          title: "摄影打卡之旅",
          status: "completed",
          startTime: "2024-04-05 08:00",
          endTime: "2024-04-05 12:00",
          cost: "¥0",
          points: [
            {
              name: "信标台",
              image: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
              tags: ["地标", "观景"],
              time: "08:00-09:00",
              cost: "",
              deleted: false
            },
            {
              name: "航站楼",
              image: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
              tags: ["历史", "建筑"],
              time: "09:30-10:30",
              cost: "",
              deleted: false
            },
            {
              name: "展园花海",
              image: "https://images.unsplash.com/photo-1522383225653-ed111181a951?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
              tags: ["花卉", "摄影"],
              time: "11:00-12:00",
              cost: "",
              deleted: false
            }
          ]
        }
      ]
    };
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '我的行程'
    });
  },
  methods: {
    toggleDetails(id) {
      if (this.activeItinerary === id) {
        this.activeItinerary = null;
      } else {
        this.activeItinerary = id;
      }
    },
    deletePoint(itineraryId, pointIndex, pointName) {
      // 在实际应用中，这里应该调用API来更新数据
      // 这里我们只是在前端标记为已删除
      this.itineraries.find(i => i.id === itineraryId).points[pointIndex].deleted = true;
      this.showNotification(`已从行程中删除: ${pointName}`, 'success');
    },
    formatDate(dateString) {
      const date = new Date(dateString);
      return `${date.getMonth() + 1}月${date.getDate()}日 ${this.formatTime(dateString)}`;
    },
    formatTime(dateString) {
      const date = new Date(dateString);
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    },
    showNotification(message, type = 'info') {
      uni.showToast({
        title: message,
        icon: type === 'success' ? 'success' : 'none',
        duration: 2000
      });
    },
    getStatusText(status) {
      const statusText = {
        'upcoming': '即将开始',
        'ongoing': '进行中',
        'completed': '已完成'
      };
      return statusText[status] || '';
    },
    getStatusClass(status) {
      const statusClass = {
        'upcoming': 'status-upcoming',
        'ongoing': 'status-ongoing',
        'completed': 'status-completed'
      };
      return statusClass[status] || '';
    }
  },
  components: {
    'park-header': () => import('@/components/park-header/park-header')
  }
}
</script>

<style>
.app-container {
  max-width: 750rpx;
  margin: 0 auto;
  background: #f8fffe;
  min-height: 100vh;
  position: relative;
}

.content {
  padding: 32rpx;
}

.section {
  margin-bottom: 48rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  color: #2c3e50;
}

/* 行程卡片 */
.itinerary-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  transition: all 0.3s;
}

.itinerary-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.15);
}

.itinerary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.itinerary-title {
  font-weight: bold;
  color: #2c3e50;
  font-size: 32rpx;
}

.itinerary-status {
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 20rpx;
  color: white;
}

.status-upcoming {
  background: #3498db;
}

.status-ongoing {
  background: #27ae60;
}

.status-completed {
  background: #95a5a6;
}

.itinerary-time {
  font-size: 24rpx;
  color: #7f8c8d;
  margin-bottom: 24rpx;
}

.itinerary-route {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  overflow-x: auto;
  padding-bottom: 8rpx;
  white-space: nowrap;
}

.route-point {
  font-size: 24rpx;
  color: #3498db;
}

.route-arrow {
  margin: 0 8rpx;
  color: #95a5a6;
  font-size: 20rpx;
}

.itinerary-cost {
  font-size: 24rpx;
  color: #e74c3c;
  font-weight: bold;
  text-align: right;
}

/* 展开的行程详情 */
.itinerary-details {
  margin-top: 24rpx;
  border-top: 2rpx solid #ecf0f1;
  padding-top: 24rpx;
}

.point-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.point-item {
  display: flex;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  position: relative;
}

.point-image {
  width: 120rpx;
  height: 120rpx;
  background-size: cover;
  background-position: center;
  border-radius: 16rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.point-info {
  flex: 1;
}

.point-name {
  font-weight: bold;
  margin-bottom: 8rpx;
  font-size: 28rpx;
}

.point-tags {
  display: flex;
  gap: 8rpx;
  margin-bottom: 8rpx;
  flex-wrap: wrap;
}

.point-tag {
  background: #3498db;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 16rpx;
}

.point-time {
  font-size: 22rpx;
  color: #7f8c8d;
}

.point-cost {
  font-size: 22rpx;
  color: #e74c3c;
  margin-top: 8rpx;
}

.delete-btn {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  border: none;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  line-height: 1;
  padding: 0;
}

.delete-btn:active {
  background: rgba(231, 76, 60, 0.2);
}
</style>