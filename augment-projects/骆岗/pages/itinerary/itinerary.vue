<template>
  <view class="container">
    <!-- 头部导航 -->
    <park-header title="我的行程"></park-header>
    
    <!-- 新建行程按钮 -->
    <view class="section">
      <button class="btn" @click="createNewItinerary">➕ 新建行程规划</button>
    </view>
    
    <!-- 我的行程安排 -->
    <view class="section">
      <view class="section-title">📅 我的行程安排</view>
      <view v-if="itineraries.length === 0" class="empty-state">
        <view class="empty-icon">📅</view>
        <view class="empty-text">暂无行程安排<br>快来规划你的游玩路线吧！</view>
      </view>
      <view v-else>
        <view 
          class="itinerary-card" 
          v-for="(item, index) in itineraries" 
          :key="index"
          @click="toggleDetails(index)">
          <view class="itinerary-header">
            <view class="itinerary-title">{{item.title}}</view>
            <view :class="['itinerary-status', 'status-'+item.status]">{{getStatusText(item.status)}}</view>
          </view>
          <view class="itinerary-time">{{formatDate(item.startTime)}} - {{formatTime(item.endTime)}}</view>
          <view class="itinerary-route">
            <text v-for="(point, pIndex) in item.points" :key="pIndex">
              <text class="route-point">{{point.name}}</text>
              <text v-if="pIndex < item.points.length - 1" class="route-arrow">→</text>
            </text>
          </view>
          <view class="itinerary-cost">预计费用: {{item.cost}}</view>
          
          <!-- 详情展开区域 -->
          <view class="itinerary-details" v-show="item.showDetails">
            <view class="point-list">
              <view class="point-item" v-for="(point, pIndex) in item.points" :key="pIndex">
                <image class="point-image" :src="point.image" mode="aspectFill"></image>
                <view class="point-info">
                  <view class="point-name">{{point.name}}</view>
                  <view class="point-tags">
                    <text v-for="(tag, tIndex) in point.tags" :key="tIndex" class="point-tag">{{tag}}</text>
                  </view>
                  <view class="point-time">{{point.time}}</view>
                  <view v-if="point.cost" class="point-cost">费用: {{point.cost}}</view>
                </view>
                <view class="delete-btn" @click.stop="deletePoint(index, pIndex)">×</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部导航 -->
    <park-footer></park-footer>
  </view>
</template>

<script>
export default {
  data() {
    return {
      itineraries: [
        {
          title: "骆岗公园一日游",
          status: "upcoming",
          startTime: "2024-04-15 09:00",
          endTime: "2024-04-15 17:00",
          cost: "¥35",
          showDetails: false,
          points: [
            {
              name: "信标台",
              image: "/static/images/attraction1.jpg",
              tags: ["地标", "观景"],
              time: "09:30-10:30",
              cost: ""
            },
            {
              name: "航空科普馆",
              image: "/static/images/attraction3.jpg",
              tags: ["科普", "体验"],
              time: "11:00-12:30",
              cost: "¥20"
            },
            {
              name: "骆岗咖啡厅",
              image: "/static/images/food1.jpg",
              tags: ["餐饮", "休息"],
              time: "12:30-13:30",
              cost: "¥15"
            },
            {
              name: "梦想大草坪",
              image: "/static/images/attraction2.jpg",
              tags: ["休闲", "野餐"],
              time: "14:00-16:00",
              cost: ""
            }
          ]
        },
        {
          title: "周末亲子游",
          status: "ongoing",
          startTime: "2024-04-10 10:00",
          endTime: "2024-04-10 16:00",
          cost: "¥50",
          showDetails: false,
          points: [
            {
              name: "航空科普馆",
              image: "/static/images/attraction3.jpg",
              tags: ["科普", "体验"],
              time: "10:00-11:30",
              cost: "¥20"
            },
            {
              name: "儿童游乐区",
              image: "/static/images/attraction4.jpg",
              tags: ["游乐", "亲子"],
              time: "11:30-13:00",
              cost: "¥30"
            },
            {
              name: "梦想大草坪",
              image: "/static/images/attraction2.jpg",
              tags: ["休闲", "野餐"],
              time: "13:30-15:30",
              cost: ""
            }
          ]
        }
      ]
    }
  },
  components: {
    'park-header': () => import('@/components/park-header/park-header'),
    'park-footer': () => import('@/components/park-footer/park-footer')
  },
  methods: {
    createNewItinerary() {
      uni.navigateTo({
        url: '/pages/route-planning/route-planning'
      });
    },
    toggleDetails(index) {
      // 关闭其他所有详情
      this.itineraries.forEach((item, i) => {
        if (i !== index) {
          this.$set(this.itineraries[i], 'showDetails', false);
        }
      });
      
      // 切换当前详情
      this.$set(this.itineraries[index], 'showDetails', !this.itineraries[index].showDetails);
    },
    deletePoint(itineraryIndex, pointIndex) {
      uni.showModal({
        title: '确认删除',
        content: `确定要从行程中删除 ${this.itineraries[itineraryIndex].points[pointIndex].name} 吗？`,
        success: (res) => {
          if (res.confirm) {
            this.itineraries[itineraryIndex].points.splice(pointIndex, 1);
            uni.showToast({
              title: '已删除',
              icon: 'success'
            });
          }
        }
      });
    },
    getStatusText(status) {
      const statusMap = {
        'upcoming': '即将开始',
        'ongoing': '进行中',
        'completed': '已完成'
      };
      return statusMap[status] || status;
    },
    formatDate(dateString) {
      const date = new Date(dateString);
      return `${date.getMonth() + 1}月${date.getDate()}日 ${this.formatTime(dateString)}`;
    },
    formatTime(dateString) {
      const date = new Date(dateString);
      return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    }
  }
}
</script>

<style>
.container {
  padding-bottom: 100rpx;
}

.section {
  padding: 20rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  border-left: 8rpx solid #4CAF50;
}

.btn {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 24rpx 0;
  border-radius: 16rpx;
  font-size: 28rpx;
  width: 100%;
}

.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #7f8c8d;
}

.empty-icon {
  font-size: 96rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 28rpx;
  line-height: 1.5;
}

.itinerary-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.itinerary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.itinerary-title {
  font-weight: bold;
  color: #2c3e50;
  font-size: 32rpx;
}

.itinerary-status {
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 20rpx;
  color: white;
}

.status-upcoming {
  background: #3498db;
}

.status-ongoing {
  background: #27ae60;
}

.status-completed {
  background: #95a5a6;
}

.itinerary-time {
  font-size: 24rpx;
  color: #7f8c8d;
  margin-bottom: 24rpx;
}

.itinerary-route {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  overflow-x: auto;
  padding-bottom: 8rpx;
  white-space: nowrap;
}

.route-point {
  font-size: 24rpx;
  color: #3498db;
}

.route-arrow {
  margin: 0 8rpx;
  color: #95a5a6;
  font-size: 20rpx;
}

.itinerary-cost {
  font-size: 24rpx;
  color: #e74c3c;
  font-weight: bold;
  text-align: right;
}

.itinerary-details {
  margin-top: 24rpx;
  border-top: 1rpx solid #ecf0f1;
  padding-top: 24rpx;
}

.point-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.point-item {
  display: flex;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  position: relative;
}

.point-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.point-info {
  flex: 1;
}

.point-name {
  font-weight: bold;
  margin-bottom: 8rpx;
  font-size: 28rpx;
}

.point-tags {
  display: flex;
  gap: 8rpx;
  margin-bottom: 8rpx;
  flex-wrap: wrap;
}

.point-tag {
  background: #3498db;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 16rpx;
}

.point-time {
  font-size: 22rpx;
  color: #7f8c8d;
}

.point-cost {
  font-size: 22rpx;
  color: #e74c3c;
  margin-top: 8rpx;
}

.delete-btn {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}
</style>