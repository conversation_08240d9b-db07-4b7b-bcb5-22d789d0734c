<template>
  <view class="container">
    <!-- 头部导航 -->
    <park-header title="我的"></park-header>
    
    <!-- 用户信息 -->
    <view class="user-header">
      <view class="user-avatar">👤</view>
      <view class="user-name">游客用户</view>
      <view class="user-stats-simple">
        <view class="stat-simple">
          <view class="stat-number">3</view>
          <view class="stat-label">游记</view>
        </view>
        <view class="stat-simple">
          <view class="stat-number">8</view>
          <view class="stat-label">打卡</view>
        </view>
        <view class="stat-simple">
          <view class="stat-number">1250</view>
          <view class="stat-label">积分</view>
        </view>
      </view>
    </view>
    
    <!-- 我的游记 -->
    <view class="section">
      <view class="section-title">我的游记</view>
      <view v-if="travelLogs.length === 0" class="empty-state">
        <view class="empty-icon">📝</view>
        <view class="empty-text">还没有发布游记<br>快去记录你的美好时光吧！</view>
      </view>
      <view v-else>
        <view 
          class="travel-log-item" 
          v-for="(log, index) in travelLogs" 
          :key="index"
          @click="viewTravelLog(log)">
          <image class="log-image" :src="log.image" mode="aspectFill"></image>
          <view class="log-info">
            <view class="log-title">{{log.title}}</view>
            <view class="log-date">发布于 {{log.date}}</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 我的订单 -->
    <view class="section">
      <view class="section-title">我的订单</view>
      <view v-if="orders.length === 0" class="empty-state">
        <view class="empty-icon">📋</view>
        <view class="empty-text">暂无订单记录</view>
      </view>
      <view v-else>
        <view 
          class="order-item" 
          v-for="(order, index) in orders" 
          :key="index"
          @click="handleOrderClick(order)">
          <view class="order-header">
            <view class="order-type">{{order.type}}</view>
            <view :class="['order-status', 'status-'+order.status]">{{getStatusText(order.status)}}</view>
          </view>
          <view class="order-time">{{order.time}}</view>
        </view>
      </view>
    </view>
    
    <!-- 设置 -->
    <view class="section">
      <view class="section-title">设置</view>
      <view class="settings-list">
        <view class="setting-item" @click="showSetting('个人信息')">
          <view class="setting-icon">👤</view>
          <view class="setting-text">个人信息</view>
          <view class="setting-arrow">></view>
        </view>
        <view class="setting-item" @click="showSetting('通知设置')">
          <view class="setting-icon">🔔</view>
          <view class="setting-text">通知设置</view>
          <view class="setting-arrow">></view>
        </view>
        <view class="setting-item" @click="showSetting('隐私设置')">
          <view class="setting-icon">🔒</view>
          <view class="setting-text">隐私设置</view>
          <view class="setting-arrow">></view>
        </view>
        <view class="setting-item" @click="showSetting('关于我们')">
          <view class="setting-icon">ℹ️</view>
          <view class="setting-text">关于我们</view>
          <view class="setting-arrow">></view>
        </view>
      </view>
    </view>
    
    <!-- 二维码弹窗 -->
    <uni-popup ref="qrPopup" type="center">
      <view class="qr-modal-content">
        <view class="qr-title">{{qrTitle}}</view>
        <view class="qr-desc">{{qrDesc}}</view>
        <view class="qr-code">📱</view>
        <button class="close-btn" @click="closeQRModal">关闭</button>
      </view>
    </uni-popup>
    
    <!-- 底部导航 -->
    <park-footer></park-footer>
  </view>
</template>

<script>
export default {
  data() {
    return {
      qrTitle: '',
      qrDesc: '',
      travelLogs: [
        {
          title: "骆岗公园春日游记",
          image: "/static/images/log1.jpg",
          date: "2024-03-18"
        },
        {
          title: "带娃逛骆岗",
          image: "/static/images/log2.jpg",
          date: "2024-03-16"
        },
        {
          title: "骆岗夜景摄影",
          image: "/static/images/log3.jpg",
          date: "2024-03-15"
        }
      ],
      orders: [
        {
          type: "无人巴士",
          status: "paid",
          time: "2024-04-15 09:30",
          canShowQR: true
        },
        {
          type: "接驳车",
          status: "used",
          time: "2024-04-10 14:20",
          canShowQR: false
        },
        {
          type: "无人巴士",
          status: "paid",
          time: "2024-04-08 16:45",
          canShowQR: true
        }
      ]
    }
  },
  components: {
    'park-header': () => import('@/components/park-header/park-header'),
    'park-footer': () => import('@/components/park-footer/park-footer')
  },
  methods: {
    viewTravelLog(log) {
      uni.showToast({
        title: `查看游记: ${log.title}`,
        icon: 'none'
      });
    },
    handleOrderClick(order) {
      if (order.type === '无人巴士' && order.canShowQR) {
        this.showQRCode(order.type);
      } else {
        uni.showToast({
          title: `查看${order.type}订单详情`,
          icon: 'none'
        });
      }
    },
    showQRCode(type) {
      this.qrTitle = `${type}乘车码`;
      this.qrDesc = '请向司机出示此二维码';
      this.$refs.qrPopup.open();
    },
    closeQRModal() {
      this.$refs.qrPopup.close();
    },
    showSetting(setting) {
      uni.showToast({
        title: setting,
        icon: 'none'
      });
    },
    getStatusText(status) {
      const statusMap = {
        'paid': '已支付',
        'used': '已使用'
      };
      return statusMap[status] || status;
    }
  }
}
</script>

<style>
.container {
  padding-bottom: 100rpx;
}

.user-header {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  text-align: center;
  margin: 20rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #3498db, #2ecc71);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: white;
  margin: 0 auto 24rpx;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 32rpx;
  color: #2c3e50;
}

.user-stats-simple {
  display: flex;
  justify-content: space-around;
}

.stat-simple {
  text-align: center;
}

.stat-simple .stat-number {
  font-size: 40rpx;
  font-weight: bold;
  color: #3498db;
  margin-bottom: 8rpx;
}

.stat-simple .stat-label {
  font-size: 24rpx;
  color: #7f8c8d;
}

.section {
  padding: 20rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  border-left: 8rpx solid #4CAF50;
}

.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #7f8c8d;
}

.empty-icon {
  font-size: 96rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 28rpx;
  line-height: 1.5;
}

.travel-log-item {
  background: white;
  border-radius: 24rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  display: flex;
  gap: 24rpx;
}

.log-image {
  width: 160rpx;
  height: 120rpx;
  border-radius: 16rpx;
  flex-shrink: 0;
}

.log-info {
  flex: 1;
}

.log-title {
  font-weight: bold;
  margin-bottom: 8rpx;
  font-size: 28rpx;
}

.log-date {
  font-size: 24rpx;
  color: #7f8c8d;
}

.order-item {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.order-type {
  font-weight: bold;
  color: #2c3e50;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 20rpx;
  color: white;
}

.status-paid {
  background: #27ae60;
}

.status-used {
  background: #95a5a6;
}

.order-time {
  font-size: 24rpx;
  color: #7f8c8d;
}

.settings-list {
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.setting-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f8f9fa;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
  width: 48rpx;
  text-align: center;
}

.setting-text {
  flex: 1;
  font-size: 28rpx;
  color: #2c3e50;
}

.setting-arrow {
  color: #95a5a6;
  font-size: 28rpx;
}

.qr-modal-content {
  background: white;
  border-radius: 24rpx;
  padding: 48rpx;
  text-align: center;
  width: 80%;
}

.qr-code {
  width: 400rpx;
  height: 400rpx;
  background: #f8f9fa;
  border: 4rpx dashed #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 32rpx auto;
  border-radius: 16rpx;
  font-size: 96rpx;
}

.qr-title {
  font-weight: bold;
  margin-bottom: 16rpx;
  font-size: 32rpx;
}

.qr-desc {
  font-size: 24rpx;
  color: #7f8c8d;
  margin-bottom: 32rpx;
}

.close-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 16rpx 32rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
}
</style>