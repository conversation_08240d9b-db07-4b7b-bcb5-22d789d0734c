<template>
  <view class="container">
    <!-- 头部导航 -->
    <park-header title="探索骆岗"></park-header>
    
    <!-- 标签导航 -->
    <view class="tab-nav">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index" 
        class="tab-nav-item" 
        :class="{ active: currentTab === index }"
        @click="switchTab(index)">
        {{tab}}
      </view>
    </view>
    
    <!-- 筛选选项 -->
    <view class="filter-options">
      <view 
        v-for="(filter, index) in filters" 
        :key="index" 
        class="filter-btn" 
        :class="{ active: currentFilter === index }"
        @click="switchFilter(index)">
        {{filter}}
      </view>
    </view>
    
    <!-- 景点列表 -->
    <view class="list-container">
      <view class="list-item" v-for="(item, index) in filteredItems" :key="index" @click="showDetail(item)">
        <image class="list-item-image" :src="item.image" mode="aspectFill"></image>
        <view class="list-item-content">
          <view class="list-item-header">
            <view class="list-item-title">{{item.name}}</view>
            <view class="list-item-meta">{{item.type || item.date || ''}}</view>
          </view>
          <view class="list-item-desc">{{item.desc}}</view>
          <view class="list-item-stats" v-if="item.views">
            <text>👁️ {{item.views}}</text>
            <text>❤️ {{item.likes}}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部导航 -->
    <park-footer></park-footer>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentTab: 0,
      currentFilter: 0,
      tabs: ['景点', '活动', '美食'],
      filters: [],
      attractions: [
        {
          name: "信标台",
          desc: "骆岗公园标志性建筑，高约50米",
          type: "景点",
          image: "/static/images/attraction1.jpg"
        },
        {
          name: "梦想大草坪",
          desc: "开阔草坪，适合野餐和休闲",
          type: "景点",
          image: "/static/images/attraction2.jpg"
        },
        {
          name: "航空科普馆",
          desc: "航空知识科普教育基地",
          type: "体验",
          image: "/static/images/attraction3.jpg"
        },
        {
          name: "城市馆",
          desc: "合肥城市发展展示",
          type: "体验",
          image: "/static/images/attraction4.jpg"
        }
      ],
      activities: [
        {
          name: "春季花展",
          desc: "樱花、桃花等春季花卉展示",
          date: "3月-4月",
          status: "进行中",
          image: "/static/images/activity1.jpg"
        },
        {
          name: "航空科普展",
          desc: "航空历史展览和飞行模拟体验",
          date: "长期展出",
          status: "进行中",
          image: "/static/images/activity2.jpg"
        },
        {
          name: "夜间灯光秀",
          desc: "信标台夜间灯光表演",
          date: "每日19:30",
          status: "进行中",
          image: "/static/images/activity3.jpg"
        }
      ],
      foods: [
        {
          name: "骆岗咖啡厅",
          desc: "位于航站楼内，提供精品咖啡",
          type: "咖啡",
          image: "/static/images/food1.jpg"
        },
        {
          name: "草坪野餐区",
          desc: "可自带食物在指定区域野餐",
          type: "野餐",
          image: "/static/images/food2.jpg"
        },
        {
          name: "园区便利店",
          desc: "提供饮料、零食等便民商品",
          type: "零售",
          image: "/static/images/food3.jpg"
        }
      ]
    }
  },
  computed: {
    filteredItems() {
      let items = [];
      
      switch(this.currentTab) {
        case 0: // 景点
          items = this.attractions;
          break;
        case 1: // 活动
          items = this.activities;
          break;
        case 2: // 美食
          items = this.foods;
          break;
      }
      
      if (this.currentFilter === 0) {
        return items;
      } else {
        const filterValue = this.filters[this.currentFilter];
        return items.filter(item => {
          if (this.currentTab === 0) return item.type === filterValue;
          if (this.currentTab === 1) return item.status === filterValue;
          if (this.currentTab === 2) return item.type === filterValue;
          return true;
        });
      }
    }
  },
  components: {
    'park-header': () => import('@/components/park-header/park-header'),
    'park-footer': () => import('@/components/park-footer/park-footer')
  },
  onLoad() {
    this.updateFilters();
  },
  methods: {
    switchTab(index) {
      this.currentTab = index;
      this.currentFilter = 0;
      this.updateFilters();
    },
    switchFilter(index) {
      this.currentFilter = index;
    },
    updateFilters() {
      switch(this.currentTab) {
        case 0: // 景点
          this.filters = ['全部类型', '景点', '体验'];
          break;
        case 1: // 活动
          this.filters = ['全部活动', '进行中', '即将开始'];
          break;
        case 2: // 美食
          this.filters = ['全部类型', '咖啡', '野餐', '零售'];
          break;
      }
    },
    showDetail(item) {
      uni.showToast({
        title: `查看${item.name}详情`,
        icon: 'none'
      });
    }
  }
}
</script>

<style>
.container {
  padding-bottom: 100rpx;
}

.tab-nav {
  display: flex;
  background: white;
  border-radius: 24rpx;
  margin: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  position: relative;
}

.tab-nav-item {
  flex: 1;
  padding: 24rpx 32rpx;
  text-align: center;
  background: none;
  border: none;
  transition: all 0.3s;
  font-size: 28rpx;
  font-weight: 500;
  color: #7f8c8d;
  position: relative;
  z-index: 2;
}

.tab-nav-item.active {
  background: linear-gradient(135deg, #3498db, #2ecc71);
  color: white;
  border-radius: 16rpx;
  margin: 8rpx;
  box-shadow: 0 4rpx 16rpx rgba(52, 152, 219, 0.3);
}

.filter-options {
  display: flex;
  gap: 16rpx;
  margin: 20rpx;
  flex-wrap: wrap;
}

.filter-btn {
  background: white;
  border: 1rpx solid #ddd;
  padding: 12rpx 24rpx;
  border-radius: 32rpx;
  font-size: 24rpx;
  transition: all 0.3s;
  color: #7f8c8d;
}

.filter-btn.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.list-container {
  padding: 0 20rpx;
}

.list-item {
  background: white;
  border-radius: 24rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
}

.list-item-image {
  width: 160rpx;
  height: 120rpx;
  border-radius: 16rpx;
  flex-shrink: 0;
}

.list-item-content {
  flex: 1;
  min-width: 0;
}

.list-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.list-item-title {
  font-weight: bold;
  color: #2c3e50;
  font-size: 28rpx;
}

.list-item-meta {
  font-size: 22rpx;
  color: #7f8c8d;
  flex-shrink: 0;
}

.list-item-desc {
  font-size: 24rpx;
  color: #7f8c8d;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.list-item-stats {
  display: flex;
  gap: 24rpx;
  font-size: 22rpx;
  color: #95a5a6;
}
</style>