<template>
  <view class="app-container">
    <park-header title="🎧 伴游导览" :show-back="true"></park-header>

    <view class="content">
      <!-- 手绘地图 -->
      <view class="map-container">
        <view class="map-overlay">
          <view class="map-content">
            <view class="current-location">📍 当前位置：信标台附近</view>
            <view class="location-desc">手绘地图导览</view>
          </view>
        </view>
        <!-- 景点标记 -->
        <view 
          class="attraction-marker marker-1" 
          :class="{ active: selectedAttraction === '信标台' }"
          @click="selectAttraction('信标台')" 
          title="信标台">1</view>
        <view 
          class="attraction-marker marker-2" 
          :class="{ active: selectedAttraction === '航站楼' }"
          @click="selectAttraction('航站楼')" 
          title="航站楼">2</view>
        <view 
          class="attraction-marker marker-3" 
          :class="{ active: selectedAttraction === '梦想大草坪' }"
          @click="selectAttraction('梦想大草坪')" 
          title="梦想大草坪">3</view>
        <view 
          class="attraction-marker marker-4" 
          :class="{ active: selectedAttraction === '科普馆' }"
          @click="selectAttraction('科普馆')" 
          title="科普馆">4</view>
      </view>

      <!-- 附近景点 -->
      <view class="section">
        <view class="section-title">📍 附近景点</view>
        <scroll-view class="attraction-list" scroll-x="true" show-scrollbar="false">
          <view class="attraction-item" @click="focusOnAttraction('信标台')">
            <view class="attraction-image" style="background-image: url('https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80')"></view>
            <view class="attraction-name">信标台</view>
            <view class="attraction-tags">
              <text class="attraction-tag">地标</text>
              <text class="attraction-tag">观景</text>
            </view>
            <view class="attraction-distance">距离: 50米</view>
          </view>
          <view class="attraction-item" @click="focusOnAttraction('航站楼')">
            <view class="attraction-image" style="background-image: url('https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80')"></view>
            <view class="attraction-name">航站楼</view>
            <view class="attraction-tags">
              <text class="attraction-tag">历史</text>
              <text class="attraction-tag">展览</text>
            </view>
            <view class="attraction-distance">距离: 200米</view>
          </view>
          <view class="attraction-item" @click="focusOnAttraction('梦想大草坪')">
            <view class="attraction-image" style="background-image: url('https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80')"></view>
            <view class="attraction-name">梦想大草坪</view>
            <view class="attraction-tags">
              <text class="attraction-tag">休闲</text>
              <text class="attraction-tag">野餐</text>
            </view>
            <view class="attraction-distance">距离: 300米</view>
          </view>
          <view class="attraction-item" @click="focusOnAttraction('航空科普馆')">
            <view class="attraction-image" style="background-image: url('https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80')"></view>
            <view class="attraction-name">航空科普馆</view>
            <view class="attraction-tags">
              <text class="attraction-tag">科普</text>
              <text class="attraction-tag">体验</text>
            </view>
            <view class="attraction-distance">距离: 400米</view>
          </view>
        </scroll-view>
      </view>

      <!-- 智能问答 -->
      <view class="section">
        <view class="section-title">🎤 智能问答</view>
        <view class="interaction-section">
          <input 
            type="text" 
            class="search-input" 
            v-model="searchInput" 
            placeholder="问我任何关于公园的问题..." 
            @keypress="handleEnter"
          />
          <view class="quick-questions">
            <button class="quick-btn" @click="quickQuestion('最近的火锅店')">最近的火锅店</button>
            <button class="quick-btn" @click="quickQuestion('卫生间在哪')">卫生间在哪</button>
            <button class="quick-btn" @click="quickQuestion('拍照地点')">拍照地点</button>
            <button class="quick-btn" @click="quickQuestion('儿童游乐')">儿童游乐</button>
          </view>
          <view class="response-area" v-if="responseText">
            <view class="response-text">{{ responseText }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 导航选项弹窗 -->
    <uni-popup ref="navModal" type="center">
      <view class="nav-modal-content">
        <button class="modal-close" @click="closeNavModal">×</button>
        <view class="nav-modal-title">选择前往{{ currentDestination }}的方式</view>
        <view class="nav-options">
          <view class="nav-option" @click="startNavigation('walking')">
            <view class="nav-option-info">
              <view class="nav-option-icon">🚶</view>
              <view class="nav-option-details">
                <view class="nav-option-name">步行</view>
                <view class="nav-option-time">约{{ walkingTime }}分钟</view>
              </view>
            </view>
            <view class="nav-option-cost free">免费</view>
          </view>
          <view class="nav-option" @click="startNavigation('bus')">
            <view class="nav-option-info">
              <view class="nav-option-icon">🚐</view>
              <view class="nav-option-details">
                <view class="nav-option-name">无人巴士</view>
                <view class="nav-option-time">约3分钟</view>
              </view>
            </view>
            <view class="nav-option-cost free">免费</view>
          </view>
          <view class="nav-option" @click="startNavigation('shuttle')">
            <view class="nav-option-info">
              <view class="nav-option-icon">🚌</view>
              <view class="nav-option-details">
                <view class="nav-option-name">接驳车</view>
                <view class="nav-option-time">约2分钟</view>
              </view>
            </view>
            <view class="nav-option-cost" @click.stop="payForTransport('5元')">5元</view>
          </view>
          <view class="nav-option" @click="startNavigation('electric')">
            <view class="nav-option-info">
              <view class="nav-option-icon">🛵</view>
              <view class="nav-option-details">
                <view class="nav-option-name">电动车</view>
                <view class="nav-option-time">约2分钟</view>
              </view>
            </view>
            <view class="nav-option-cost" @click.stop="payForTransport('10元')">10元</view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentTransport: 'walk',
      selectedAttraction: null,
      searchInput: '',
      responseText: '',
      currentDestination: '',
      currentDistance: 0,
      walkingTime: 5,
      attractionDescriptions: {
        '信标台': '欢迎来到信标台！这里是骆岗公园的标志性建筑，高约50米。您可以登顶俯瞰整个公园的美景。',
        '航站楼': '这里是保留的原机场航站楼建筑，现在改造为展览和活动空间，展示着合肥机场的历史。',
        '梦想大草坪': '欢迎来到梦想大草坪！这里是占地约100亩的开阔区域，是野餐和休闲的理想场所。',
        '科普馆': '欢迎来到航空科普馆！这里有丰富的航空知识展示和飞行模拟体验。'
      },
      responses: {
        '最近的火锅店': '最近的火锅店在公园东门外200米处，有"蜀香园火锅"和"老码头火锅"两家，营业时间11:00-22:00。',
        '卫生间在哪': '最近的卫生间在信标台东侧50米处，24小时开放，设有无障碍设施和母婴室。',
        '拍照地点': '推荐拍照地点：1.信标台顶部-全景视角 2.航站楼前广场-建筑摄影 3.梦想大草坪-人像摄影 4.展园花海-花卉摄影',
        '儿童游乐': '儿童游乐区位于梦想大草坪南侧，有滑梯、秋千、沙坑等设施，适合3-12岁儿童，开放时间8:00-18:00。'
      }
    };
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '伴游导览'
    });
    
    // 模拟用户进入不同景点区域
    this.startAttractionSimulation();
  },
  methods: {
    selectAttraction(name) {
      this.selectedAttraction = name;
      
      // 模拟进入景点区域
      setTimeout(() => {
        this.playAttractionAudio(name);
      }, 500);
    },
    
    playAttractionAudio(name) {
      const description = this.attractionDescriptions[name];
      if (description) {
        this.showNotification(`🔊 ${description}`, 'info');
      }
    },
    
    focusOnAttraction(name) {
      this.selectedAttraction = name;
      this.showNavigationOptions(name, this.getAttractionDistance(name));
    },
    
    getAttractionDistance(name) {
      const distances = {
        '信标台': 50,
        '航站楼': 200,
        '梦想大草坪': 300,
        '航空科普馆': 400
      };
      
      return distances[name] || 100;
    },
    
    quickQuestion(question) {
      this.searchInput = question;
      this.handleQuestion(question);
    },
    
    handleEnter(event) {
      if (event.keyCode === 13) {
        const question = this.searchInput.trim();
        if (question) {
          this.handleQuestion(question);
        }
      }
    },
    
    handleQuestion(question) {
      let response = this.responses[question];
      
      if (!response) {
        // 简单关键词匹配
        if (question.includes('餐厅') || question.includes('吃饭')) {
          response = '园区内有骆岗咖啡厅，位于航站楼内。园区外东门有多家餐厅可选择。';
        } else if (question.includes('停车')) {
          response = '公园设有大型停车场，位于东门和西门，停车费5元/小时，可通过手机支付。';
        } else if (question.includes('门票')) {
          response = '骆岗公园免费开放！部分体验项目收费：航空科普馆20元，VR体验30元。';
        } else {
          response = '抱歉，我没有理解您的问题。您可以问我关于景点、设施、餐饮、交通等信息。';
        }
      }
      
      this.responseText = response;
      
      // 如果相关，在地图上标记位置
      if (question.includes('火锅') || question.includes('餐厅')) {
        this.markLocationOnMap('restaurant');
      }
    },
    
    markLocationOnMap(type) {
      // 这里会在地图上添加餐厅、设施等标记
      this.showNotification('已在地图上标注相关位置', 'success');
    },
    
    showNavigationOptions(destination, distance) {
      this.currentDestination = destination;
      this.currentDistance = distance;
      
      // 计算步行时间（假设步行速度为80米/分钟）
      this.walkingTime = Math.ceil(distance / 80);
      
      this.$refs.navModal.open();
    },
    
    closeNavModal() {
      this.$refs.navModal.close();
    },
    
    startNavigation(method) {
      const methods = {
        'walking': '步行',
        'bus': '无人巴士',
        'shuttle': '接驳车',
        'electric': '电动车'
      };
      
      this.closeNavModal();
      this.showNotification(`开始${methods[method]}导航到${this.currentDestination}`, 'success');
      
      // 模拟在地图上显示路线
      setTimeout(() => {
        this.showNotification(`路线已在地图上标注`, 'info');
      }, 1000);
    },
    
    payForTransport(cost) {
      this.showNotification(`正在跳转到支付页面，费用：${cost}`, 'info');
    },
    
    showNotification(message, type = 'info') {
      uni.showToast({
        title: message,
        icon: type === 'success' ? 'success' : 'none',
        duration: 3000
      });
    },
    
    startAttractionSimulation() {
      // 模拟用户进入不同景点区域
      setInterval(() => {
        if (Math.random() > 0.95) { // 每个间隔有5%的几率
          const attractions = ['信标台', '航站楼', '梦想大草坪', '科普馆'];
          const randomAttraction = attractions[Math.floor(Math.random() * attractions.length)];
          this.selectedAttraction = randomAttraction;
        }
      }, 5000);
    }
  },
  components: {
    'park-header': () => import('@/components/park-header/park-header')
  }
}
</script>

<style>
.app-container {
  max-width: 750rpx;
  margin: 0 auto;
  background: rgba(255,255,255,0.95);
  min-height: 100vh;
  position: relative;
}

.content {
  padding: 40rpx 32rpx;
}

.map-container {
  width: 100%;
  height: 600rpx;
  background: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80') center/cover;
  border-radius: 24rpx;
  position: relative;
  margin-bottom: 40rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
}

.map-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(52, 152, 219, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-content {
  text-align: center;
  color: white;
  background: rgba(0,0,0,0.5);
  padding: 32rpx;
  border-radius: 16rpx;
  backdrop-filter: blur(10px);
}

.current-location {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.location-desc {
  font-size: 28rpx;
  opacity: 0.9;
}

.attraction-marker {
  position: absolute;
  width: 60rpx;
  height: 60rpx;
  background: #e74c3c;
  border: 6rpx solid white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.attraction-marker.active {
  background: #f39c12;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.marker-1 { top: 20%; left: 30%; }
.marker-2 { top: 40%; left: 60%; }
.marker-3 { top: 70%; left: 25%; }
.marker-4 { top: 60%; left: 75%; }

.section {
  margin-bottom: 48rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  color: #2c3e50;
}

.attraction-list {
  display: flex;
  white-space: nowrap;
  padding-bottom: 16rpx;
}

.attraction-item {
  min-width: 280rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  transition: all 0.3s;
  flex-shrink: 0;
  text-align: center;
  margin-right: 24rpx;
}

.attraction-image {
  width: 100%;
  height: 160rpx;
  background-size: cover;
  background-position: center;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.attraction-name {
  font-weight: bold;
  margin-bottom: 8rpx;
  font-size: 24rpx;
  color: #2c3e50;
}

.attraction-tags {
  display: flex;
  gap: 4rpx;
  justify-content: center;
  margin-bottom: 8rpx;
  flex-wrap: wrap;
}

.attraction-tag {
  background: #3498db;
  color: white;
  padding: 2rpx 8rpx;
  border-radius: 12rpx;
  font-size: 16rpx;
}

.attraction-distance {
  font-size: 22rpx;
  color: #7f8c8d;
  margin-bottom: 12rpx;
}

.interaction-section {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
  margin-bottom: 40rpx;
}

.search-input {
  width: 100%;
  padding: 24rpx 32rpx;
  border: 2rpx solid #ddd;
  border-radius: 50rpx;
  font-size: 28rpx;
  outline: none;
  margin-bottom: 24rpx;
}

.quick-questions {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.quick-btn {
  background: #ecf0f1;
  border: none;
  padding: 12rpx 24rpx;
  border-radius: 32rpx;
  font-size: 24rpx;
  color: #2c3e50;
  transition: all 0.3s;
}

.response-area {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-top: 24rpx;
}

.response-text {
  font-size: 28rpx;
  line-height: 1.4;
  color: #2c3e50;
}

.nav-modal-content {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  max-width: 600rpx;
  width: 90%;
  position: relative;
}

.nav-modal-title {
  font-weight: bold;
  margin-bottom: 32rpx;
  text-align: center;
  color: #2c3e50;
  font-size: 32rpx;
}

.nav-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.nav-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s;
}

.nav-option-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.nav-option-icon {
  font-size: 40rpx;
}

.nav-option-details {
  flex: 1;
}

.nav-option-name {
  font-weight: bold;
  font-size: 24rpx;
}

.nav-option-time {
  font-size: 20rpx;
  color: #7f8c8d;
}

.nav-option-cost {
  background: #27ae60;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  font-size: 18rpx;
}

.nav-option-cost.free {
  background: #95a5a6;
}

.modal-close {
  position: absolute;
  top: 20rpx;
  right: 30rpx;
  background: none;
  border: none;
  font-size: 40rpx;
  color: #7f8c8d;
}
</style>