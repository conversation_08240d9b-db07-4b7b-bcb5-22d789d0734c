# 骆岗公园 uniapp 项目

这是一个使用 uniapp 框架开发的骆岗公园移动应用，提供公园导览、景点介绍、行程规划等功能。

## 项目结构

```
├── components/              # 公共组件
│   ├── park-header/        # 顶部导航栏组件
│   ├── park-swiper/        # 轮播图组件
│   ├── attraction-card/    # 景点卡片组件
│   └── park-footer/        # 底部导航栏组件
├── pages/                   # 页面
│   ├── index/              # 首页
│   ├── explore/            # 探索页面
│   ├── itinerary/          # 行程页面
│   ├── community/          # 社区页面
│   └── profile/            # 个人资料页面
├── static/                  # 静态资源
│   └── images/             # 图片资源
├── App.vue                  # 应用入口
├── main.js                  # Vue实例初始化
├── manifest.json            # 应用配置
├── pages.json               # 页面路由配置
└── uni.scss                 # 全局样式变量
```

## 功能列表

1. **首页**：公园概览、特色景点和推荐路线
2. **探索**：浏览景点、活动和美食
3. **行程**：管理用户的游玩计划
4. **社区**：查看游记和攻略
5. **个人资料**：展示用户信息和设置

## 运行项目

1. 安装依赖

```bash
npm install
```

2. 运行开发服务器

```bash
npm run dev:h5
```

3. 构建生产版本

```bash
npm run build:h5
```

## 技术栈

- Vue2
- uniapp
- Sass