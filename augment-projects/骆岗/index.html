<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>骆岗公园</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fffe;
            min-height: 100vh;
            color: #333;
        }
        
        .app-container {
            max-width: 414px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #4CAF50;
            margin-bottom: 10px;
        }
        
        .preview-section {
            margin-bottom: 30px;
        }
        
        .preview-section h2 {
            border-left: 5px solid #4CAF50;
            padding-left: 10px;
            margin-bottom: 15px;
        }
        
        .preview-image {
            width: 100%;
            height: 200px;
            background-color: #eee;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            margin-bottom: 15px;
        }
        
        .page-list {
            list-style: none;
        }
        
        .page-list li {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-list li:last-child {
            border-bottom: none;
        }
        
        .page-name {
            font-weight: bold;
        }
        
        .page-desc {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <h1>骆岗公园 App</h1>
            <p>基于 uniapp 的 Vue2 项目</p>
        </div>
        
        <div class="preview-section">
            <h2>项目预览</h2>
            <div class="preview-image">
                <span>骆岗公园应用界面预览</span>
            </div>
            <p>这是一个使用 uniapp 框架开发的骆岗公园移动应用，提供公园导览、景点介绍、行程规划等功能。</p>
        </div>
        
        <div class="preview-section">
            <h2>页面列表</h2>
            <ul class="page-list">
                <li>
                    <span class="page-name">首页</span>
                    <span class="page-desc">公园概览、特色景点和推荐路线</span>
                </li>
                <li>
                    <span class="page-name">探索</span>
                    <span class="page-desc">浏览景点、活动和美食</span>
                </li>
                <li>
                    <span class="page-name">行程</span>
                    <span class="page-desc">管理用户的游玩计划</span>
                </li>
                <li>
                    <span class="page-name">社区</span>
                    <span class="page-desc">查看游记和攻略</span>
                </li>
                <li>
                    <span class="page-name">个人资料</span>
                    <span class="page-desc">展示用户信息和设置</span>
                </li>
            </ul>
        </div>
        
        <div class="preview-section">
            <h2>组件列表</h2>
            <ul class="page-list">
                <li>
                    <span class="page-name">顶部导航栏</span>
                    <span class="page-desc">park-header</span>
                </li>
                <li>
                    <span class="page-name">轮播图</span>
                    <span class="page-desc">park-swiper</span>
                </li>
                <li>
                    <span class="page-name">景点卡片</span>
                    <span class="page-desc">attraction-card</span>
                </li>
                <li>
                    <span class="page-name">底部导航栏</span>
                    <span class="page-desc">park-footer</span>
                </li>
            </ul>
        </div>
    </div>
</body>
</html>