<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管网GIS地图浏览系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://webapi.amap.com/maps?v=2.0&key=YOUR_AMAP_KEY&plugin=AMap.Scale,AMap.ToolBar,AMap.MouseTool,AMap.PlaceSearch,AMap.Geocoder,AMap.DistrictSearch"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            background: #f5f7fa;
            height: 100vh;
            overflow: hidden;
        }

        .gis-container {
            display: flex;
            height: 100vh;
        }

        /* 左侧工具栏 */
        .toolbar {
            width: 320px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .toolbar-header {
            padding: 20px;
            background: rgba(0,0,0,0.1);
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .toolbar-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .toolbar-subtitle {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .toolbar-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        .tool-section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .tool-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .tool-btn {
            padding: 10px 12px;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            text-align: center;
        }

        .tool-btn:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .tool-btn.active {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
        }

        .tool-btn.full-width {
            grid-column: 1 / -1;
        }

        /* 输入框样式 */
        .input-group {
            margin-bottom: 15px;
        }

        .input-label {
            display: block;
            margin-bottom: 5px;
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .input-field {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 6px;
            background: rgba(255,255,255,0.1);
            color: white;
            font-size: 0.9rem;
        }

        .input-field::placeholder {
            color: rgba(255,255,255,0.6);
        }

        .input-field:focus {
            outline: none;
            border-color: rgba(255,255,255,0.6);
            background: rgba(255,255,255,0.15);
        }

        /* 选择框样式 */
        .select-field {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 6px;
            background: rgba(255,255,255,0.1);
            color: white;
            font-size: 0.9rem;
        }

        .select-field option {
            background: #667eea;
            color: white;
        }

        /* 复选框组 */
        .checkbox-group {
            display: grid;
            grid-template-columns: 1fr;
            gap: 8px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 0;
        }

        .checkbox-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: white;
        }

        .checkbox-item label {
            font-size: 0.9rem;
            cursor: pointer;
        }

        /* 书签列表 */
        .bookmark-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .bookmark-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: rgba(255,255,255,0.1);
            border-radius: 6px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .bookmark-item:hover {
            background: rgba(255,255,255,0.2);
        }

        .bookmark-name {
            font-size: 0.9rem;
            flex: 1;
        }

        .bookmark-actions {
            display: flex;
            gap: 5px;
        }

        .bookmark-btn {
            padding: 4px 6px;
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 4px;
            color: white;
            cursor: pointer;
            font-size: 0.8rem;
        }

        .bookmark-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        /* 地图容器 */
        .map-container {
            flex: 1;
            position: relative;
        }

        #mapContainer {
            width: 100%;
            height: 100%;
        }

        /* 地图控制按钮 */
        .map-controls {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 999;
        }

        .control-btn {
            width: 40px;
            height: 40px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            color: #666;
        }

        .control-btn:hover {
            background: #f0f0f0;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .control-btn.active {
            background: #667eea;
            color: white;
        }

        /* 信息面板 */
        .info-panel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            width: 350px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            display: none;
            z-index: 999;
        }

        .info-panel.show {
            display: block;
            animation: slideUp 0.3s ease-out;
        }

        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .info-header {
            padding: 15px 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .info-title {
            font-size: 1.1rem;
            font-weight: 600;
        }

        .info-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .info-close:hover {
            background: rgba(255,255,255,0.2);
        }

        .info-content {
            padding: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: #666;
        }

        .info-value {
            color: #333;
            text-align: right;
        }

        /* 测量结果面板 */
        .measure-panel {
            position: absolute;
            top: 80px;
            right: 20px;
            width: 250px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            display: none;
            z-index: 999;
        }

        .measure-panel.show {
            display: block;
        }

        .measure-header {
            padding: 12px 15px;
            background: #667eea;
            color: white;
            border-radius: 8px 8px 0 0;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .measure-content {
            padding: 15px;
        }

        .measure-result {
            font-size: 1.1rem;
            font-weight: 600;
            color: #667eea;
            text-align: center;
            padding: 10px;
            background: #f8f9ff;
            border-radius: 6px;
            margin-bottom: 10px;
        }

        .measure-actions {
            display: flex;
            gap: 8px;
        }

        .measure-btn {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .measure-btn:hover {
            background: #f0f0f0;
        }

        .measure-btn.primary {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .measure-btn.primary:hover {
            background: #5a6fd8;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .toolbar {
                width: 280px;
            }
            
            .info-panel {
                width: 300px;
                left: 10px;
                bottom: 10px;
            }
            
            .measure-panel {
                width: 220px;
                right: 10px;
            }
        }

        /* 滚动条样式 */
        .toolbar-content::-webkit-scrollbar,
        .bookmark-list::-webkit-scrollbar,
        .info-content::-webkit-scrollbar {
            width: 6px;
        }

        .toolbar-content::-webkit-scrollbar-track,
        .bookmark-list::-webkit-scrollbar-track,
        .info-content::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
            border-radius: 3px;
        }

        .toolbar-content::-webkit-scrollbar-thumb,
        .bookmark-list::-webkit-scrollbar-thumb,
        .info-content::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
        }

        .toolbar-content::-webkit-scrollbar-thumb:hover,
        .bookmark-list::-webkit-scrollbar-thumb:hover,
        .info-content::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            color: #333;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 10px;
            max-width: 350px;
            animation: slideInRight 0.3s ease-out;
        }

        .notification.success {
            border-left: 4px solid #4caf50;
        }

        .notification.info {
            border-left: 4px solid #2196f3;
        }

        .notification.warning {
            border-left: 4px solid #ff9800;
        }

        .notification.error {
            border-left: 4px solid #f44336;
        }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="gis-container">
        <!-- 左侧工具栏 -->
        <div class="toolbar">
            <div class="toolbar-header">
                <div class="toolbar-title">
                    <i class="fas fa-map"></i>
                    管网GIS地图
                </div>
                <div class="toolbar-subtitle">地图浏览与分析系统</div>
            </div>
            
            <div class="toolbar-content">
                <!-- 基础操作 -->
                <div class="tool-section">
                    <div class="section-title">
                        <i class="fas fa-mouse-pointer"></i>
                        基础操作
                    </div>
                    <div class="tool-group">
                        <button class="tool-btn active" id="selectTool" onclick="setMapTool('select')">
                            <i class="fas fa-mouse-pointer"></i>
                            选择
                        </button>
                        <button class="tool-btn" id="panTool" onclick="setMapTool('pan')">
                            <i class="fas fa-hand-paper"></i>
                            平移
                        </button>
                        <button class="tool-btn" onclick="zoomToFit()">
                            <i class="fas fa-expand-arrows-alt"></i>
                            全幅
                        </button>
                        <button class="tool-btn" onclick="clearSelection()">
                            <i class="fas fa-times"></i>
                            清除
                        </button>
                    </div>
                </div>

                <!-- 缩放控制 -->
                <div class="tool-section">
                    <div class="section-title">
                        <i class="fas fa-search-plus"></i>
                        缩放控制
                    </div>
                    <div class="tool-group">
                        <button class="tool-btn" id="zoomInTool" onclick="setMapTool('zoomIn')">
                            <i class="fas fa-search-plus"></i>
                            放大
                        </button>
                        <button class="tool-btn" id="zoomOutTool" onclick="setMapTool('zoomOut')">
                            <i class="fas fa-search-minus"></i>
                            缩小
                        </button>
                        <button class="tool-btn" onclick="previousView()">
                            <i class="fas fa-arrow-left"></i>
                            前视图
                        </button>
                        <button class="tool-btn" onclick="nextView()">
                            <i class="fas fa-arrow-right"></i>
                            后视图
                        </button>
                    </div>
                    <div class="input-group">
                        <label class="input-label">缩放比例</label>
                        <select class="select-field" id="zoomLevel" onchange="setZoomLevel()">
                            <option value="10">1:100万</option>
                            <option value="12">1:50万</option>
                            <option value="14">1:10万</option>
                            <option value="16" selected>1:5万</option>
                            <option value="18">1:1万</option>
                            <option value="20">1:5千</option>
                        </select>
                    </div>
                </div>

                <!-- 测量工具 -->
                <div class="tool-section">
                    <div class="section-title">
                        <i class="fas fa-ruler"></i>
                        量算功能
                    </div>
                    <div class="tool-group">
                        <button class="tool-btn" id="measureDistance" onclick="setMapTool('measureDistance')">
                            <i class="fas fa-ruler"></i>
                            距离
                        </button>
                        <button class="tool-btn" id="measureArea" onclick="setMapTool('measureArea')">
                            <i class="fas fa-vector-square"></i>
                            面积
                        </button>
                    </div>
                </div>

                <!-- 定位查询 -->
                <div class="tool-section">
                    <div class="section-title">
                        <i class="fas fa-search"></i>
                        定位查询
                    </div>
                    <div class="input-group">
                        <label class="input-label">搜索地点</label>
                        <input type="text" class="input-field" id="searchInput" placeholder="输入地点名称或坐标" onkeypress="handleSearchKeyPress(event)">
                    </div>
                    <button class="tool-btn full-width" onclick="searchLocation()">
                        <i class="fas fa-search"></i>
                        搜索定位
                    </button>
                </div>

                <!-- 图层控制 -->
                <div class="tool-section">
                    <div class="section-title">
                        <i class="fas fa-layer-group"></i>
                        图层控制
                    </div>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="pipelineLayer" checked onchange="toggleLayer('pipeline')">
                            <label for="pipelineLayer">管道网络</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="valveLayer" checked onchange="toggleLayer('valve')">
                            <label for="valveLayer">阀门设施</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="pumpLayer" checked onchange="toggleLayer('pump')">
                            <label for="pumpLayer">泵站设备</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="monitorLayer" onchange="toggleLayer('monitor')">
                            <label for="monitorLayer">监测设备</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="dmaLayer" onchange="toggleLayer('dma')">
                            <label for="dmaLayer">DMA分区</label>
                        </div>
                    </div>
                </div>

                <!-- 设施过滤 -->
                <div class="tool-section">
                    <div class="section-title">
                        <i class="fas fa-filter"></i>
                        设施过滤
                    </div>
                    <div class="input-group">
                        <label class="input-label">管径范围</label>
                        <select class="select-field" id="diameterFilter" onchange="applyFilter()">
                            <option value="">全部管径</option>
                            <option value="small">DN100-300</option>
                            <option value="medium">DN400-600</option>
                            <option value="large">DN800以上</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label class="input-label">管材类型</label>
                        <select class="select-field" id="materialFilter" onchange="applyFilter()">
                            <option value="">全部管材</option>
                            <option value="cast_iron">铸铁管</option>
                            <option value="ductile_iron">球墨铸铁</option>
                            <option value="steel">钢管</option>
                            <option value="pe">PE管</option>
                        </select>
                    </div>
                </div>

                <!-- 地图书签 -->
                <div class="tool-section">
                    <div class="section-title">
                        <i class="fas fa-bookmark"></i>
                        地图书签
                    </div>
                    <div class="input-group">
                        <input type="text" class="input-field" id="bookmarkName" placeholder="输入书签名称">
                    </div>
                    <button class="tool-btn full-width" onclick="addBookmark()">
                        <i class="fas fa-plus"></i>
                        添加书签
                    </button>
                    <div class="bookmark-list" id="bookmarkList">
                        <!-- 书签列表将在这里动态生成 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 地图容器 -->
        <div class="map-container">
            <div id="mapContainer"></div>
            
            <!-- 地图控制按钮 -->
            <div class="map-controls">
                <button class="control-btn" onclick="toggleSatellite()" title="卫星图">
                    <i class="fas fa-satellite"></i>
                </button>
                <button class="control-btn" onclick="showLegend()" title="图例">
                    <i class="fas fa-list"></i>
                </button>
                <button class="control-btn" onclick="printMap()" title="打印">
                    <i class="fas fa-print"></i>
                </button>
            </div>

            <!-- 信息面板 -->
            <div class="info-panel" id="infoPanel">
                <div class="info-header">
                    <div class="info-title" id="infoTitle">设施信息</div>
                    <button class="info-close" onclick="closeInfoPanel()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="info-content" id="infoContent">
                    <!-- 设施信息将在这里显示 -->
                </div>
            </div>

            <!-- 测量结果面板 -->
            <div class="measure-panel" id="measurePanel">
                <div class="measure-header">
                    <span id="measureTitle">测量结果</span>
                    <button class="info-close" onclick="closeMeasurePanel()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="measure-content">
                    <div class="measure-result" id="measureResult">0</div>
                    <div class="measure-actions">
                        <button class="measure-btn" onclick="clearMeasure()">清除</button>
                        <button class="measure-btn primary" onclick="continueMeasure()">继续</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let map;
        let currentTool = 'select';
        let mouseTool;
        let placeSearch;
        let geocoder;
        let viewHistory = [];
        let currentViewIndex = -1;
        let bookmarks = [];
        let layers = {
            pipeline: [],
            valve: [],
            pump: [],
            monitor: [],
            dma: []
        };
        let measureOverlays = [];
        let selectedFeature = null;

        // 初始化地图
        function initMap() {
            // 由于没有真实的高德地图API Key，这里使用模拟实现
            console.log('初始化高德地图...');
            
            // 模拟地图容器
            const mapContainer = document.getElementById('mapContainer');
            mapContainer.innerHTML = `
                <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); 
                           display: flex; align-items: center; justify-content: center; color: #1976d2; font-size: 1.2rem;">
                    <div style="text-align: center;">
                        <i class="fas fa-map" style="font-size: 4rem; margin-bottom: 20px;"></i>
                        <div style="font-size: 1.5rem; font-weight: 600; margin-bottom: 10px;">高德地图加载中...</div>
                        <div style="font-size: 1rem; opacity: 0.8;">管网GIS地图浏览系统</div>
                        <div style="margin-top: 20px; font-size: 0.9rem;">
                            <div>• 支持地图平移、缩放、选择等基础操作</div>
                            <div>• 提供距离测量、面积测量功能</div>
                            <div>• 集成定位查询、图层控制、书签管理</div>
                            <div>• 实现设施信息展示、过滤筛选功能</div>
                        </div>
                    </div>
                </div>
            `;

            // 模拟初始化完成
            setTimeout(() => {
                showNotification('地图系统初始化完成', 'success');
                initMockData();
            }, 1000);

            /*
            // 真实的高德地图初始化代码（需要有效的API Key）
            map = new AMap.Map('mapContainer', {
                zoom: 16,
                center: [116.397428, 39.90923],
                mapStyle: 'amap://styles/normal',
                features: ['bg', 'road', 'building', 'point']
            });

            // 添加工具栏
            map.addControl(new AMap.ToolBar({
                position: {
                    top: '20px',
                    left: '20px'
                }
            }));

            // 添加比例尺
            map.addControl(new AMap.Scale({
                position: {
                    bottom: '20px',
                    left: '20px'
                }
            }));

            // 初始化鼠标工具
            mouseTool = new AMap.MouseTool(map);

            // 初始化搜索服务
            placeSearch = new AMap.PlaceSearch({
                pageSize: 10,
                pageIndex: 1,
                city: '010'
            });

            // 初始化地理编码
            geocoder = new AMap.Geocoder({
                city: '010'
            });

            // 地图点击事件
            map.on('click', function(e) {
                if (currentTool === 'select') {
                    handleMapClick(e);
                }
            });

            // 地图视图变化事件
            map.on('moveend', function() {
                saveCurrentView();
            });

            map.on('zoomend', function() {
                saveCurrentView();
            });
            */
        }

        // 初始化模拟数据
        function initMockData() {
            // 模拟管网设施数据
            const mockFacilities = [
                { id: 1, type: 'pipeline', name: '主干管道-001', diameter: 'DN600', material: '球墨铸铁', length: '1.2km', status: '正常' },
                { id: 2, type: 'valve', name: '控制阀门-V001', diameter: 'DN400', pressure: '0.6MPa', status: '开启' },
                { id: 3, type: 'pump', name: '增压泵站-P001', capacity: '500m³/h', power: '75kW', status: '运行' },
                { id: 4, type: 'monitor', name: '压力监测-M001', pressure: '0.45MPa', flow: '320m³/h', status: '在线' }
            ];

            // 模拟书签数据
            bookmarks = [
                { name: '水厂区域', center: [116.397428, 39.90923], zoom: 16 },
                { name: '主管网', center: [116.407428, 39.91923], zoom: 14 },
                { name: '泵站集群', center: [116.387428, 39.89923], zoom: 17 }
            ];

            updateBookmarkList();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
        });

        // 设置地图工具
        function setMapTool(tool) {
            // 清除之前的工具状态
            document.querySelectorAll('.tool-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // 设置当前工具
            currentTool = tool;
            document.getElementById(tool + 'Tool')?.classList.add('active');

            // 根据工具类型设置鼠标样式和行为
            switch(tool) {
                case 'select':
                    document.getElementById('selectTool').classList.add('active');
                    showNotification('已切换到选择工具', 'info');
                    break;
                case 'pan':
                    document.getElementById('panTool').classList.add('active');
                    showNotification('已切换到平移工具', 'info');
                    break;
                case 'zoomIn':
                    document.getElementById('zoomInTool').classList.add('active');
                    showNotification('已切换到放大工具', 'info');
                    break;
                case 'zoomOut':
                    document.getElementById('zoomOutTool').classList.add('active');
                    showNotification('已切换到缩小工具', 'info');
                    break;
                case 'measureDistance':
                    document.getElementById('measureDistance').classList.add('active');
                    showNotification('已切换到距离测量工具', 'info');
                    showMeasurePanel('距离测量');
                    break;
                case 'measureArea':
                    document.getElementById('measureArea').classList.add('active');
                    showNotification('已切换到面积测量工具', 'info');
                    showMeasurePanel('面积测量');
                    break;
            }
        }

        // 全幅显示
        function zoomToFit() {
            showNotification('地图已调整到全幅显示', 'info');
            // 模拟全幅显示
            console.log('执行全幅显示');
        }

        // 清除选择
        function clearSelection() {
            selectedFeature = null;
            closeInfoPanel();
            showNotification('已清除所有选择', 'info');
        }

        // 设置缩放级别
        function setZoomLevel() {
            const level = document.getElementById('zoomLevel').value;
            showNotification(`地图缩放级别已设置为 ${level}`, 'info');
            // 实际实现中会调用 map.setZoom(parseInt(level))
        }

        // 前视图
        function previousView() {
            if (currentViewIndex > 0) {
                currentViewIndex--;
                showNotification('已切换到前一个视图', 'info');
            } else {
                showNotification('已经是第一个视图', 'warning');
            }
        }

        // 后视图
        function nextView() {
            if (currentViewIndex < viewHistory.length - 1) {
                currentViewIndex++;
                showNotification('已切换到后一个视图', 'info');
            } else {
                showNotification('已经是最后一个视图', 'warning');
            }
        }

        // 保存当前视图
        function saveCurrentView() {
            // 模拟保存视图状态
            const currentView = {
                center: [116.397428, 39.90923],
                zoom: 16,
                timestamp: Date.now()
            };
            
            viewHistory.push(currentView);
            currentViewIndex = viewHistory.length - 1;
            
            // 限制历史记录数量
            if (viewHistory.length > 20) {
                viewHistory.shift();
                currentViewIndex--;
            }
        }

        // 搜索定位
        function searchLocation() {
            const query = document.getElementById('searchInput').value.trim();
            if (!query) {
                showNotification('请输入搜索内容', 'warning');
                return;
            }

            showNotification(`正在搜索: ${query}`, 'info');
            
            // 模拟搜索结果
            setTimeout(() => {
                showNotification(`已定位到: ${query}`, 'success');
                // 实际实现中会调用地理编码服务
            }, 1000);
        }

        // 处理搜索输入框回车事件
        function handleSearchKeyPress(event) {
            if (event.key === 'Enter') {
                searchLocation();
            }
        }

        // 切换图层显示
        function toggleLayer(layerType) {
            const checkbox = document.getElementById(layerType + 'Layer');
            const isVisible = checkbox.checked;
            
            showNotification(`${getLayerName(layerType)}图层已${isVisible ? '显示' : '隐藏'}`, 'info');
            
            // 实际实现中会控制对应图层的显示/隐藏
            console.log(`Toggle layer: ${layerType}, visible: ${isVisible}`);
        }

        // 获取图层名称
        function getLayerName(layerType) {
            const names = {
                'pipeline': '管道网络',
                'valve': '阀门设施',
                'pump': '泵站设备',
                'monitor': '监测设备',
                'dma': 'DMA分区'
            };
            return names[layerType] || layerType;
        }

        // 应用过滤条件
        function applyFilter() {
            const diameterFilter = document.getElementById('diameterFilter').value;
            const materialFilter = document.getElementById('materialFilter').value;
            
            let filterText = '已应用过滤条件: ';
            const filters = [];
            
            if (diameterFilter) {
                filters.push(`管径${diameterFilter}`);
            }
            if (materialFilter) {
                filters.push(`管材${materialFilter}`);
            }
            
            if (filters.length > 0) {
                filterText += filters.join(', ');
                showNotification(filterText, 'info');
            } else {
                showNotification('已清除所有过滤条件', 'info');
            }
        }

        // 添加书签
        function addBookmark() {
            const name = document.getElementById('bookmarkName').value.trim();
            if (!name) {
                showNotification('请输入书签名称', 'warning');
                return;
            }

            // 模拟当前地图状态
            const bookmark = {
                name: name,
                center: [116.397428, 39.90923],
                zoom: 16,
                id: Date.now()
            };

            bookmarks.push(bookmark);
            updateBookmarkList();
            document.getElementById('bookmarkName').value = '';
            showNotification(`书签 "${name}" 已添加`, 'success');
        }

        // 更新书签列表
        function updateBookmarkList() {
            const bookmarkList = document.getElementById('bookmarkList');
            bookmarkList.innerHTML = '';

            bookmarks.forEach(bookmark => {
                const bookmarkItem = document.createElement('div');
                bookmarkItem.className = 'bookmark-item';
                bookmarkItem.innerHTML = `
                    <div class="bookmark-name" onclick="gotoBookmark(${bookmark.id})">${bookmark.name}</div>
                    <div class="bookmark-actions">
                        <button class="bookmark-btn" onclick="editBookmark(${bookmark.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="bookmark-btn" onclick="deleteBookmark(${bookmark.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;
                bookmarkList.appendChild(bookmarkItem);
            });
        }

        // 跳转到书签
        function gotoBookmark(bookmarkId) {
            const bookmark = bookmarks.find(b => b.id === bookmarkId);
            if (bookmark) {
                showNotification(`已跳转到书签: ${bookmark.name}`, 'success');
                // 实际实现中会设置地图中心和缩放级别
                console.log('Goto bookmark:', bookmark);
            }
        }

        // 编辑书签
        function editBookmark(bookmarkId) {
            const bookmark = bookmarks.find(b => b.id === bookmarkId);
            if (bookmark) {
                const newName = prompt('请输入新的书签名称:', bookmark.name);
                if (newName && newName.trim()) {
                    bookmark.name = newName.trim();
                    updateBookmarkList();
                    showNotification('书签已更新', 'success');
                }
            }
        }

        // 删除书签
        function deleteBookmark(bookmarkId) {
            if (confirm('确定要删除这个书签吗？')) {
                bookmarks = bookmarks.filter(b => b.id !== bookmarkId);
                updateBookmarkList();
                showNotification('书签已删除', 'success');
            }
        }

        // 切换卫星图
        function toggleSatellite() {
            showNotification('已切换地图模式', 'info');
            // 实际实现中会切换地图图层
        }

        // 显示图例
        function showLegend() {
            showNotification('图例功能开发中...', 'info');
        }

        // 打印地图
        function printMap() {
            showNotification('正在准备打印...', 'info');
            setTimeout(() => {
                showNotification('打印功能开发中...', 'info');
            }, 1000);
        }

        // 显示信息面板
        function showInfoPanel(feature) {
            const panel = document.getElementById('infoPanel');
            const title = document.getElementById('infoTitle');
            const content = document.getElementById('infoContent');

            title.textContent = feature.name || '设施信息';
            
            let infoHTML = '';
            Object.keys(feature).forEach(key => {
                if (key !== 'id' && key !== 'type') {
                    infoHTML += `
                        <div class="info-item">
                            <div class="info-label">${getFieldLabel(key)}:</div>
                            <div class="info-value">${feature[key]}</div>
                        </div>
                    `;
                }
            });

            content.innerHTML = infoHTML;
            panel.classList.add('show');
        }

        // 关闭信息面板
        function closeInfoPanel() {
            document.getElementById('infoPanel').classList.remove('show');
        }

        // 显示测量面板
        function showMeasurePanel(title) {
            const panel = document.getElementById('measurePanel');
            const titleElement = document.getElementById('measureTitle');
            const result = document.getElementById('measureResult');

            titleElement.textContent = title;
            result.textContent = '0';
            panel.classList.add('show');
        }

        // 关闭测量面板
        function closeMeasurePanel() {
            document.getElementById('measurePanel').classList.remove('show');
            clearMeasure();
        }

        // 清除测量
        function clearMeasure() {
            document.getElementById('measureResult').textContent = '0';
            showNotification('已清除测量结果', 'info');
        }

        // 继续测量
        function continueMeasure() {
            showNotification('继续测量...', 'info');
        }

        // 获取字段标签
        function getFieldLabel(field) {
            const labels = {
                'name': '名称',
                'diameter': '管径',
                'material': '材质',
                'length': '长度',
                'pressure': '压力',
                'capacity': '容量',
                'power': '功率',
                'flow': '流量',
                'status': '状态'
            };
            return labels[field] || field;
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            
            const icon = getNotificationIcon(type);
            notification.innerHTML = `
                <i class="${icon}"></i>
                <span>${message}</span>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-out';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 获取通知图标
        function getNotificationIcon(type) {
            const icons = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };
            return icons[type] || icons.info;
        }
    </script>
</body>
</html>
