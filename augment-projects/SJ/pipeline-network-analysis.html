<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管网分析系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/d3/7.8.5/d3.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .analysis-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 头部样式 */
        .analysis-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 25px;
            text-align: center;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }

        .main-title {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }

        .subtitle {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 15px;
        }

        .analysis-tabs {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }

        .tab-btn {
            padding: 12px 25px;
            background: rgba(102, 126, 234, 0.1);
            border: 2px solid rgba(102, 126, 234, 0.3);
            border-radius: 25px;
            color: #667eea;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .tab-btn.active {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .tab-btn:hover:not(.active) {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-1px);
        }

        /* 内容区域 */
        .content-area {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            min-height: 700px;
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.5s ease-in-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 分析面板 */
        .analysis-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 5px solid #667eea;
        }

        .panel-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .input-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .input-group {
            display: flex;
            flex-direction: column;
        }

        .input-label {
            font-weight: 600;
            color: #555;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .input-field {
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .input-field:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .select-field {
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
            cursor: pointer;
        }

        /* 按钮样式 */
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ff9800, #f57c00);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }

        /* 结果展示区域 */
        .result-area {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-top: 25px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .result-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }

        /* 图表容器 */
        .chart-container {
            position: relative;
            height: 400px;
            margin-bottom: 20px;
        }

        .chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-bottom: 25px;
        }

        .chart-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }

        .chart-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 数据表格 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }

        .data-table tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .data-table .number {
            text-align: right;
            font-weight: 600;
        }

        .status-normal { color: #4caf50; font-weight: 600; }
        .status-warning { color: #ff9800; font-weight: 600; }
        .status-danger { color: #f44336; font-weight: 600; }

        /* 网络图容器 */
        .network-container {
            width: 100%;
            height: 500px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f8f9fa;
            position: relative;
            overflow: hidden;
        }

        .network-svg {
            width: 100%;
            height: 100%;
        }

        /* 断面图容器 */
        .section-container {
            width: 100%;
            height: 400px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: linear-gradient(180deg, #e3f2fd 0%, #f8f9fa 100%);
            position: relative;
            overflow: hidden;
        }

        /* 图例 */
        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 5px 10px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            font-size: 0.9rem;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
        }

        /* 预警信息 */
        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
        }

        .alert-danger {
            background: #ffebee;
            color: #c62828;
            border-left: 4px solid #f44336;
        }

        .alert-warning {
            background: #fff3e0;
            color: #ef6c00;
            border-left: 4px solid #ff9800;
        }

        .alert-info {
            background: #e3f2fd;
            color: #1565c0;
            border-left: 4px solid #2196f3;
        }

        .alert-success {
            background: #e8f5e8;
            color: #2e7d32;
            border-left: 4px solid #4caf50;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .chart-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .analysis-container {
                padding: 15px;
            }

            .main-title {
                font-size: 2rem;
                flex-direction: column;
                gap: 10px;
            }

            .analysis-tabs {
                flex-direction: column;
                align-items: center;
            }

            .input-grid {
                grid-template-columns: 1fr;
            }

            .btn-group {
                flex-direction: column;
                align-items: center;
            }
        }

        /* 加载动画 */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: #667eea;
            font-size: 1.1rem;
        }

        .loading i {
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 打印样式 */
        @media print {
            body {
                background: white;
            }
            
            .analysis-header,
            .btn-group,
            .analysis-tabs {
                display: none;
            }
            
            .content-area {
                background: white;
                box-shadow: none;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="analysis-container">
        <!-- 系统头部 -->
        <div class="analysis-header">
            <h1 class="main-title">
                <i class="fas fa-project-diagram"></i>
                管网分析系统
                <i class="fas fa-chart-line"></i>
            </h1>
            <p class="subtitle">Pipeline Network Analysis System - 爆管分析 · 连通性分析 · 断面分析</p>
            
            <div class="analysis-tabs">
                <button class="tab-btn active" data-tab="burst-analysis">
                    <i class="fas fa-exclamation-triangle"></i>
                    爆管分析
                </button>
                <button class="tab-btn" data-tab="connectivity-analysis">
                    <i class="fas fa-project-diagram"></i>
                    连通性分析
                </button>
                <button class="tab-btn" data-tab="section-analysis">
                    <i class="fas fa-cut"></i>
                    断面分析
                </button>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 爆管分析 -->
            <div id="burst-analysis" class="tab-content active">
                <div class="analysis-panel">
                    <div class="panel-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        爆管事件分析
                    </div>
                    
                    <div class="input-grid">
                        <div class="input-group">
                            <label class="input-label">
                                <i class="fas fa-map-marker-alt"></i>
                                爆管位置
                            </label>
                            <input type="text" class="input-field" id="burstLocation" placeholder="输入爆管位置" value="建设路与春华路交叉口">
                        </div>
                        
                        <div class="input-group">
                            <label class="input-label">
                                <i class="fas fa-ruler"></i>
                                管道规格
                            </label>
                            <select class="select-field" id="pipeSpec">
                                <option value="DN400">DN400</option>
                                <option value="DN500">DN500</option>
                                <option value="DN600">DN600</option>
                                <option value="DN800">DN800</option>
                            </select>
                        </div>
                        
                        <div class="input-group">
                            <label class="input-label">
                                <i class="fas fa-tachometer-alt"></i>
                                管道压力
                            </label>
                            <input type="number" class="input-field" id="pipePressure" placeholder="输入压力值" value="0.6" step="0.1">
                        </div>
                        
                        <div class="input-group">
                            <label class="input-label">
                                <i class="fas fa-clock"></i>
                                爆管时间
                            </label>
                            <input type="datetime-local" class="input-field" id="burstTime" value="2024-01-15T14:30">
                        </div>
                    </div>
                    
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="analyzeBurst()">
                            <i class="fas fa-search"></i>
                            开始分析
                        </button>
                        <button class="btn btn-success" onclick="generateValveMap()">
                            <i class="fas fa-map"></i>
                            生成关阀示意图
                        </button>
                        <button class="btn btn-warning" onclick="generateApplication()">
                            <i class="fas fa-file-alt"></i>
                            生成申请表
                        </button>
                        <button class="btn btn-danger" onclick="printResults()">
                            <i class="fas fa-print"></i>
                            打印结果
                        </button>
                    </div>
                </div>

                <div class="result-area" id="burstResults" style="display: none;">
                    <div class="result-title">
                        <i class="fas fa-chart-bar"></i>
                        爆管分析结果
                    </div>
                    
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <div>
                            <strong>紧急情况：</strong>建设路DN400主管爆管，影响用户约2,500户，需要立即关闭相关阀门并启动应急供水
                        </div>
                    </div>

                    <div class="chart-grid">
                        <div class="chart-card">
                            <div class="chart-header">
                                <div class="chart-title">
                                    <i class="fas fa-users"></i>
                                    影响范围分析
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="impactChart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <div class="chart-title">
                                    <i class="fas fa-stopwatch"></i>
                                    关阀时序分析
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="valveSequenceChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>阀门编号</th>
                                <th>阀门位置</th>
                                <th>阀门类型</th>
                                <th>操作顺序</th>
                                <th>预计时间</th>
                                <th>影响用户</th>
                                <th>操作状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>V-001</td>
                                <td>建设路北段</td>
                                <td>闸阀</td>
                                <td class="number">1</td>
                                <td>5分钟</td>
                                <td class="number">800户</td>
                                <td class="status-warning">待执行</td>
                            </tr>
                            <tr>
                                <td>V-003</td>
                                <td>春华路东段</td>
                                <td>蝶阀</td>
                                <td class="number">2</td>
                                <td>8分钟</td>
                                <td class="number">1,200户</td>
                                <td class="status-warning">待执行</td>
                            </tr>
                            <tr>
                                <td>V-007</td>
                                <td>交叉口西侧</td>
                                <td>闸阀</td>
                                <td class="number">3</td>
                                <td>12分钟</td>
                                <td class="number">500户</td>
                                <td class="status-warning">待执行</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 连通性分析 -->
            <div id="connectivity-analysis" class="tab-content">
                <div class="analysis-panel">
                    <div class="panel-title">
                        <i class="fas fa-project-diagram"></i>
                        管网连通性分析
                    </div>
                    
                    <div class="input-grid">
                        <div class="input-group">
                            <label class="input-label">
                                <i class="fas fa-map"></i>
                                分析区域
                            </label>
                            <select class="select-field" id="analysisArea">
                                <option value="全网">全网分析</option>
                                <option value="东区">东区管网</option>
                                <option value="西区">西区管网</option>
                                <option value="南区">南区管网</option>
                                <option value="北区">北区管网</option>
                            </select>
                        </div>
                        
                        <div class="input-group">
                            <label class="input-label">
                                <i class="fas fa-filter"></i>
                                管径过滤
                            </label>
                            <select class="select-field" id="diameterFilter">
                                <option value="all">全部管径</option>
                                <option value="main">主干管(≥DN400)</option>
                                <option value="branch">支管(DN200-DN300)</option>
                                <option value="service">服务管(≤DN150)</option>
                            </select>
                        </div>
                        
                        <div class="input-group">
                            <label class="input-label">
                                <i class="fas fa-cog"></i>
                                分析深度
                            </label>
                            <select class="select-field" id="analysisDepth">
                                <option value="basic">基础分析</option>
                                <option value="detailed">详细分析</option>
                                <option value="comprehensive">全面分析</option>
                            </select>
                        </div>
                        
                        <div class="input-group">
                            <label class="input-label">
                                <i class="fas fa-exclamation-triangle"></i>
                                故障模拟
                            </label>
                            <input type="text" class="input-field" id="faultSimulation" placeholder="输入故障管段编号">
                        </div>
                    </div>
                    
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="analyzeConnectivity()">
                            <i class="fas fa-play"></i>
                            开始分析
                        </button>
                        <button class="btn btn-success" onclick="exportConnectivityReport()">
                            <i class="fas fa-download"></i>
                            导出报告
                        </button>
                    </div>
                </div>

                <div class="result-area" id="connectivityResults" style="display: none;">
                    <div class="result-title">
                        <i class="fas fa-project-diagram"></i>
                        连通性分析结果
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <div>
                            <strong>分析完成：</strong>管网整体连通性良好，发现3个潜在薄弱环节，建议加强监控
                        </div>
                    </div>

                    <div class="chart-grid">
                        <div class="chart-card">
                            <div class="chart-header">
                                <div class="chart-title">
                                    <i class="fas fa-chart-pie"></i>
                                    连通性统计
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="connectivityStatsChart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <div class="chart-title">
                                    <i class="fas fa-network-wired"></i>
                                    管网拓扑图
                                </div>
                            </div>
                            <div class="network-container">
                                <svg class="network-svg" id="networkSvg"></svg>
                            </div>
                        </div>
                    </div>

                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>节点编号</th>
                                <th>节点类型</th>
                                <th>连接数量</th>
                                <th>冗余度</th>
                                <th>重要性等级</th>
                                <th>风险评估</th>
                                <th>建议措施</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>N-001</td>
                                <td>主干节点</td>
                                <td class="number">6</td>
                                <td class="number">85%</td>
                                <td class="status-danger">高</td>
                                <td class="status-normal">低风险</td>
                                <td>保持现状</td>
                            </tr>
                            <tr>
                                <td>N-015</td>
                                <td>分支节点</td>
                                <td class="number">3</td>
                                <td class="number">60%</td>
                                <td class="status-warning">中</td>
                                <td class="status-warning">中风险</td>
                                <td>增加备用连接</td>
                            </tr>
                            <tr>
                                <td>N-028</td>
                                <td>末端节点</td>
                                <td class="number">2</td>
                                <td class="number">40%</td>
                                <td class="status-normal">低</td>
                                <td class="status-danger">高风险</td>
                                <td>建设环状管网</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 断面分析 -->
            <div id="section-analysis" class="tab-content">
                <div class="analysis-panel">
                    <div class="panel-title">
                        <i class="fas fa-cut"></i>
                        管网断面分析
                    </div>
                    
                    <div class="input-grid">
                        <div class="input-group">
                            <label class="input-label">
                                <i class="fas fa-arrows-alt-h"></i>
                                分析类型
                            </label>
                            <select class="select-field" id="sectionType">
                                <option value="cross">横断面分析</option>
                                <option value="longitudinal">纵断面分析</option>
                            </select>
                        </div>
                        
                        <div class="input-group">
                            <label class="input-label">
                                <i class="fas fa-map-marker-alt"></i>
                                起始坐标
                            </label>
                            <input type="text" class="input-field" id="startCoord" placeholder="输入起始坐标" value="116.397,39.909">
                        </div>
                        
                        <div class="input-group">
                            <label class="input-label">
                                <i class="fas fa-map-marker-alt"></i>
                                结束坐标
                            </label>
                            <input type="text" class="input-field" id="endCoord" placeholder="输入结束坐标" value="116.407,39.919">
                        </div>
                        
                        <div class="input-group">
                            <label class="input-label">
                                <i class="fas fa-ruler"></i>
                                分析精度
                            </label>
                            <select class="select-field" id="analysisAccuracy">
                                <option value="high">高精度(1m)</option>
                                <option value="medium">中精度(5m)</option>
                                <option value="low">低精度(10m)</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="analyzeCrossSection()">
                            <i class="fas fa-search"></i>
                            横断面分析
                        </button>
                        <button class="btn btn-primary" onclick="analyzeLongitudinalSection()">
                            <i class="fas fa-search"></i>
                            纵断面分析
                        </button>
                        <button class="btn btn-success" onclick="exportSectionData()">
                            <i class="fas fa-download"></i>
                            导出数据
                        </button>
                    </div>
                </div>

                <div class="result-area" id="sectionResults" style="display: none;">
                    <div class="result-title">
                        <i class="fas fa-chart-area"></i>
                        断面分析结果
                    </div>
                    
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <div>
                            <strong>分析完成：</strong>断面分析已完成，发现5条管线，无冲突检测，埋深分布合理
                        </div>
                    </div>

                    <div class="chart-grid">
                        <div class="chart-card">
                            <div class="chart-header">
                                <div class="chart-title">
                                    <i class="fas fa-chart-bar"></i>
                                    管径分布统计
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="diameterDistChart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <div class="chart-title">
                                    <i class="fas fa-layer-group"></i>
                                    埋深分布图
                                </div>
                            </div>
                            <div class="section-container">
                                <svg class="network-svg" id="sectionSvg"></svg>
                            </div>
                        </div>
                    </div>

                    <div class="legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background: #2196f3;"></div>
                            <span>给水管道</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #4caf50;"></div>
                            <span>排水管道</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #ff9800;"></div>
                            <span>燃气管道</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #9c27b0;"></div>
                            <span>电力管道</span>
                        </div>
                    </div>

                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>管线编号</th>
                                <th>管线类型</th>
                                <th>管径(mm)</th>
                                <th>埋深(m)</th>
                                <th>材质</th>
                                <th>建设年代</th>
                                <th>状态评估</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>P-001</td>
                                <td>给水主管</td>
                                <td class="number">600</td>
                                <td class="number">2.5</td>
                                <td>球墨铸铁</td>
                                <td>2015</td>
                                <td class="status-normal">良好</td>
                            </tr>
                            <tr>
                                <td>P-002</td>
                                <td>给水支管</td>
                                <td class="number">300</td>
                                <td class="number">1.8</td>
                                <td>PE管</td>
                                <td>2018</td>
                                <td class="status-normal">良好</td>
                            </tr>
                            <tr>
                                <td>P-003</td>
                                <td>排水管</td>
                                <td class="number">400</td>
                                <td class="number">3.2</td>
                                <td>混凝土</td>
                                <td>2010</td>
                                <td class="status-warning">一般</td>
                            </tr>
                            <tr>
                                <td>P-004</td>
                                <td>燃气管</td>
                                <td class="number">200</td>
                                <td class="number">1.2</td>
                                <td>PE管</td>
                                <td>2020</td>
                                <td class="status-normal">优秀</td>
                            </tr>
                            <tr>
                                <td>P-005</td>
                                <td>电力管</td>
                                <td class="number">150</td>
                                <td class="number">0.8</td>
                                <td>PVC管</td>
                                <td>2019</td>
                                <td class="status-normal">良好</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTab = 'burst-analysis';
        let charts = {};

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('管网分析系统初始化...');
            initTabEvents();
            console.log('系统初始化完成');
        });

        // 初始化标签页事件
        function initTabEvents() {
            const tabs = document.querySelectorAll('.tab-btn');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    switchTab(tabId, this);
                });
            });
        }

        // 标签页切换
        function switchTab(tabId, clickedTab) {
            currentTab = tabId;
            
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有激活状态
            document.querySelectorAll('.tab-btn').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中内容
            const targetContent = document.getElementById(tabId);
            if (targetContent) {
                targetContent.classList.add('active');
            }
            
            // 激活选中标签
            if (clickedTab) {
                clickedTab.classList.add('active');
            }
        }

        // 爆管分析
        function analyzeBurst() {
            showNotification('正在进行爆管分析...', 'info');
            
            setTimeout(() => {
                document.getElementById('burstResults').style.display = 'block';
                initBurstCharts();
                showNotification('爆管分析完成！', 'success');
            }, 2000);
        }

        // 初始化爆管分析图表
        function initBurstCharts() {
            // 影响范围图表
            const impactCtx = document.getElementById('impactChart');
            if (impactCtx && !Chart.getChart(impactCtx)) {
                charts.impact = new Chart(impactCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['停水用户', '降压用户', '正常用户'],
                        datasets: [{
                            data: [2500, 800, 15700],
                            backgroundColor: ['#f44336', '#ff9800', '#4caf50'],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { position: 'bottom' }
                        }
                    }
                });
            }

            // 关阀时序图表
            const sequenceCtx = document.getElementById('valveSequenceChart');
            if (sequenceCtx && !Chart.getChart(sequenceCtx)) {
                charts.sequence = new Chart(sequenceCtx, {
                    type: 'bar',
                    data: {
                        labels: ['V-001', 'V-003', 'V-007'],
                        datasets: [{
                            label: '关阀时间(分钟)',
                            data: [5, 8, 12],
                            backgroundColor: ['#2196f3', '#ff9800', '#4caf50'],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: { display: true, text: '时间(分钟)' }
                            }
                        }
                    }
                });
            }
        }

        // 生成关阀示意图
        function generateValveMap() {
            showNotification('正在生成关阀示意图...', 'info');
            setTimeout(() => {
                showNotification('关阀示意图已生成！', 'success');
            }, 1500);
        }

        // 生成申请表
        function generateApplication() {
            showNotification('正在生成开关阀申请表...', 'info');
            setTimeout(() => {
                showNotification('申请表已生成！', 'success');
            }, 1500);
        }

        // 连通性分析
        function analyzeConnectivity() {
            showNotification('正在进行连通性分析...', 'info');
            
            setTimeout(() => {
                document.getElementById('connectivityResults').style.display = 'block';
                initConnectivityCharts();
                drawNetworkDiagram();
                showNotification('连通性分析完成！', 'success');
            }, 3000);
        }

        // 初始化连通性分析图表
        function initConnectivityCharts() {
            const statsCtx = document.getElementById('connectivityStatsChart');
            if (statsCtx && !Chart.getChart(statsCtx)) {
                charts.connectivityStats = new Chart(statsCtx, {
                    type: 'pie',
                    data: {
                        labels: ['高冗余节点', '中冗余节点', '低冗余节点', '薄弱节点'],
                        datasets: [{
                            data: [45, 35, 15, 5],
                            backgroundColor: ['#4caf50', '#2196f3', '#ff9800', '#f44336'],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { position: 'bottom' }
                        }
                    }
                });
            }
        }

        // 绘制网络拓扑图
        function drawNetworkDiagram() {
            const svg = d3.select('#networkSvg');
            svg.selectAll('*').remove();
            
            const width = 400;
            const height = 300;
            
            // 模拟网络节点数据
            const nodes = [
                {id: 'N1', x: 100, y: 100, type: 'main'},
                {id: 'N2', x: 200, y: 80, type: 'branch'},
                {id: 'N3', x: 300, y: 120, type: 'end'},
                {id: 'N4', x: 150, y: 180, type: 'branch'},
                {id: 'N5', x: 250, y: 200, type: 'end'}
            ];
            
            const links = [
                {source: 'N1', target: 'N2'},
                {source: 'N2', target: 'N3'},
                {source: 'N1', target: 'N4'},
                {source: 'N4', target: 'N5'},
                {source: 'N2', target: 'N4'}
            ];
            
            // 绘制连接线
            svg.selectAll('line')
                .data(links)
                .enter()
                .append('line')
                .attr('x1', d => nodes.find(n => n.id === d.source).x)
                .attr('y1', d => nodes.find(n => n.id === d.source).y)
                .attr('x2', d => nodes.find(n => n.id === d.target).x)
                .attr('y2', d => nodes.find(n => n.id === d.target).y)
                .attr('stroke', '#667eea')
                .attr('stroke-width', 3);
            
            // 绘制节点
            svg.selectAll('circle')
                .data(nodes)
                .enter()
                .append('circle')
                .attr('cx', d => d.x)
                .attr('cy', d => d.y)
                .attr('r', d => d.type === 'main' ? 12 : d.type === 'branch' ? 8 : 6)
                .attr('fill', d => d.type === 'main' ? '#f44336' : d.type === 'branch' ? '#ff9800' : '#4caf50')
                .attr('stroke', '#fff')
                .attr('stroke-width', 2);
            
            // 添加节点标签
            svg.selectAll('text')
                .data(nodes)
                .enter()
                .append('text')
                .attr('x', d => d.x)
                .attr('y', d => d.y + 25)
                .attr('text-anchor', 'middle')
                .attr('font-size', '12px')
                .attr('fill', '#333')
                .text(d => d.id);
        }

        // 横断面分析
        function analyzeCrossSection() {
            showNotification('正在进行横断面分析...', 'info');
            
            setTimeout(() => {
                document.getElementById('sectionResults').style.display = 'block';
                initSectionCharts();
                drawCrossSectionDiagram();
                showNotification('横断面分析完成！', 'success');
            }, 2500);
        }

        // 纵断面分析
        function analyzeLongitudinalSection() {
            showNotification('正在进行纵断面分析...', 'info');
            
            setTimeout(() => {
                document.getElementById('sectionResults').style.display = 'block';
                initSectionCharts();
                drawLongitudinalSectionDiagram();
                showNotification('纵断面分析完成！', 'success');
            }, 2500);
        }

        // 初始化断面分析图表
        function initSectionCharts() {
            const diameterCtx = document.getElementById('diameterDistChart');
            if (diameterCtx && !Chart.getChart(diameterCtx)) {
                charts.diameterDist = new Chart(diameterCtx, {
                    type: 'bar',
                    data: {
                        labels: ['DN150', 'DN200', 'DN300', 'DN400', 'DN600'],
                        datasets: [{
                            label: '管线数量',
                            data: [1, 1, 1, 1, 1],
                            backgroundColor: ['#9c27b0', '#ff9800', '#4caf50', '#4caf50', '#2196f3'],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: { display: true, text: '数量' }
                            }
                        }
                    }
                });
            }
        }

        // 绘制横断面图
        function drawCrossSectionDiagram() {
            const svg = d3.select('#sectionSvg');
            svg.selectAll('*').remove();
            
            const width = 400;
            const height = 300;
            
            // 模拟管线数据
            const pipes = [
                {name: 'P-001', x: 50, y: 200, width: 30, height: 15, color: '#2196f3'},
                {name: 'P-002', x: 120, y: 220, width: 20, height: 10, color: '#2196f3'},
                {name: 'P-003', x: 180, y: 180, width: 25, height: 12, color: '#4caf50'},
                {name: 'P-004', x: 250, y: 240, width: 15, height: 8, color: '#ff9800'},
                {name: 'P-005', x: 320, y: 250, width: 12, height: 6, color: '#9c27b0'}
            ];
            
            // 绘制地面线
            svg.append('line')
                .attr('x1', 0)
                .attr('y1', 50)
                .attr('x2', width)
                .attr('y2', 50)
                .attr('stroke', '#8bc34a')
                .attr('stroke-width', 3);
            
            // 绘制管线
            svg.selectAll('rect')
                .data(pipes)
                .enter()
                .append('rect')
                .attr('x', d => d.x)
                .attr('y', d => d.y)
                .attr('width', d => d.width)
                .attr('height', d => d.height)
                .attr('fill', d => d.color)
                .attr('stroke', '#333')
                .attr('stroke-width', 1);
            
            // 添加管线标签
            svg.selectAll('text')
                .data(pipes)
                .enter()
                .append('text')
                .attr('x', d => d.x + d.width/2)
                .attr('y', d => d.y - 5)
                .attr('text-anchor', 'middle')
                .attr('font-size', '10px')
                .attr('fill', '#333')
                .text(d => d.name);
        }

        // 绘制纵断面图
        function drawLongitudinalSectionDiagram() {
            const svg = d3.select('#sectionSvg');
            svg.selectAll('*').remove();
            
            const width = 400;
            const height = 300;
            
            // 绘制地形线
            const terrainData = [
                {x: 0, y: 100}, {x: 100, y: 95}, {x: 200, y: 110}, 
                {x: 300, y: 105}, {x: 400, y: 120}
            ];
            
            const line = d3.line()
                .x(d => d.x)
                .y(d => d.y);
            
            svg.append('path')
                .datum(terrainData)
                .attr('fill', 'none')
                .attr('stroke', '#8bc34a')
                .attr('stroke-width', 3)
                .attr('d', line);
            
            // 绘制管线
            const pipeData = [
                {x: 0, y: 180}, {x: 100, y: 175}, {x: 200, y: 185}, 
                {x: 300, y: 180}, {x: 400, y: 190}
            ];
            
            svg.append('path')
                .datum(pipeData)
                .attr('fill', 'none')
                .attr('stroke', '#2196f3')
                .attr('stroke-width', 8)
                .attr('d', line);
            
            // 添加标注
            svg.append('text')
                .attr('x', 20)
                .attr('y', 90)
                .attr('font-size', '12px')
                .attr('fill', '#8bc34a')
                .text('地面线');
            
            svg.append('text')
                .attr('x', 20)
                .attr('y', 170)
                .attr('font-size', '12px')
                .attr('fill', '#2196f3')
                .text('管道中心线');
        }

        // 导出连通性报告
        function exportConnectivityReport() {
            showNotification('正在导出连通性分析报告...', 'info');
            setTimeout(() => {
                showNotification('报告导出成功！', 'success');
            }, 1500);
        }

        // 导出断面数据
        function exportSectionData() {
            showNotification('正在导出断面分析数据...', 'info');
            setTimeout(() => {
                showNotification('数据导出成功！', 'success');
            }, 1500);
        }

        // 打印结果
        function printResults() {
            showNotification('正在准备打印...', 'info');
            setTimeout(() => {
                window.print();
            }, 1000);
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${getNotificationColor(type)};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                z-index: 10000;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                max-width: 350px;
                animation: slideInRight 0.3s ease-out;
            `;
            
            const icon = getNotificationIcon(type);
            notification.innerHTML = `<i class="${icon}" style="margin-right: 8px;"></i>${message}`;
            
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-out';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 获取通知颜色
        function getNotificationColor(type) {
            const colors = {
                'success': '#4caf50',
                'error': '#f44336',
                'warning': '#ff9800',
                'info': '#2196f3'
            };
            return colors[type] || colors.info;
        }

        // 获取通知图标
        function getNotificationIcon(type) {
            const icons = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };
            return icons[type] || icons.info;
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
