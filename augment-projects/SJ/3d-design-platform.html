<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三维设计平台 - 多格式3D模型管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #333;
            overflow: hidden;
            height: 100vh;
        }

        /* 顶部导航栏 */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            position: relative;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.5rem;
            font-weight: bold;
            color: #2a5298;
        }

        .logo i {
            margin-right: 10px;
            font-size: 2rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-menu li {
            position: relative;
        }

        .nav-menu a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            padding: 10px 15px;
            border-radius: 6px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-menu a:hover {
            background: rgba(42, 82, 152, 0.1);
            color: #2a5298;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: linear-gradient(45deg, #2a5298, #1e3c72);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        /* 主要内容区域 */
        .main-container {
            display: flex;
            height: calc(100vh - 60px);
        }

        /* 左侧工具栏 */
        .left-toolbar {
            width: 80px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px 0;
            gap: 15px;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        }

        .tool-button {
            width: 50px;
            height: 50px;
            border: none;
            border-radius: 10px;
            background: rgba(42, 82, 152, 0.1);
            color: #2a5298;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .tool-button:hover {
            background: #2a5298;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(42, 82, 152, 0.3);
        }

        .tool-button.active {
            background: #2a5298;
            color: white;
        }

        /* 工具提示 */
        .tool-button::after {
            content: attr(data-tooltip);
            position: absolute;
            left: 70px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.8rem;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }

        .tool-button:hover::after {
            opacity: 1;
        }

        /* 中央3D视窗 */
        .viewport {
            flex: 1;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .model-viewer {
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .model-placeholder {
            text-align: center;
            color: rgba(42, 82, 152, 0.6);
        }

        .model-placeholder i {
            font-size: 4rem;
            margin-bottom: 20px;
            display: block;
        }

        .model-placeholder h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .model-placeholder p {
            font-size: 1rem;
            opacity: 0.8;
        }

        /* 视窗控制器 */
        .viewport-controls {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .control-button {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
            color: #2a5298;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .control-button:hover {
            background: #2a5298;
            color: white;
            transform: scale(1.1);
        }

        /* 右侧面板 */
        .right-panel {
            width: 350px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-left: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
        }

        .panel-tabs {
            display: flex;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .tab-button {
            flex: 1;
            padding: 15px;
            border: none;
            background: transparent;
            color: #666;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-button.active {
            color: #2a5298;
            border-bottom-color: #2a5298;
            background: rgba(42, 82, 152, 0.05);
        }

        .tab-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .tab-panel {
            display: none;
        }

        .tab-panel.active {
            display: block;
        }

        /* 底部状态栏 */
        .status-bar {
            height: 40px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            font-size: 0.85rem;
            color: #666;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4caf50;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .right-panel {
                width: 300px;
            }

            .modal-content {
                width: 95%;
                margin: 20px;
            }

            .performance-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .header {
                padding: 0 15px;
            }

            .nav-menu {
                display: none;
            }

            .logo {
                font-size: 1.2rem;
            }

            .left-toolbar {
                width: 60px;
                padding: 15px 0;
                gap: 10px;
            }

            .tool-button {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }

            .right-panel {
                width: 280px;
            }

            .tab-content {
                padding: 15px;
            }

            .status-bar {
                padding: 0 15px;
                font-size: 0.8rem;
            }

            .status-left, .status-right {
                gap: 10px;
            }

            .modal-content {
                width: 95%;
                max-height: 90vh;
            }

            .modal-header {
                padding: 15px 20px;
            }

            .modal-body {
                padding: 20px;
            }

            .performance-grid {
                grid-template-columns: 1fr 1fr;
                gap: 10px;
            }

            .compatibility-list {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .main-container {
                flex-direction: column;
            }

            .left-toolbar {
                width: 100%;
                height: 60px;
                flex-direction: row;
                justify-content: center;
                padding: 10px 0;
                order: 2;
            }

            .viewport {
                order: 1;
                height: 300px;
            }

            .right-panel {
                width: 100%;
                order: 3;
                height: 400px;
            }

            .tool-button {
                width: 35px;
                height: 35px;
                font-size: 0.9rem;
            }

            .format-grid, .stats-grid {
                grid-template-columns: 1fr;
            }

            .location-inputs {
                grid-template-columns: 1fr;
            }

            .upload-actions {
                flex-direction: column;
            }

            .performance-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 增强的交互效果 */
        .panel {
            transition: all 0.3s ease;
        }

        .panel:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .tab-button {
            position: relative;
            overflow: hidden;
        }

        .tab-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(42, 82, 152, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .tab-button:hover::before {
            left: 100%;
        }

        /* 滚动条美化 */
        .tab-content::-webkit-scrollbar,
        .document-list::-webkit-scrollbar,
        .results-list::-webkit-scrollbar,
        .modal-body::-webkit-scrollbar {
            width: 6px;
        }

        .tab-content::-webkit-scrollbar-track,
        .document-list::-webkit-scrollbar-track,
        .results-list::-webkit-scrollbar-track,
        .modal-body::-webkit-scrollbar-track {
            background: rgba(42, 82, 152, 0.1);
            border-radius: 3px;
        }

        .tab-content::-webkit-scrollbar-thumb,
        .document-list::-webkit-scrollbar-thumb,
        .results-list::-webkit-scrollbar-thumb,
        .modal-body::-webkit-scrollbar-thumb {
            background: rgba(42, 82, 152, 0.3);
            border-radius: 3px;
        }

        .tab-content::-webkit-scrollbar-thumb:hover,
        .document-list::-webkit-scrollbar-thumb:hover,
        .results-list::-webkit-scrollbar-thumb:hover,
        .modal-body::-webkit-scrollbar-thumb:hover {
            background: rgba(42, 82, 152, 0.5);
        }

        /* 加载状态动画 */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        /* 成功状态动画 */
        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .success-animation {
            animation: successPulse 0.6s ease-in-out;
        }

        /* 错误状态动画 */
        @keyframes errorShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .error-animation {
            animation: errorShake 0.5s ease-in-out;
        }

        /* 暗色主题支持 */
        @media (prefers-color-scheme: dark) {
            body {
                background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            }

            .header, .left-toolbar, .right-panel, .status-bar {
                background: rgba(30, 30, 50, 0.95);
                color: #e0e0e0;
            }

            .modal-content {
                background: #2a2a3e;
                color: #e0e0e0;
            }
        }

        /* 格式支持面板样式 */
        .format-category {
            margin-bottom: 25px;
        }

        .category-title {
            font-size: 0.95rem;
            color: #2a5298;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
        }

        .format-grid {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .format-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background: rgba(42, 82, 152, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(42, 82, 152, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .format-item:hover {
            background: rgba(42, 82, 152, 0.1);
            border-color: rgba(42, 82, 152, 0.2);
            transform: translateY(-1px);
        }

        .format-icon {
            width: 35px;
            height: 35px;
            border-radius: 6px;
            background: linear-gradient(45deg, #2a5298, #1e3c72);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
            margin-right: 12px;
        }

        .format-info {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .format-name {
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }

        .format-ext {
            font-size: 0.8rem;
            color: #666;
            margin-top: 2px;
        }

        .format-status {
            font-size: 1rem;
        }

        /* 导入统计样式 */
        .import-stats {
            background: rgba(42, 82, 152, 0.05);
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            border: 1px solid rgba(42, 82, 152, 0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 10px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            display: block;
            font-size: 1.5rem;
            font-weight: bold;
            color: #2a5298;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #666;
        }

        /* 导入按钮样式 */
        .import-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .import-btn {
            flex: 1;
            padding: 12px 16px;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .import-btn.primary {
            background: #2a5298;
            color: white;
        }

        .import-btn.primary:hover {
            background: #1e3c72;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(42, 82, 152, 0.3);
        }

        .import-btn.secondary {
            background: rgba(42, 82, 152, 0.1);
            color: #2a5298;
            border: 1px solid rgba(42, 82, 152, 0.2);
        }

        .import-btn.secondary:hover {
            background: rgba(42, 82, 152, 0.15);
            border-color: rgba(42, 82, 152, 0.3);
        }

        /* 文档管理面板样式 */
        .upload-area {
            margin-bottom: 25px;
        }

        .upload-zone {
            border: 2px dashed rgba(42, 82, 152, 0.3);
            border-radius: 10px;
            padding: 30px 20px;
            text-align: center;
            background: rgba(42, 82, 152, 0.02);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-zone:hover {
            border-color: rgba(42, 82, 152, 0.5);
            background: rgba(42, 82, 152, 0.05);
        }

        .upload-zone.dragover {
            border-color: #2a5298;
            background: rgba(42, 82, 152, 0.1);
        }

        .upload-zone i {
            font-size: 2.5rem;
            color: rgba(42, 82, 152, 0.6);
            margin-bottom: 15px;
            display: block;
        }

        .upload-zone p {
            margin: 5px 0;
            color: #666;
        }

        .upload-hint {
            font-size: 0.8rem;
            color: #999 !important;
        }

        .upload-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .upload-btn {
            flex: 1;
            padding: 10px 15px;
            border: none;
            border-radius: 6px;
            background: #2a5298;
            color: white;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .upload-btn:hover {
            background: #1e3c72;
            transform: translateY(-1px);
        }

        .upload-btn.batch {
            background: rgba(42, 82, 152, 0.1);
            color: #2a5298;
            border: 1px solid rgba(42, 82, 152, 0.2);
        }

        .upload-btn.batch:hover {
            background: rgba(42, 82, 152, 0.15);
        }

        /* 文档搜索样式 */
        .doc-search {
            margin-bottom: 20px;
        }

        .search-input-group {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-input-group i {
            position: absolute;
            left: 12px;
            color: #999;
            z-index: 1;
        }

        .search-input {
            flex: 1;
            padding: 10px 12px 10px 35px;
            border: 1px solid rgba(42, 82, 152, 0.2);
            border-radius: 6px 0 0 6px;
            font-size: 0.9rem;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            border-color: #2a5298;
        }

        .search-btn {
            padding: 10px 15px;
            border: 1px solid rgba(42, 82, 152, 0.2);
            border-left: none;
            border-radius: 0 6px 6px 0;
            background: rgba(42, 82, 152, 0.05);
            color: #2a5298;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-btn:hover {
            background: rgba(42, 82, 152, 0.1);
        }

        /* 文档列表样式 */
        .document-list {
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 20px;
        }

        .doc-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 8px;
            background: rgba(42, 82, 152, 0.02);
            border: 1px solid rgba(42, 82, 152, 0.1);
            transition: all 0.3s ease;
        }

        .doc-item:hover {
            background: rgba(42, 82, 152, 0.05);
            border-color: rgba(42, 82, 152, 0.2);
            transform: translateY(-1px);
        }

        .doc-icon {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            margin-right: 12px;
        }

        .doc-info {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .doc-name {
            font-weight: 500;
            color: #333;
            font-size: 0.9rem;
            margin-bottom: 3px;
        }

        .doc-meta {
            font-size: 0.75rem;
            color: #999;
        }

        .doc-actions {
            display: flex;
            gap: 5px;
        }

        .doc-action-btn {
            width: 30px;
            height: 30px;
            border: none;
            border-radius: 4px;
            background: rgba(42, 82, 152, 0.1);
            color: #2a5298;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .doc-action-btn:hover {
            background: #2a5298;
            color: white;
            transform: scale(1.1);
        }

        /* 文档统计样式 */
        .doc-stats {
            background: rgba(42, 82, 152, 0.05);
            border-radius: 8px;
            padding: 15px;
            border: 1px solid rgba(42, 82, 152, 0.1);
        }

        .stats-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .stats-row:last-child {
            margin-bottom: 0;
        }

        .stats-label {
            font-size: 0.85rem;
            color: #666;
        }

        .stats-value {
            font-size: 0.85rem;
            font-weight: 600;
            color: #2a5298;
        }

        /* 搜索浏览面板样式 */
        .advanced-search {
            background: rgba(42, 82, 152, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
            border: 1px solid rgba(42, 82, 152, 0.1);
        }

        .search-group {
            margin-bottom: 15px;
        }

        .search-group:last-child {
            margin-bottom: 0;
        }

        .search-group label {
            display: block;
            font-size: 0.85rem;
            color: #2a5298;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .search-select,
        .search-input-field {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid rgba(42, 82, 152, 0.2);
            border-radius: 6px;
            font-size: 0.9rem;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .search-select:focus,
        .search-input-field:focus {
            border-color: #2a5298;
        }

        .location-inputs {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 8px;
        }

        .coord-input {
            padding: 8px 10px;
            border: 1px solid rgba(42, 82, 152, 0.2);
            border-radius: 6px;
            font-size: 0.85rem;
            text-align: center;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .coord-input:focus {
            border-color: #2a5298;
        }

        .search-execute-btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            background: #2a5298;
            color: white;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-top: 15px;
        }

        .search-execute-btn:hover {
            background: #1e3c72;
            transform: translateY(-1px);
        }

        /* 搜索结果样式 */
        .search-results {
            margin-bottom: 25px;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(42, 82, 152, 0.1);
        }

        .results-count {
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
        }

        .view-options {
            display: flex;
            gap: 5px;
        }

        .view-btn {
            width: 30px;
            height: 30px;
            border: 1px solid rgba(42, 82, 152, 0.2);
            border-radius: 4px;
            background: transparent;
            color: #2a5298;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .view-btn.active,
        .view-btn:hover {
            background: #2a5298;
            color: white;
        }

        .results-list {
            max-height: 250px;
            overflow-y: auto;
        }

        .result-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 8px;
            background: rgba(42, 82, 152, 0.02);
            border: 1px solid rgba(42, 82, 152, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .result-item:hover {
            background: rgba(42, 82, 152, 0.05);
            border-color: rgba(42, 82, 152, 0.2);
            transform: translateY(-1px);
        }

        .result-icon {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            margin-right: 12px;
        }

        .result-info {
            flex: 1;
        }

        .result-name {
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .result-details {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .detail-item {
            font-size: 0.75rem;
            color: #666;
            background: rgba(42, 82, 152, 0.1);
            padding: 2px 6px;
            border-radius: 3px;
        }

        .result-actions {
            display: flex;
            gap: 5px;
        }

        .result-action-btn {
            width: 28px;
            height: 28px;
            border: none;
            border-radius: 4px;
            background: rgba(42, 82, 152, 0.1);
            color: #2a5298;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .result-action-btn:hover {
            background: #2a5298;
            color: white;
            transform: scale(1.1);
        }

        /* 快速过滤样式 */
        .quick-filters {
            margin-bottom: 20px;
        }

        .filter-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .filter-tag {
            padding: 6px 12px;
            border-radius: 15px;
            background: rgba(42, 82, 152, 0.1);
            color: #2a5298;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(42, 82, 152, 0.2);
        }

        .filter-tag.active,
        .filter-tag:hover {
            background: #2a5298;
            color: white;
            border-color: #2a5298;
        }

        /* 属性面板样式 */
        .property-panel {
            background: rgba(42, 82, 152, 0.05);
            border-radius: 10px;
            padding: 15px;
            border: 1px solid rgba(42, 82, 152, 0.1);
            margin-top: 20px;
        }

        .property-content {
            font-size: 0.85rem;
            line-height: 1.6;
        }

        /* 状态栏增强样式 */
        .tech-info, .compression-info, .memory-info, .fps-info, .version-info {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .tech-details-btn {
            width: 30px;
            height: 30px;
            border: 1px solid rgba(42, 82, 152, 0.3);
            border-radius: 4px;
            background: rgba(42, 82, 152, 0.1);
            color: #2a5298;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .tech-details-btn:hover {
            background: #2a5298;
            color: white;
        }

        /* 技术详情弹窗样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .modal-header {
            background: linear-gradient(135deg, #2a5298, #1e3c72);
            color: white;
            padding: 20px 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s ease;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 25px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .tech-section {
            margin-bottom: 25px;
        }

        .tech-section h4 {
            color: #2a5298;
            font-size: 1.1rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .tech-section ul {
            list-style: none;
            padding: 0;
        }

        .tech-section li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(42, 82, 152, 0.1);
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .tech-section li:last-child {
            border-bottom: none;
        }

        .performance-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .perf-item {
            background: rgba(42, 82, 152, 0.05);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            border: 1px solid rgba(42, 82, 152, 0.1);
        }

        .perf-label {
            display: block;
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 5px;
        }

        .perf-value {
            display: block;
            font-size: 1.5rem;
            font-weight: bold;
            color: #2a5298;
        }

        .compatibility-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .compat-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 15px;
            background: rgba(42, 82, 152, 0.05);
            border-radius: 6px;
            border: 1px solid rgba(42, 82, 152, 0.1);
        }

        .compat-item i {
            color: #2a5298;
            margin-right: 8px;
        }

        .compat-status {
            color: #4caf50;
            font-weight: bold;
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(42, 82, 152, 0.3);
            border-radius: 50%;
            border-top-color: #2a5298;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="logo">
            <i class="fas fa-cube"></i>
            <span>三维设计平台</span>
        </div>
        
        <nav>
            <ul class="nav-menu">
                <li><a href="#"><i class="fas fa-home"></i>首页</a></li>
                <li><a href="#"><i class="fas fa-project-diagram"></i>项目管理</a></li>
                <li><a href="#"><i class="fas fa-upload"></i>模型导入</a></li>
                <li><a href="#"><i class="fas fa-folder"></i>文档管理</a></li>
                <li><a href="#"><i class="fas fa-cog"></i>设置</a></li>
            </ul>
        </nav>
        
        <div class="user-info">
            <span>管理员</span>
            <div class="user-avatar">A</div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="main-container">
        <!-- 左侧工具栏 -->
        <div class="left-toolbar">
            <button class="tool-button active" data-tooltip="选择工具">
                <i class="fas fa-mouse-pointer"></i>
            </button>
            <button class="tool-button" data-tooltip="旋转视图">
                <i class="fas fa-redo"></i>
            </button>
            <button class="tool-button" data-tooltip="缩放">
                <i class="fas fa-search-plus"></i>
            </button>
            <button class="tool-button" data-tooltip="平移">
                <i class="fas fa-arrows-alt"></i>
            </button>
            <button class="tool-button" data-tooltip="剖切">
                <i class="fas fa-cut"></i>
            </button>
            <button class="tool-button" data-tooltip="隔离">
                <i class="fas fa-eye"></i>
            </button>
            <button class="tool-button" data-tooltip="测量">
                <i class="fas fa-ruler"></i>
            </button>
            <button class="tool-button" data-tooltip="标注">
                <i class="fas fa-comment"></i>
            </button>
            <button class="tool-button" data-tooltip="截图">
                <i class="fas fa-camera"></i>
            </button>
        </div>

        <!-- 中央3D视窗 -->
        <div class="viewport">
            <div class="model-viewer">
                <div class="model-placeholder">
                    <i class="fas fa-cube"></i>
                    <h3>3D模型视窗</h3>
                    <p>支持多种格式的三维模型展示</p>
                    <p>拖拽模型文件到此处或点击导入按钮</p>
                </div>
            </div>
            
            <!-- 视窗控制器 -->
            <div class="viewport-controls">
                <button class="control-button" title="适应窗口">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="control-button" title="重置视图">
                    <i class="fas fa-home"></i>
                </button>
                <button class="control-button" title="全屏">
                    <i class="fas fa-expand-arrows-alt"></i>
                </button>
            </div>
        </div>

        <!-- 右侧面板 -->
        <div class="right-panel">
            <div class="panel-tabs">
                <button class="tab-button active" data-tab="formats">格式支持</button>
                <button class="tab-button" data-tab="documents">文档管理</button>
                <button class="tab-button" data-tab="search">搜索浏览</button>
            </div>
            
            <div class="tab-content">
                <!-- 格式支持面板 -->
                <div class="tab-panel active" id="formats-panel">
                    <h4 style="margin-bottom: 20px; color: #2a5298; font-size: 1.1rem;">
                        <i class="fas fa-file-import"></i> 支持的模型格式
                    </h4>

                    <!-- BIM软件格式 -->
                    <div class="format-category">
                        <h5 class="category-title">
                            <i class="fas fa-building"></i> BIM软件格式
                        </h5>
                        <div class="format-grid">
                            <div class="format-item" data-format="revit">
                                <div class="format-icon">
                                    <i class="fas fa-cube"></i>
                                </div>
                                <div class="format-info">
                                    <span class="format-name">Revit</span>
                                    <span class="format-ext">.rvt, .rfa</span>
                                </div>
                                <div class="format-status">
                                    <i class="fas fa-check-circle" style="color: #4caf50;"></i>
                                </div>
                            </div>

                            <div class="format-item" data-format="ifc">
                                <div class="format-icon">
                                    <i class="fas fa-sitemap"></i>
                                </div>
                                <div class="format-info">
                                    <span class="format-name">IFC</span>
                                    <span class="format-ext">.ifc</span>
                                </div>
                                <div class="format-status">
                                    <i class="fas fa-check-circle" style="color: #4caf50;"></i>
                                </div>
                            </div>

                            <div class="format-item" data-format="pdms">
                                <div class="format-icon">
                                    <i class="fas fa-industry"></i>
                                </div>
                                <div class="format-info">
                                    <span class="format-name">PDMS</span>
                                    <span class="format-ext">.pdms</span>
                                </div>
                                <div class="format-status">
                                    <i class="fas fa-check-circle" style="color: #4caf50;"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- CAD软件格式 -->
                    <div class="format-category">
                        <h5 class="category-title">
                            <i class="fas fa-drafting-compass"></i> CAD软件格式
                        </h5>
                        <div class="format-grid">
                            <div class="format-item" data-format="solidworks">
                                <div class="format-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="format-info">
                                    <span class="format-name">SolidWorks</span>
                                    <span class="format-ext">.sldprt, .sldasm</span>
                                </div>
                                <div class="format-status">
                                    <i class="fas fa-check-circle" style="color: #4caf50;"></i>
                                </div>
                            </div>

                            <div class="format-item" data-format="proe">
                                <div class="format-icon">
                                    <i class="fas fa-tools"></i>
                                </div>
                                <div class="format-info">
                                    <span class="format-name">Pro/E</span>
                                    <span class="format-ext">.prt, .asm</span>
                                </div>
                                <div class="format-status">
                                    <i class="fas fa-check-circle" style="color: #4caf50;"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 通用3D格式 -->
                    <div class="format-category">
                        <h5 class="category-title">
                            <i class="fas fa-shapes"></i> 通用3D格式
                        </h5>
                        <div class="format-grid">
                            <div class="format-item" data-format="3ds">
                                <div class="format-icon">
                                    <i class="fas fa-cube"></i>
                                </div>
                                <div class="format-info">
                                    <span class="format-name">3DS Max</span>
                                    <span class="format-ext">.3ds, .max</span>
                                </div>
                                <div class="format-status">
                                    <i class="fas fa-check-circle" style="color: #4caf50;"></i>
                                </div>
                            </div>

                            <div class="format-item" data-format="3dxml">
                                <div class="format-icon">
                                    <i class="fas fa-file-code"></i>
                                </div>
                                <div class="format-info">
                                    <span class="format-name">3DXML</span>
                                    <span class="format-ext">.3dxml</span>
                                </div>
                                <div class="format-status">
                                    <i class="fas fa-check-circle" style="color: #4caf50;"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 导入统计 -->
                    <div class="import-stats">
                        <h5 style="margin-bottom: 15px; color: #2a5298;">
                            <i class="fas fa-chart-bar"></i> 导入统计
                        </h5>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-number">8</span>
                                <span class="stat-label">支持格式</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">156</span>
                                <span class="stat-label">已导入模型</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">2.4GB</span>
                                <span class="stat-label">总文件大小</span>
                            </div>
                        </div>
                    </div>

                    <!-- 导入按钮 -->
                    <div class="import-actions">
                        <button class="import-btn primary">
                            <i class="fas fa-upload"></i>
                            导入新模型
                        </button>
                        <button class="import-btn secondary">
                            <i class="fas fa-folder-open"></i>
                            批量导入
                        </button>
                    </div>
                </div>
                
                <!-- 文档管理面板 -->
                <div class="tab-panel" id="documents-panel">
                    <h4 style="margin-bottom: 20px; color: #2a5298; font-size: 1.1rem;">
                        <i class="fas fa-folder-open"></i> 文档管理
                    </h4>

                    <!-- 文档上传区域 -->
                    <div class="upload-area">
                        <div class="upload-zone" id="uploadZone">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>拖拽文件到此处或点击上传</p>
                            <p class="upload-hint">支持 Office、PDF、图纸等格式</p>
                            <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.dwg,.dxf" style="display: none;">
                        </div>
                        <div class="upload-actions">
                            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                                <i class="fas fa-plus"></i> 选择文件
                            </button>
                            <button class="upload-btn batch">
                                <i class="fas fa-layer-group"></i> 批量上传
                            </button>
                        </div>
                    </div>

                    <!-- 文档搜索 -->
                    <div class="doc-search">
                        <div class="search-input-group">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="搜索文档..." class="search-input">
                            <button class="search-btn">
                                <i class="fas fa-filter"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 文档列表 -->
                    <div class="document-list">
                        <div class="doc-item">
                            <div class="doc-icon">
                                <i class="fas fa-file-pdf" style="color: #e74c3c;"></i>
                            </div>
                            <div class="doc-info">
                                <span class="doc-name">项目设计说明书.pdf</span>
                                <span class="doc-meta">2.4MB • 2024-01-15</span>
                            </div>
                            <div class="doc-actions">
                                <button class="doc-action-btn" title="预览">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="doc-action-btn" title="标注">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="doc-action-btn" title="下载">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>

                        <div class="doc-item">
                            <div class="doc-icon">
                                <i class="fas fa-file-word" style="color: #2980b9;"></i>
                            </div>
                            <div class="doc-info">
                                <span class="doc-name">技术规范文档.docx</span>
                                <span class="doc-meta">1.8MB • 2024-01-14</span>
                            </div>
                            <div class="doc-actions">
                                <button class="doc-action-btn" title="预览">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="doc-action-btn" title="标注">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="doc-action-btn" title="下载">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>

                        <div class="doc-item">
                            <div class="doc-icon">
                                <i class="fas fa-file-excel" style="color: #27ae60;"></i>
                            </div>
                            <div class="doc-info">
                                <span class="doc-name">设备清单.xlsx</span>
                                <span class="doc-meta">856KB • 2024-01-13</span>
                            </div>
                            <div class="doc-actions">
                                <button class="doc-action-btn" title="预览">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="doc-action-btn" title="标注">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="doc-action-btn" title="下载">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>

                        <div class="doc-item">
                            <div class="doc-icon">
                                <i class="fas fa-file-image" style="color: #f39c12;"></i>
                            </div>
                            <div class="doc-info">
                                <span class="doc-name">建筑图纸.dwg</span>
                                <span class="doc-meta">3.2MB • 2024-01-12</span>
                            </div>
                            <div class="doc-actions">
                                <button class="doc-action-btn" title="预览">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="doc-action-btn" title="标注">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="doc-action-btn" title="下载">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 文档统计 -->
                    <div class="doc-stats">
                        <div class="stats-row">
                            <span class="stats-label">总文档数:</span>
                            <span class="stats-value">24</span>
                        </div>
                        <div class="stats-row">
                            <span class="stats-label">总大小:</span>
                            <span class="stats-value">156MB</span>
                        </div>
                        <div class="stats-row">
                            <span class="stats-label">最近更新:</span>
                            <span class="stats-value">2小时前</span>
                        </div>
                    </div>
                </div>
                
                <!-- 搜索浏览面板 -->
                <div class="tab-panel" id="search-panel">
                    <h4 style="margin-bottom: 20px; color: #2a5298; font-size: 1.1rem;">
                        <i class="fas fa-search"></i> 搜索浏览
                    </h4>

                    <!-- 高级搜索 -->
                    <div class="advanced-search">
                        <div class="search-group">
                            <label>对象类型</label>
                            <select class="search-select">
                                <option value="all">全部对象</option>
                                <option value="equipment">设备</option>
                                <option value="pipeline">管线</option>
                                <option value="structure">结构</option>
                                <option value="electrical">电气</option>
                            </select>
                        </div>

                        <div class="search-group">
                            <label>关键词</label>
                            <input type="text" class="search-input-field" placeholder="输入设备名称、编号等...">
                        </div>

                        <div class="search-group">
                            <label>位置范围</label>
                            <div class="location-inputs">
                                <input type="text" placeholder="X坐标" class="coord-input">
                                <input type="text" placeholder="Y坐标" class="coord-input">
                                <input type="text" placeholder="Z坐标" class="coord-input">
                            </div>
                        </div>

                        <button class="search-execute-btn">
                            <i class="fas fa-search"></i> 执行搜索
                        </button>
                    </div>

                    <!-- 搜索结果 -->
                    <div class="search-results">
                        <div class="results-header">
                            <span class="results-count">找到 12 个对象</span>
                            <div class="view-options">
                                <button class="view-btn active" data-view="list">
                                    <i class="fas fa-list"></i>
                                </button>
                                <button class="view-btn" data-view="grid">
                                    <i class="fas fa-th"></i>
                                </button>
                            </div>
                        </div>

                        <div class="results-list">
                            <div class="result-item" data-object-id="EQ001">
                                <div class="result-icon">
                                    <i class="fas fa-cog" style="color: #e74c3c;"></i>
                                </div>
                                <div class="result-info">
                                    <div class="result-name">离心泵 P-001</div>
                                    <div class="result-details">
                                        <span class="detail-item">类型: 设备</span>
                                        <span class="detail-item">位置: (125.5, 89.2, 12.0)</span>
                                        <span class="detail-item">状态: 运行中</span>
                                    </div>
                                </div>
                                <div class="result-actions">
                                    <button class="result-action-btn" title="定位">
                                        <i class="fas fa-crosshairs"></i>
                                    </button>
                                    <button class="result-action-btn" title="属性">
                                        <i class="fas fa-info-circle"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="result-item" data-object-id="PL001">
                                <div class="result-icon">
                                    <i class="fas fa-grip-lines" style="color: #3498db;"></i>
                                </div>
                                <div class="result-info">
                                    <div class="result-name">主供水管线 ML-001</div>
                                    <div class="result-details">
                                        <span class="detail-item">类型: 管线</span>
                                        <span class="detail-item">长度: 45.8m</span>
                                        <span class="detail-item">直径: DN200</span>
                                    </div>
                                </div>
                                <div class="result-actions">
                                    <button class="result-action-btn" title="定位">
                                        <i class="fas fa-crosshairs"></i>
                                    </button>
                                    <button class="result-action-btn" title="属性">
                                        <i class="fas fa-info-circle"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="result-item" data-object-id="ST001">
                                <div class="result-icon">
                                    <i class="fas fa-building" style="color: #f39c12;"></i>
                                </div>
                                <div class="result-info">
                                    <div class="result-name">支撑梁 B-001</div>
                                    <div class="result-details">
                                        <span class="detail-item">类型: 结构</span>
                                        <span class="detail-item">材质: Q345B</span>
                                        <span class="detail-item">规格: H400×200×8×13</span>
                                    </div>
                                </div>
                                <div class="result-actions">
                                    <button class="result-action-btn" title="定位">
                                        <i class="fas fa-crosshairs"></i>
                                    </button>
                                    <button class="result-action-btn" title="属性">
                                        <i class="fas fa-info-circle"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 快速过滤 -->
                    <div class="quick-filters">
                        <h5 style="margin-bottom: 10px; color: #2a5298;">
                            <i class="fas fa-filter"></i> 快速过滤
                        </h5>
                        <div class="filter-tags">
                            <span class="filter-tag active" data-filter="all">全部 (156)</span>
                            <span class="filter-tag" data-filter="equipment">设备 (45)</span>
                            <span class="filter-tag" data-filter="pipeline">管线 (78)</span>
                            <span class="filter-tag" data-filter="structure">结构 (23)</span>
                            <span class="filter-tag" data-filter="electrical">电气 (10)</span>
                        </div>
                    </div>

                    <!-- 属性面板 -->
                    <div class="property-panel" id="propertyPanel" style="display: none;">
                        <h5 style="margin-bottom: 15px; color: #2a5298;">
                            <i class="fas fa-info-circle"></i> 对象属性
                        </h5>
                        <div class="property-content">
                            <!-- 属性内容将通过JavaScript动态填充 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="status-bar">
        <div class="status-left">
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>系统运行正常</span>
            </div>
            <span class="tech-info" title="轻量化技术：采用LOD多级细节、几何压缩、纹理优化等技术">
                <i class="fas fa-compress-alt"></i> 轻量化引擎已启用
            </span>
            <span>模型数量: 0</span>
            <span class="compression-info" title="平均压缩率85%，加载速度提升300%">
                <i class="fas fa-tachometer-alt"></i> 压缩率: 85%
            </span>
        </div>

        <div class="status-right">
            <span class="memory-info" title="当前内存使用情况">
                <i class="fas fa-memory"></i> 内存: 256MB
            </span>
            <span class="fps-info" title="实时渲染帧率">
                <i class="fas fa-chart-line"></i> FPS: 60
            </span>
            <span class="version-info" title="平台版本信息">
                <i class="fas fa-info-circle"></i> v2.1.0
            </span>
            <button class="tech-details-btn" onclick="showTechDetails()" title="查看技术详情">
                <i class="fas fa-cog"></i>
            </button>
        </div>
    </div>

    <!-- 技术详情弹窗 -->
    <div id="techModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-microchip"></i> 轻量化技术详情</h3>
                <button class="modal-close" onclick="closeTechDetails()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="tech-section">
                    <h4><i class="fas fa-compress-alt"></i> 模型压缩技术</h4>
                    <ul>
                        <li><strong>几何压缩：</strong>采用八叉树和网格简化算法，压缩率达85%</li>
                        <li><strong>纹理优化：</strong>智能纹理合并和压缩，减少内存占用60%</li>
                        <li><strong>LOD技术：</strong>多级细节模型，根据视距自动切换精度</li>
                    </ul>
                </div>

                <div class="tech-section">
                    <h4><i class="fas fa-rocket"></i> 加载优化</h4>
                    <ul>
                        <li><strong>流式加载：</strong>支持大模型分块加载，首屏显示时间&lt;2秒</li>
                        <li><strong>缓存机制：</strong>智能缓存策略，重复访问速度提升300%</li>
                        <li><strong>预加载：</strong>后台预加载相关模型，无缝切换体验</li>
                    </ul>
                </div>

                <div class="tech-section">
                    <h4><i class="fas fa-chart-bar"></i> 性能指标</h4>
                    <div class="performance-grid">
                        <div class="perf-item">
                            <span class="perf-label">平均压缩率</span>
                            <span class="perf-value">85%</span>
                        </div>
                        <div class="perf-item">
                            <span class="perf-label">加载速度提升</span>
                            <span class="perf-value">300%</span>
                        </div>
                        <div class="perf-item">
                            <span class="perf-label">内存节省</span>
                            <span class="perf-value">60%</span>
                        </div>
                        <div class="perf-item">
                            <span class="perf-label">渲染帧率</span>
                            <span class="perf-value">60 FPS</span>
                        </div>
                        <div class="perf-item">
                            <span class="perf-label">支持模型数</span>
                            <span class="perf-value">1000+</span>
                        </div>
                        <div class="perf-item">
                            <span class="perf-label">并发用户</span>
                            <span class="perf-value">100+</span>
                        </div>
                    </div>
                </div>

                <div class="tech-section">
                    <h4><i class="fas fa-shield-alt"></i> 兼容性支持</h4>
                    <div class="compatibility-list">
                        <div class="compat-item">
                            <i class="fas fa-desktop"></i>
                            <span>桌面浏览器</span>
                            <span class="compat-status">✓</span>
                        </div>
                        <div class="compat-item">
                            <i class="fas fa-mobile-alt"></i>
                            <span>移动设备</span>
                            <span class="compat-status">✓</span>
                        </div>
                        <div class="compat-item">
                            <i class="fas fa-tablet-alt"></i>
                            <span>平板电脑</span>
                            <span class="compat-status">✓</span>
                        </div>
                        <div class="compat-item">
                            <i class="fas fa-vr-cardboard"></i>
                            <span>VR设备</span>
                            <span class="compat-status">✓</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 基础JavaScript功能
        document.addEventListener('DOMContentLoaded', function() {
            initializePlatform();
        });

        function initializePlatform() {
            setupTabSwitching();
            setupToolbarInteractions();
            setupViewportControls();
            setupDocumentManagement();
            setupSearchFunctionality();
            setupDragAndDrop();

            // 初始化状态
            updateStatusBar('系统已就绪');
            showNotification('三维设计平台已启动', 'success');
        }

        // 标签页切换功能
        function setupTabSwitching() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabPanels = document.querySelectorAll('.tab-panel');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const targetTab = button.getAttribute('data-tab');
                    
                    // 移除所有活动状态
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabPanels.forEach(panel => panel.classList.remove('active'));
                    
                    // 激活当前标签
                    button.classList.add('active');
                    document.getElementById(targetTab + '-panel').classList.add('active');
                });
            });
        }

        // 工具栏交互
        function setupToolbarInteractions() {
            const toolButtons = document.querySelectorAll('.tool-button');

            toolButtons.forEach(button => {
                button.addEventListener('click', () => {
                    toolButtons.forEach(btn => btn.classList.remove('active'));
                    button.classList.add('active');

                    // 根据工具类型执行相应操作
                    const tooltip = button.getAttribute('data-tooltip');
                    handleToolSelection(tooltip, button);
                });
            });
        }

        // 处理工具选择
        function handleToolSelection(toolName, button) {
            const viewport = document.querySelector('.model-viewer');
            const placeholder = document.querySelector('.model-placeholder');

            // 更新状态栏信息
            updateStatusBar(`当前工具: ${toolName}`);

            // 根据工具类型显示不同的提示信息
            switch(toolName) {
                case '选择工具':
                    placeholder.innerHTML = `
                        <i class="fas fa-mouse-pointer"></i>
                        <h3>选择工具已激活</h3>
                        <p>点击模型对象进行选择</p>
                        <p>支持单选和多选操作</p>
                    `;
                    break;
                case '旋转视图':
                    placeholder.innerHTML = `
                        <i class="fas fa-redo"></i>
                        <h3>旋转视图模式</h3>
                        <p>拖拽鼠标旋转3D视图</p>
                        <p>按住Shift键约束旋转轴</p>
                    `;
                    break;
                case '缩放':
                    placeholder.innerHTML = `
                        <i class="fas fa-search-plus"></i>
                        <h3>缩放工具已激活</h3>
                        <p>滚动鼠标滚轮进行缩放</p>
                        <p>或拖拽鼠标进行区域缩放</p>
                    `;
                    break;
                case '平移':
                    placeholder.innerHTML = `
                        <i class="fas fa-arrows-alt"></i>
                        <h3>平移视图模式</h3>
                        <p>拖拽鼠标平移视图</p>
                        <p>按住中键进行快速平移</p>
                    `;
                    break;
                case '剖切':
                    placeholder.innerHTML = `
                        <i class="fas fa-cut"></i>
                        <h3>剖切工具已激活</h3>
                        <p>创建剖切平面查看内部结构</p>
                        <p>支持多个剖切平面组合</p>
                    `;
                    break;
                case '隔离':
                    placeholder.innerHTML = `
                        <i class="fas fa-eye"></i>
                        <h3>隔离显示模式</h3>
                        <p>隐藏或显示选定对象</p>
                        <p>便于专注查看特定组件</p>
                    `;
                    break;
                case '测量':
                    placeholder.innerHTML = `
                        <i class="fas fa-ruler"></i>
                        <h3>测量工具已激活</h3>
                        <p>测量距离、角度、面积等</p>
                        <p>点击两点测量距离</p>
                    `;
                    break;
                case '标注':
                    placeholder.innerHTML = `
                        <i class="fas fa-comment"></i>
                        <h3>标注工具已激活</h3>
                        <p>为模型添加文字标注</p>
                        <p>支持多种标注样式</p>
                    `;
                    break;
                case '截图':
                    placeholder.innerHTML = `
                        <i class="fas fa-camera"></i>
                        <h3>截图工具已激活</h3>
                        <p>捕获当前视图</p>
                        <p>支持高分辨率导出</p>
                    `;
                    simulateScreenshot();
                    break;
            }

            // 添加工具激活动画
            button.style.transform = 'scale(0.9)';
            setTimeout(() => {
                button.style.transform = 'scale(1)';
            }, 150);
        }

        // 模拟截图功能
        function simulateScreenshot() {
            setTimeout(() => {
                showNotification('截图已保存到本地', 'success');
            }, 1000);
        }

        // 更新状态栏
        function updateStatusBar(message) {
            const statusLeft = document.querySelector('.status-left');
            const existingTool = statusLeft.querySelector('.current-tool');

            if (existingTool) {
                existingTool.textContent = message;
            } else {
                const toolStatus = document.createElement('span');
                toolStatus.className = 'current-tool';
                toolStatus.textContent = message;
                statusLeft.appendChild(toolStatus);
            }
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                background: ${type === 'success' ? '#4caf50' : '#2a5298'};
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 0.9rem;
                z-index: 10000;
                animation: slideInRight 0.3s ease-out;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            // 添加滑入动画
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);

            // 3秒后移除
            setTimeout(() => {
                notification.style.animation = 'slideInRight 0.3s ease-out reverse';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 文档管理功能
        function setupDocumentManagement() {
            // 文档操作按钮事件
            document.querySelectorAll('.doc-action-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const action = btn.title;
                    const docItem = btn.closest('.doc-item');
                    const docName = docItem.querySelector('.doc-name').textContent;

                    switch(action) {
                        case '预览':
                            showNotification(`正在预览: ${docName}`);
                            break;
                        case '标注':
                            showNotification(`打开标注工具: ${docName}`);
                            break;
                        case '下载':
                            showNotification(`开始下载: ${docName}`, 'success');
                            break;
                    }
                });
            });

            // 批量上传按钮
            document.querySelector('.upload-btn.batch').addEventListener('click', () => {
                showNotification('批量上传功能已启动');
            });
        }

        // 搜索功能
        function setupSearchFunctionality() {
            // 搜索执行按钮
            document.querySelector('.search-execute-btn').addEventListener('click', () => {
                const objectType = document.querySelector('.search-select').value;
                const keyword = document.querySelector('.search-input-field').value;

                showNotification(`搜索中: ${objectType} - ${keyword || '全部'}`);

                // 模拟搜索延迟
                setTimeout(() => {
                    showNotification('搜索完成，找到 12 个对象', 'success');
                }, 1500);
            });

            // 结果项点击事件
            document.querySelectorAll('.result-item').forEach(item => {
                item.addEventListener('click', () => {
                    const objectId = item.getAttribute('data-object-id');
                    const objectName = item.querySelector('.result-name').textContent;

                    // 高亮选中项
                    document.querySelectorAll('.result-item').forEach(i => i.classList.remove('selected'));
                    item.classList.add('selected');

                    showObjectProperties(objectId, objectName);
                });
            });

            // 结果操作按钮
            document.querySelectorAll('.result-action-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const action = btn.title;
                    const resultItem = btn.closest('.result-item');
                    const objectName = resultItem.querySelector('.result-name').textContent;

                    if (action === '定位') {
                        showNotification(`正在定位: ${objectName}`);
                        // 模拟在3D视图中定位对象
                        highlightObjectInViewport(objectName);
                    } else if (action === '属性') {
                        const objectId = resultItem.getAttribute('data-object-id');
                        showObjectProperties(objectId, objectName);
                    }
                });
            });

            // 快速过滤标签
            document.querySelectorAll('.filter-tag').forEach(tag => {
                tag.addEventListener('click', () => {
                    document.querySelectorAll('.filter-tag').forEach(t => t.classList.remove('active'));
                    tag.classList.add('active');

                    const filter = tag.getAttribute('data-filter');
                    showNotification(`过滤器已应用: ${tag.textContent}`);
                });
            });

            // 视图切换按钮
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');

                    const view = btn.getAttribute('data-view');
                    showNotification(`切换到${view === 'list' ? '列表' : '网格'}视图`);
                });
            });
        }

        // 显示对象属性
        function showObjectProperties(objectId, objectName) {
            const propertyPanel = document.getElementById('propertyPanel');
            const propertyContent = propertyPanel.querySelector('.property-content');

            // 模拟属性数据
            const properties = {
                'EQ001': {
                    '对象名称': '离心泵 P-001',
                    '设备类型': '离心泵',
                    '型号规格': 'IS80-65-160',
                    '流量': '50 m³/h',
                    '扬程': '32 m',
                    '功率': '7.5 kW',
                    '位置坐标': '(125.5, 89.2, 12.0)',
                    '安装日期': '2023-06-15',
                    '运行状态': '正常运行'
                },
                'PL001': {
                    '对象名称': '主供水管线 ML-001',
                    '管线类型': '供水管',
                    '管径': 'DN200',
                    '材质': '不锈钢304',
                    '长度': '45.8 m',
                    '压力等级': 'PN16',
                    '保温类型': '岩棉保温',
                    '安装标高': '+12.000',
                    '设计温度': '80°C'
                },
                'ST001': {
                    '对象名称': '支撑梁 B-001',
                    '构件类型': '钢梁',
                    '截面规格': 'H400×200×8×13',
                    '材质等级': 'Q345B',
                    '长度': '6.0 m',
                    '重量': '284 kg',
                    '防腐涂层': '环氧富锌底漆',
                    '安装位置': '标高+15.500',
                    '设计荷载': '150 kN/m'
                }
            };

            const props = properties[objectId] || {};
            let html = '';

            for (const [key, value] of Object.entries(props)) {
                html += `
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid rgba(42, 82, 152, 0.1);">
                        <span style="font-weight: 500; color: #2a5298;">${key}:</span>
                        <span style="color: #666;">${value}</span>
                    </div>
                `;
            }

            propertyContent.innerHTML = html;
            propertyPanel.style.display = 'block';

            showNotification(`已显示 ${objectName} 的属性信息`);
        }

        // 在视窗中高亮对象
        function highlightObjectInViewport(objectName) {
            const viewport = document.querySelector('.model-viewer');
            const placeholder = viewport.querySelector('.model-placeholder');

            // 临时显示定位效果
            placeholder.innerHTML = `
                <i class="fas fa-crosshairs" style="color: #e74c3c; animation: pulse 1s infinite;"></i>
                <h3 style="color: #e74c3c;">正在定位对象</h3>
                <p><strong>${objectName}</strong></p>
                <p>对象已在3D视图中高亮显示</p>
            `;

            // 2秒后恢复默认显示
            setTimeout(() => {
                placeholder.innerHTML = `
                    <i class="fas fa-cube"></i>
                    <h3>3D模型视窗</h3>
                    <p>支持多种格式的三维模型展示</p>
                    <p>拖拽模型文件到此处或点击导入按钮</p>
                `;
            }, 2000);
        }

        // 拖拽上传功能
        function setupDragAndDrop() {
            const uploadZone = document.getElementById('uploadZone');
            const fileInput = document.getElementById('fileInput');

            // 文档上传区域拖拽
            uploadZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadZone.classList.add('dragover');
            });

            uploadZone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                uploadZone.classList.remove('dragover');
            });

            uploadZone.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadZone.classList.remove('dragover');

                const files = Array.from(e.dataTransfer.files);
                handleFileUpload(files);
            });

            // 点击上传
            uploadZone.addEventListener('click', () => {
                fileInput.click();
            });

            fileInput.addEventListener('change', (e) => {
                const files = Array.from(e.target.files);
                handleFileUpload(files);
            });

            // 3D视窗拖拽（用于模型文件）
            const viewport = document.querySelector('.model-viewer');

            viewport.addEventListener('dragover', (e) => {
                e.preventDefault();
                viewport.style.background = 'radial-gradient(circle at center, rgba(42, 82, 152, 0.1) 0%, transparent 70%)';
            });

            viewport.addEventListener('dragleave', (e) => {
                e.preventDefault();
                viewport.style.background = 'radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%)';
            });

            viewport.addEventListener('drop', (e) => {
                e.preventDefault();
                viewport.style.background = 'radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%)';

                const files = Array.from(e.dataTransfer.files);
                handleModelUpload(files);
            });
        }

        // 处理文档上传
        function handleFileUpload(files) {
            if (files.length === 0) return;

            showNotification(`正在上传 ${files.length} 个文件...`);

            files.forEach((file, index) => {
                setTimeout(() => {
                    showNotification(`${file.name} 上传完成`, 'success');

                    // 模拟添加到文档列表
                    if (index === files.length - 1) {
                        setTimeout(() => {
                            showNotification('所有文件上传完成', 'success');
                        }, 500);
                    }
                }, (index + 1) * 1000);
            });
        }

        // 处理模型上传
        function handleModelUpload(files) {
            const modelFiles = files.filter(file => {
                const ext = file.name.toLowerCase().split('.').pop();
                return ['rvt', 'rfa', 'ifc', 'pdms', '3ds', 'max', '3dxml', 'sldprt', 'sldasm', 'prt', 'asm'].includes(ext);
            });

            if (modelFiles.length === 0) {
                showNotification('请上传支持的3D模型格式文件');
                return;
            }

            showNotification(`正在处理 ${modelFiles.length} 个模型文件...`);

            const placeholder = document.querySelector('.model-placeholder');
            placeholder.innerHTML = `
                <div class="loading"></div>
                <h3>正在加载模型...</h3>
                <p>文件: ${modelFiles[0].name}</p>
                <p>轻量化处理中，请稍候...</p>
            `;

            // 模拟模型加载过程
            setTimeout(() => {
                placeholder.innerHTML = `
                    <i class="fas fa-cube" style="color: #4caf50; font-size: 5rem;"></i>
                    <h3 style="color: #4caf50;">模型加载完成</h3>
                    <p><strong>${modelFiles[0].name}</strong></p>
                    <p>轻量化压缩率: 85% | 加载时间: 2.3秒</p>
                `;

                showNotification('3D模型加载完成', 'success');
                updateModelStats();
            }, 3000);
        }

        // 更新模型统计信息
        function updateModelStats() {
            const statusBar = document.querySelector('.status-bar');
            const modelCount = statusBar.querySelector('.status-left').children[2];
            modelCount.textContent = '模型数量: 1';

            const memoryUsage = statusBar.querySelector('.status-right').children[0];
            memoryUsage.textContent = '内存使用: 512MB';
        }

        // 添加选中样式
        const additionalStyles = `
            .result-item.selected {
                background: rgba(42, 82, 152, 0.15) !important;
                border-color: #2a5298 !important;
                transform: translateY(-1px);
            }

            .current-tool {
                margin-left: 20px;
                font-weight: 500;
                color: #2a5298;
            }
        `;

        const styleSheet = document.createElement('style');
        styleSheet.textContent = additionalStyles;
        document.head.appendChild(styleSheet);

        // 技术详情弹窗功能
        function showTechDetails() {
            const modal = document.getElementById('techModal');
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';

            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeTechDetails();
                }
            });
        }

        function closeTechDetails() {
            const modal = document.getElementById('techModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // ESC键关闭弹窗
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeTechDetails();
            }
        });

        // 实时更新性能指标
        function updatePerformanceMetrics() {
            const fpsInfo = document.querySelector('.fps-info');
            const memoryInfo = document.querySelector('.memory-info');

            // 模拟FPS波动
            const fps = 58 + Math.floor(Math.random() * 5);
            fpsInfo.innerHTML = `<i class="fas fa-chart-line"></i> FPS: ${fps}`;

            // 模拟内存使用变化
            const memory = 250 + Math.floor(Math.random() * 50);
            memoryInfo.innerHTML = `<i class="fas fa-memory"></i> 内存: ${memory}MB`;
        }

        // 每5秒更新一次性能指标
        setInterval(updatePerformanceMetrics, 5000);

        // 全局函数声明（供HTML调用）
        window.showTechDetails = showTechDetails;
        window.closeTechDetails = closeTechDetails;

        // 视窗控制
        function setupViewportControls() {
            const controlButtons = document.querySelectorAll('.control-button');
            
            controlButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // 添加点击效果
                    button.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        button.style.transform = 'scale(1.1)';
                    }, 100);
                });
            });
        }
    </script>
</body>
</html>
