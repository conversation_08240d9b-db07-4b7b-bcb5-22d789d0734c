<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实验室智能管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 头部样式 */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .main-title {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }

        .subtitle {
            color: #7f8c8d;
            font-size: 1.2rem;
            margin-bottom: 20px;
        }

        .feature-tags {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .feature-tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 导航标签 */
        .nav-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 10px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            gap: 8px;
        }

        .nav-tab {
            flex: 1;
            padding: 18px 25px;
            background: transparent;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            color: #7f8c8d;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .nav-tab.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
            transform: translateY(-2px);
        }

        .nav-tab:hover:not(.active) {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            transform: translateY(-1px);
        }

        /* 内容区域 */
        .content-area {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 35px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            min-height: 700px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 卡片网格 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .info-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .info-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 15s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .info-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #2c3e50;
        }

        .card-content {
            position: relative;
            z-index: 1;
        }

        /* 药剂监测样式 */
        .reagent-monitoring .info-card {
            border-left-color: #3498db;
        }

        .reagent-monitoring .card-icon {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }

        .reagent-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
        }

        .reagent-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .reagent-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .status-normal {
            background: #d4edda;
            color: #155724;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
        }

        .status-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: #e9ecef;
            border-radius: 6px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            border-radius: 6px;
            transition: width 0.3s ease;
        }

        .progress-normal { background: linear-gradient(90deg, #28a745, #20c997); }
        .progress-warning { background: linear-gradient(90deg, #ffc107, #fd7e14); }
        .progress-danger { background: linear-gradient(90deg, #dc3545, #e83e8c); }

        .reagent-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            font-size: 0.9rem;
        }

        .info-item {
            text-align: center;
        }

        .info-label {
            color: #6c757d;
            margin-bottom: 5px;
        }

        .info-value {
            font-weight: 600;
            color: #495057;
        }

        /* 药剂签收样式 */
        .reagent-receipt .info-card {
            border-left-color: #28a745;
        }

        .reagent-receipt .card-icon {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .receipt-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
            position: relative;
        }

        .receipt-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .receipt-number {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .receipt-date {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .receipt-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            color: #6c757d;
            font-size: 0.85rem;
            margin-bottom: 3px;
        }

        .detail-value {
            font-weight: 500;
            color: #495057;
        }

        .signature-area {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border: 2px dashed #dee2e6;
            text-align: center;
            margin-top: 15px;
        }

        .signature-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-weight: 500;
        }

        .signature-signed {
            color: #28a745;
        }

        .signature-pending {
            color: #ffc107;
        }

        /* 样品管理样式 */
        .sample-management .info-card {
            border-left-color: #e74c3c;
        }

        .sample-management .card-icon {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .sample-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
        }

        .sample-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .sample-id {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            font-family: 'Courier New', monospace;
        }

        .sample-type {
            background: #e9ecef;
            color: #495057;
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .sample-timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline-item {
            position: relative;
            padding-bottom: 20px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -22px;
            top: 8px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 3px solid #dee2e6;
            background: white;
        }

        .timeline-item.completed::before {
            border-color: #28a745;
            background: #28a745;
        }

        .timeline-item.current::before {
            border-color: #007bff;
            background: #007bff;
        }

        .timeline-item::after {
            content: '';
            position: absolute;
            left: -16px;
            top: 20px;
            width: 2px;
            height: calc(100% - 12px);
            background: #dee2e6;
        }

        .timeline-item:last-child::after {
            display: none;
        }

        .timeline-content {
            background: #f8f9fa;
            padding: 12px 15px;
            border-radius: 8px;
            border-left: 3px solid #dee2e6;
        }

        .timeline-item.completed .timeline-content {
            border-left-color: #28a745;
        }

        .timeline-item.current .timeline-content {
            border-left-color: #007bff;
        }

        .timeline-title {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }

        .timeline-time {
            color: #6c757d;
            font-size: 0.85rem;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        .stat-content {
            position: relative;
            z-index: 1;
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .stat-number {
            font-size: 2.2rem;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .main-title {
                font-size: 2rem;
                flex-direction: column;
                gap: 10px;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .nav-tab {
                padding: 15px 20px;
            }

            .card-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .reagent-info,
            .receipt-details {
                grid-template-columns: 1fr;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.6s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-30px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* 按钮样式 */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: white;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header fade-in">
            <h1 class="main-title">
                <i class="fas fa-flask"></i>
                实验室智能管理系统
                <i class="fas fa-microscope"></i>
            </h1>
            <p class="subtitle">Laboratory Intelligent Management System</p>
            <div class="feature-tags">
                <div class="feature-tag">
                    <i class="fas fa-tint"></i>
                    药剂智能监测
                </div>
                <div class="feature-tag">
                    <i class="fas fa-signature"></i>
                    电子签收管理
                </div>
                <div class="feature-tag">
                    <i class="fas fa-vial"></i>
                    样品全程追踪
                </div>
            </div>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs slide-in">
            <button class="nav-tab active" onclick="showTab('reagent-monitoring')">
                <i class="fas fa-tint"></i>
                药剂监测
            </button>
            <button class="nav-tab" onclick="showTab('reagent-receipt')">
                <i class="fas fa-signature"></i>
                药剂签收
            </button>
            <button class="nav-tab" onclick="showTab('sample-management')">
                <i class="fas fa-vial"></i>
                样品管理
            </button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 药剂监测标签页 -->
            <div id="reagent-monitoring" class="tab-content active reagent-monitoring">
                <h2 style="color: #2c3e50; margin-bottom: 25px; display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-tint"></i>
                    药剂监测管理
                </h2>

                <!-- 统计概览 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-flask"></i>
                            </div>
                            <div class="stat-number">12</div>
                            <div class="stat-label">监测药剂总数</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-number">8</div>
                            <div class="stat-label">正常状态</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-number">3</div>
                            <div class="stat-label">需要补充</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-number">1</div>
                            <div class="stat-label">紧急补充</div>
                        </div>
                    </div>
                </div>

                <!-- 药剂监测列表 -->
                <div class="card-grid">
                    <div class="info-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-tint"></i>
                            </div>
                            <div class="card-title">混凝剂监测</div>
                        </div>
                        <div class="card-content">
                            <div class="reagent-item">
                                <div class="reagent-header">
                                    <div class="reagent-name">聚合氯化铝(PAC)</div>
                                    <div class="status-badge status-danger">
                                        <i class="fas fa-exclamation-circle"></i>
                                        紧急补充
                                    </div>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-danger" style="width: 15%;"></div>
                                </div>
                                <div class="reagent-info">
                                    <div class="info-item">
                                        <div class="info-label">当前液位</div>
                                        <div class="info-value">15%</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">日均消耗</div>
                                        <div class="info-value">8.5L</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">预计可用</div>
                                        <div class="info-value">2天</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">建议补充</div>
                                        <div class="info-value" style="color: #dc3545; font-weight: bold;">今日</div>
                                    </div>
                                </div>
                            </div>

                            <div class="reagent-item">
                                <div class="reagent-header">
                                    <div class="reagent-name">聚丙烯酰胺(PAM)</div>
                                    <div class="status-badge status-warning">
                                        <i class="fas fa-clock"></i>
                                        需要补充
                                    </div>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-warning" style="width: 35%;"></div>
                                </div>
                                <div class="reagent-info">
                                    <div class="info-item">
                                        <div class="info-label">当前液位</div>
                                        <div class="info-value">35%</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">日均消耗</div>
                                        <div class="info-value">3.2L</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">预计可用</div>
                                        <div class="info-value">5天</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">建议补充</div>
                                        <div class="info-value" style="color: #ffc107; font-weight: bold;">3天内</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="info-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-flask"></i>
                            </div>
                            <div class="card-title">消毒剂监测</div>
                        </div>
                        <div class="card-content">
                            <div class="reagent-item">
                                <div class="reagent-header">
                                    <div class="reagent-name">次氯酸钠</div>
                                    <div class="status-badge status-normal">
                                        <i class="fas fa-check-circle"></i>
                                        状态正常
                                    </div>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-normal" style="width: 75%;"></div>
                                </div>
                                <div class="reagent-info">
                                    <div class="info-item">
                                        <div class="info-label">当前液位</div>
                                        <div class="info-value">75%</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">日均消耗</div>
                                        <div class="info-value">12.8L</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">预计可用</div>
                                        <div class="info-value">15天</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">建议补充</div>
                                        <div class="info-value" style="color: #28a745; font-weight: bold;">10天后</div>
                                    </div>
                                </div>
                            </div>

                            <div class="reagent-item">
                                <div class="reagent-header">
                                    <div class="reagent-name">二氧化氯</div>
                                    <div class="status-badge status-warning">
                                        <i class="fas fa-clock"></i>
                                        需要补充
                                    </div>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill progress-warning" style="width: 28%;"></div>
                                </div>
                                <div class="reagent-info">
                                    <div class="info-item">
                                        <div class="info-label">当前液位</div>
                                        <div class="info-value">28%</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">日均消耗</div>
                                        <div class="info-value">5.6L</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">预计可用</div>
                                        <div class="info-value">6天</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">建议补充</div>
                                        <div class="info-value" style="color: #ffc107; font-weight: bold;">4天内</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 药剂签收标签页 -->
            <div id="reagent-receipt" class="tab-content reagent-receipt">
                <h2 style="color: #2c3e50; margin-bottom: 25px; display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-signature"></i>
                    药剂签收管理
                </h2>

                <!-- 统计概览 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-truck"></i>
                            </div>
                            <div class="stat-number">28</div>
                            <div class="stat-label">本月收货单</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-check-double"></i>
                            </div>
                            <div class="stat-number">25</div>
                            <div class="stat-label">已签收</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-number">3</div>
                            <div class="stat-label">待签收</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="stat-number">89%</div>
                            <div class="stat-label">签收及时率</div>
                        </div>
                    </div>
                </div>

                <!-- 待签收收货单 -->
                <div class="card-grid">
                    <div class="info-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="card-title">待签收收货单</div>
                        </div>
                        <div class="card-content">
                            <div class="receipt-item" data-receipt-id="RCP20240115001">
                                <div class="receipt-header">
                                    <div>
                                        <div class="receipt-number">RCP20240115001</div>
                                        <div class="receipt-date">2024-01-15 09:30</div>
                                    </div>
                                    <div class="status-badge status-warning">
                                        <i class="fas fa-clock"></i>
                                        待签收
                                    </div>
                                </div>
                                <div class="receipt-details">
                                    <div class="detail-item">
                                        <div class="detail-label">供应商</div>
                                        <div class="detail-value">华东化工有限公司</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">药剂名称</div>
                                        <div class="detail-value">聚合氯化铝(PAC)</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">数量</div>
                                        <div class="detail-value">500L</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">批次号</div>
                                        <div class="detail-value">PAC240115A</div>
                                    </div>
                                </div>
                                <div class="signature-area">
                                    <div class="signature-status signature-pending">
                                        <i class="fas fa-pen"></i>
                                        等待签收确认
                                    </div>
                                    <button class="btn btn-success btn-sm" style="margin-top: 10px;" onclick="signReceipt('RCP20240115001')">
                                        <i class="fas fa-signature"></i>
                                        立即签收
                                    </button>
                                </div>
                            </div>

                            <div class="receipt-item" data-receipt-id="RCP20240115002">
                                <div class="receipt-header">
                                    <div>
                                        <div class="receipt-number">RCP20240115002</div>
                                        <div class="receipt-date">2024-01-15 14:20</div>
                                    </div>
                                    <div class="status-badge status-warning">
                                        <i class="fas fa-clock"></i>
                                        待签收
                                    </div>
                                </div>
                                <div class="receipt-details">
                                    <div class="detail-item">
                                        <div class="detail-label">供应商</div>
                                        <div class="detail-value">绿源环保科技</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">药剂名称</div>
                                        <div class="detail-value">次氯酸钠</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">数量</div>
                                        <div class="detail-value">800L</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">批次号</div>
                                        <div class="detail-value">NaClO240115B</div>
                                    </div>
                                </div>
                                <div class="signature-area">
                                    <div class="signature-status signature-pending">
                                        <i class="fas fa-pen"></i>
                                        等待签收确认
                                    </div>
                                    <button class="btn btn-success btn-sm" style="margin-top: 10px;" onclick="signReceipt('RCP20240115002')">
                                        <i class="fas fa-signature"></i>
                                        立即签收
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="info-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="card-title">已签收记录</div>
                        </div>
                        <div class="card-content">
                            <div class="receipt-item">
                                <div class="receipt-header">
                                    <div>
                                        <div class="receipt-number">RCP20240114003</div>
                                        <div class="receipt-date">2024-01-14 16:45</div>
                                    </div>
                                    <div class="status-badge status-normal">
                                        <i class="fas fa-check-circle"></i>
                                        已签收
                                    </div>
                                </div>
                                <div class="receipt-details">
                                    <div class="detail-item">
                                        <div class="detail-label">供应商</div>
                                        <div class="detail-value">中化水处理</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">药剂名称</div>
                                        <div class="detail-value">聚丙烯酰胺(PAM)</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">数量</div>
                                        <div class="detail-value">200L</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">签收人</div>
                                        <div class="detail-value">张三</div>
                                    </div>
                                </div>
                                <div class="signature-area">
                                    <div class="signature-status signature-signed">
                                        <i class="fas fa-check-double"></i>
                                        已完成电子签收 - 2024-01-14 17:02
                                    </div>
                                    <button class="btn btn-primary btn-sm" style="margin-top: 10px;" onclick="viewReceipt('RCP20240114003')">
                                        <i class="fas fa-file-alt"></i>
                                        查看电子签收单
                                    </button>
                                </div>
                            </div>

                            <div class="receipt-item">
                                <div class="receipt-header">
                                    <div>
                                        <div class="receipt-number">RCP20240114004</div>
                                        <div class="receipt-date">2024-01-14 10:15</div>
                                    </div>
                                    <div class="status-badge status-normal">
                                        <i class="fas fa-check-circle"></i>
                                        已签收
                                    </div>
                                </div>
                                <div class="receipt-details">
                                    <div class="detail-item">
                                        <div class="detail-label">供应商</div>
                                        <div class="detail-value">蓝星化工</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">药剂名称</div>
                                        <div class="detail-value">二氧化氯</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">数量</div>
                                        <div class="detail-value">300L</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">签收人</div>
                                        <div class="detail-value">李四</div>
                                    </div>
                                </div>
                                <div class="signature-area">
                                    <div class="signature-status signature-signed">
                                        <i class="fas fa-check-double"></i>
                                        已完成电子签收 - 2024-01-14 10:32
                                    </div>
                                    <button class="btn btn-primary btn-sm" style="margin-top: 10px;" onclick="viewReceipt('RCP20240114004')">
                                        <i class="fas fa-file-alt"></i>
                                        查看电子签收单
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 样品管理标签页 -->
            <div id="sample-management" class="tab-content sample-management">
                <h2 style="color: #2c3e50; margin-bottom: 25px; display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-vial"></i>
                    化验样品管理
                </h2>

                <!-- 统计概览 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-vial"></i>
                            </div>
                            <div class="stat-number">156</div>
                            <div class="stat-label">样品总数</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-play"></i>
                            </div>
                            <div class="stat-number">23</div>
                            <div class="stat-label">检测中</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-number">128</div>
                            <div class="stat-label">已完成</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-number">5</div>
                            <div class="stat-label">待分配</div>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="card-grid" style="margin-bottom: 25px;">
                    <div class="info-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="card-title">快速查询</div>
                        </div>
                        <div class="card-content">
                            <div style="display: flex; gap: 10px; margin-bottom: 15px;">
                                <input type="text" id="sampleSearchInput" placeholder="输入样品标识 (如: WS-20240115-001)"
                                       style="flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                                <button class="btn btn-primary" onclick="searchSampleById()">
                                    <i class="fas fa-search"></i>
                                    查询
                                </button>
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                                <button class="btn btn-success btn-sm" onclick="trackSubSamples('WS-20240115-001')">
                                    <i class="fas fa-sitemap"></i>
                                    分样跟踪
                                </button>
                                <button class="btn btn-warning btn-sm" onclick="showSampleReport()">
                                    <i class="fas fa-chart-bar"></i>
                                    统计报告
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="info-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                            <div class="card-title">样品登记</div>
                        </div>
                        <div class="card-content">
                            <div style="display: grid; gap: 10px;">
                                <select style="padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                                    <option>选择样品类型</option>
                                    <option>进水口水样</option>
                                    <option>出水口水样</option>
                                    <option>沉淀池污泥</option>
                                    <option>回流污泥</option>
                                </select>
                                <input type="text" placeholder="采样地点"
                                       style="padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                                <button class="btn btn-primary" onclick="registerNewSample()">
                                    <i class="fas fa-plus"></i>
                                    登记样品
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 样品跟踪 -->
                <div class="card-grid">
                    <div class="info-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-tint"></i>
                            </div>
                            <div class="card-title">水样检测跟踪</div>
                        </div>
                        <div class="card-content">
                            <div class="sample-item">
                                <div class="sample-header">
                                    <div>
                                        <div class="sample-id">WS-20240115-001</div>
                                        <div style="margin-top: 5px;">
                                            <span class="sample-type">进水口水样</span>
                                            <span class="status-badge status-warning" style="margin-left: 10px;">
                                                <i class="fas fa-play"></i>
                                                检测中
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="sample-timeline">
                                    <div class="timeline-item completed">
                                        <div class="timeline-content">
                                            <div class="timeline-title">样品登记</div>
                                            <div class="timeline-time">2024-01-15 08:30 - 张三</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item completed">
                                        <div class="timeline-content">
                                            <div class="timeline-title">样品分配</div>
                                            <div class="timeline-time">2024-01-15 09:15 - 实验室主管</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item current">
                                        <div class="timeline-content">
                                            <div class="timeline-title">常规检测</div>
                                            <div class="timeline-time">2024-01-15 10:00 - 李四 (进行中)</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item">
                                        <div class="timeline-content">
                                            <div class="timeline-title">数据审核</div>
                                            <div class="timeline-time">待执行</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item">
                                        <div class="timeline-content">
                                            <div class="timeline-title">报告生成</div>
                                            <div class="timeline-time">待执行</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="sample-item">
                                <div class="sample-header">
                                    <div>
                                        <div class="sample-id">WS-20240115-002</div>
                                        <div style="margin-top: 5px;">
                                            <span class="sample-type">出水口水样</span>
                                            <span class="status-badge status-normal" style="margin-left: 10px;">
                                                <i class="fas fa-check-circle"></i>
                                                已完成
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="sample-timeline">
                                    <div class="timeline-item completed">
                                        <div class="timeline-content">
                                            <div class="timeline-title">样品登记</div>
                                            <div class="timeline-time">2024-01-15 08:45 - 王五</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item completed">
                                        <div class="timeline-content">
                                            <div class="timeline-title">样品分配</div>
                                            <div class="timeline-time">2024-01-15 09:30 - 实验室主管</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item completed">
                                        <div class="timeline-content">
                                            <div class="timeline-title">常规检测</div>
                                            <div class="timeline-time">2024-01-15 11:20 - 赵六</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item completed">
                                        <div class="timeline-content">
                                            <div class="timeline-title">数据审核</div>
                                            <div class="timeline-time">2024-01-15 14:15 - 实验室主管</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item completed">
                                        <div class="timeline-content">
                                            <div class="timeline-title">报告生成</div>
                                            <div class="timeline-time">2024-01-15 15:30 - 系统自动</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="info-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-seedling"></i>
                            </div>
                            <div class="card-title">污泥样检测跟踪</div>
                        </div>
                        <div class="card-content">
                            <div class="sample-item">
                                <div class="sample-header">
                                    <div>
                                        <div class="sample-id">***********-001</div>
                                        <div style="margin-top: 5px;">
                                            <span class="sample-type">沉淀池污泥</span>
                                            <span class="status-badge status-warning" style="margin-left: 10px;">
                                                <i class="fas fa-play"></i>
                                                检测中
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="sample-timeline">
                                    <div class="timeline-item completed">
                                        <div class="timeline-content">
                                            <div class="timeline-title">样品登记</div>
                                            <div class="timeline-time">2024-01-15 09:00 - 张三</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item completed">
                                        <div class="timeline-content">
                                            <div class="timeline-title">样品预处理</div>
                                            <div class="timeline-time">2024-01-15 10:30 - 李四</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item completed">
                                        <div class="timeline-content">
                                            <div class="timeline-title">分样处理</div>
                                            <div class="timeline-time">2024-01-15 11:45 - 李四</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item current">
                                        <div class="timeline-content">
                                            <div class="timeline-title">含水率检测</div>
                                            <div class="timeline-time">2024-01-15 13:20 - 王五 (进行中)</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item">
                                        <div class="timeline-content">
                                            <div class="timeline-title">重金属检测</div>
                                            <div class="timeline-time">待执行</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="sample-item">
                                <div class="sample-header">
                                    <div>
                                        <div class="sample-id">SS-20240114-003</div>
                                        <div style="margin-top: 5px;">
                                            <span class="sample-type">回流污泥</span>
                                            <span class="status-badge status-normal" style="margin-left: 10px;">
                                                <i class="fas fa-check-circle"></i>
                                                已完成
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="sample-timeline">
                                    <div class="timeline-item completed">
                                        <div class="timeline-content">
                                            <div class="timeline-title">样品登记</div>
                                            <div class="timeline-time">2024-01-14 14:20 - 赵六</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item completed">
                                        <div class="timeline-content">
                                            <div class="timeline-title">样品预处理</div>
                                            <div class="timeline-time">2024-01-14 15:30 - 王五</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item completed">
                                        <div class="timeline-content">
                                            <div class="timeline-title">MLSS检测</div>
                                            <div class="timeline-time">2024-01-14 16:45 - 张三</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item completed">
                                        <div class="timeline-content">
                                            <div class="timeline-title">数据审核</div>
                                            <div class="timeline-time">2024-01-15 08:15 - 实验室主管</div>
                                        </div>
                                    </div>
                                    <div class="timeline-item completed">
                                        <div class="timeline-content">
                                            <div class="timeline-title">报告归档</div>
                                            <div class="timeline-time">2024-01-15 09:00 - 系统自动</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 标签页切换功能
        function showTab(tabId) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有标签的激活状态
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签页内容
            const targetTab = document.getElementById(tabId);
            if (targetTab) {
                targetTab.classList.add('active');
            }

            // 激活选中的标签
            event.target.classList.add('active');

            // 根据标签页加载相应内容
            if (tabId === 'reagent-receipt') {
                loadReagentReceipt();
            } else if (tabId === 'sample-management') {
                loadSampleManagement();
            }
        }

        // 加载药剂签收内容
        function loadReagentReceipt() {
            // 更新统计数据
            updateReceiptStats();
        }

        // 加载样品管理内容
        function loadSampleManagement() {
            // 更新统计数据
            updateSampleStats();
        }

        // 更新药剂签收统计
        function updateReceiptStats() {
            // 模拟数据更新
            console.log('更新药剂签收统计数据');
        }

        // 更新样品管理统计
        function updateSampleStats() {
            // 模拟数据更新
            console.log('更新样品管理统计数据');
        }

        // 药剂签收功能
        function signReceipt(receiptId) {
            if (confirm(`确认签收收货单 ${receiptId} 吗？`)) {
                // 模拟签收过程
                showNotification('正在生成电子签收单...', 'info');

                setTimeout(() => {
                    // 更新界面状态
                    const receiptItem = document.querySelector(`[data-receipt-id="${receiptId}"]`);
                    if (receiptItem) {
                        const statusBadge = receiptItem.querySelector('.status-badge');
                        statusBadge.className = 'status-badge status-normal';
                        statusBadge.innerHTML = '<i class="fas fa-check-circle"></i> 已签收';

                        const signatureArea = receiptItem.querySelector('.signature-area');
                        signatureArea.innerHTML = `
                            <div class="signature-status signature-signed">
                                <i class="fas fa-check-double"></i>
                                已完成电子签收 - ${new Date().toLocaleString()}
                            </div>
                            <button class="btn btn-primary btn-sm" style="margin-top: 10px;" onclick="viewReceipt('${receiptId}')">
                                <i class="fas fa-file-alt"></i>
                                查看电子签收单
                            </button>
                        `;
                    }

                    showNotification('电子签收单生成成功！', 'success');
                }, 2000);
            }
        }

        // 查看电子签收单
        function viewReceipt(receiptId) {
            showNotification(`正在打开收货单 ${receiptId} 的电子签收单...`, 'info');

            // 模拟打开电子签收单
            setTimeout(() => {
                const receiptWindow = window.open('', '_blank', 'width=800,height=600');
                receiptWindow.document.write(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>电子签收单 - ${receiptId}</title>
                        <style>
                            body { font-family: Arial, sans-serif; padding: 20px; }
                            .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
                            .content { line-height: 1.6; }
                            .signature { margin-top: 40px; text-align: right; }
                            .stamp { color: red; font-weight: bold; border: 2px solid red; padding: 10px; display: inline-block; }
                        </style>
                    </head>
                    <body>
                        <div class="header">
                            <h1>药剂收货电子签收单</h1>
                            <p>签收单号：${receiptId}</p>
                        </div>
                        <div class="content">
                            <p><strong>收货时间：</strong>${new Date().toLocaleString()}</p>
                            <p><strong>供应商：</strong>华东化工有限公司</p>
                            <p><strong>药剂名称：</strong>聚合氯化铝(PAC)</p>
                            <p><strong>数量：</strong>500L</p>
                            <p><strong>批次号：</strong>PAC240115A</p>
                            <p><strong>质量状况：</strong>包装完好，无泄漏</p>
                            <p><strong>签收人：</strong>张三</p>
                        </div>
                        <div class="signature">
                            <div class="stamp">电子签收</div>
                            <p>签收时间：${new Date().toLocaleString()}</p>
                        </div>
                    </body>
                    </html>
                `);
                receiptWindow.document.close();
            }, 1000);
        }

        // 样品查询功能
        function searchSample(sampleId) {
            if (!sampleId) {
                showNotification('请输入样品标识！', 'error');
                return;
            }

            showNotification(`正在查询样品 ${sampleId} 的位置和状态...`, 'info');

            // 模拟查询过程
            setTimeout(() => {
                const sampleInfo = {
                    id: sampleId,
                    type: '进水口水样',
                    location: '实验室A区-工作台3',
                    status: '检测中',
                    assignee: '李四',
                    progress: '常规检测阶段'
                };

                showNotification(`样品 ${sampleId} 当前位置：${sampleInfo.location}，状态：${sampleInfo.status}`, 'success');
            }, 1500);
        }

        // 样品分样跟踪
        function trackSubSamples(parentSampleId) {
            showNotification(`正在查询样品 ${parentSampleId} 的分样情况...`, 'info');

            setTimeout(() => {
                const subSamples = [
                    { id: `${parentSampleId}-A`, test: 'COD检测', status: '已完成' },
                    { id: `${parentSampleId}-B`, test: 'BOD检测', status: '检测中' },
                    { id: `${parentSampleId}-C`, test: '重金属检测', status: '待检测' }
                ];

                let message = `样品 ${parentSampleId} 分样跟踪：\n`;
                subSamples.forEach(sub => {
                    message += `${sub.id}: ${sub.test} - ${sub.status}\n`;
                });

                showNotification(message, 'info');
            }, 1500);
        }

        // 显示通知消息
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${getNotificationColor(type)};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                z-index: 10000;
                animation: slideInRight 0.3s ease-out;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                max-width: 400px;
                word-wrap: break-word;
                white-space: pre-line;
            `;

            const icon = getNotificationIcon(type);
            notification.innerHTML = `<i class="${icon}" style="margin-right: 8px;"></i>${message}`;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-out';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 4000);
        }

        // 获取通知颜色
        function getNotificationColor(type) {
            const colors = {
                'success': '#28a745',
                'error': '#dc3545',
                'warning': '#ffc107',
                'info': '#17a2b8'
            };
            return colors[type] || colors.info;
        }

        // 获取通知图标
        function getNotificationIcon(type) {
            const icons = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };
            return icons[type] || icons.info;
        }

        // 通过输入框搜索样品
        function searchSampleById() {
            const sampleId = document.getElementById('sampleSearchInput').value.trim();
            if (sampleId) {
                searchSample(sampleId);
            } else {
                showNotification('请输入样品标识！', 'error');
            }
        }

        // 登记新样品
        function registerNewSample() {
            const sampleId = generateSampleId();
            showNotification(`正在登记新样品，样品标识：${sampleId}`, 'info');

            setTimeout(() => {
                showNotification(`样品 ${sampleId} 登记成功！`, 'success');
                // 清空表单
                const inputs = document.querySelectorAll('#sample-management input, #sample-management select');
                inputs.forEach(input => {
                    if (input.tagName === 'SELECT') {
                        input.selectedIndex = 0;
                    } else {
                        input.value = '';
                    }
                });
            }, 1500);
        }

        // 生成样品标识
        function generateSampleId() {
            const now = new Date();
            const dateStr = now.getFullYear() +
                String(now.getMonth() + 1).padStart(2, '0') +
                String(now.getDate()).padStart(2, '0');
            const randomNum = String(Math.floor(Math.random() * 1000)).padStart(3, '0');
            return `WS-${dateStr}-${randomNum}`;
        }

        // 显示样品统计报告
        function showSampleReport() {
            showNotification('正在生成样品统计报告...', 'info');

            setTimeout(() => {
                const reportData = {
                    totalSamples: 156,
                    waterSamples: 98,
                    sludgeSamples: 58,
                    completedTests: 128,
                    pendingTests: 23,
                    averageProcessTime: '2.5天'
                };

                const reportWindow = window.open('', '_blank', 'width=900,height=700');
                reportWindow.document.write(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>样品管理统计报告</title>
                        <style>
                            body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
                            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
                            .header { text-align: center; border-bottom: 2px solid #667eea; padding-bottom: 20px; margin-bottom: 30px; }
                            .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
                            .stat-item { background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; border-radius: 10px; text-align: center; }
                            .stat-number { font-size: 2rem; font-weight: bold; margin-bottom: 5px; }
                            .stat-label { font-size: 0.9rem; opacity: 0.9; }
                            .chart-placeholder { background: #f8f9fa; border: 2px dashed #dee2e6; height: 200px; display: flex; align-items: center; justify-content: center; border-radius: 10px; margin: 20px 0; }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <div class="header">
                                <h1>样品管理统计报告</h1>
                                <p>报告生成时间：${new Date().toLocaleString()}</p>
                            </div>

                            <div class="stats-grid">
                                <div class="stat-item">
                                    <div class="stat-number">${reportData.totalSamples}</div>
                                    <div class="stat-label">样品总数</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">${reportData.waterSamples}</div>
                                    <div class="stat-label">水样数量</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">${reportData.sludgeSamples}</div>
                                    <div class="stat-label">污泥样数量</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">${reportData.completedTests}</div>
                                    <div class="stat-label">已完成检测</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">${reportData.pendingTests}</div>
                                    <div class="stat-label">检测中</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">${reportData.averageProcessTime}</div>
                                    <div class="stat-label">平均处理时间</div>
                                </div>
                            </div>

                            <div class="chart-placeholder">
                                <div style="text-align: center; color: #666;">
                                    <i class="fas fa-chart-bar" style="font-size: 3rem; margin-bottom: 10px;"></i>
                                    <p>样品处理趋势图表</p>
                                </div>
                            </div>

                            <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                                <h3>关键指标分析</h3>
                                <ul style="line-height: 1.8;">
                                    <li>样品处理完成率：${Math.round(reportData.completedTests / reportData.totalSamples * 100)}%</li>
                                    <li>水样占比：${Math.round(reportData.waterSamples / reportData.totalSamples * 100)}%</li>
                                    <li>污泥样占比：${Math.round(reportData.sludgeSamples / reportData.totalSamples * 100)}%</li>
                                    <li>平均处理周期：${reportData.averageProcessTime}</li>
                                </ul>
                            </div>
                        </div>
                    </body>
                    </html>
                `);
                reportWindow.document.close();
                showNotification('统计报告生成完成！', 'success');
            }, 2000);
        }

        // 药剂液位监测预警
        function checkReagentLevels() {
            const reagents = [
                { name: '聚合氯化铝(PAC)', level: 15, consumption: 8.5, status: 'danger' },
                { name: '聚丙烯酰胺(PAM)', level: 35, consumption: 3.2, status: 'warning' },
                { name: '次氯酸钠', level: 75, consumption: 12.8, status: 'normal' },
                { name: '二氧化氯', level: 28, consumption: 5.6, status: 'warning' }
            ];

            const criticalReagents = reagents.filter(r => r.status === 'danger');
            const warningReagents = reagents.filter(r => r.status === 'warning');

            if (criticalReagents.length > 0) {
                const names = criticalReagents.map(r => r.name).join('、');
                showNotification(`紧急提醒：${names} 液位过低，请立即补充！`, 'error');
            } else if (warningReagents.length > 0) {
                const names = warningReagents.map(r => r.name).join('、');
                showNotification(`注意：${names} 液位偏低，建议近期补充`, 'warning');
            }
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加一些交互效果
            const cards = document.querySelectorAll('.info-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // 页面加载完成后进行药剂液位检查
            setTimeout(() => {
                checkReagentLevels();
            }, 3000);

            // 添加回车键搜索功能
            const searchInput = document.getElementById('sampleSearchInput');
            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        searchSampleById();
                    }
                });
            }

            // 定期检查药剂液位（每30分钟）
            setInterval(() => {
                checkReagentLevels();
            }, 30 * 60 * 1000);
        });
    </script>
</body>
</html>
