<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>能源管理驾驶舱</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .dashboard-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 头部样式 */
        .dashboard-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px 35px;
            margin-bottom: 25px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .dashboard-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .main-title {
            font-size: 2.8rem;
            color: #1e3c72;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            position: relative;
            z-index: 1;
        }

        .subtitle {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }

        .time-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            font-size: 0.95rem;
            color: #888;
            position: relative;
            z-index: 1;
        }

        /* 区域选择器 */
        .area-selector {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .area-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            background: rgba(30, 60, 114, 0.1);
            color: #1e3c72;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
        }

        .area-btn.active {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            box-shadow: 0 6px 20px rgba(30, 60, 114, 0.3);
            transform: translateY(-2px);
        }

        .area-btn:hover:not(.active) {
            background: rgba(30, 60, 114, 0.2);
            transform: translateY(-1px);
        }

        /* 关键指标卡片 */
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .kpi-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .kpi-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 45px rgba(0, 0, 0, 0.15);
        }

        .kpi-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #1e3c72, #2a5298, #4facfe);
        }

        .kpi-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .kpi-title {
            font-size: 1.1rem;
            color: #666;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .kpi-trend {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .trend-up {
            background: #ffebee;
            color: #c62828;
        }

        .trend-down {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .trend-stable {
            background: #fff3e0;
            color: #ef6c00;
        }

        .kpi-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1e3c72;
            margin-bottom: 10px;
        }

        .kpi-unit {
            font-size: 1rem;
            color: #888;
            margin-left: 5px;
        }

        .kpi-comparison {
            display: flex;
            justify-content: space-between;
            font-size: 0.9rem;
            color: #666;
        }

        /* 图表容器 */
        .charts-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 25px;
            margin-bottom: 30px;
        }

        .chart-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #1e3c72;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chart-controls {
            display: flex;
            gap: 10px;
        }

        .chart-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            color: #666;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .chart-btn.active {
            background: #1e3c72;
            color: white;
            border-color: #1e3c72;
        }

        .chart-container {
            position: relative;
            height: 350px;
        }

        /* 数据表格 */
        .data-table-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table th {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
        }

        .data-table tr:hover {
            background: rgba(30, 60, 114, 0.05);
        }

        .data-table .number {
            text-align: right;
            font-weight: 600;
        }

        .data-table .cost {
            color: #d32f2f;
            font-weight: 600;
        }

        /* 成本分析区域 */
        .cost-analysis {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-bottom: 30px;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .cost-analysis {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: 15px;
            }

            .main-title {
                font-size: 2.2rem;
                flex-direction: column;
                gap: 10px;
            }

            .area-selector {
                flex-direction: column;
                align-items: center;
            }

            .kpi-grid {
                grid-template-columns: 1fr;
            }

            .time-info {
                flex-direction: column;
                gap: 10px;
            }

            .chart-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.6s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-30px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* 数值动画 */
        .counter {
            animation: countUp 2s ease-out;
        }

        @keyframes countUp {
            from { opacity: 0; transform: scale(0.5); }
            to { opacity: 1; transform: scale(1); }
        }

        /* 状态指示器 */
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-normal { background: #4caf50; }
        .status-warning { background: #ff9800; }
        .status-danger { background: #f44336; }

        /* 工具提示 */
        .tooltip {
            position: relative;
            cursor: help;
        }

        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.8rem;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s;
            z-index: 1000;
        }

        .tooltip:hover::after {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 头部信息 -->
        <div class="dashboard-header fade-in">
            <h1 class="main-title">
                <i class="fas fa-tachometer-alt"></i>
                能源管理驾驶舱
                <i class="fas fa-bolt"></i>
            </h1>
            <p class="subtitle">Energy Management Dashboard - 实时监控 · 精准分析 · 智能决策</p>
            <div class="time-info">
                <span><i class="fas fa-calendar-day"></i> 数据日期：2024年1月15日</span>
                <span><i class="fas fa-sync-alt"></i> 更新时间：14:30:25</span>
                <span><i class="fas fa-signal"></i> 数据状态：实时在线</span>
            </div>
        </div>

        <!-- 区域选择器 -->
        <div class="area-selector slide-in">
            <button class="area-btn active" onclick="switchArea('water-treatment')">
                <i class="fas fa-industry"></i>
                制水车间
            </button>
            <button class="area-btn" onclick="switchArea('pump-room')">
                <i class="fas fa-cogs"></i>
                泵房区域
            </button>
            <button class="area-btn" onclick="switchArea('laboratory')">
                <i class="fas fa-flask"></i>
                化验室
            </button>
            <button class="area-btn" onclick="switchArea('office')">
                <i class="fas fa-building"></i>
                办公区域
            </button>
            <button class="area-btn" onclick="switchArea('storage')">
                <i class="fas fa-warehouse"></i>
                仓储区域
            </button>
        </div>

        <!-- 关键指标 -->
        <div class="kpi-grid fade-in">
            <div class="kpi-card">
                <div class="kpi-header">
                    <div class="kpi-title">
                        <i class="fas fa-tint"></i>
                        单吨水能耗
                    </div>
                    <div class="kpi-trend trend-down">
                        <i class="fas fa-arrow-down"></i>
                        -3.2%
                    </div>
                </div>
                <div class="kpi-value counter">
                    0.85
                    <span class="kpi-unit">kWh/吨</span>
                </div>
                <div class="kpi-comparison">
                    <span>昨日：0.88</span>
                    <span>上月：0.91</span>
                </div>
            </div>

            <div class="kpi-card">
                <div class="kpi-header">
                    <div class="kpi-title">
                        <i class="fas fa-flask"></i>
                        单吨水药耗
                    </div>
                    <div class="kpi-trend trend-up">
                        <i class="fas fa-arrow-up"></i>
                        +1.8%
                    </div>
                </div>
                <div class="kpi-value counter">
                    12.5
                    <span class="kpi-unit">元/吨</span>
                </div>
                <div class="kpi-comparison">
                    <span>昨日：12.3</span>
                    <span>上月：11.8</span>
                </div>
            </div>

            <div class="kpi-card">
                <div class="kpi-header">
                    <div class="kpi-title">
                        <i class="fas fa-coins"></i>
                        能耗成本
                    </div>
                    <div class="kpi-trend trend-down">
                        <i class="fas fa-arrow-down"></i>
                        -2.1%
                    </div>
                </div>
                <div class="kpi-value counter">
                    28,650
                    <span class="kpi-unit">元</span>
                </div>
                <div class="kpi-comparison">
                    <span>昨日：29,280</span>
                    <span>预算：30,000</span>
                </div>
            </div>

            <div class="kpi-card">
                <div class="kpi-header">
                    <div class="kpi-title">
                        <i class="fas fa-prescription-bottle"></i>
                        药耗成本
                    </div>
                    <div class="kpi-trend trend-stable">
                        <i class="fas fa-minus"></i>
                        +0.5%
                    </div>
                </div>
                <div class="kpi-value counter">
                    15,420
                    <span class="kpi-unit">元</span>
                </div>
                <div class="kpi-comparison">
                    <span>昨日：15,350</span>
                    <span>预算：16,000</span>
                </div>
            </div>

            <div class="kpi-card">
                <div class="kpi-header">
                    <div class="kpi-title">
                        <i class="fas fa-bolt"></i>
                        总用电量
                    </div>
                    <div class="kpi-trend trend-down">
                        <i class="fas fa-arrow-down"></i>
                        -4.2%
                    </div>
                </div>
                <div class="kpi-value counter">
                    33,680
                    <span class="kpi-unit">kWh</span>
                </div>
                <div class="kpi-comparison">
                    <span>昨日：35,160</span>
                    <span>上月均值：36,200</span>
                </div>
            </div>

            <div class="kpi-card">
                <div class="kpi-header">
                    <div class="kpi-title">
                        <i class="fas fa-water"></i>
                        产水量
                    </div>
                    <div class="kpi-trend trend-up">
                        <i class="fas fa-arrow-up"></i>
                        +2.3%
                    </div>
                </div>
                <div class="kpi-value counter">
                    39,600
                    <span class="kpi-unit">吨</span>
                </div>
                <div class="kpi-comparison">
                    <span>昨日：38,700</span>
                    <span>计划：40,000</span>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-grid">
            <div class="chart-card fade-in">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-line"></i>
                        能耗趋势分析
                    </h3>
                    <div class="chart-controls">
                        <button class="chart-btn active" onclick="switchPeriod('day')">日</button>
                        <button class="chart-btn" onclick="switchPeriod('week')">周</button>
                        <button class="chart-btn" onclick="switchPeriod('month')">月</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="energyTrendChart"></canvas>
                </div>
            </div>

            <div class="chart-card fade-in">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-pie"></i>
                        能耗结构分布
                    </h3>
                </div>
                <div class="chart-container">
                    <canvas id="energyStructureChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 成本分析 -->
        <div class="cost-analysis">
            <div class="chart-card fade-in">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-bar"></i>
                        药剂消耗分析
                    </h3>
                </div>
                <div class="chart-container">
                    <canvas id="chemicalConsumptionChart"></canvas>
                </div>
            </div>

            <div class="chart-card fade-in">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-coins"></i>
                        成本构成分析
                    </h3>
                </div>
                <div class="chart-container">
                    <canvas id="costAnalysisChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 详细数据表格 -->
        <div class="data-table-card fade-in">
            <div class="chart-header">
                <h3 class="chart-title">
                    <i class="fas fa-table"></i>
                    能耗与药耗详细数据
                </h3>
                <div class="chart-controls">
                    <button class="chart-btn active" onclick="switchDataView('consumption')">消耗数量</button>
                    <button class="chart-btn" onclick="switchDataView('cost')">成本分析</button>
                    <button class="chart-btn" onclick="exportData()">
                        <i class="fas fa-download"></i>
                        导出数据
                    </button>
                </div>
            </div>

            <!-- 消耗数量视图 -->
            <div id="consumptionView">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>项目类别</th>
                            <th>项目名称</th>
                            <th>今日消耗</th>
                            <th>昨日消耗</th>
                            <th>本月累计</th>
                            <th>上月同期</th>
                            <th>同比变化</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td rowspan="3"><strong>能源消耗</strong></td>
                            <td>用电量</td>
                            <td class="number">33,680 kWh</td>
                            <td class="number">35,160 kWh</td>
                            <td class="number">506,200 kWh</td>
                            <td class="number">523,800 kWh</td>
                            <td class="number" style="color: #2e7d32;">-3.4%</td>
                            <td><span class="status-indicator status-normal"></span>正常</td>
                        </tr>
                        <tr>
                            <td>用水量</td>
                            <td class="number">2,850 吨</td>
                            <td class="number">2,920 吨</td>
                            <td class="number">42,750 吨</td>
                            <td class="number">44,200 吨</td>
                            <td class="number" style="color: #2e7d32;">-3.3%</td>
                            <td><span class="status-indicator status-normal"></span>正常</td>
                        </tr>
                        <tr>
                            <td>天然气</td>
                            <td class="number">1,250 m³</td>
                            <td class="number">1,180 m³</td>
                            <td class="number">18,750 m³</td>
                            <td class="number">17,800 m³</td>
                            <td class="number" style="color: #d32f2f;">+5.3%</td>
                            <td><span class="status-indicator status-warning"></span>偏高</td>
                        </tr>
                        <tr>
                            <td rowspan="6"><strong>药剂消耗</strong></td>
                            <td>聚合氯化铝(PAC)</td>
                            <td class="number">850 kg</td>
                            <td class="number">820 kg</td>
                            <td class="number">12,750 kg</td>
                            <td class="number">11,900 kg</td>
                            <td class="number" style="color: #d32f2f;">+7.1%</td>
                            <td><span class="status-indicator status-warning"></span>偏高</td>
                        </tr>
                        <tr>
                            <td>聚丙烯酰胺(PAM)</td>
                            <td class="number">120 kg</td>
                            <td class="number">115 kg</td>
                            <td class="number">1,800 kg</td>
                            <td class="number">1,725 kg</td>
                            <td class="number" style="color: #d32f2f;">+4.3%</td>
                            <td><span class="status-indicator status-normal"></span>正常</td>
                        </tr>
                        <tr>
                            <td>次氯酸钠</td>
                            <td class="number">680 kg</td>
                            <td class="number">695 kg</td>
                            <td class="number">10,200 kg</td>
                            <td class="number">10,425 kg</td>
                            <td class="number" style="color: #2e7d32;">-2.2%</td>
                            <td><span class="status-indicator status-normal"></span>正常</td>
                        </tr>
                        <tr>
                            <td>二氧化氯</td>
                            <td class="number">95 kg</td>
                            <td class="number">88 kg</td>
                            <td class="number">1,425 kg</td>
                            <td class="number">1,320 kg</td>
                            <td class="number" style="color: #d32f2f;">+8.0%</td>
                            <td><span class="status-indicator status-warning"></span>偏高</td>
                        </tr>
                        <tr>
                            <td>石灰</td>
                            <td class="number">450 kg</td>
                            <td class="number">465 kg</td>
                            <td class="number">6,750 kg</td>
                            <td class="number">6,975 kg</td>
                            <td class="number" style="color: #2e7d32;">-3.2%</td>
                            <td><span class="status-indicator status-normal"></span>正常</td>
                        </tr>
                        <tr>
                            <td>活性炭</td>
                            <td class="number">280 kg</td>
                            <td class="number">275 kg</td>
                            <td class="number">4,200 kg</td>
                            <td class="number">4,125 kg</td>
                            <td class="number" style="color: #d32f2f;">+1.8%</td>
                            <td><span class="status-indicator status-normal"></span>正常</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 成本分析视图 -->
            <div id="costView" style="display: none;">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>成本类别</th>
                            <th>项目名称</th>
                            <th>单价</th>
                            <th>今日成本</th>
                            <th>昨日成本</th>
                            <th>本月累计</th>
                            <th>预算对比</th>
                            <th>成本占比</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td rowspan="3"><strong>能源成本</strong></td>
                            <td>电费</td>
                            <td class="number">0.85 元/kWh</td>
                            <td class="number cost">28,628 元</td>
                            <td class="number cost">29,886 元</td>
                            <td class="number cost">430,270 元</td>
                            <td class="number" style="color: #2e7d32;">-4.2%</td>
                            <td class="number">64.8%</td>
                        </tr>
                        <tr>
                            <td>水费</td>
                            <td class="number">3.20 元/吨</td>
                            <td class="number cost">9,120 元</td>
                            <td class="number cost">9,344 元</td>
                            <td class="number cost">136,800 元</td>
                            <td class="number" style="color: #2e7d32;">-2.1%</td>
                            <td class="number">20.6%</td>
                        </tr>
                        <tr>
                            <td>天然气费</td>
                            <td class="number">2.80 元/m³</td>
                            <td class="number cost">3,500 元</td>
                            <td class="number cost">3,304 元</td>
                            <td class="number cost">52,500 元</td>
                            <td class="number" style="color: #d32f2f;">+8.5%</td>
                            <td class="number">7.9%</td>
                        </tr>
                        <tr>
                            <td rowspan="6"><strong>药剂成本</strong></td>
                            <td>聚合氯化铝(PAC)</td>
                            <td class="number">2.80 元/kg</td>
                            <td class="number cost">2,380 元</td>
                            <td class="number cost">2,296 元</td>
                            <td class="number cost">35,700 元</td>
                            <td class="number" style="color: #d32f2f;">+12.3%</td>
                            <td class="number">15.4%</td>
                        </tr>
                        <tr>
                            <td>聚丙烯酰胺(PAM)</td>
                            <td class="number">18.50 元/kg</td>
                            <td class="number cost">2,220 元</td>
                            <td class="number cost">2,128 元</td>
                            <td class="number cost">33,300 元</td>
                            <td class="number" style="color: #d32f2f;">+6.8%</td>
                            <td class="number">14.4%</td>
                        </tr>
                        <tr>
                            <td>次氯酸钠</td>
                            <td class="number">1.20 元/kg</td>
                            <td class="number cost">816 元</td>
                            <td class="number cost">834 元</td>
                            <td class="number cost">12,240 元</td>
                            <td class="number" style="color: #2e7d32;">-1.8%</td>
                            <td class="number">5.3%</td>
                        </tr>
                        <tr>
                            <td>二氧化氯</td>
                            <td class="number">8.50 元/kg</td>
                            <td class="number cost">808 元</td>
                            <td class="number cost">748 元</td>
                            <td class="number cost">12,113 元</td>
                            <td class="number" style="color: #d32f2f;">+15.2%</td>
                            <td class="number">5.2%</td>
                        </tr>
                        <tr>
                            <td>石灰</td>
                            <td class="number">0.45 元/kg</td>
                            <td class="number cost">203 元</td>
                            <td class="number cost">209 元</td>
                            <td class="number cost">3,038 元</td>
                            <td class="number" style="color: #2e7d32;">-2.8%</td>
                            <td class="number">1.3%</td>
                        </tr>
                        <tr>
                            <td>活性炭</td>
                            <td class="number">12.00 元/kg</td>
                            <td class="number cost">3,360 元</td>
                            <td class="number cost">3,300 元</td>
                            <td class="number cost">50,400 元</td>
                            <td class="number" style="color: #d32f2f;">+3.2%</td>
                            <td class="number">21.8%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 对比分析 -->
        <div class="charts-grid">
            <div class="chart-card fade-in">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-balance-scale"></i>
                        同比环比分析
                    </h3>
                    <div class="chart-controls">
                        <button class="chart-btn active" onclick="switchComparison('yoy')">同比</button>
                        <button class="chart-btn" onclick="switchComparison('mom')">环比</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="comparisonChart"></canvas>
                </div>
            </div>

            <div class="chart-card fade-in">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-bullseye"></i>
                        预算执行情况
                    </h3>
                </div>
                <div class="chart-container">
                    <canvas id="budgetChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 效率指标 -->
        <div class="data-table-card fade-in">
            <div class="chart-header">
                <h3 class="chart-title">
                    <i class="fas fa-chart-line"></i>
                    效率指标分析
                </h3>
            </div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 20px;">
                <div style="background: linear-gradient(135deg, #e3f2fd, #bbdefb); padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 0.9rem; color: #1565c0; margin-bottom: 8px;">
                        <i class="fas fa-tint"></i> 单吨水电耗
                    </div>
                    <div style="font-size: 2rem; font-weight: bold; color: #0d47a1;">0.85</div>
                    <div style="font-size: 0.8rem; color: #1976d2;">kWh/吨 (目标: 0.90)</div>
                </div>

                <div style="background: linear-gradient(135deg, #e8f5e8, #c8e6c8); padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 0.9rem; color: #2e7d32; margin-bottom: 8px;">
                        <i class="fas fa-flask"></i> 单吨水药耗
                    </div>
                    <div style="font-size: 2rem; font-weight: bold; color: #1b5e20;">12.5</div>
                    <div style="font-size: 0.8rem; color: #388e3c;">元/吨 (目标: 13.0)</div>
                </div>

                <div style="background: linear-gradient(135deg, #fff3e0, #ffe0b2); padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 0.9rem; color: #ef6c00; margin-bottom: 8px;">
                        <i class="fas fa-percentage"></i> 设备利用率
                    </div>
                    <div style="font-size: 2rem; font-weight: bold; color: #e65100;">92.3</div>
                    <div style="font-size: 0.8rem; color: #f57c00;">% (目标: 90.0%)</div>
                </div>

                <div style="background: linear-gradient(135deg, #fce4ec, #f8bbd9); padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 0.9rem; color: #c2185b; margin-bottom: 8px;">
                        <i class="fas fa-coins"></i> 综合成本
                    </div>
                    <div style="font-size: 2rem; font-weight: bold; color: #ad1457;">1.12</div>
                    <div style="font-size: 0.8rem; color: #e91e63;">元/吨 (目标: 1.15)</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            startRealTimeUpdate();
            animateCounters();
        });

        // 初始化图表
        function initializeCharts() {
            initEnergyTrendChart();
            initEnergyStructureChart();
            initChemicalConsumptionChart();
            initCostAnalysisChart();
            initComparisonChart();
            initBudgetChart();
        }

        // 能耗趋势图
        function initEnergyTrendChart() {
            const ctx = document.getElementById('energyTrendChart');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                    datasets: [{
                        label: '用电量 (kWh)',
                        data: [2800, 2200, 3500, 4200, 4800, 4100, 3200],
                        borderColor: '#1e3c72',
                        backgroundColor: 'rgba(30, 60, 114, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '用水量 (吨)',
                        data: [1200, 800, 1800, 2200, 2500, 2100, 1600],
                        borderColor: '#2a5298',
                        backgroundColor: 'rgba(42, 82, 152, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        }
                    }
                }
            });
        }

        // 能耗结构饼图
        function initEnergyStructureChart() {
            const ctx = document.getElementById('energyStructureChart');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['水泵用电', '照明用电', '设备用电', '空调用电', '其他用电'],
                    datasets: [{
                        data: [45, 15, 25, 10, 5],
                        backgroundColor: [
                            '#1e3c72',
                            '#2a5298',
                            '#4facfe',
                            '#00f2fe',
                            '#43e97b'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // 药剂消耗柱状图
        function initChemicalConsumptionChart() {
            const ctx = document.getElementById('chemicalConsumptionChart');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['PAC', 'PAM', '次氯酸钠', '二氧化氯', '石灰', '活性炭'],
                    datasets: [{
                        label: '消耗量 (kg)',
                        data: [850, 120, 680, 95, 450, 280],
                        backgroundColor: [
                            '#1e3c72',
                            '#2a5298',
                            '#4facfe',
                            '#00f2fe',
                            '#43e97b',
                            '#f093fb'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '消耗量 (kg)'
                            }
                        }
                    }
                }
            });
        }

        // 成本分析图
        function initCostAnalysisChart() {
            const ctx = document.getElementById('costAnalysisChart');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['电费', '水费', '药剂费', '人工费', '维护费'],
                    datasets: [{
                        label: '成本 (元)',
                        data: [28650, 3200, 15420, 8500, 4200],
                        backgroundColor: [
                            '#1e3c72',
                            '#2a5298',
                            '#4facfe',
                            '#00f2fe',
                            '#43e97b'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '成本 (元)'
                            }
                        }
                    }
                }
            });
        }

        // 区域切换
        function switchArea(area) {
            // 更新按钮状态
            document.querySelectorAll('.area-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 模拟数据更新
            updateDashboardData(area);
        }

        // 时间周期切换
        function switchPeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 这里可以更新图表数据
            console.log('切换到' + period + '视图');
        }

        // 更新仪表板数据
        function updateDashboardData(area) {
            // 模拟不同区域的数据
            const areaData = {
                'water-treatment': {
                    title: '制水车间',
                    energyConsumption: '0.85',
                    chemicalCost: '12.5',
                    totalCost: '28,650'
                },
                'pump-room': {
                    title: '泵房区域',
                    energyConsumption: '0.92',
                    chemicalCost: '8.3',
                    totalCost: '35,200'
                },
                'laboratory': {
                    title: '化验室',
                    energyConsumption: '0.15',
                    chemicalCost: '15.8',
                    totalCost: '4,500'
                }
            };

            // 更新页面标题和数据
            console.log('切换到' + areaData[area]?.title || area);
        }

        // 数值动画
        function animateCounters() {
            const counters = document.querySelectorAll('.counter');
            counters.forEach(counter => {
                const target = parseFloat(counter.textContent);
                const increment = target / 100;
                let current = 0;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    
                    const unit = counter.querySelector('.kpi-unit');
                    const unitText = unit ? unit.textContent : '';
                    counter.innerHTML = current.toFixed(target < 10 ? 1 : 0) + 
                        (unit ? `<span class="kpi-unit">${unitText}</span>` : '');
                }, 20);
            });
        }

        // 实时数据更新
        function startRealTimeUpdate() {
            setInterval(() => {
                // 更新时间
                const now = new Date();
                const timeStr = now.toLocaleTimeString();
                document.querySelector('.time-info span:nth-child(2)').innerHTML = 
                    `<i class="fas fa-sync-alt"></i> 更新时间：${timeStr}`;
                
                // 模拟数据微调
                updateRandomData();
            }, 30000); // 每30秒更新一次
        }

        // 同比环比分析图
        function initComparisonChart() {
            const ctx = document.getElementById('comparisonChart');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['用电量', '用水量', 'PAC', 'PAM', '次氯酸钠', '二氧化氯'],
                    datasets: [{
                        label: '同比变化 (%)',
                        data: [-3.4, -3.3, 7.1, 4.3, -2.2, 8.0],
                        backgroundColor: function(context) {
                            const value = context.parsed.y;
                            return value > 0 ? '#f44336' : '#4caf50';
                        },
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '变化率 (%)'
                            }
                        }
                    }
                }
            });
        }

        // 预算执行情况图
        function initBudgetChart() {
            const ctx = document.getElementById('budgetChart');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['已使用预算', '剩余预算'],
                    datasets: [{
                        data: [68.5, 31.5],
                        backgroundColor: ['#1e3c72', '#e0e0e0'],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    },
                    cutout: '60%'
                }
            });
        }

        // 数据视图切换
        function switchDataView(view) {
            // 更新按钮状态
            document.querySelectorAll('.chart-btn').forEach(btn => {
                if (btn.textContent.includes('消耗数量') || btn.textContent.includes('成本分析')) {
                    btn.classList.remove('active');
                }
            });
            event.target.classList.add('active');

            // 切换视图
            if (view === 'consumption') {
                document.getElementById('consumptionView').style.display = 'block';
                document.getElementById('costView').style.display = 'none';
            } else if (view === 'cost') {
                document.getElementById('consumptionView').style.display = 'none';
                document.getElementById('costView').style.display = 'block';
            }
        }

        // 对比分析切换
        function switchComparison(type) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                if (btn.textContent === '同比' || btn.textContent === '环比') {
                    btn.classList.remove('active');
                }
            });
            event.target.classList.add('active');

            // 更新图表数据
            const chart = Chart.getChart('comparisonChart');
            if (chart) {
                if (type === 'yoy') {
                    chart.data.datasets[0].data = [-3.4, -3.3, 7.1, 4.3, -2.2, 8.0];
                    chart.data.datasets[0].label = '同比变化 (%)';
                } else {
                    chart.data.datasets[0].data = [-1.2, -0.8, 2.3, 1.8, -0.5, 3.2];
                    chart.data.datasets[0].label = '环比变化 (%)';
                }
                chart.update();
            }
        }

        // 导出数据
        function exportData() {
            // 模拟数据导出
            const data = [
                ['项目类别', '项目名称', '今日消耗', '昨日消耗', '本月累计', '上月同期', '同比变化'],
                ['能源消耗', '用电量', '33,680 kWh', '35,160 kWh', '506,200 kWh', '523,800 kWh', '-3.4%'],
                ['能源消耗', '用水量', '2,850 吨', '2,920 吨', '42,750 吨', '44,200 吨', '-3.3%'],
                ['药剂消耗', 'PAC', '850 kg', '820 kg', '12,750 kg', '11,900 kg', '+7.1%']
            ];

            let csvContent = '';
            data.forEach(row => {
                csvContent += row.join(',') + '\n';
            });

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `能耗数据_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 显示导出成功提示
            showNotification('数据导出成功！', 'success');
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${getNotificationColor(type)};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                z-index: 10000;
                animation: slideInRight 0.3s ease-out;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                max-width: 300px;
            `;

            const icon = getNotificationIcon(type);
            notification.innerHTML = `<i class="${icon}" style="margin-right: 8px;"></i>${message}`;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-out';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 获取通知颜色
        function getNotificationColor(type) {
            const colors = {
                'success': '#4caf50',
                'error': '#f44336',
                'warning': '#ff9800',
                'info': '#2196f3'
            };
            return colors[type] || colors.info;
        }

        // 获取通知图标
        function getNotificationIcon(type) {
            const icons = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };
            return icons[type] || icons.info;
        }

        // 随机更新数据
        function updateRandomData() {
            const kpiValues = document.querySelectorAll('.kpi-value');
            kpiValues.forEach(value => {
                const current = parseFloat(value.textContent.replace(/,/g, ''));
                const variation = (Math.random() - 0.5) * 0.02; // ±1%的变化
                const newValue = current * (1 + variation);

                const unit = value.querySelector('.kpi-unit');
                const unitText = unit ? unit.textContent : '';

                if (current < 10) {
                    value.innerHTML = newValue.toFixed(1) +
                        (unit ? `<span class="kpi-unit">${unitText}</span>` : '');
                } else {
                    value.innerHTML = Math.round(newValue).toLocaleString() +
                        (unit ? `<span class="kpi-unit">${unitText}</span>` : '');
                }
            });

            // 更新趋势指示器
            updateTrendIndicators();
        }

        // 更新趋势指示器
        function updateTrendIndicators() {
            const trends = document.querySelectorAll('.kpi-trend');
            trends.forEach(trend => {
                const change = (Math.random() - 0.5) * 10; // ±5%的变化
                const absChange = Math.abs(change);

                if (absChange < 1) {
                    trend.className = 'kpi-trend trend-stable';
                    trend.innerHTML = '<i class="fas fa-minus"></i>' + absChange.toFixed(1) + '%';
                } else if (change > 0) {
                    trend.className = 'kpi-trend trend-up';
                    trend.innerHTML = '<i class="fas fa-arrow-up"></i>+' + absChange.toFixed(1) + '%';
                } else {
                    trend.className = 'kpi-trend trend-down';
                    trend.innerHTML = '<i class="fas fa-arrow-down"></i>-' + absChange.toFixed(1) + '%';
                }
            });
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
