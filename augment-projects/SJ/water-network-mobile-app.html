<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>供水管网移动APP</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        /* 移动端容器 */
        .mobile-container {
            max-width: 414px;
            margin: 0 auto;
            background: #f8f9fa;
            min-height: 100vh;
            position: relative;
            box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
        }

        /* 状态栏 */
        .status-bar {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 头部导航 */
        .app-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .app-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .app-subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .notification-badge {
            position: absolute;
            top: 15px;
            right: 20px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 700;
        }

        /* 主要内容区域 */
        .main-content {
            padding: 0;
            padding-bottom: 80px;
        }

        /* 标签页导航 */
        .tab-nav {
            background: white;
            display: flex;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .tab-item {
            flex: 1;
            padding: 15px 10px;
            text-align: center;
            background: none;
            border: none;
            color: #666;
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .tab-item.active {
            color: #667eea;
        }

        .tab-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .tab-icon {
            display: block;
            font-size: 1.2rem;
            margin-bottom: 5px;
        }

        /* 标签页内容 */
        .tab-content {
            display: none;
            padding: 20px;
            animation: fadeIn 0.3s ease-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 工作台卡片 */
        .workspace-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 25px;
        }

        .workspace-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .workspace-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .workspace-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .workspace-title {
            font-size: 1rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 8px;
        }

        .workspace-desc {
            font-size: 0.8rem;
            color: #666;
            line-height: 1.4;
        }

        .workspace-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: 700;
        }

        /* 统计卡片 */
        .stats-row {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .stat-card {
            flex: 1;
            background: white;
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #666;
        }

        /* 消息列表 */
        .message-list {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .message-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: flex-start;
            gap: 15px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .message-item:hover {
            background: #f8f9fa;
        }

        .message-item:last-child {
            border-bottom: none;
        }

        .message-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.1rem;
            flex-shrink: 0;
        }

        .message-icon.alarm { background: #ff4757; }
        .message-icon.task { background: #2ed573; }
        .message-icon.info { background: #3742fa; }

        .message-content {
            flex: 1;
        }

        .message-title {
            font-size: 0.95rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .message-desc {
            font-size: 0.8rem;
            color: #666;
            line-height: 1.4;
            margin-bottom: 5px;
        }

        .message-time {
            font-size: 0.75rem;
            color: #999;
        }

        .message-badge {
            width: 8px;
            height: 8px;
            background: #ff4757;
            border-radius: 50%;
            flex-shrink: 0;
            margin-top: 8px;
        }

        /* 地图容器 */
        .map-container {
            background: white;
            border-radius: 15px;
            height: 300px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .map-placeholder {
            height: 100%;
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #1976d2;
        }

        .map-placeholder i {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .map-controls {
            position: absolute;
            top: 15px;
            right: 15px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .map-btn {
            width: 40px;
            height: 40px;
            background: white;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            cursor: pointer;
            color: #667eea;
            font-size: 1.1rem;
        }

        /* 设备卡片 */
        .device-grid {
            display: grid;
            gap: 15px;
        }

        .device-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .device-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .device-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .device-name {
            font-size: 1.1rem;
            font-weight: 700;
            color: #333;
        }

        .device-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-normal { background: #d4edda; color: #155724; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-danger { background: #f8d7da; color: #721c24; }

        .device-info {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }

        .device-param {
            text-align: center;
        }

        .param-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #667eea;
        }

        .param-label {
            font-size: 0.8rem;
            color: #666;
            margin-top: 3px;
        }

        .device-actions {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 8px;
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #e0e0e0;
        }

        /* 扫码界面 */
        .scan-container {
            text-align: center;
            padding: 40px 20px;
        }

        .scan-frame {
            width: 250px;
            height: 250px;
            border: 3px solid #667eea;
            border-radius: 15px;
            margin: 0 auto 30px;
            position: relative;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .scan-frame::before,
        .scan-frame::after {
            content: '';
            position: absolute;
            width: 30px;
            height: 30px;
            border: 3px solid #667eea;
        }

        .scan-frame::before {
            top: -3px;
            left: -3px;
            border-right: none;
            border-bottom: none;
        }

        .scan-frame::after {
            bottom: -3px;
            right: -3px;
            border-left: none;
            border-top: none;
        }

        .scan-icon {
            font-size: 4rem;
            color: #667eea;
            opacity: 0.7;
        }

        .scan-text {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 30px;
        }

        .scan-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .scan-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            display: flex;
            box-shadow: 0 -2px 15px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .nav-item {
            flex: 1;
            padding: 12px 10px;
            text-align: center;
            background: none;
            border: none;
            color: #999;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-item.active {
            color: #667eea;
        }

        .nav-item i {
            display: block;
            font-size: 1.3rem;
            margin-bottom: 5px;
        }

        /* 浮动按钮 */
        .fab {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            z-index: 999;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
        }

        /* 响应式调整 */
        @media (max-width: 375px) {
            .mobile-container {
                max-width: 100%;
            }

            .workspace-grid {
                grid-template-columns: 1fr;
            }

            .stats-row {
                flex-direction: column;
            }
        }

        /* 加载动画 */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: #667eea;
            font-size: 1rem;
        }

        .loading i {
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 模态框 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px;
            max-width: 350px;
            width: 100%;
            text-align: center;
        }

        .modal-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
        }

        .modal-text {
            font-size: 1rem;
            color: #666;
            line-height: 1.5;
            margin-bottom: 25px;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
        }

        .modal-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .modal-btn.primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .modal-btn.secondary {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #e0e0e0;
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>15:30</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
                <span>85%</span>
            </div>
        </div>

        <!-- 应用头部 -->
        <div class="app-header">
            <h1 class="app-title">供水管网移动APP</h1>
            <p class="app-subtitle">Water Network Mobile System</p>
            <div class="notification-badge">5</div>
        </div>

        <!-- 标签页导航 -->
        <div class="tab-nav">
            <button class="tab-item active" data-tab="workspace">
                <i class="tab-icon fas fa-home"></i>
                工作台
            </button>
            <button class="tab-item" data-tab="messages">
                <i class="tab-icon fas fa-bell"></i>
                消息
            </button>
            <button class="tab-item" data-tab="map">
                <i class="tab-icon fas fa-map"></i>
                地图
            </button>
            <button class="tab-item" data-tab="devices">
                <i class="tab-icon fas fa-cogs"></i>
                设备
            </button>
            <button class="tab-item" data-tab="scan">
                <i class="tab-icon fas fa-qrcode"></i>
                扫码
            </button>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 个人工作台 -->
            <div id="workspace" class="tab-content active">
                <!-- 统计数据 -->
                <div class="stats-row">
                    <div class="stat-card">
                        <div class="stat-value">12</div>
                        <div class="stat-label">待办任务</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">5</div>
                        <div class="stat-label">报警消息</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">156</div>
                        <div class="stat-label">监控设备</div>
                    </div>
                </div>

                <!-- 工作台功能 -->
                <div class="workspace-grid">
                    <div class="workspace-card" onclick="openFunction('patrol')">
                        <div class="workspace-icon">
                            <i class="fas fa-route"></i>
                        </div>
                        <div class="workspace-title">巡检任务</div>
                        <div class="workspace-desc">今日巡检8个点位</div>
                        <div class="workspace-badge">3</div>
                    </div>

                    <div class="workspace-card" onclick="openFunction('repair')">
                        <div class="workspace-icon">
                            <i class="fas fa-wrench"></i>
                        </div>
                        <div class="workspace-title">维修工单</div>
                        <div class="workspace-desc">待处理工单5个</div>
                        <div class="workspace-badge">5</div>
                    </div>

                    <div class="workspace-card" onclick="openFunction('monitor')">
                        <div class="workspace-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="workspace-title">实时监控</div>
                        <div class="workspace-desc">156个设备在线</div>
                    </div>

                    <div class="workspace-card" onclick="openFunction('report')">
                        <div class="workspace-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="workspace-title">工作报告</div>
                        <div class="workspace-desc">生成日报周报</div>
                    </div>

                    <div class="workspace-card" onclick="openFunction('emergency')">
                        <div class="workspace-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="workspace-title">应急处置</div>
                        <div class="workspace-desc">应急预案管理</div>
                        <div class="workspace-badge">1</div>
                    </div>

                    <div class="workspace-card" onclick="openFunction('analysis')">
                        <div class="workspace-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="workspace-title">数据分析</div>
                        <div class="workspace-desc">运行数据统计</div>
                    </div>
                </div>
            </div>

            <!-- 报警消息 -->
            <div id="messages" class="tab-content">
                <div class="message-list">
                    <div class="message-item" onclick="openMessage('alarm', 1)">
                        <div class="message-icon alarm">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-title">高压预警</div>
                            <div class="message-desc">东区泵站出口压力超过0.8MPa，请立即检查</div>
                            <div class="message-time">2分钟前</div>
                        </div>
                        <div class="message-badge"></div>
                    </div>

                    <div class="message-item" onclick="openMessage('task', 2)">
                        <div class="message-icon task">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-title">巡检提醒</div>
                            <div class="message-desc">建设路监测点巡检任务即将到期，请及时完成</div>
                            <div class="message-time">15分钟前</div>
                        </div>
                        <div class="message-badge"></div>
                    </div>

                    <div class="message-item" onclick="openMessage('alarm', 3)">
                        <div class="message-icon alarm">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-title">设备故障</div>
                            <div class="message-desc">西区3号水泵运行异常，振动值超标</div>
                            <div class="message-time">1小时前</div>
                        </div>
                        <div class="message-badge"></div>
                    </div>

                    <div class="message-item" onclick="openMessage('info', 4)">
                        <div class="message-icon info">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-title">系统通知</div>
                            <div class="message-desc">系统将于今晚22:00进行维护升级</div>
                            <div class="message-time">2小时前</div>
                        </div>
                    </div>

                    <div class="message-item" onclick="openMessage('task', 5)">
                        <div class="message-icon task">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-title">任务完成</div>
                            <div class="message-desc">南区管网压力调节任务已完成</div>
                            <div class="message-time">3小时前</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- GIS地图 -->
            <div id="map" class="tab-content">
                <div class="map-container">
                    <div class="map-placeholder">
                        <i class="fas fa-map-marked-alt"></i>
                        <div style="font-size: 1.2rem; font-weight: 600; margin-bottom: 10px;">GIS地图定位</div>
                        <div style="font-size: 0.9rem;">实时查看设备位置信息</div>
                    </div>
                    <div class="map-controls">
                        <button class="map-btn" onclick="locateMe()">
                            <i class="fas fa-crosshairs"></i>
                        </button>
                        <button class="map-btn" onclick="toggleLayer()">
                            <i class="fas fa-layer-group"></i>
                        </button>
                        <button class="map-btn" onclick="searchDevice()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- 设备列表 -->
                <div class="device-grid">
                    <div class="device-card" onclick="viewDeviceLocation('pump-001')">
                        <div class="device-header">
                            <div class="device-name">东区1号水泵</div>
                            <div class="device-status status-normal">正常</div>
                        </div>
                        <div style="font-size: 0.85rem; color: #666; margin-bottom: 10px;">
                            <i class="fas fa-map-marker-alt"></i>
                            东经120.123°, 北纬30.456°
                        </div>
                        <div class="device-actions">
                            <button class="action-btn btn-primary" onclick="navigateToDevice('pump-001')">
                                <i class="fas fa-directions"></i>
                                导航
                            </button>
                            <button class="action-btn btn-secondary" onclick="viewDeviceDetail('pump-001')">
                                <i class="fas fa-info-circle"></i>
                                详情
                            </button>
                        </div>
                    </div>

                    <div class="device-card" onclick="viewDeviceLocation('valve-005')">
                        <div class="device-header">
                            <div class="device-name">建设路控制阀</div>
                            <div class="device-status status-warning">预警</div>
                        </div>
                        <div style="font-size: 0.85rem; color: #666; margin-bottom: 10px;">
                            <i class="fas fa-map-marker-alt"></i>
                            东经120.145°, 北纬30.478°
                        </div>
                        <div class="device-actions">
                            <button class="action-btn btn-primary" onclick="navigateToDevice('valve-005')">
                                <i class="fas fa-directions"></i>
                                导航
                            </button>
                            <button class="action-btn btn-secondary" onclick="viewDeviceDetail('valve-005')">
                                <i class="fas fa-info-circle"></i>
                                详情
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设备状态 -->
            <div id="devices" class="tab-content">
                <div class="device-grid">
                    <div class="device-card">
                        <div class="device-header">
                            <div class="device-name">东区1号水泵</div>
                            <div class="device-status status-normal">运行正常</div>
                        </div>
                        <div class="device-info">
                            <div class="device-param">
                                <div class="param-value">1,480</div>
                                <div class="param-label">转速(rpm)</div>
                            </div>
                            <div class="device-param">
                                <div class="param-value">0.62</div>
                                <div class="param-label">出口压力(MPa)</div>
                            </div>
                            <div class="device-param">
                                <div class="param-value">85.2</div>
                                <div class="param-label">电流(A)</div>
                            </div>
                            <div class="device-param">
                                <div class="param-value">8.5</div>
                                <div class="param-label">运行时长(h)</div>
                            </div>
                        </div>
                        <div class="device-actions">
                            <button class="action-btn btn-primary" onclick="controlDevice('pump-001')">
                                <i class="fas fa-play-circle"></i>
                                控制
                            </button>
                            <button class="action-btn btn-secondary" onclick="viewHistory('pump-001')">
                                <i class="fas fa-history"></i>
                                历史
                            </button>
                        </div>
                    </div>

                    <div class="device-card">
                        <div class="device-header">
                            <div class="device-name">西区3号水泵</div>
                            <div class="device-status status-danger">故障停机</div>
                        </div>
                        <div class="device-info">
                            <div class="device-param">
                                <div class="param-value">0</div>
                                <div class="param-label">转速(rpm)</div>
                            </div>
                            <div class="device-param">
                                <div class="param-value">0.00</div>
                                <div class="param-label">出口压力(MPa)</div>
                            </div>
                            <div class="device-param">
                                <div class="param-value">0.0</div>
                                <div class="param-label">电流(A)</div>
                            </div>
                            <div class="device-param">
                                <div class="param-value">故障</div>
                                <div class="param-label">运行状态</div>
                            </div>
                        </div>
                        <div class="device-actions">
                            <button class="action-btn btn-primary" onclick="repairDevice('pump-003')">
                                <i class="fas fa-wrench"></i>
                                报修
                            </button>
                            <button class="action-btn btn-secondary" onclick="viewAlarm('pump-003')">
                                <i class="fas fa-exclamation-triangle"></i>
                                报警
                            </button>
                        </div>
                    </div>

                    <div class="device-card">
                        <div class="device-header">
                            <div class="device-name">建设路控制阀</div>
                            <div class="device-status status-warning">需要关注</div>
                        </div>
                        <div class="device-info">
                            <div class="device-param">
                                <div class="param-value">75</div>
                                <div class="param-label">开度(%)</div>
                            </div>
                            <div class="device-param">
                                <div class="param-value">0.16</div>
                                <div class="param-label">压差(MPa)</div>
                            </div>
                            <div class="device-param">
                                <div class="param-value">正常</div>
                                <div class="param-label">阀门状态</div>
                            </div>
                            <div class="device-param">
                                <div class="param-value">自动</div>
                                <div class="param-label">控制模式</div>
                            </div>
                        </div>
                        <div class="device-actions">
                            <button class="action-btn btn-primary" onclick="adjustValve('valve-005')">
                                <i class="fas fa-sliders-h"></i>
                                调节
                            </button>
                            <button class="action-btn btn-secondary" onclick="viewTrend('valve-005')">
                                <i class="fas fa-chart-line"></i>
                                趋势
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 扫码查看 -->
            <div id="scan" class="tab-content">
                <div class="scan-container">
                    <div class="scan-frame">
                        <i class="scan-icon fas fa-qrcode"></i>
                    </div>
                    <div class="scan-text">将设备二维码放入框内即可扫描</div>
                    <button class="scan-btn" onclick="startScan()">
                        <i class="fas fa-camera"></i>
                        开始扫描
                    </button>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <button class="nav-item active" data-tab="workspace">
                <i class="fas fa-home"></i>
                工作台
            </button>
            <button class="nav-item" data-tab="messages">
                <i class="fas fa-bell"></i>
                消息
            </button>
            <button class="nav-item" data-tab="map">
                <i class="fas fa-map"></i>
                地图
            </button>
            <button class="nav-item" data-tab="devices">
                <i class="fas fa-cogs"></i>
                设备
            </button>
            <button class="nav-item" data-tab="scan">
                <i class="fas fa-qrcode"></i>
                扫码
            </button>
        </div>

        <!-- 浮动按钮 -->
        <button class="fab" onclick="quickAction()">
            <i class="fas fa-plus"></i>
        </button>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-title" id="modalTitle">标题</div>
            <div class="modal-text" id="modalText">内容</div>
            <div class="modal-buttons">
                <button class="modal-btn secondary" onclick="closeModal()">取消</button>
                <button class="modal-btn primary" onclick="confirmAction()">确定</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTab = 'workspace';
        let currentAction = null;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('供水管网移动APP初始化...');
            
            // 初始化导航事件
            initNavEvents();
            
            // 模拟推送通知
            setTimeout(() => {
                showNotification('新的报警消息', '东区泵站压力异常，请及时处理');
            }, 3000);
            
            console.log('APP初始化完成');
        });

        // 初始化导航事件
        function initNavEvents() {
            // 顶部标签导航
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(item => {
                item.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    switchTab(tabId, this);
                });
            });

            // 底部导航
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    switchTab(tabId, this);
                });
            });
        }

        // 标签页切换
        function switchTab(tabId, clickedItem) {
            console.log('切换到标签页:', tabId);
            
            currentTab = tabId;
            
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有激活状态
            document.querySelectorAll('.tab-item, .nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示选中内容
            const targetContent = document.getElementById(tabId);
            if (targetContent) {
                targetContent.classList.add('active');
            }
            
            // 激活对应的导航项
            document.querySelectorAll(`[data-tab="${tabId}"]`).forEach(item => {
                item.classList.add('active');
            });
        }

        // 工作台功能
        function openFunction(type) {
            const functionNames = {
                'patrol': '巡检任务',
                'repair': '维修工单',
                'monitor': '实时监控',
                'report': '工作报告',
                'emergency': '应急处置',
                'analysis': '数据分析'
            };
            
            showModal('功能提示', `正在打开${functionNames[type]}功能...`);
            
            setTimeout(() => {
                closeModal();
                showToast(`${functionNames[type]}功能已打开`);
            }, 1500);
        }

        // 消息处理
        function openMessage(type, id) {
            const messageTypes = {
                'alarm': '报警消息',
                'task': '任务提醒',
                'info': '系统通知'
            };
            
            showModal(messageTypes[type], `正在查看消息详情...`);
            
            setTimeout(() => {
                closeModal();
                showToast('消息已标记为已读');
            }, 1000);
        }

        // 地图功能
        function locateMe() {
            showToast('正在定位当前位置...');
            
            setTimeout(() => {
                showToast('定位成功！');
            }, 2000);
        }

        function toggleLayer() {
            showToast('图层切换成功');
        }

        function searchDevice() {
            showModal('设备搜索', '请输入要搜索的设备名称或编号');
        }

        function viewDeviceLocation(deviceId) {
            showToast(`正在查看设备 ${deviceId} 位置信息`);
        }

        function navigateToDevice(deviceId) {
            showToast(`正在导航到设备 ${deviceId}`);
        }

        function viewDeviceDetail(deviceId) {
            showToast(`正在查看设备 ${deviceId} 详细信息`);
        }

        // 设备控制
        function controlDevice(deviceId) {
            currentAction = () => {
                showToast(`设备 ${deviceId} 控制指令已发送`);
            };
            showModal('设备控制', `确定要控制设备 ${deviceId} 吗？`);
        }

        function viewHistory(deviceId) {
            showToast(`正在查看设备 ${deviceId} 历史数据`);
        }

        function repairDevice(deviceId) {
            currentAction = () => {
                showToast(`设备 ${deviceId} 报修工单已创建`);
            };
            showModal('设备报修', `确定要为设备 ${deviceId} 创建报修工单吗？`);
        }

        function viewAlarm(deviceId) {
            showToast(`正在查看设备 ${deviceId} 报警信息`);
        }

        function adjustValve(deviceId) {
            showToast(`正在打开阀门 ${deviceId} 调节界面`);
        }

        function viewTrend(deviceId) {
            showToast(`正在查看设备 ${deviceId} 运行趋势`);
        }

        // 扫码功能
        function startScan() {
            showToast('正在启动摄像头...');
            
            setTimeout(() => {
                // 模拟扫码成功
                const mockDeviceInfo = {
                    id: 'PUMP-001-2024',
                    name: '东区1号水泵',
                    model: 'WQ-500',
                    installDate: '2020-03-15',
                    manufacturer: '水泵制造公司',
                    status: '正常运行'
                };
                
                showDeviceInfo(mockDeviceInfo);
            }, 3000);
        }

        function showDeviceInfo(deviceInfo) {
            const infoText = `
                设备编号: ${deviceInfo.id}
                设备名称: ${deviceInfo.name}
                设备型号: ${deviceInfo.model}
                安装日期: ${deviceInfo.installDate}
                制造商: ${deviceInfo.manufacturer}
                运行状态: ${deviceInfo.status}
            `;
            
            showModal('设备信息', infoText);
        }

        // 快速操作
        function quickAction() {
            const actions = ['创建巡检任务', '上报设备故障', '查看实时数据', '生成工作报告'];
            const randomAction = actions[Math.floor(Math.random() * actions.length)];
            showToast(`快速操作: ${randomAction}`);
        }

        // 模态框控制
        function showModal(title, text) {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalText').textContent = text;
            document.getElementById('modal').classList.add('show');
        }

        function closeModal() {
            document.getElementById('modal').classList.remove('show');
            currentAction = null;
        }

        function confirmAction() {
            if (currentAction) {
                currentAction();
                currentAction = null;
            }
            closeModal();
        }

        // Toast提示
        function showToast(message) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 15px 25px;
                border-radius: 25px;
                z-index: 3000;
                font-size: 0.9rem;
                max-width: 300px;
                text-align: center;
                animation: fadeInOut 2s ease-out;
            `;
            
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 2000);
        }

        // 推送通知
        function showNotification(title, message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 80px;
                left: 50%;
                transform: translateX(-50%);
                background: white;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
                z-index: 3000;
                max-width: 350px;
                width: 90%;
                animation: slideDown 0.5s ease-out;
            `;
            
            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 15px;">
                    <div style="width: 40px; height: 40px; background: #ff4757; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div style="flex: 1;">
                        <div style="font-weight: 700; color: #333; margin-bottom: 5px;">${title}</div>
                        <div style="font-size: 0.9rem; color: #666;">${message}</div>
                    </div>
                    <button onclick="this.parentNode.parentNode.remove()" style="background: none; border: none; color: #999; font-size: 1.2rem; cursor: pointer;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'slideUp 0.5s ease-out';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 500);
                }
            }, 5000);
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInOut {
                0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            }
            
            @keyframes slideDown {
                from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
                to { transform: translateX(-50%) translateY(0); opacity: 1; }
            }
            
            @keyframes slideUp {
                from { transform: translateX(-50%) translateY(0); opacity: 1; }
                to { transform: translateX(-50%) translateY(-100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // 模拟实时数据更新
        setInterval(() => {
            updateDeviceData();
        }, 5000);

        function updateDeviceData() {
            // 模拟设备数据更新
            const paramValues = document.querySelectorAll('.param-value');
            paramValues.forEach(element => {
                const currentValue = parseFloat(element.textContent);
                if (!isNaN(currentValue) && currentValue > 0) {
                    const variation = (Math.random() - 0.5) * 0.1;
                    const newValue = currentValue * (1 + variation);
                    
                    if (currentValue < 10) {
                        element.textContent = newValue.toFixed(2);
                    } else if (currentValue < 100) {
                        element.textContent = newValue.toFixed(1);
                    } else {
                        element.textContent = Math.round(newValue).toLocaleString();
                    }
                }
            });
        }
    </script>
</body>
</html>
