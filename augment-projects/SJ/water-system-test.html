<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水量智能预测调度系统 - 测试版</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #0f4c75 0%, #3282b8 50%, #bbe1fa 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 25px;
            text-align: center;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }

        .main-title {
            font-size: 2.5rem;
            color: #0f4c75;
            margin-bottom: 10px;
        }

        .nav-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 10px;
            margin-bottom: 25px;
            gap: 8px;
        }

        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            background: transparent;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            color: #666;
            transition: all 0.3s ease;
        }

        .nav-tab.active {
            background: linear-gradient(135deg, #0f4c75, #3282b8);
            color: white;
            transform: translateY(-2px);
        }

        .content-area {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            min-height: 600px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .kpi-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #2196f3;
        }

        .kpi-title {
            font-size: 1rem;
            color: #666;
            margin-bottom: 15px;
        }

        .kpi-value {
            font-size: 2rem;
            font-weight: bold;
            color: #0f4c75;
            margin-bottom: 10px;
        }

        .chart-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #0f4c75;
            margin-bottom: 20px;
        }

        .chart-container {
            position: relative;
            height: 400px;
        }

        .level-gauge {
            background: linear-gradient(180deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            margin-bottom: 25px;
        }

        .gauge-container {
            position: relative;
            width: 200px;
            height: 300px;
            margin: 0 auto;
            background: linear-gradient(180deg, #f5f5f5 0%, #e0e0e0 100%);
            border-radius: 10px;
            border: 3px solid #0f4c75;
            overflow: hidden;
        }

        .water-level {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background: linear-gradient(180deg, #2196f3 0%, #1976d2 100%);
            transition: height 2s ease;
            border-radius: 0 0 7px 7px;
        }

        .level-value {
            font-size: 2rem;
            font-weight: bold;
            color: #0f4c75;
            margin-top: 20px;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-left: 4px solid #ffc107;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #0f4c75, #3282b8);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .nav-tabs {
                flex-direction: column;
            }
            
            .kpi-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="main-title">
                <i class="fas fa-tint"></i>
                水量智能预测调度系统
                <i class="fas fa-brain"></i>
            </h1>
            <p>Water Intelligent Prediction & Dispatch System</p>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" data-tab="prediction">
                <i class="fas fa-chart-line"></i>
                水量智能预测
            </button>
            <button class="nav-tab" data-tab="monitoring">
                <i class="fas fa-eye"></i>
                清水池监控
            </button>
            <button class="nav-tab" data-tab="dispatch">
                <i class="fas fa-cogs"></i>
                调度策略管理
            </button>
            <button class="nav-tab" data-tab="analysis">
                <i class="fas fa-chart-bar"></i>
                调度分析
            </button>
        </div>

        <div class="content-area">
            <!-- 水量智能预测 -->
            <div id="prediction" class="tab-content active">
                <h2 style="color: #0f4c75; margin-bottom: 25px;">
                    <i class="fas fa-chart-line"></i>
                    水量智能预测
                </h2>

                <div class="kpi-grid">
                    <div class="kpi-card">
                        <div class="kpi-title">
                            <i class="fas fa-arrow-right"></i>
                            进厂水量预测
                        </div>
                        <div class="kpi-value">42,580 <span style="font-size: 1rem; color: #888;">m³/日</span></div>
                        <div style="font-size: 0.9rem; color: #666;">预测偏差：±3.2%</div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-title">
                            <i class="fas fa-arrow-left"></i>
                            出厂水量预测
                        </div>
                        <div class="kpi-value">39,650 <span style="font-size: 1rem; color: #888;">m³/日</span></div>
                        <div style="font-size: 0.9rem; color: #666;">预测偏差：±2.8%</div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-title">
                            <i class="fas fa-bullseye"></i>
                            预测精度
                        </div>
                        <div class="kpi-value">96.4 <span style="font-size: 1rem; color: #888;">%</span></div>
                        <div style="font-size: 0.9rem; color: #666;">本月均值：96.1%</div>
                    </div>
                </div>

                <div class="chart-card">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-area"></i>
                        水量预测趋势
                    </h3>
                    <div class="chart-container">
                        <canvas id="predictionChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 清水池监控 -->
            <div id="monitoring" class="tab-content">
                <h2 style="color: #0f4c75; margin-bottom: 25px;">
                    <i class="fas fa-eye"></i>
                    清水池监控
                </h2>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div>
                        <strong>液位预警：</strong>2号清水池液位接近上限阈值，当前液位4.8m，上限阈值5.0m
                    </div>
                </div>

                <div class="level-gauge">
                    <h3 style="color: #0f4c75; margin-bottom: 20px;">1号清水池</h3>
                    <div class="gauge-container">
                        <div class="water-level" id="waterLevel" style="height: 65%;"></div>
                    </div>
                    <div class="level-value">3.25m</div>
                    <div style="color: #4caf50; margin-top: 10px;">
                        <i class="fas fa-check-circle"></i> 正常范围
                    </div>
                </div>

                <div class="chart-card">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-line"></i>
                        液位变化趋势
                    </h3>
                    <div class="chart-container">
                        <canvas id="levelChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 调度策略管理 -->
            <div id="dispatch" class="tab-content">
                <h2 style="color: #0f4c75; margin-bottom: 25px;">
                    <i class="fas fa-cogs"></i>
                    调度策略管理
                </h2>

                <div class="kpi-grid">
                    <div class="kpi-card">
                        <div class="kpi-title">
                            <i class="fas fa-pump-soap"></i>
                            进厂水泵频率
                        </div>
                        <div class="kpi-value">45 <span style="font-size: 1rem; color: #888;">Hz</span></div>
                        <div style="font-size: 0.9rem; color: #666;">范围：35-50Hz</div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-title">
                            <i class="fas fa-industry"></i>
                            制水车间负荷
                        </div>
                        <div class="kpi-value">85 <span style="font-size: 1rem; color: #888;">%</span></div>
                        <div style="font-size: 0.9rem; color: #666;">最大：95%</div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-title">
                            <i class="fas fa-pump-medical"></i>
                            出厂水泵台数
                        </div>
                        <div class="kpi-value">3 <span style="font-size: 1rem; color: #888;">台</span></div>
                        <div style="font-size: 0.9rem; color: #666;">备用：1台</div>
                    </div>
                </div>

                <div class="chart-card">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-area"></i>
                        调度效果预测
                    </h3>
                    <div class="chart-container">
                        <canvas id="dispatchChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 调度分析 -->
            <div id="analysis" class="tab-content">
                <h2 style="color: #0f4c75; margin-bottom: 25px;">
                    <i class="fas fa-chart-bar"></i>
                    调度分析
                </h2>

                <div class="kpi-grid">
                    <div class="kpi-card">
                        <div class="kpi-title">
                            <i class="fas fa-bullseye"></i>
                            调度成功率
                        </div>
                        <div class="kpi-value">94.8 <span style="font-size: 1rem; color: #888;">%</span></div>
                        <div style="font-size: 0.9rem; color: #666;">上月：92.7%</div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-title">
                            <i class="fas fa-clock"></i>
                            平均响应时间
                        </div>
                        <div class="kpi-value">8.5 <span style="font-size: 1rem; color: #888;">分钟</span></div>
                        <div style="font-size: 0.9rem; color: #666;">目标：<10分钟</div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-title">
                            <i class="fas fa-leaf"></i>
                            节能效果
                        </div>
                        <div class="kpi-value">12.3 <span style="font-size: 1rem; color: #888;">%</span></div>
                        <div style="font-size: 0.9rem; color: #666;">节约：8,420元</div>
                    </div>
                </div>

                <div class="chart-card">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-line"></i>
                        预测vs实际对比
                    </h3>
                    <div class="chart-container">
                        <canvas id="analysisChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面开始加载...');
            
            // 检查Chart.js是否加载
            if (typeof Chart === 'undefined') {
                console.error('Chart.js未加载');
                return;
            }
            
            console.log('Chart.js已加载，版本:', Chart.version);
            
            // 初始化标签页事件
            initTabEvents();
            
            // 初始化图表
            setTimeout(() => {
                initPredictionChart();
                startAnimation();
            }, 500);
        });

        // 初始化标签页事件
        function initTabEvents() {
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    showTab(tabId, this);
                });
            });
            console.log('标签页事件初始化完成');
        }

        // 标签页切换
        function showTab(tabId, clickedTab) {
            console.log('切换到标签页:', tabId);
            
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有激活状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中内容
            const targetContent = document.getElementById(tabId);
            if (targetContent) {
                targetContent.classList.add('active');
            }
            
            // 激活选中标签
            clickedTab.classList.add('active');
            
            // 根据标签页初始化对应图表
            setTimeout(() => {
                switch(tabId) {
                    case 'monitoring':
                        initLevelChart();
                        break;
                    case 'dispatch':
                        initDispatchChart();
                        break;
                    case 'analysis':
                        initAnalysisChart();
                        break;
                }
            }, 100);
        }

        // 初始化预测图表
        function initPredictionChart() {
            const ctx = document.getElementById('predictionChart');
            if (!ctx) return;
            
            try {
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                        datasets: [{
                            label: '进厂水量预测',
                            data: [1650, 1420, 1890, 2150, 2380, 2100, 1750],
                            borderColor: '#2196f3',
                            backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            tension: 0.4,
                            fill: true
                        }, {
                            label: '出厂水量预测',
                            data: [1580, 1350, 1720, 1980, 2200, 1950, 1650],
                            borderColor: '#4caf50',
                            backgroundColor: 'rgba(76, 175, 80, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '水量 (m³/h)'
                                }
                            }
                        }
                    }
                });
                console.log('预测图表初始化成功');
            } catch (error) {
                console.error('预测图表初始化失败:', error);
            }
        }

        // 初始化液位图表
        function initLevelChart() {
            const ctx = document.getElementById('levelChart');
            if (!ctx || Chart.getChart(ctx)) return;
            
            try {
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['14:00', '14:10', '14:20', '14:30', '14:40', '14:50', '15:00'],
                        datasets: [{
                            label: '1号池液位',
                            data: [3.2, 3.25, 3.3, 3.25, 3.2, 3.22, 3.25],
                            borderColor: '#2196f3',
                            backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            tension: 0.4,
                            fill: true
                        }, {
                            label: '2号池液位',
                            data: [4.6, 4.65, 4.7, 4.75, 4.8, 4.82, 4.8],
                            borderColor: '#f44336',
                            backgroundColor: 'rgba(244, 67, 54, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 6,
                                title: {
                                    display: true,
                                    text: '液位 (m)'
                                }
                            }
                        }
                    }
                });
                console.log('液位图表初始化成功');
            } catch (error) {
                console.error('液位图表初始化失败:', error);
            }
        }

        // 初始化调度图表
        function initDispatchChart() {
            const ctx = document.getElementById('dispatchChart');
            if (!ctx || Chart.getChart(ctx)) return;
            
            try {
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['当前', '+15min', '+30min', '+45min', '+60min'],
                        datasets: [{
                            label: '预测液位',
                            data: [3.25, 3.15, 3.05, 2.95, 2.85],
                            borderColor: '#2196f3',
                            backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            tension: 0.4,
                            fill: true
                        }, {
                            label: '目标液位',
                            data: [3.0, 3.0, 3.0, 3.0, 3.0],
                            borderColor: '#4caf50',
                            borderDash: [5, 5]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 5,
                                title: {
                                    display: true,
                                    text: '液位 (m)'
                                }
                            }
                        }
                    }
                });
                console.log('调度图表初始化成功');
            } catch (error) {
                console.error('调度图表初始化失败:', error);
            }
        }

        // 初始化分析图表
        function initAnalysisChart() {
            const ctx = document.getElementById('analysisChart');
            if (!ctx || Chart.getChart(ctx)) return;
            
            try {
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['1/9', '1/10', '1/11', '1/12', '1/13', '1/14', '1/15'],
                        datasets: [{
                            label: '预测值',
                            data: [41500, 42200, 41800, 42500, 42100, 41900, 42580],
                            borderColor: '#2196f3',
                            backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            tension: 0.4
                        }, {
                            label: '实际值',
                            data: [41680, 42150, 41750, 42480, 42180, 41850, 42520],
                            borderColor: '#ff9800',
                            backgroundColor: 'rgba(255, 152, 0, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                title: {
                                    display: true,
                                    text: '水量 (m³/日)'
                                }
                            }
                        }
                    }
                });
                console.log('分析图表初始化成功');
            } catch (error) {
                console.error('分析图表初始化失败:', error);
            }
        }

        // 启动动画
        function startAnimation() {
            // 液位动画
            const waterLevel = document.getElementById('waterLevel');
            if (waterLevel) {
                setInterval(() => {
                    const currentHeight = parseFloat(waterLevel.style.height) || 65;
                    const variation = (Math.random() - 0.5) * 4;
                    const newHeight = Math.max(20, Math.min(95, currentHeight + variation));
                    waterLevel.style.height = newHeight + '%';
                }, 3000);
            }
        }
    </script>
</body>
</html>
