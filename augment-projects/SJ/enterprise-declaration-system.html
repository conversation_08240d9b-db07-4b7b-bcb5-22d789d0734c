<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业申报系统 - 智能化企业评价与服务平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .nav-tab.active {
            background: #007bff;
            color: white;
        }

        .nav-tab:hover {
            background: #e9ecef;
        }

        .nav-tab.active:hover {
            background: #0056b3;
        }

        .tab-content {
            display: none;
            padding: 30px;
        }

        .tab-content.active {
            display: block;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #007bff;
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .feature-card h3 {
            color: #007bff;
            margin-bottom: 10px;
            font-size: 1.3em;
        }

        .feature-list {
            list-style: none;
            margin-top: 15px;
        }

        .feature-list li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }

        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .evaluation-flow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .flow-step {
            background: #e9ecef;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            flex: 1;
            margin: 10px;
            min-width: 150px;
            position: relative;
        }

        .flow-step.active {
            background: #007bff;
            color: white;
        }

        .flow-step:not(:last-child):after {
            content: "→";
            position: absolute;
            right: -20px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5em;
            color: #007bff;
        }

        .service-matrix {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 20px;
        }

        .service-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .service-item:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }

        .btn-primary {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .nav-tabs {
                flex-direction: column;
            }
            
            .evaluation-flow {
                flex-direction: column;
            }
            
            .flow-step:not(:last-child):after {
                content: "↓";
                right: 50%;
                top: 100%;
                transform: translateX(50%);
            }
            
            .service-matrix {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>企业申报系统</h1>
            <p>智能化企业评价与精准服务平台</p>
        </div>

        <div class="main-content">
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="showTab('declaration')">在线申报</button>
                <button class="nav-tab" onclick="showTab('evaluation')">评价分类</button>
                <button class="nav-tab" onclick="showTab('analytics')">数据分析</button>
                <button class="nav-tab" onclick="showTab('services')">精准服务</button>
            </div>

            <!-- 在线申报模块 -->
            <div id="declaration" class="tab-content active">
                <h2>企业在线申报功能</h2>
                <p>支持企业通过PC/移动端在线提交基础信息、经营数据及创新成果，实现动态申报与材料更新。</p>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>企业基础信息管理</h3>
                        <p>全面收集企业基本信息、股权结构、组织架构等核心数据</p>
                        <ul class="feature-list">
                            <li>企业基本信息维护</li>
                            <li>股权结构信息管理</li>
                            <li>组织架构信息录入</li>
                            <li>信息变更历史记录</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <h3>经营数据申报</h3>
                        <p>支持财务数据、经营指标、成长性指标的在线申报</p>
                        <ul class="feature-list">
                            <li>财务数据录入（近三年）</li>
                            <li>经营指标统计</li>
                            <li>成长性指标分析</li>
                            <li>Excel批量导入</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <h3>创新成果申报</h3>
                        <p>展示企业研发投入、知识产权、创新产品等创新能力</p>
                        <ul class="feature-list">
                            <li>研发投入数据统计</li>
                            <li>知识产权管理</li>
                            <li>创新产品/服务展示</li>
                            <li>产学研合作记录</li>
                        </ul>
                    </div>
                </div>
                
                <div style="text-align: center;">
                    <button class="btn-primary">开始申报</button>
                    <button class="btn-primary">查看申报进度</button>
                </div>
            </div>

            <!-- 评价分类模块 -->
            <div id="evaluation" class="tab-content">
                <h2>企业评价与分类管理</h2>
                <p>内置"雏鹰-瞪羚-独角兽"梯度评价指标体系，融合自动化初筛、人工复核及专家评审。</p>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">500</div>
                        <div>年度遴选企业</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">100</div>
                        <div>重点服务企业</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">3</div>
                        <div>评价梯度分类</div>
                    </div>
                </div>
                
                <div class="evaluation-flow">
                    <div class="flow-step active">
                        <h4>自动化初筛</h4>
                        <p>数据采集与初步筛选</p>
                    </div>
                    <div class="flow-step">
                        <h4>人工复核</h4>
                        <p>数据验证与实地调研</p>
                    </div>
                    <div class="flow-step">
                        <h4>专家评审</h4>
                        <p>专业评估与最终认定</p>
                    </div>
                </div>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>雏鹰企业</h3>
                        <p>成立3年以内的高成长初创企业</p>
                        <ul class="feature-list">
                            <li>营收增长率 ≥ 30%</li>
                            <li>研发投入占比 ≥ 3%</li>
                            <li>员工规模 10-50人</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <h3>瞪羚企业</h3>
                        <p>3-10年快速成长的创新型企业</p>
                        <ul class="feature-list">
                            <li>营收复合增长率 ≥ 25%</li>
                            <li>研发投入占比 ≥ 5%</li>
                            <li>年营收 2000万-10亿元</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <h3>独角兽企业</h3>
                        <p>估值超10亿美元的行业领军企业</p>
                        <ul class="feature-list">
                            <li>企业估值 ≥ 10亿美元</li>
                            <li>行业排名前3</li>
                            <li>颠覆性技术创新</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 数据分析模块 -->
            <div id="analytics" class="tab-content">
                <h2>数据可视化分析平台</h2>
                <p>构建企业数据库与可视化分析看板，实时监测成长轨迹。</p>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>总览看板</h3>
                        <p>关键指标展示与趋势分析</p>
                        <ul class="feature-list">
                            <li>申报企业总数统计</li>
                            <li>各类别企业分布</li>
                            <li>地区分布热力图</li>
                            <li>评审通过率分析</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <h3>企业画像</h3>
                        <p>个体企业全方位数据展示</p>
                        <ul class="feature-list">
                            <li>成长轨迹可视化</li>
                            <li>关键指标雷达图</li>
                            <li>同行业对比分析</li>
                            <li>风险预警提示</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <h3>实时监测</h3>
                        <p>动态数据更新与异常告警</p>
                        <ul class="feature-list">
                            <li>实时数据流处理</li>
                            <li>经营状态监测</li>
                            <li>财务指标波动预警</li>
                            <li>外部风险事件跟踪</li>
                        </ul>
                    </div>
                </div>
                
                <div style="text-align: center;">
                    <button class="btn-primary">查看数据看板</button>
                    <button class="btn-primary">生成分析报告</button>
                </div>
            </div>

            <!-- 精准服务模块 -->
            <div id="services" class="tab-content">
                <h2>精准服务匹配系统</h2>
                <p>针对100家重点企业，智能匹配政策、融资及孵化资源，提供精准赋能服务。</p>
                
                <div class="service-matrix">
                    <div class="service-item">
                        <h4>政策资源</h4>
                        <p>财税、人才、土地、金融等政策匹配</p>
                    </div>
                    <div class="service-item">
                        <h4>融资资源</h4>
                        <p>银行贷款、基金投资、债券融资</p>
                    </div>
                    <div class="service-item">
                        <h4>孵化资源</h4>
                        <p>创业辅导、技术支持、市场推广</p>
                    </div>
                    <div class="service-item">
                        <h4>需求征集</h4>
                        <p>在线调研、定期走访、专题座谈</p>
                    </div>
                    <div class="service-item">
                        <h4>服务跟踪</h4>
                        <p>全流程管理、进度监控、效果评估</p>
                    </div>
                    <div class="service-item">
                        <h4>成效评估</h4>
                        <p>服务质量评价、满意度调查</p>
                    </div>
                </div>
                
                <div class="feature-card">
                    <h3>智能匹配算法</h3>
                    <p>基于企业特征和需求，智能推荐最适合的服务资源</p>
                    <ul class="feature-list">
                        <li>多维度特征分析</li>
                        <li>需求智能识别</li>
                        <li>资源精准匹配</li>
                        <li>成功率预测</li>
                    </ul>
                </div>
                
                <div style="text-align: center;">
                    <button class="btn-primary">申请服务</button>
                    <button class="btn-primary">查看服务进度</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签页按钮的激活状态
            const tabButtons = document.querySelectorAll('.nav-tab');
            tabButtons.forEach(button => {
                button.classList.remove('active');
            });
            
            // 显示选中的标签页内容
            document.getElementById(tabName).classList.add('active');
            
            // 激活对应的标签页按钮
            event.target.classList.add('active');
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('企业申报系统页面加载完成');
        });
    </script>
</body>
</html>
