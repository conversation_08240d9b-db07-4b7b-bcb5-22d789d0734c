<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水量智能预测调度系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f4c75 0%, #3282b8 50%, #bbe1fa 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 25px;
            text-align: center;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }

        .main-title {
            font-size: 2.8rem;
            color: #0f4c75;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }

        .subtitle {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 15px;
        }

        .status-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            font-size: 0.95rem;
            color: #888;
            flex-wrap: wrap;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-online { color: #4caf50; }
        .status-warning { color: #ff9800; }

        .nav-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 10px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            gap: 8px;
        }

        .nav-tab {
            flex: 1;
            padding: 18px 25px;
            background: transparent;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            color: #7f8c8d;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .nav-tab.active {
            background: linear-gradient(135deg, #0f4c75, #3282b8);
            color: white;
            box-shadow: 0 6px 20px rgba(15, 76, 117, 0.3);
            transform: translateY(-2px);
        }

        .nav-tab:hover:not(.active) {
            background: rgba(15, 76, 117, 0.1);
            color: #0f4c75;
            transform: translateY(-1px);
        }

        .content-area {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 35px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            min-height: 700px;
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.5s ease-in-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .kpi-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            border-left: 5px solid #2196f3;
        }

        .kpi-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 45px rgba(0, 0, 0, 0.15);
        }

        .kpi-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .kpi-title {
            font-size: 1.1rem;
            color: #666;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .kpi-trend {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .trend-up { background: #ffebee; color: #c62828; }
        .trend-down { background: #e8f5e8; color: #2e7d32; }
        .trend-stable { background: #fff3e0; color: #ef6c00; }

        .kpi-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #0f4c75;
            margin-bottom: 10px;
        }

        .kpi-unit {
            font-size: 1rem;
            color: #888;
            margin-left: 5px;
        }

        .kpi-comparison {
            display: flex;
            justify-content: space-between;
            font-size: 0.9rem;
            color: #666;
        }

        .chart-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #0f4c75;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chart-controls {
            display: flex;
            gap: 10px;
        }

        .chart-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            color: #666;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .chart-btn.active {
            background: #0f4c75;
            color: white;
            border-color: #0f4c75;
        }

        .chart-btn:hover:not(.active) {
            background: #f5f5f5;
        }

        .chart-container {
            position: relative;
            height: 400px;
            background: #f8f9fa;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chart-placeholder {
            text-align: center;
            color: #666;
        }

        .chart-placeholder i {
            font-size: 4rem;
            color: #0f4c75;
            margin-bottom: 20px;
            display: block;
        }

        .level-monitor {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 25px;
            margin-bottom: 30px;
        }

        .level-gauge {
            background: linear-gradient(180deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
        }

        .gauge-container {
            position: relative;
            width: 200px;
            height: 300px;
            margin: 0 auto 20px;
            background: linear-gradient(180deg, #f5f5f5 0%, #e0e0e0 100%);
            border-radius: 10px;
            border: 3px solid #0f4c75;
            overflow: hidden;
        }

        .water-level {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background: linear-gradient(180deg, #2196f3 0%, #1976d2 100%);
            transition: height 2s ease;
            border-radius: 0 0 7px 7px;
            height: 65%;
        }

        .level-markers {
            position: absolute;
            right: -40px;
            top: 0;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            font-size: 0.8rem;
            color: #666;
            padding: 5px 0;
        }

        .level-value {
            font-size: 2rem;
            font-weight: bold;
            color: #0f4c75;
            margin-bottom: 10px;
        }

        .level-status {
            color: #4caf50;
            font-size: 0.9rem;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-left: 4px solid #ffc107;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left: 4px solid #17a2b8;
        }

        .dispatch-commands {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .command-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #0f4c75;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .command-content {
            flex: 1;
        }

        .command-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .command-detail {
            font-size: 0.9rem;
            color: #666;
        }

        .command-status {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-pending { background: #fff3cd; color: #856404; }
        .status-executing { background: #cce5ff; color: #004085; }
        .status-completed { background: #d4edda; color: #155724; }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table th {
            background: linear-gradient(135deg, #0f4c75, #3282b8);
            color: white;
            font-weight: 600;
        }

        .data-table tr:hover {
            background: rgba(15, 76, 117, 0.05);
        }

        .data-table .number {
            text-align: right;
            font-weight: 600;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #0f4c75, #3282b8);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(15, 76, 117, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: white;
        }

        @media (max-width: 1200px) {
            .level-monitor {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .main-title {
                font-size: 2.2rem;
                flex-direction: column;
                gap: 10px;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .nav-tab {
                padding: 15px 20px;
            }

            .kpi-grid {
                grid-template-columns: 1fr;
            }

            .status-info {
                flex-direction: column;
                gap: 10px;
            }

            .chart-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 系统头部 -->
        <div class="header">
            <h1 class="main-title">
                <i class="fas fa-tint"></i>
                水量智能预测调度系统
                <i class="fas fa-brain"></i>
            </h1>
            <p class="subtitle">Water Intelligent Prediction & Dispatch System</p>
            <div class="status-info">
                <div class="status-item status-online">
                    <i class="fas fa-circle"></i>
                    <span>预测模型：运行正常</span>
                </div>
                <div class="status-item status-online">
                    <i class="fas fa-circle"></i>
                    <span>监控系统：在线</span>
                </div>
                <div class="status-item status-warning">
                    <i class="fas fa-circle"></i>
                    <span>调度状态：执行中</span>
                </div>
                <div class="status-item status-online">
                    <i class="fas fa-sync-alt"></i>
                    <span id="updateTime">更新时间：14:30:25</span>
                </div>
            </div>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs">
            <button class="nav-tab active" data-tab="prediction">
                <i class="fas fa-chart-line"></i>
                水量智能预测
            </button>
            <button class="nav-tab" data-tab="monitoring">
                <i class="fas fa-eye"></i>
                清水池监控
            </button>
            <button class="nav-tab" data-tab="dispatch">
                <i class="fas fa-cogs"></i>
                调度策略管理
            </button>
            <button class="nav-tab" data-tab="analysis">
                <i class="fas fa-chart-bar"></i>
                调度分析
            </button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 水量智能预测标签页 -->
            <div id="prediction" class="tab-content active">
                <h2 style="color: #0f4c75; margin-bottom: 25px; display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-chart-line"></i>
                    水量智能预测
                </h2>

                <!-- 预测指标 -->
                <div class="kpi-grid">
                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-arrow-right"></i>
                                进厂水量预测
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +2.3%
                            </div>
                        </div>
                        <div class="kpi-value">
                            42,580
                            <span class="kpi-unit">m³/日</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>预测偏差：±3.2%</span>
                            <span>置信度：95.8%</span>
                        </div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-arrow-left"></i>
                                出厂水量预测
                            </div>
                            <div class="kpi-trend trend-stable">
                                <i class="fas fa-minus"></i>
                                +0.8%
                            </div>
                        </div>
                        <div class="kpi-value">
                            39,650
                            <span class="kpi-unit">m³/日</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>预测偏差：±2.8%</span>
                            <span>置信度：97.2%</span>
                        </div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-balance-scale"></i>
                                水量平衡度
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -1.2%
                            </div>
                        </div>
                        <div class="kpi-value">
                            93.1
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>目标值：95%</span>
                            <span>历史均值：92.5%</span>
                        </div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-bullseye"></i>
                                预测精度
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +0.5%
                            </div>
                        </div>
                        <div class="kpi-value">
                            96.4
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>昨日：95.9%</span>
                            <span>本月均值：96.1%</span>
                        </div>
                    </div>
                </div>

                <!-- 预测图表 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-chart-area"></i>
                            水量预测趋势
                        </h3>
                        <div class="chart-controls">
                            <button class="chart-btn active" onclick="switchPeriod('24h')">24小时</button>
                            <button class="chart-btn" onclick="switchPeriod('7d')">7天</button>
                            <button class="chart-btn" onclick="switchPeriod('30d')">30天</button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-line"></i>
                            <h3>水量预测趋势图</h3>
                            <p>显示进厂水量、出厂水量预测和实际值对比</p>
                            <div style="margin-top: 20px;">
                                <canvas id="predictionChart" width="800" height="300" style="display: none;"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-chart-pie"></i>
                            影响因素分析
                        </h3>
                    </div>
                    <div class="chart-container">
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-pie"></i>
                            <h3>影响因素权重分布</h3>
                            <p>历史规律35% | 季节因素25% | 节假日15% | 天气12% | 政策8% | 其他5%</p>
                        </div>
                    </div>
                </div>

                <!-- 预测模型信息 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-brain"></i>
                            预测模型状态
                        </h3>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                        <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; text-align: center;">
                            <div style="font-size: 0.9rem; color: #1565c0; margin-bottom: 8px;">
                                <i class="fas fa-database"></i> 训练数据量
                            </div>
                            <div style="font-size: 1.8rem; font-weight: bold; color: #0d47a1;">2,847</div>
                            <div style="font-size: 0.8rem; color: #1976d2;">天</div>
                        </div>
                        
                        <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; text-align: center;">
                            <div style="font-size: 0.9rem; color: #2e7d32; margin-bottom: 8px;">
                                <i class="fas fa-sync-alt"></i> 模型更新频率
                            </div>
                            <div style="font-size: 1.8rem; font-weight: bold; color: #1b5e20;">每日</div>
                            <div style="font-size: 0.8rem; color: #388e3c;">自动更新</div>
                        </div>
                        
                        <div style="background: #fff3e0; padding: 20px; border-radius: 10px; text-align: center;">
                            <div style="font-size: 0.9rem; color: #ef6c00; margin-bottom: 8px;">
                                <i class="fas fa-cog"></i> 算法类型
                            </div>
                            <div style="font-size: 1.2rem; font-weight: bold; color: #e65100;">LSTM+RF</div>
                            <div style="font-size: 0.8rem; color: #f57c00;">混合模型</div>
                        </div>
                        
                        <div style="background: #fce4ec; padding: 20px; border-radius: 10px; text-align: center;">
                            <div style="font-size: 0.9rem; color: #c2185b; margin-bottom: 8px;">
                                <i class="fas fa-clock"></i> 预测周期
                            </div>
                            <div style="font-size: 1.8rem; font-weight: bold; color: #ad1457;">72</div>
                            <div style="font-size: 0.8rem; color: #e91e63;">小时</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 清水池监控标签页 -->
            <div id="monitoring" class="tab-content">
                <h2 style="color: #0f4c75; margin-bottom: 25px; display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-eye"></i>
                    清水池监控
                </h2>

                <!-- 监控预警 -->
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div>
                        <strong>液位预警：</strong>2号清水池液位接近上限阈值，当前液位4.8m，上限阈值5.0m
                        <div style="margin-top: 5px; font-size: 0.9rem;">建议调整出厂水泵运行参数或减少进厂水量</div>
                    </div>
                </div>

                <!-- 液位监控 -->
                <div class="level-monitor">
                    <div class="level-gauge">
                        <h3 style="color: #0f4c75; margin-bottom: 20px;">
                            <i class="fas fa-tint"></i>
                            1号清水池
                        </h3>
                        <div class="gauge-container">
                            <div class="water-level" id="waterLevel"></div>
                            <div class="level-markers">
                                <span>5.0m</span>
                                <span>4.0m</span>
                                <span>3.0m</span>
                                <span>2.0m</span>
                                <span>1.0m</span>
                                <span>0.0m</span>
                            </div>
                        </div>
                        <div class="level-value">3.25m</div>
                        <div class="level-status">
                            <i class="fas fa-check-circle"></i> 正常范围
                        </div>
                    </div>

                    <div class="chart-card" style="margin: 0;">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-line"></i>
                                液位变化趋势
                            </h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="switchLevelPeriod('1h')">1小时</button>
                                <button class="chart-btn" onclick="switchLevelPeriod('6h')">6小时</button>
                                <button class="chart-btn" onclick="switchLevelPeriod('24h')">24小时</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <div class="chart-placeholder">
                                <i class="fas fa-chart-line"></i>
                                <h3>液位变化趋势图</h3>
                                <p>1号池：3.25m | 2号池：4.80m | 3号池：2.15m</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 多池监控 -->
                <div class="kpi-grid">
                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-tint"></i>
                                2号清水池
                            </div>
                            <div class="kpi-trend" style="background: #ffebee; color: #c62828;">
                                <i class="fas fa-exclamation-triangle"></i>
                                偏高
                            </div>
                        </div>
                        <div class="kpi-value">
                            4.80
                            <span class="kpi-unit">m</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>容量：96%</span>
                            <span>状态：接近上限</span>
                        </div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-tint"></i>
                                3号清水池
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                下降
                            </div>
                        </div>
                        <div class="kpi-value">
                            2.15
                            <span class="kpi-unit">m</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>容量：43%</span>
                            <span>状态：正常</span>
                        </div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-calculator"></i>
                                总储水量
                            </div>
                            <div class="kpi-trend trend-stable">
                                <i class="fas fa-minus"></i>
                                +0.2%
                            </div>
                        </div>
                        <div class="kpi-value">
                            15,240
                            <span class="kpi-unit">m³</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>总容量：68%</span>
                            <span>安全余量：32%</span>
                        </div>
                    </div>
                </div>

                <!-- 传感器状态 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-satellite-dish"></i>
                            传感器状态监控
                        </h3>
                        <button class="btn btn-primary" onclick="refreshSensors()">
                            <i class="fas fa-sync-alt"></i>
                            刷新状态
                        </button>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>传感器编号</th>
                                <th>安装位置</th>
                                <th>传感器类型</th>
                                <th>当前读数</th>
                                <th>最后更新</th>
                                <th>通信状态</th>
                                <th>运行状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>LS-001</td>
                                <td>1号清水池</td>
                                <td>超声波液位计</td>
                                <td class="number">3.25 m</td>
                                <td>14:30:15</td>
                                <td><span style="color: #4caf50;">在线</span></td>
                                <td><span style="color: #4caf50;">正常</span></td>
                            </tr>
                            <tr>
                                <td>LS-002</td>
                                <td>2号清水池</td>
                                <td>超声波液位计</td>
                                <td class="number">4.80 m</td>
                                <td>14:30:12</td>
                                <td><span style="color: #4caf50;">在线</span></td>
                                <td><span style="color: #ff9800;">预警</span></td>
                            </tr>
                            <tr>
                                <td>LS-003</td>
                                <td>3号清水池</td>
                                <td>超声波液位计</td>
                                <td class="number">2.15 m</td>
                                <td>14:30:18</td>
                                <td><span style="color: #4caf50;">在线</span></td>
                                <td><span style="color: #4caf50;">正常</span></td>
                            </tr>
                            <tr>
                                <td>PS-001</td>
                                <td>进水管道</td>
                                <td>压力传感器</td>
                                <td class="number">0.45 MPa</td>
                                <td>14:30:10</td>
                                <td><span style="color: #4caf50;">在线</span></td>
                                <td><span style="color: #4caf50;">正常</span></td>
                            </tr>
                            <tr>
                                <td>FS-001</td>
                                <td>出水管道</td>
                                <td>流量传感器</td>
                                <td class="number">1,652 m³/h</td>
                                <td>14:29:45</td>
                                <td><span style="color: #f44336;">离线</span></td>
                                <td><span style="color: #f44336;">故障</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 调度策略管理标签页 -->
            <div id="dispatch" class="tab-content">
                <h2 style="color: #0f4c75; margin-bottom: 25px; display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-cogs"></i>
                    调度策略管理
                </h2>

                <!-- 当前调度状态 -->
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <strong>调度执行中：</strong>基于预测结果和液位监控，系统正在执行自动调度策略
                        <div style="margin-top: 5px; font-size: 0.9rem;">预计15分钟内完成调度调整，请关注执行结果</div>
                    </div>
                </div>

                <!-- 调度指标 -->
                <div class="kpi-grid">
                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-pump-soap"></i>
                                进厂水泵频率
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -5Hz
                            </div>
                        </div>
                        <div class="kpi-value">
                            45
                            <span class="kpi-unit">Hz</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>目标：45Hz</span>
                            <span>范围：35-50Hz</span>
                        </div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-industry"></i>
                                制水车间负荷
                            </div>
                            <div class="kpi-trend trend-stable">
                                <i class="fas fa-minus"></i>
                                稳定
                            </div>
                        </div>
                        <div class="kpi-value">
                            85
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>目标：85%</span>
                            <span>最大：95%</span>
                        </div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-pump-medical"></i>
                                出厂水泵台数
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +1台
                            </div>
                        </div>
                        <div class="kpi-value">
                            3
                            <span class="kpi-unit">台</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>运行：3台</span>
                            <span>备用：1台</span>
                        </div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-tachometer-alt"></i>
                                管网压力
                            </div>
                            <div class="kpi-trend trend-stable">
                                <i class="fas fa-check-circle"></i>
                                正常
                            </div>
                        </div>
                        <div class="kpi-value">
                            0.42
                            <span class="kpi-unit">MPa</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>目标：0.4-0.5</span>
                            <span>状态：正常</span>
                        </div>
                    </div>
                </div>

                <!-- 调度指令 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-list-alt"></i>
                            当前调度指令
                        </h3>
                        <button class="btn btn-primary" onclick="generateNewDispatch()">
                            <i class="fas fa-sync-alt"></i>
                            重新生成
                        </button>
                    </div>
                    <div class="dispatch-commands">
                        <div class="command-item">
                            <div class="command-content">
                                <div class="command-title">调整2号出厂水泵运行参数</div>
                                <div class="command-detail">频率从40Hz调整至45Hz，预计增加出水量200m³/h</div>
                            </div>
                            <div class="command-status status-executing">执行中</div>
                        </div>

                        <div class="command-item">
                            <div class="command-content">
                                <div class="command-title">启动4号出厂水泵</div>
                                <div class="command-detail">为应对2号池液位过高，启动备用水泵增加出水能力</div>
                            </div>
                            <div class="command-status status-pending">待执行</div>
                        </div>

                        <div class="command-item">
                            <div class="command-content">
                                <div class="command-title">降低进厂水泵频率</div>
                                <div class="command-detail">1号进厂水泵频率从50Hz降至45Hz，减少进水量</div>
                            </div>
                            <div class="command-status status-completed">已完成</div>
                        </div>
                    </div>
                </div>

                <!-- 调度效果预测 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-chart-area"></i>
                            调度效果预测
                        </h3>
                    </div>
                    <div class="chart-container">
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-area"></i>
                            <h3>调度效果预测图</h3>
                            <p>预测液位：3.25m → 3.05m → 2.95m → 2.85m</p>
                            <p>目标液位：3.0m（稳定范围）</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 调度分析标签页 -->
            <div id="analysis" class="tab-content">
                <h2 style="color: #0f4c75; margin-bottom: 25px; display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-chart-bar"></i>
                    调度分析
                </h2>

                <!-- 分析指标 -->
                <div class="kpi-grid">
                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-bullseye"></i>
                                调度成功率
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +2.1%
                            </div>
                        </div>
                        <div class="kpi-value">
                            94.8
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>本月：94.8%</span>
                            <span>上月：92.7%</span>
                        </div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-clock"></i>
                                平均响应时间
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -1.2min
                            </div>
                        </div>
                        <div class="kpi-value">
                            8.5
                            <span class="kpi-unit">分钟</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>目标：<10分钟</span>
                            <span>最快：3.2分钟</span>
                        </div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-leaf"></i>
                                节能效果
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +3.8%
                            </div>
                        </div>
                        <div class="kpi-value">
                            12.3
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>节约电费：2,850元</span>
                            <span>本月累计：8,420元</span>
                        </div>
                    </div>

                    <div class="kpi-card">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-chart-line"></i>
                                预测精度提升
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +1.5%
                            </div>
                        </div>
                        <div class="kpi-value">
                            96.4
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>初始：89.2%</span>
                            <span>改进：+7.2%</span>
                        </div>
                    </div>
                </div>

                <!-- 分析图表 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-chart-line"></i>
                            预测vs实际对比
                        </h3>
                        <div class="chart-controls">
                            <button class="chart-btn active" onclick="switchAnalysisPeriod('7d')">7天</button>
                            <button class="chart-btn" onclick="switchAnalysisPeriod('30d')">30天</button>
                            <button class="chart-btn" onclick="switchAnalysisPeriod('90d')">90天</button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <div class="chart-placeholder">
                            <i class="fas fa-chart-line"></i>
                            <h3>预测精度分析图</h3>
                            <p>预测值：42,580 m³/日 | 实际值：42,520 m³/日</p>
                            <p>预测偏差：±1.4% | 精度：96.4%</p>
                        </div>
                    </div>
                </div>

                <!-- 调度数据统计 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-table"></i>
                            调度数据统计分析
                        </h3>
                        <button class="btn btn-success" onclick="exportAnalysisData()">
                            <i class="fas fa-download"></i>
                            导出报告
                        </button>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>调度次数</th>
                                <th>成功次数</th>
                                <th>成功率</th>
                                <th>平均响应时间</th>
                                <th>液位稳定度</th>
                                <th>能耗节约</th>
                                <th>预测偏差</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2024-01-15</td>
                                <td class="number">24</td>
                                <td class="number">23</td>
                                <td class="number">95.8%</td>
                                <td class="number">7.2分钟</td>
                                <td class="number">92.5%</td>
                                <td class="number">8.3%</td>
                                <td class="number">±2.1%</td>
                            </tr>
                            <tr>
                                <td>2024-01-14</td>
                                <td class="number">28</td>
                                <td class="number">26</td>
                                <td class="number">92.9%</td>
                                <td class="number">9.1分钟</td>
                                <td class="number">89.2%</td>
                                <td class="number">6.7%</td>
                                <td class="number">±3.5%</td>
                            </tr>
                            <tr>
                                <td>2024-01-13</td>
                                <td class="number">22</td>
                                <td class="number">21</td>
                                <td class="number">95.5%</td>
                                <td class="number">8.8分钟</td>
                                <td class="number">94.1%</td>
                                <td class="number">9.2%</td>
                                <td class="number">±2.8%</td>
                            </tr>
                            <tr>
                                <td>2024-01-12</td>
                                <td class="number">26</td>
                                <td class="number">24</td>
                                <td class="number">92.3%</td>
                                <td class="number">10.5分钟</td>
                                <td class="number">87.6%</td>
                                <td class="number">5.4%</td>
                                <td class="number">±4.2%</td>
                            </tr>
                            <tr>
                                <td>2024-01-11</td>
                                <td class="number">30</td>
                                <td class="number">29</td>
                                <td class="number">96.7%</td>
                                <td class="number">6.9分钟</td>
                                <td class="number">95.8%</td>
                                <td class="number">11.1%</td>
                                <td class="number">±1.9%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 优化建议 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-lightbulb"></i>
                            模型优化建议
                        </h3>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 25px;">
                        <div>
                            <h4 style="color: #0f4c75; margin-bottom: 15px;">预测模型优化</h4>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                                <div style="font-weight: 600; color: #2e7d32; margin-bottom: 8px;">
                                    <i class="fas fa-check-circle"></i> 建议采纳
                                </div>
                                <div style="font-size: 0.9rem; line-height: 1.5;">
                                    增加天气数据权重，特别是降雨量对用水需求的影响，预计可提升预测精度2-3%
                                </div>
                            </div>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                                <div style="font-weight: 600; color: #ff9800; margin-bottom: 8px;">
                                    <i class="fas fa-exclamation-triangle"></i> 待评估
                                </div>
                                <div style="font-size: 0.9rem; line-height: 1.5;">
                                    考虑引入社会活动数据（如大型活动、停水通知等）作为预测因子
                                </div>
                            </div>
                        </div>

                        <div>
                            <h4 style="color: #0f4c75; margin-bottom: 15px;">调度策略优化</h4>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                                <div style="font-weight: 600; color: #2e7d32; margin-bottom: 8px;">
                                    <i class="fas fa-check-circle"></i> 已实施
                                </div>
                                <div style="font-size: 0.9rem; line-height: 1.5;">
                                    优化水泵启停策略，减少频繁启停，延长设备寿命，节能效果提升15%
                                </div>
                            </div>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                                <div style="font-weight: 600; color: #ff9800; margin-bottom: 8px;">
                                    <i class="fas fa-cog"></i> 调整中
                                </div>
                                <div style="font-size: 0.9rem; line-height: 1.5;">
                                    引入分时电价策略，在低电价时段适当提高储水量，降低运行成本
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTab = 'prediction';
        let updateInterval;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('水量智能预测调度系统初始化...');
            
            // 初始化标签页事件
            initTabEvents();
            
            // 启动实时更新
            startRealTimeUpdate();
            
            // 启动数值动画
            animateCounters();
            
            console.log('系统初始化完成');
        });

        // 初始化标签页事件
        function initTabEvents() {
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    switchTab(tabId, this);
                });
            });
        }

        // 标签页切换
        function switchTab(tabId, clickedTab) {
            console.log('切换到标签页:', tabId);
            
            // 更新当前标签页
            currentTab = tabId;
            
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有激活状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中内容
            const targetContent = document.getElementById(tabId);
            if (targetContent) {
                targetContent.classList.add('active');
            }
            
            // 激活选中标签
            if (clickedTab) {
                clickedTab.classList.add('active');
            }
            
            // 加载对应内容
            setTimeout(() => {
                loadTabContent(tabId);
            }, 100);
        }

        // 加载标签页内容
        function loadTabContent(tabId) {
            switch(tabId) {
                case 'monitoring':
                    loadMonitoringContent();
                    break;
                case 'dispatch':
                    loadDispatchContent();
                    break;
                case 'analysis':
                    loadAnalysisContent();
                    break;
                default:
                    console.log('预测标签页已激活');
            }
        }

        // 切换时间周期
        function switchPeriod(period) {
            // 更新按钮状态
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            console.log('切换到时间周期:', period);
            showNotification(`已切换到${period}视图`, 'info');
        }

        // 数值动画
        function animateCounters() {
            const counters = document.querySelectorAll('.kpi-value');
            counters.forEach(counter => {
                const text = counter.textContent;
                const number = parseFloat(text.replace(/[^\d.]/g, ''));
                
                if (isNaN(number)) return;
                
                let current = 0;
                const increment = number / 50;
                const unit = counter.querySelector('.kpi-unit');
                const unitText = unit ? unit.textContent : '';
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= number) {
                        current = number;
                        clearInterval(timer);
                    }
                    
                    if (number < 100) {
                        counter.innerHTML = current.toFixed(1) + 
                            (unitText ? `<span class="kpi-unit">${unitText}</span>` : '');
                    } else {
                        counter.innerHTML = Math.round(current).toLocaleString() + 
                            (unitText ? `<span class="kpi-unit">${unitText}</span>` : '');
                    }
                }, 40);
            });
        }

        // 实时数据更新
        function startRealTimeUpdate() {
            updateInterval = setInterval(() => {
                // 更新时间
                const now = new Date();
                const timeStr = now.toLocaleTimeString();
                document.getElementById('updateTime').textContent = `更新时间：${timeStr}`;
                
                // 模拟数据更新
                updateRandomData();
            }, 30000);
        }

        // 更新随机数据
        function updateRandomData() {
            const kpiValues = document.querySelectorAll('.kpi-value');
            kpiValues.forEach(value => {
                const text = value.textContent;
                const number = parseFloat(text.replace(/[^\d.]/g, ''));
                
                if (isNaN(number)) return;
                
                const variation = (Math.random() - 0.5) * 0.02; // ±1%变化
                const newNumber = number * (1 + variation);
                const unit = value.querySelector('.kpi-unit');
                const unitText = unit ? unit.textContent : '';
                
                if (number < 100) {
                    value.innerHTML = newNumber.toFixed(1) + 
                        (unitText ? `<span class="kpi-unit">${unitText}</span>` : '');
                } else {
                    value.innerHTML = Math.round(newNumber).toLocaleString() + 
                        (unitText ? `<span class="kpi-unit">${unitText}</span>` : '');
                }
            });
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${getNotificationColor(type)};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                z-index: 10000;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                max-width: 300px;
                animation: slideInRight 0.3s ease-out;
            `;
            
            const icon = getNotificationIcon(type);
            notification.innerHTML = `<i class="${icon}" style="margin-right: 8px;"></i>${message}`;
            
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-out';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 获取通知颜色
        function getNotificationColor(type) {
            const colors = {
                'success': '#4caf50',
                'error': '#f44336',
                'warning': '#ff9800',
                'info': '#2196f3'
            };
            return colors[type] || colors.info;
        }

        // 获取通知图标
        function getNotificationIcon(type) {
            const icons = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };
            return icons[type] || icons.info;
        }

        // 加载监控内容
        function loadMonitoringContent() {
            console.log('加载清水池监控内容');
            startLevelAnimation();
        }

        // 加载调度内容
        function loadDispatchContent() {
            console.log('加载调度策略管理内容');
        }

        // 加载分析内容
        function loadAnalysisContent() {
            console.log('加载调度分析内容');
        }

        // 启动液位动画
        function startLevelAnimation() {
            const waterLevel = document.getElementById('waterLevel');
            if (waterLevel) {
                setInterval(() => {
                    const currentHeight = parseFloat(waterLevel.style.height) || 65;
                    const variation = (Math.random() - 0.5) * 4; // ±2%变化
                    const newHeight = Math.max(20, Math.min(95, currentHeight + variation));
                    waterLevel.style.height = newHeight + '%';

                    // 更新液位数值
                    const levelValue = document.querySelector('.level-value');
                    if (levelValue) {
                        const newLevel = (newHeight / 100 * 5).toFixed(2);
                        levelValue.textContent = newLevel + 'm';
                    }
                }, 3000);
            }
        }

        // 切换液位监控周期
        function switchLevelPeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            console.log('切换液位监控周期:', period);
            showNotification(`已切换到${period}液位视图`, 'info');
        }

        // 刷新传感器状态
        function refreshSensors() {
            showNotification('正在刷新传感器状态...', 'info');

            setTimeout(() => {
                showNotification('传感器状态已更新！', 'success');

                // 模拟更新传感器数据
                const sensorRows = document.querySelectorAll('.data-table tbody tr');
                sensorRows.forEach(row => {
                    const timeCell = row.cells[4];
                    if (timeCell) {
                        const now = new Date();
                        timeCell.textContent = now.toLocaleTimeString();
                    }
                });
            }, 1500);
        }

        // 生成新调度
        function generateNewDispatch() {
            showNotification('正在生成新的调度策略...', 'info');

            setTimeout(() => {
                showNotification('新调度策略已生成！', 'success');

                // 模拟更新调度指令状态
                const commands = document.querySelectorAll('.command-status');
                commands.forEach((status, index) => {
                    if (index === 0) {
                        status.className = 'command-status status-completed';
                        status.textContent = '已完成';
                    } else if (index === 1) {
                        status.className = 'command-status status-executing';
                        status.textContent = '执行中';
                    }
                });
            }, 2000);
        }

        // 切换分析周期
        function switchAnalysisPeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            console.log('切换分析周期:', period);
            showNotification(`已切换到${period}分析视图`, 'info');
        }

        // 导出分析数据
        function exportAnalysisData() {
            showNotification('正在生成分析报告...', 'info');

            // 模拟数据导出
            const data = [
                ['日期', '调度次数', '成功次数', '成功率', '平均响应时间', '液位稳定度', '能耗节约', '预测偏差'],
                ['2024-01-15', '24', '23', '95.8%', '7.2分钟', '92.5%', '8.3%', '±2.1%'],
                ['2024-01-14', '28', '26', '92.9%', '9.1分钟', '89.2%', '6.7%', '±3.5%'],
                ['2024-01-13', '22', '21', '95.5%', '8.8分钟', '94.1%', '9.2%', '±2.8%']
            ];

            let csvContent = '';
            data.forEach(row => {
                csvContent += row.join(',') + '\n';
            });

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `调度分析报告_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            setTimeout(() => {
                showNotification('分析报告导出成功！', 'success');
            }, 2000);
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
