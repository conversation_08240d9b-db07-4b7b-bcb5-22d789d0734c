<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生产数据分析对比系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 头部标题 */
        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .main-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .date-info {
            display: flex;
            justify-content: center;
            gap: 40px;
            font-size: 1rem;
        }

        .date-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
        }

        /* 卡片样式 */
        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .card-title {
            font-size: 1.4rem;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        /* 关键指标网格 */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .metric-content {
            position: relative;
            z-index: 1;
        }

        .metric-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .metric-value {
            font-size: 2.2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 1rem;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .metric-change {
            font-size: 0.9rem;
            padding: 5px 10px;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.2);
        }

        .positive { color: #2ecc71; }
        .negative { color: #e74c3c; }
        .neutral { color: #f39c12; }

        /* 对比分析表格 */
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #ecf0f1;
        }

        .comparison-table th {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            font-weight: 600;
        }

        .comparison-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .comparison-table tr:hover {
            background: #e3f2fd;
        }

        .trend-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .trend-up {
            background: #d4edda;
            color: #155724;
        }

        .trend-down {
            background: #f8d7da;
            color: #721c24;
        }

        .trend-stable {
            background: #fff3cd;
            color: #856404;
        }

        /* 图表容器 */
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        .chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-top: 30px;
        }

        /* 设备状态网格 */
        .equipment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .equipment-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #3498db;
        }

        .equipment-name {
            font-weight: 600;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .equipment-status {
            font-size: 0.9rem;
            margin-bottom: 8px;
        }

        .status-normal { color: #27ae60; }
        .status-warning { color: #f39c12; }
        .status-error { color: #e74c3c; }

        .equipment-efficiency {
            font-size: 1.2rem;
            font-weight: bold;
            color: #3498db;
        }

        /* 质量分析 */
        .quality-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .quality-item {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border-radius: 10px;
        }

        .quality-value {
            font-size: 2rem;
            font-weight: bold;
            color: #2e7d32;
            margin-bottom: 5px;
        }

        .quality-label {
            font-size: 0.9rem;
            color: #4a4a4a;
        }

        /* 能耗分析 */
        .energy-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .energy-item {
            text-align: center;
            padding: 15px;
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
            border-radius: 10px;
        }

        .energy-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ef6c00;
            margin-bottom: 5px;
        }

        .energy-label {
            font-size: 0.8rem;
            color: #5d4037;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .main-title {
                font-size: 2rem;
            }

            .date-info {
                flex-direction: column;
                gap: 15px;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
            }

            .chart-grid {
                grid-template-columns: 1fr;
            }

            .comparison-table {
                font-size: 0.85rem;
            }

            .comparison-table th,
            .comparison-table td {
                padding: 10px 8px;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.6s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-30px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* 警告提示 */
        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin: 20px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-warning {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            color: #856404;
        }

        .alert-danger {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
            color: #721c24;
        }

        .alert-success {
            background: #d4edda;
            border-left: 4px solid #28a745;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header fade-in">
            <h1 class="main-title">
                <i class="fas fa-chart-line"></i>
                生产数据分析对比系统
            </h1>
            <p class="subtitle">昨日生产分析 · 数据对比 · 趋势预测</p>
            <div class="date-info">
                <div class="date-item">
                    <i class="fas fa-calendar-day"></i>
                    昨日：2024年1月14日
                </div>
                <div class="date-item">
                    <i class="fas fa-calendar-check"></i>
                    今日：2024年1月15日
                </div>
            </div>
        </div>

        <!-- 关键指标概览 -->
        <div class="card slide-in">
            <h2 class="card-title">
                <i class="fas fa-tachometer-alt"></i>
                关键指标概览
            </h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-content">
                        <div class="metric-icon">
                            <i class="fas fa-industry"></i>
                        </div>
                        <div class="metric-value">2,847</div>
                        <div class="metric-label">总产量 (件)</div>
                        <div class="metric-change positive">
                            <i class="fas fa-arrow-up"></i> +5.2% vs 昨日
                        </div>
                    </div>
                </div>

                <div class="metric-card">
                    <div class="metric-content">
                        <div class="metric-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="metric-value">1,245</div>
                        <div class="metric-label">总能耗 (kWh)</div>
                        <div class="metric-change negative">
                            <i class="fas fa-arrow-down"></i> -2.8% vs 昨日
                        </div>
                    </div>
                </div>

                <div class="metric-card">
                    <div class="metric-content">
                        <div class="metric-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="metric-value">97.8%</div>
                        <div class="metric-label">产品合格率</div>
                        <div class="metric-change positive">
                            <i class="fas fa-arrow-up"></i> +1.2% vs 昨日
                        </div>
                    </div>
                </div>

                <div class="metric-card">
                    <div class="metric-content">
                        <div class="metric-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="metric-value">94.5%</div>
                        <div class="metric-label">设备综合效率</div>
                        <div class="metric-change neutral">
                            <i class="fas fa-minus"></i> -0.3% vs 昨日
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 生产数据对比分析 -->
        <div class="card fade-in">
            <h2 class="card-title">
                <i class="fas fa-balance-scale"></i>
                生产数据对比分析
            </h2>
            
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>指标项目</th>
                        <th>昨日数据</th>
                        <th>今日数据</th>
                        <th>变化幅度</th>
                        <th>趋势分析</th>
                        <th>状态评估</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>总产量 (件)</strong></td>
                        <td>2,705</td>
                        <td>2,847</td>
                        <td class="positive">+142 (+5.2%)</td>
                        <td><span class="trend-indicator trend-up"><i class="fas fa-arrow-up"></i> 上升</span></td>
                        <td><span class="trend-indicator trend-up">优秀</span></td>
                    </tr>
                    <tr>
                        <td><strong>A类产品产量</strong></td>
                        <td>1,623</td>
                        <td>1,708</td>
                        <td class="positive">+85 (+5.2%)</td>
                        <td><span class="trend-indicator trend-up"><i class="fas fa-arrow-up"></i> 上升</span></td>
                        <td><span class="trend-indicator trend-up">良好</span></td>
                    </tr>
                    <tr>
                        <td><strong>B类产品产量</strong></td>
                        <td>1,082</td>
                        <td>1,139</td>
                        <td class="positive">+57 (+5.3%)</td>
                        <td><span class="trend-indicator trend-up"><i class="fas fa-arrow-up"></i> 上升</span></td>
                        <td><span class="trend-indicator trend-up">良好</span></td>
                    </tr>
                    <tr>
                        <td><strong>总能耗 (kWh)</strong></td>
                        <td>1,281</td>
                        <td>1,245</td>
                        <td class="positive">-36 (-2.8%)</td>
                        <td><span class="trend-indicator trend-down"><i class="fas fa-arrow-down"></i> 下降</span></td>
                        <td><span class="trend-indicator trend-up">优秀</span></td>
                    </tr>
                    <tr>
                        <td><strong>单位产品能耗</strong></td>
                        <td>0.474 kWh/件</td>
                        <td>0.437 kWh/件</td>
                        <td class="positive">-0.037 (-7.8%)</td>
                        <td><span class="trend-indicator trend-down"><i class="fas fa-arrow-down"></i> 下降</span></td>
                        <td><span class="trend-indicator trend-up">优秀</span></td>
                    </tr>
                    <tr>
                        <td><strong>产品合格率</strong></td>
                        <td>96.6%</td>
                        <td>97.8%</td>
                        <td class="positive">+1.2%</td>
                        <td><span class="trend-indicator trend-up"><i class="fas fa-arrow-up"></i> 上升</span></td>
                        <td><span class="trend-indicator trend-up">优秀</span></td>
                    </tr>
                    <tr>
                        <td><strong>设备综合效率</strong></td>
                        <td>94.8%</td>
                        <td>94.5%</td>
                        <td class="negative">-0.3%</td>
                        <td><span class="trend-indicator trend-stable"><i class="fas fa-minus"></i> 稳定</span></td>
                        <td><span class="trend-indicator trend-stable">正常</span></td>
                    </tr>
                    <tr>
                        <td><strong>平均生产周期</strong></td>
                        <td>4.2 小时/批</td>
                        <td>3.9 小时/批</td>
                        <td class="positive">-0.3 (-7.1%)</td>
                        <td><span class="trend-indicator trend-down"><i class="fas fa-arrow-down"></i> 下降</span></td>
                        <td><span class="trend-indicator trend-up">优秀</span></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 设备运行状态分析 -->
        <div class="card slide-in">
            <h2 class="card-title">
                <i class="fas fa-cogs"></i>
                设备运行状态分析
            </h2>
            
            <div class="equipment-grid">
                <div class="equipment-item">
                    <div class="equipment-name">生产线A</div>
                    <div class="equipment-status status-normal">
                        <i class="fas fa-check-circle"></i> 运行正常
                    </div>
                    <div class="equipment-efficiency">96.2%</div>
                    <div style="font-size: 0.8rem; color: #666; margin-top: 5px;">
                        昨日: 95.8% (+0.4%)
                    </div>
                </div>

                <div class="equipment-item">
                    <div class="equipment-name">生产线B</div>
                    <div class="equipment-status status-warning">
                        <i class="fas fa-exclamation-triangle"></i> 效率偏低
                    </div>
                    <div class="equipment-efficiency">89.3%</div>
                    <div style="font-size: 0.8rem; color: #666; margin-top: 5px;">
                        昨日: 92.1% (-2.8%)
                    </div>
                </div>

                <div class="equipment-item">
                    <div class="equipment-name">包装线1</div>
                    <div class="equipment-status status-normal">
                        <i class="fas fa-check-circle"></i> 运行正常
                    </div>
                    <div class="equipment-efficiency">98.7%</div>
                    <div style="font-size: 0.8rem; color: #666; margin-top: 5px;">
                        昨日: 97.9% (+0.8%)
                    </div>
                </div>

                <div class="equipment-item">
                    <div class="equipment-name">包装线2</div>
                    <div class="equipment-status status-normal">
                        <i class="fas fa-check-circle"></i> 运行正常
                    </div>
                    <div class="equipment-efficiency">97.4%</div>
                    <div style="font-size: 0.8rem; color: #666; margin-top: 5px;">
                        昨日: 96.8% (+0.6%)
                    </div>
                </div>

                <div class="equipment-item">
                    <div class="equipment-name">质检设备</div>
                    <div class="equipment-status status-normal">
                        <i class="fas fa-check-circle"></i> 运行正常
                    </div>
                    <div class="equipment-efficiency">99.1%</div>
                    <div style="font-size: 0.8rem; color: #666; margin-top: 5px;">
                        昨日: 98.8% (+0.3%)
                    </div>
                </div>

                <div class="equipment-item">
                    <div class="equipment-name">物料输送</div>
                    <div class="equipment-status status-normal">
                        <i class="fas fa-check-circle"></i> 运行正常
                    </div>
                    <div class="equipment-efficiency">95.6%</div>
                    <div style="font-size: 0.8rem; color: #666; margin-top: 5px;">
                        昨日: 94.9% (+0.7%)
                    </div>
                </div>
            </div>
        </div>

        <!-- 产品质量分析 -->
        <div class="card fade-in">
            <h2 class="card-title">
                <i class="fas fa-award"></i>
                产品质量分析
            </h2>
            
            <div class="quality-metrics">
                <div class="quality-item">
                    <div class="quality-value">97.8%</div>
                    <div class="quality-label">总体合格率</div>
                    <div style="font-size: 0.8rem; color: #666; margin-top: 5px;">
                        昨日: 96.6% (+1.2%)
                    </div>
                </div>

                <div class="quality-item">
                    <div class="quality-value">98.4%</div>
                    <div class="quality-label">A类产品合格率</div>
                    <div style="font-size: 0.8rem; color: #666; margin-top: 5px;">
                        昨日: 97.1% (+1.3%)
                    </div>
                </div>

                <div class="quality-item">
                    <div class="quality-value">96.9%</div>
                    <div class="quality-label">B类产品合格率</div>
                    <div style="font-size: 0.8rem; color: #666; margin-top: 5px;">
                        昨日: 95.8% (+1.1%)
                    </div>
                </div>

                <div class="quality-item">
                    <div class="quality-value">63</div>
                    <div class="quality-label">不合格品数量</div>
                    <div style="font-size: 0.8rem; color: #666; margin-top: 5px;">
                        昨日: 92 (-29件)
                    </div>
                </div>

                <div class="quality-item">
                    <div class="quality-value">2.1%</div>
                    <div class="quality-label">返工率</div>
                    <div style="font-size: 0.8rem; color: #666; margin-top: 5px;">
                        昨日: 2.8% (-0.7%)
                    </div>
                </div>

                <div class="quality-item">
                    <div class="quality-value">0.1%</div>
                    <div class="quality-label">报废率</div>
                    <div style="font-size: 0.8rem; color: #666; margin-top: 5px;">
                        昨日: 0.6% (-0.5%)
                    </div>
                </div>
            </div>
        </div>

        <!-- 能耗分析 -->
        <div class="card slide-in">
            <h2 class="card-title">
                <i class="fas fa-leaf"></i>
                能耗分析
            </h2>
            
            <div class="energy-breakdown">
                <div class="energy-item">
                    <div class="energy-value">1,245</div>
                    <div class="energy-label">总能耗 (kWh)</div>
                    <div style="font-size: 0.7rem; color: #666; margin-top: 5px;">
                        昨日: 1,281 (-2.8%)
                    </div>
                </div>

                <div class="energy-item">
                    <div class="energy-value">892</div>
                    <div class="energy-label">生产用电 (kWh)</div>
                    <div style="font-size: 0.7rem; color: #666; margin-top: 5px;">
                        昨日: 918 (-2.8%)
                    </div>
                </div>

                <div class="energy-item">
                    <div class="energy-value">198</div>
                    <div class="energy-label">照明用电 (kWh)</div>
                    <div style="font-size: 0.7rem; color: #666; margin-top: 5px;">
                        昨日: 205 (-3.4%)
                    </div>
                </div>

                <div class="energy-item">
                    <div class="energy-value">155</div>
                    <div class="energy-label">空调用电 (kWh)</div>
                    <div style="font-size: 0.7rem; color: #666; margin-top: 5px;">
                        昨日: 158 (-1.9%)
                    </div>
                </div>

                <div class="energy-item">
                    <div class="energy-value">0.437</div>
                    <div class="energy-label">单位能耗 (kWh/件)</div>
                    <div style="font-size: 0.7rem; color: #666; margin-top: 5px;">
                        昨日: 0.474 (-7.8%)
                    </div>
                </div>

                <div class="energy-item">
                    <div class="energy-value">87.3%</div>
                    <div class="energy-label">能效比</div>
                    <div style="font-size: 0.7rem; color: #666; margin-top: 5px;">
                        昨日: 84.1% (+3.2%)
                    </div>
                </div>
            </div>
        </div>

        <!-- 趋势分析图表 -->
        <div class="card fade-in">
            <h2 class="card-title">
                <i class="fas fa-chart-area"></i>
                生产趋势分析
            </h2>

            <div class="chart-grid">
                <div>
                    <h3 style="text-align: center; margin-bottom: 15px; color: #2c3e50;">产量与能耗对比趋势</h3>
                    <div class="chart-container">
                        <canvas id="productionTrendChart"></canvas>
                    </div>
                </div>

                <div>
                    <h3 style="text-align: center; margin-bottom: 15px; color: #2c3e50;">质量指标变化趋势</h3>
                    <div class="chart-container">
                        <canvas id="qualityTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备效率对比 -->
        <div class="card slide-in">
            <h2 class="card-title">
                <i class="fas fa-tachometer-alt"></i>
                设备效率对比分析
            </h2>

            <div class="chart-container">
                <canvas id="equipmentEfficiencyChart"></canvas>
            </div>
        </div>

        <!-- 详细数据分析 -->
        <div class="card fade-in">
            <h2 class="card-title">
                <i class="fas fa-microscope"></i>
                详细数据分析
            </h2>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 20px;">
                <div>
                    <h3 style="color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px;">
                        <i class="fas fa-clock"></i> 时段产量分析
                    </h3>
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #ecf0f1;">
                                <th style="padding: 10px; text-align: left;">时段</th>
                                <th style="padding: 10px; text-align: center;">昨日产量</th>
                                <th style="padding: 10px; text-align: center;">今日产量</th>
                                <th style="padding: 10px; text-align: center;">变化</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="padding: 8px;">08:00-12:00</td>
                                <td style="padding: 8px; text-align: center;">678</td>
                                <td style="padding: 8px; text-align: center;">712</td>
                                <td style="padding: 8px; text-align: center; color: #27ae60;">+34</td>
                            </tr>
                            <tr style="background: #f8f9fa;">
                                <td style="padding: 8px;">12:00-16:00</td>
                                <td style="padding: 8px; text-align: center;">692</td>
                                <td style="padding: 8px; text-align: center;">728</td>
                                <td style="padding: 8px; text-align: center; color: #27ae60;">+36</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px;">16:00-20:00</td>
                                <td style="padding: 8px; text-align: center;">685</td>
                                <td style="padding: 8px; text-align: center;">719</td>
                                <td style="padding: 8px; text-align: center; color: #27ae60;">+34</td>
                            </tr>
                            <tr style="background: #f8f9fa;">
                                <td style="padding: 8px;">20:00-24:00</td>
                                <td style="padding: 8px; text-align: center;">650</td>
                                <td style="padding: 8px; text-align: center;">688</td>
                                <td style="padding: 8px; text-align: center; color: #27ae60;">+38</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div>
                    <h3 style="color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #e74c3c; padding-bottom: 5px;">
                        <i class="fas fa-exclamation-circle"></i> 异常事件记录
                    </h3>
                    <div style="space-y: 10px;">
                        <div style="padding: 10px; background: #fff3cd; border-left: 4px solid #ffc107; margin-bottom: 10px; border-radius: 4px;">
                            <div style="font-weight: 600; color: #856404;">09:15 - 生产线B效率下降</div>
                            <div style="font-size: 0.9rem; color: #666; margin-top: 5px;">
                                效率从95%降至89%，持续时间45分钟，已安排技术人员检查
                            </div>
                        </div>

                        <div style="padding: 10px; background: #f8d7da; border-left: 4px solid #dc3545; margin-bottom: 10px; border-radius: 4px;">
                            <div style="font-weight: 600; color: #721c24;">14:30 - 质检设备校准</div>
                            <div style="font-size: 0.9rem; color: #666; margin-top: 5px;">
                                例行校准导致生产暂停15分钟，质量检测精度提升
                            </div>
                        </div>

                        <div style="padding: 10px; background: #d4edda; border-left: 4px solid #28a745; margin-bottom: 10px; border-radius: 4px;">
                            <div style="font-weight: 600; color: #155724;">16:45 - 新工艺优化生效</div>
                            <div style="font-size: 0.9rem; color: #666; margin-top: 5px;">
                                新的生产工艺参数调整，单位能耗降低8%，效果显著
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 问题识别与建议 -->
        <div class="card fade-in">
            <h2 class="card-title">
                <i class="fas fa-lightbulb"></i>
                问题识别与优化建议
            </h2>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 25px;">
                <div>
                    <h3 style="color: #e74c3c; margin-bottom: 15px;">
                        <i class="fas fa-exclamation-triangle"></i> 识别问题
                    </h3>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <div>
                            <strong>设备效率异常：</strong>生产线B效率较昨日下降2.8%，建议检查设备运行状态，进行预防性维护。
                        </div>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-clock"></i>
                        <div>
                            <strong>生产节拍优化：</strong>虽然生产周期缩短，但需关注设备负荷，避免过度使用影响设备寿命。
                        </div>
                    </div>
                </div>

                <div>
                    <h3 style="color: #27ae60; margin-bottom: 15px;">
                        <i class="fas fa-thumbs-up"></i> 优化成效
                    </h3>

                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <div>
                            <strong>质量提升显著：</strong>产品合格率较昨日提升1.2%，质量管控措施效果良好，建议继续保持。
                        </div>
                    </div>

                    <div class="alert alert-success">
                        <i class="fas fa-leaf"></i>
                        <div>
                            <strong>能耗优化成效：</strong>单位产品能耗下降7.8%，节能措施效果显著，建议推广至其他生产线。
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 预测与建议 -->
        <div class="card slide-in">
            <h2 class="card-title">
                <i class="fas fa-crystal-ball"></i>
                明日生产预测与建议
            </h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; border-radius: 10px;">
                    <h4 style="margin-bottom: 15px;"><i class="fas fa-chart-line"></i> 产量预测</h4>
                    <div style="font-size: 1.5rem; font-weight: bold; margin-bottom: 10px;">2,950 - 3,100 件</div>
                    <div style="font-size: 0.9rem; opacity: 0.9;">
                        基于当前趋势，预计明日产量将继续保持增长态势，建议确保原材料充足供应
                    </div>
                </div>

                <div style="background: linear-gradient(135deg, #f093fb, #f5576c); color: white; padding: 20px; border-radius: 10px;">
                    <h4 style="margin-bottom: 15px;"><i class="fas fa-bolt"></i> 能耗预测</h4>
                    <div style="font-size: 1.5rem; font-weight: bold; margin-bottom: 10px;">1,280 - 1,320 kWh</div>
                    <div style="font-size: 0.9rem; opacity: 0.9;">
                        随着产量增加，总能耗可能上升，但单位能耗预计保持优化水平
                    </div>
                </div>

                <div style="background: linear-gradient(135deg, #4facfe, #00f2fe); color: white; padding: 20px; border-radius: 10px;">
                    <h4 style="margin-bottom: 15px;"><i class="fas fa-shield-alt"></i> 质量预测</h4>
                    <div style="font-size: 1.5rem; font-weight: bold; margin-bottom: 10px;">97.5% - 98.2%</div>
                    <div style="font-size: 0.9rem; opacity: 0.9;">
                        质量控制措施持续生效，预计合格率将维持在高水平
                    </div>
                </div>
            </div>

            <div style="margin-top: 25px; padding: 20px; background: #f8f9fa; border-radius: 10px; border-left: 4px solid #3498db;">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">
                    <i class="fas fa-clipboard-list"></i> 明日生产建议
                </h4>
                <ul style="list-style: none; padding: 0;">
                    <li style="padding: 8px 0; border-bottom: 1px solid #ecf0f1;">
                        <i class="fas fa-wrench" style="color: #f39c12; margin-right: 10px;"></i>
                        <strong>设备维护：</strong>对生产线B进行深度检查和维护，确保设备效率恢复正常水平
                    </li>
                    <li style="padding: 8px 0; border-bottom: 1px solid #ecf0f1;">
                        <i class="fas fa-boxes" style="color: #27ae60; margin-right: 10px;"></i>
                        <strong>物料准备：</strong>根据产量预测，提前准备充足的原材料，避免生产中断
                    </li>
                    <li style="padding: 8px 0; border-bottom: 1px solid #ecf0f1;">
                        <i class="fas fa-users" style="color: #3498db; margin-right: 10px;"></i>
                        <strong>人员安排：</strong>合理安排班次，确保关键岗位人员充足，特别是质检环节
                    </li>
                    <li style="padding: 8px 0;">
                        <i class="fas fa-chart-bar" style="color: #9b59b6; margin-right: 10px;"></i>
                        <strong>监控重点：</strong>重点监控生产线B的运行状态和整体设备综合效率指标
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
        });

        function initializeCharts() {
            // 产量与能耗对比趋势图
            const productionCtx = document.getElementById('productionTrendChart').getContext('2d');
            new Chart(productionCtx, {
                type: 'line',
                data: {
                    labels: ['08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00'],
                    datasets: [{
                        label: '昨日产量',
                        data: [85, 170, 340, 510, 680, 850, 1020, 1190],
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '今日产量',
                        data: [89, 178, 356, 534, 712, 890, 1068, 1246],
                        borderColor: '#27ae60',
                        backgroundColor: 'rgba(39, 174, 96, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '昨日能耗',
                        data: [40, 80, 160, 240, 320, 400, 480, 560],
                        borderColor: '#f39c12',
                        backgroundColor: 'rgba(243, 156, 18, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }, {
                        label: '今日能耗',
                        data: [39, 78, 156, 234, 312, 390, 468, 546],
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '产量 (件)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '能耗 (kWh)'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });

            // 质量指标变化趋势图
            const qualityCtx = document.getElementById('qualityTrendChart').getContext('2d');
            new Chart(qualityCtx, {
                type: 'bar',
                data: {
                    labels: ['合格率', '返工率', '报废率', '一次通过率'],
                    datasets: [{
                        label: '昨日',
                        data: [96.6, 2.8, 0.6, 94.2],
                        backgroundColor: 'rgba(231, 76, 60, 0.7)',
                        borderColor: '#e74c3c',
                        borderWidth: 1
                    }, {
                        label: '今日',
                        data: [97.8, 2.1, 0.1, 95.7],
                        backgroundColor: 'rgba(39, 174, 96, 0.7)',
                        borderColor: '#27ae60',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: '百分比 (%)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });

            // 设备效率对比图
            const equipmentCtx = document.getElementById('equipmentEfficiencyChart').getContext('2d');
            new Chart(equipmentCtx, {
                type: 'radar',
                data: {
                    labels: ['生产线A', '生产线B', '包装线1', '包装线2', '质检设备', '物料输送'],
                    datasets: [{
                        label: '昨日效率',
                        data: [95.8, 92.1, 97.9, 96.8, 98.8, 94.9],
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.2)',
                        pointBackgroundColor: '#e74c3c'
                    }, {
                        label: '今日效率',
                        data: [96.2, 89.3, 98.7, 97.4, 99.1, 95.6],
                        borderColor: '#27ae60',
                        backgroundColor: 'rgba(39, 174, 96, 0.2)',
                        pointBackgroundColor: '#27ae60'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: '效率 (%)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>
