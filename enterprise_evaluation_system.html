<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业评价体系管理平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 10px;
            padding: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .nav-tab.active {
            background: #667eea;
            color: white;
        }
        
        .nav-tab:hover:not(.active) {
            background: #f0f2f5;
        }
        
        .content-section {
            display: none;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .content-section.active {
            display: block;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f2f5;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .table tr:hover {
            background: #f8f9fa;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-approved {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 1000px;
            max-height: 90vh;
            overflow-y: auto;
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #000;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #495057;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
        }
        
        .indicator-group {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .indicator-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .indicator-item:last-child {
            border-bottom: none;
        }
        
        .weight-input {
            width: 80px;
            text-align: center;
        }
        
        .enterprise-card {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        
        .enterprise-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .enterprise-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .enterprise-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .enterprise-type {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .type-eagle {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .type-gazelle {
            background: #f3e5f5;
            color: #7b1fa2;
        }
        
        .type-unicorn {
            background: #fff8e1;
            color: #f57c00;
        }
        
        .enterprise-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
        }
        
        .info-label {
            color: #666;
            font-size: 14px;
        }
        
        .info-value {
            font-weight: 500;
            color: #333;
        }
        
        .ai-result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #667eea;
            margin-top: 15px;
        }
        
        .ai-result-header {
            font-weight: 600;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .score-bar {
            background: #e9ecef;
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .score-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>企业评价体系管理平台</h1>
            <p>雏鹰-瞪羚-独角兽梯度评价指标体系</p>
        </div>
        
        <div class="nav-tabs">
            <div class="nav-tab active" onclick="switchTab('version-management')">版本管理</div>
            <div class="nav-tab" onclick="switchTab('evaluation-config')">评价体系配置</div>
            <div class="nav-tab" onclick="switchTab('intelligent-review')">智能评审</div>
        </div>
        
        <!-- 版本管理 -->
        <div id="version-management" class="content-section active">
            <div class="section-header">
                <h2>评价模版管理</h2>
                <button class="btn btn-primary" onclick="openModal('template-modal')">新增模版</button>
            </div>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>模版名称</th>
                        <th>版本号</th>
                        <th>创建时间</th>
                        <th>状态</th>
                        <th>适用类型</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="template-list">
                    <!-- 模版数据将通过JavaScript动态生成 -->
                </tbody>
            </table>
        </div>
        
        <!-- 评价体系配置 -->
        <div id="evaluation-config" class="content-section">
            <div class="section-header">
                <h2>评价指标配置</h2>
                <div>
                    <select id="config-type" class="form-control" style="width: 200px; display: inline-block;">
                        <option value="eagle">雏鹰企业</option>
                        <option value="gazelle">瞪羚企业</option>
                        <option value="unicorn">独角兽企业</option>
                    </select>
                    <button class="btn btn-primary" onclick="saveConfig()" style="margin-left: 10px;">保存配置</button>
                </div>
            </div>
            
            <div id="config-content">
                <!-- 配置内容将通过JavaScript动态生成 -->
            </div>
        </div>
        
        <!-- 智能评审 -->
        <div id="intelligent-review" class="content-section">
            <div class="section-header">
                <h2>企业申报审核</h2>
                <div>
                    <button class="btn btn-primary" onclick="generateSampleData()">生成样例数据</button>
                    <select id="review-filter" class="form-control" style="width: 150px; display: inline-block; margin-left: 10px;">
                        <option value="all">全部</option>
                        <option value="pending">待审核</option>
                        <option value="approved">已审核</option>
                    </select>
                </div>
            </div>
            
            <div id="enterprise-list">
                <!-- 企业申报数据将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <!-- 模版配置弹窗 -->
    <div id="template-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('template-modal')">&times;</span>
            <h3>新增评价模版</h3>
            <form id="template-form">
                <div class="form-group">
                    <label>模版名称</label>
                    <input type="text" class="form-control" id="template-name" required>
                </div>
                <div class="form-group">
                    <label>版本号</label>
                    <input type="text" class="form-control" id="template-version" value="1.0" required>
                </div>
                <div class="form-group">
                    <label>适用类型</label>
                    <select class="form-control" id="template-type" required>
                        <option value="">请选择</option>
                        <option value="eagle">雏鹰企业</option>
                        <option value="gazelle">瞪羚企业</option>
                        <option value="unicorn">独角兽企业</option>
                        <option value="all">通用模版</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>模版描述</label>
                    <textarea class="form-control" id="template-desc" rows="3"></textarea>
                </div>
                <div style="text-align: right; margin-top: 30px;">
                    <button type="button" class="btn" onclick="closeModal('template-modal')" style="margin-right: 10px;">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 企业详情审核弹窗 -->
    <div id="enterprise-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('enterprise-modal')">&times;</span>
            <div id="enterprise-detail-content">
                <!-- 企业详情内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <script>
        // 全局数据存储
        let templates = [
            {
                id: 1,
                name: '2024年雏鹰企业评价标准',
                version: '1.0',
                createTime: '2024-01-15',
                status: 'active',
                type: 'eagle',
                description: '适用于初创期高成长企业的评价标准'
            },
            {
                id: 2,
                name: '2024年瞪羚企业评价标准',
                version: '1.2',
                createTime: '2024-02-20',
                status: 'active',
                type: 'gazelle',
                description: '适用于快速成长期企业的评价标准'
            },
            {
                id: 3,
                name: '2024年独角兽企业评价标准',
                version: '1.1',
                createTime: '2024-03-10',
                status: 'inactive',
                type: 'unicorn',
                description: '适用于高估值独角兽企业的评价标准'
            }
        ];

        let enterprises = [];

        // 评价指标配置数据
        const evaluationConfigs = {
            eagle: {
                name: '雏鹰企业评价指标',
                basicConditions: [
                    { name: '成立时间', value: '3年以内', required: true },
                    { name: '注册资本', value: '100万元以上', required: true },
                    { name: '员工规模', value: '10-50人', required: true },
                    { name: '年营业收入', value: '500万-2000万元', required: true }
                ],
                indicators: [
                    { category: '成长性指标', items: [
                        { name: '营收增长率', threshold: '≥ 30%', weight: 25 },
                        { name: '利润增长率', threshold: '≥ 20%', weight: 20 },
                        { name: '员工增长率', threshold: '≥ 15%', weight: 15 }
                    ]},
                    { category: '创新能力指标', items: [
                        { name: '研发投入占比', threshold: '≥ 3%', weight: 20 },
                        { name: '专利申请数量', threshold: '≥ 2件', weight: 10 },
                        { name: '技术人员占比', threshold: '≥ 30%', weight: 10 }
                    ]}
                ]
            },
            gazelle: {
                name: '瞪羚企业评价指标',
                basicConditions: [
                    { name: '成立时间', value: '3-10年', required: true },
                    { name: '年营业收入', value: '2000万-10亿元', required: true },
                    { name: '连续3年营收增长率', value: '≥ 20%', required: true }
                ],
                indicators: [
                    { category: '成长性指标', items: [
                        { name: '营收复合增长率', threshold: '≥ 25%', weight: 30 },
                        { name: '市场份额增长', threshold: '持续增长', weight: 15 },
                        { name: '业务扩张能力', threshold: '优秀', weight: 15 }
                    ]},
                    { category: '创新能力指标', items: [
                        { name: '研发投入占比', threshold: '≥ 5%', weight: 25 },
                        { name: '核心技术专利', threshold: '≥ 10件', weight: 10 },
                        { name: '产品创新度评分', threshold: '≥ 80分', weight: 5 }
                    ]}
                ]
            },
            unicorn: {
                name: '独角兽企业评价指标',
                basicConditions: [
                    { name: '企业估值', value: '≥ 10亿美元', required: true },
                    { name: '成立时间', value: '≤ 10年', required: true },
                    { name: '获得私募股权投资', value: '是', required: true }
                ],
                indicators: [
                    { category: '市场地位指标', items: [
                        { name: '行业排名', threshold: '前3名', weight: 30 },
                        { name: '市场占有率', threshold: '≥ 10%', weight: 20 },
                        { name: '品牌影响力评分', threshold: '≥ 90分', weight: 15 }
                    ]},
                    { category: '创新引领指标', items: [
                        { name: '颠覆性技术创新', threshold: '具备', weight: 20 },
                        { name: '商业模式创新', threshold: '具备', weight: 10 },
                        { name: '生态构建能力', threshold: '强', weight: 5 }
                    ]}
                ]
            }
        };

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderTemplateList();
            renderEvaluationConfig();
            generateSampleData();
        });

        // 切换标签页
        function switchTab(tabId) {
            // 隐藏所有内容区域
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // 移除所有标签的激活状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的内容区域
            document.getElementById(tabId).classList.add('active');

            // 激活选中的标签
            event.target.classList.add('active');
        }

        // 渲染模版列表
        function renderTemplateList() {
            const tbody = document.getElementById('template-list');
            tbody.innerHTML = '';

            templates.forEach(template => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${template.name}</td>
                    <td>v${template.version}</td>
                    <td>${template.createTime}</td>
                    <td><span class="status-badge ${template.status === 'active' ? 'status-active' : 'status-inactive'}">${template.status === 'active' ? '启用' : '停用'}</span></td>
                    <td>${getTypeLabel(template.type)}</td>
                    <td>
                        <button class="btn btn-primary" onclick="editTemplate(${template.id})" style="margin-right: 5px;">编辑</button>
                        <button class="btn ${template.status === 'active' ? 'btn-warning' : 'btn-success'}" onclick="toggleTemplateStatus(${template.id})" style="margin-right: 5px;">
                            ${template.status === 'active' ? '停用' : '启用'}
                        </button>
                        <button class="btn btn-danger" onclick="deleteTemplate(${template.id})">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 获取类型标签
        function getTypeLabel(type) {
            const labels = {
                'eagle': '雏鹰企业',
                'gazelle': '瞪羚企业',
                'unicorn': '独角兽企业',
                'all': '通用模版'
            };
            return labels[type] || type;
        }

        // 渲染评价配置
        function renderEvaluationConfig() {
            const configType = document.getElementById('config-type').value;
            const config = evaluationConfigs[configType];
            const content = document.getElementById('config-content');

            let html = `<h3>${config.name}</h3>`;

            // 基础条件
            html += `
                <div class="indicator-group">
                    <h4>基础条件</h4>
            `;

            config.basicConditions.forEach(condition => {
                html += `
                    <div class="indicator-item">
                        <span>${condition.name}</span>
                        <span>${condition.value}</span>
                    </div>
                `;
            });

            html += `</div>`;

            // 评价指标
            config.indicators.forEach(category => {
                html += `
                    <div class="indicator-group">
                        <h4>${category.category}</h4>
                `;

                category.items.forEach((item, index) => {
                    html += `
                        <div class="indicator-item">
                            <div>
                                <strong>${item.name}</strong>
                                <div style="color: #666; font-size: 12px;">${item.threshold}</div>
                            </div>
                            <div style="display: flex; align-items: center;">
                                <span style="margin-right: 10px;">权重:</span>
                                <input type="number" class="form-control weight-input" value="${item.weight}" min="0" max="100">
                                <span style="margin-left: 5px;">%</span>
                            </div>
                        </div>
                    `;
                });

                html += `</div>`;
            });

            content.innerHTML = html;
        }

        // 配置类型改变事件
        document.getElementById('config-type').addEventListener('change', renderEvaluationConfig);

        // 模态框操作
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }

        // 模版管理功能
        function editTemplate(id) {
            const template = templates.find(t => t.id === id);
            if (template) {
                document.getElementById('template-name').value = template.name;
                document.getElementById('template-version').value = template.version;
                document.getElementById('template-type').value = template.type;
                document.getElementById('template-desc').value = template.description;
                openModal('template-modal');
            }
        }

        function toggleTemplateStatus(id) {
            const template = templates.find(t => t.id === id);
            if (template) {
                template.status = template.status === 'active' ? 'inactive' : 'active';
                renderTemplateList();
            }
        }

        function deleteTemplate(id) {
            if (confirm('确定要删除这个模版吗？')) {
                templates = templates.filter(t => t.id !== id);
                renderTemplateList();
            }
        }

        // 保存配置
        function saveConfig() {
            alert('配置已保存！');
        }

        // 模版表单提交
        document.getElementById('template-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const newTemplate = {
                id: templates.length + 1,
                name: document.getElementById('template-name').value,
                version: document.getElementById('template-version').value,
                createTime: new Date().toISOString().split('T')[0],
                status: 'active',
                type: document.getElementById('template-type').value,
                description: document.getElementById('template-desc').value
            };

            templates.push(newTemplate);
            renderTemplateList();
            closeModal('template-modal');

            // 清空表单
            document.getElementById('template-form').reset();
        });

        // 生成样例数据
        function generateSampleData() {
            enterprises = [
                {
                    id: 1,
                    name: '智慧科技有限公司',
                    type: 'eagle',
                    establishTime: '2022-03-15',
                    registeredCapital: '200万元',
                    employees: 35,
                    revenue: '1200万元',
                    revenueGrowth: 45,
                    profitGrowth: 25,
                    employeeGrowth: 20,
                    rdRatio: 8,
                    patents: 5,
                    techRatio: 40,
                    status: 'pending',
                    aiScore: 85,
                    aiResult: '符合雏鹰企业标准，建议通过审核',
                    submitTime: '2024-08-20'
                },
                {
                    id: 2,
                    name: '创新医疗科技股份有限公司',
                    type: 'gazelle',
                    establishTime: '2019-06-10',
                    registeredCapital: '5000万元',
                    employees: 180,
                    revenue: '8500万元',
                    revenueGrowth: 35,
                    marketShare: 12,
                    businessExpansion: '优秀',
                    rdRatio: 12,
                    corePatents: 25,
                    innovationScore: 88,
                    status: 'approved',
                    aiScore: 92,
                    aiResult: '完全符合瞪羚企业标准，各项指标优秀',
                    submitTime: '2024-08-18',
                    reviewTime: '2024-08-22'
                },
                {
                    id: 3,
                    name: '绿色能源科技有限公司',
                    type: 'eagle',
                    establishTime: '2023-01-20',
                    registeredCapital: '150万元',
                    employees: 28,
                    revenue: '800万元',
                    revenueGrowth: 25,
                    profitGrowth: 15,
                    employeeGrowth: 12,
                    rdRatio: 5,
                    patents: 3,
                    techRatio: 35,
                    status: 'pending',
                    aiScore: 72,
                    aiResult: '部分指标未达标，建议人工复审',
                    submitTime: '2024-08-21'
                },
                {
                    id: 4,
                    name: '数字金融服务有限公司',
                    type: 'unicorn',
                    establishTime: '2018-09-05',
                    registeredCapital: '1亿元',
                    employees: 500,
                    valuation: '15亿美元',
                    industryRank: 2,
                    marketShare: 15,
                    brandScore: 95,
                    disruptiveTech: true,
                    businessModel: true,
                    ecosystem: '强',
                    status: 'approved',
                    aiScore: 96,
                    aiResult: '完全符合独角兽企业标准，行业领先',
                    submitTime: '2024-08-15',
                    reviewTime: '2024-08-19'
                },
                {
                    id: 5,
                    name: '智能制造技术有限公司',
                    type: 'gazelle',
                    establishTime: '2020-11-12',
                    registeredCapital: '3000万元',
                    employees: 120,
                    revenue: '4200万元',
                    revenueGrowth: 28,
                    marketShare: 8,
                    businessExpansion: '良好',
                    rdRatio: 6,
                    corePatents: 15,
                    innovationScore: 82,
                    status: 'pending',
                    aiScore: 78,
                    aiResult: '基本符合瞪羚企业标准，建议关注研发投入',
                    submitTime: '2024-08-22'
                },
                {
                    id: 6,
                    name: '新材料研发有限公司',
                    type: 'eagle',
                    establishTime: '2022-07-08',
                    registeredCapital: '300万元',
                    employees: 42,
                    revenue: '1500万元',
                    revenueGrowth: 55,
                    profitGrowth: 35,
                    employeeGrowth: 25,
                    rdRatio: 15,
                    patents: 8,
                    techRatio: 50,
                    status: 'approved',
                    aiScore: 91,
                    aiResult: '优秀的雏鹰企业，各项指标表现突出',
                    submitTime: '2024-08-17',
                    reviewTime: '2024-08-21'
                },
                {
                    id: 7,
                    name: '生物科技创新有限公司',
                    type: 'gazelle',
                    establishTime: '2019-04-25',
                    registeredCapital: '8000万元',
                    employees: 220,
                    revenue: '1.2亿元',
                    revenueGrowth: 42,
                    marketShare: 18,
                    businessExpansion: '优秀',
                    rdRatio: 18,
                    corePatents: 35,
                    innovationScore: 92,
                    status: 'pending',
                    aiScore: 94,
                    aiResult: '完全符合瞪羚企业标准，建议优先通过',
                    submitTime: '2024-08-23'
                },
                {
                    id: 8,
                    name: '人工智能应用有限公司',
                    type: 'eagle',
                    establishTime: '2023-05-10',
                    registeredCapital: '250万元',
                    employees: 38,
                    revenue: '1000万元',
                    revenueGrowth: 38,
                    profitGrowth: 22,
                    employeeGrowth: 18,
                    rdRatio: 12,
                    patents: 6,
                    techRatio: 45,
                    status: 'pending',
                    aiScore: 86,
                    aiResult: '符合雏鹰企业标准，技术实力较强',
                    submitTime: '2024-08-24'
                },
                {
                    id: 9,
                    name: '清洁技术发展有限公司',
                    type: 'gazelle',
                    establishTime: '2020-08-30',
                    registeredCapital: '4500万元',
                    employees: 150,
                    revenue: '6800万元',
                    revenueGrowth: 32,
                    marketShare: 10,
                    businessExpansion: '良好',
                    rdRatio: 8,
                    corePatents: 20,
                    innovationScore: 85,
                    status: 'approved',
                    aiScore: 88,
                    aiResult: '符合瞪羚企业标准，发展前景良好',
                    submitTime: '2024-08-16',
                    reviewTime: '2024-08-20'
                },
                {
                    id: 10,
                    name: '区块链技术有限公司',
                    type: 'eagle',
                    establishTime: '2022-12-05',
                    registeredCapital: '180万元',
                    employees: 32,
                    revenue: '900万元',
                    revenueGrowth: 42,
                    profitGrowth: 28,
                    employeeGrowth: 22,
                    rdRatio: 10,
                    patents: 4,
                    techRatio: 42,
                    status: 'pending',
                    aiScore: 83,
                    aiResult: '符合雏鹰企业标准，区块链技术应用前景广阔',
                    submitTime: '2024-08-25'
                }
            ];

            renderEnterpriseList();
        }

        // 渲染企业列表
        function renderEnterpriseList() {
            const filter = document.getElementById('review-filter').value;
            const filteredEnterprises = filter === 'all' ? enterprises :
                enterprises.filter(e => e.status === filter);

            const container = document.getElementById('enterprise-list');
            container.innerHTML = '';

            filteredEnterprises.forEach(enterprise => {
                const card = document.createElement('div');
                card.className = 'enterprise-card';

                card.innerHTML = `
                    <div class="enterprise-header">
                        <div class="enterprise-name">${enterprise.name}</div>
                        <div class="enterprise-type type-${enterprise.type}">${getTypeLabel(enterprise.type)}</div>
                    </div>

                    <div class="enterprise-info">
                        <div class="info-item">
                            <span class="info-label">成立时间:</span>
                            <span class="info-value">${enterprise.establishTime}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">注册资本:</span>
                            <span class="info-value">${enterprise.registeredCapital}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">员工规模:</span>
                            <span class="info-value">${enterprise.employees}人</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">年营业收入:</span>
                            <span class="info-value">${enterprise.revenue}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">申报时间:</span>
                            <span class="info-value">${enterprise.submitTime}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">审核状态:</span>
                            <span class="status-badge ${enterprise.status === 'pending' ? 'status-pending' : 'status-approved'}">
                                ${enterprise.status === 'pending' ? '待审核' : '已审核'}
                            </span>
                        </div>
                    </div>

                    <div class="ai-result">
                        <div class="ai-result-header">AI智能评审结果</div>
                        <div>综合评分: ${enterprise.aiScore}分</div>
                        <div class="score-bar">
                            <div class="score-fill" style="width: ${enterprise.aiScore}%"></div>
                        </div>
                        <div>${enterprise.aiResult}</div>
                    </div>

                    <div style="text-align: right; margin-top: 15px;">
                        <button class="btn btn-primary" onclick="viewEnterpriseDetail(${enterprise.id})" style="margin-right: 10px;">查看详情</button>
                        ${enterprise.status === 'pending' ?
                            `<button class="btn btn-success" onclick="approveEnterprise(${enterprise.id})" style="margin-right: 10px;">通过审核</button>
                             <button class="btn btn-danger" onclick="rejectEnterprise(${enterprise.id})">拒绝</button>` :
                            `<span style="color: #28a745; font-weight: 500;">已于 ${enterprise.reviewTime} 审核通过</span>`
                        }
                    </div>
                `;

                container.appendChild(card);
            });
        }

        // 筛选改变事件
        document.getElementById('review-filter').addEventListener('change', renderEnterpriseList);

        // 查看企业详情
        function viewEnterpriseDetail(id) {
            const enterprise = enterprises.find(e => e.id === id);
            if (!enterprise) return;

            let detailHtml = `
                <h3>${enterprise.name} - 详细信息</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin: 30px 0;">
                    <div>
                        <h4>基本信息</h4>
                        <div class="info-item" style="margin: 10px 0;">
                            <span class="info-label">企业名称:</span>
                            <span class="info-value">${enterprise.name}</span>
                        </div>
                        <div class="info-item" style="margin: 10px 0;">
                            <span class="info-label">申报类型:</span>
                            <span class="info-value">${getTypeLabel(enterprise.type)}</span>
                        </div>
                        <div class="info-item" style="margin: 10px 0;">
                            <span class="info-label">成立时间:</span>
                            <span class="info-value">${enterprise.establishTime}</span>
                        </div>
                        <div class="info-item" style="margin: 10px 0;">
                            <span class="info-label">注册资本:</span>
                            <span class="info-value">${enterprise.registeredCapital}</span>
                        </div>
                        <div class="info-item" style="margin: 10px 0;">
                            <span class="info-label">员工规模:</span>
                            <span class="info-value">${enterprise.employees}人</span>
                        </div>
                        <div class="info-item" style="margin: 10px 0;">
                            <span class="info-label">年营业收入:</span>
                            <span class="info-value">${enterprise.revenue}</span>
                        </div>
                    </div>

                    <div>
                        <h4>关键指标</h4>
            `;

            if (enterprise.type === 'eagle') {
                detailHtml += `
                    <div class="info-item" style="margin: 10px 0;">
                        <span class="info-label">营收增长率:</span>
                        <span class="info-value">${enterprise.revenueGrowth}%</span>
                    </div>
                    <div class="info-item" style="margin: 10px 0;">
                        <span class="info-label">利润增长率:</span>
                        <span class="info-value">${enterprise.profitGrowth}%</span>
                    </div>
                    <div class="info-item" style="margin: 10px 0;">
                        <span class="info-label">员工增长率:</span>
                        <span class="info-value">${enterprise.employeeGrowth}%</span>
                    </div>
                    <div class="info-item" style="margin: 10px 0;">
                        <span class="info-label">研发投入占比:</span>
                        <span class="info-value">${enterprise.rdRatio}%</span>
                    </div>
                    <div class="info-item" style="margin: 10px 0;">
                        <span class="info-label">专利申请数量:</span>
                        <span class="info-value">${enterprise.patents}件</span>
                    </div>
                    <div class="info-item" style="margin: 10px 0;">
                        <span class="info-label">技术人员占比:</span>
                        <span class="info-value">${enterprise.techRatio}%</span>
                    </div>
                `;
            } else if (enterprise.type === 'gazelle') {
                detailHtml += `
                    <div class="info-item" style="margin: 10px 0;">
                        <span class="info-label">营收增长率:</span>
                        <span class="info-value">${enterprise.revenueGrowth}%</span>
                    </div>
                    <div class="info-item" style="margin: 10px 0;">
                        <span class="info-label">市场份额:</span>
                        <span class="info-value">${enterprise.marketShare}%</span>
                    </div>
                    <div class="info-item" style="margin: 10px 0;">
                        <span class="info-label">业务扩张能力:</span>
                        <span class="info-value">${enterprise.businessExpansion}</span>
                    </div>
                    <div class="info-item" style="margin: 10px 0;">
                        <span class="info-label">研发投入占比:</span>
                        <span class="info-value">${enterprise.rdRatio}%</span>
                    </div>
                    <div class="info-item" style="margin: 10px 0;">
                        <span class="info-label">核心技术专利:</span>
                        <span class="info-value">${enterprise.corePatents}件</span>
                    </div>
                    <div class="info-item" style="margin: 10px 0;">
                        <span class="info-label">产品创新度评分:</span>
                        <span class="info-value">${enterprise.innovationScore}分</span>
                    </div>
                `;
            } else if (enterprise.type === 'unicorn') {
                detailHtml += `
                    <div class="info-item" style="margin: 10px 0;">
                        <span class="info-label">企业估值:</span>
                        <span class="info-value">${enterprise.valuation}</span>
                    </div>
                    <div class="info-item" style="margin: 10px 0;">
                        <span class="info-label">行业排名:</span>
                        <span class="info-value">第${enterprise.industryRank}名</span>
                    </div>
                    <div class="info-item" style="margin: 10px 0;">
                        <span class="info-label">市场占有率:</span>
                        <span class="info-value">${enterprise.marketShare}%</span>
                    </div>
                    <div class="info-item" style="margin: 10px 0;">
                        <span class="info-label">品牌影响力评分:</span>
                        <span class="info-value">${enterprise.brandScore}分</span>
                    </div>
                    <div class="info-item" style="margin: 10px 0;">
                        <span class="info-label">颠覆性技术创新:</span>
                        <span class="info-value">${enterprise.disruptiveTech ? '具备' : '不具备'}</span>
                    </div>
                    <div class="info-item" style="margin: 10px 0;">
                        <span class="info-label">商业模式创新:</span>
                        <span class="info-value">${enterprise.businessModel ? '具备' : '不具备'}</span>
                    </div>
                `;
            }

            detailHtml += `
                    </div>
                </div>

                <div class="ai-result" style="margin: 30px 0;">
                    <div class="ai-result-header">AI智能评审详细分析</div>
                    <div style="margin: 15px 0;">
                        <strong>综合评分: ${enterprise.aiScore}分</strong>
                        <div class="score-bar">
                            <div class="score-fill" style="width: ${enterprise.aiScore}%"></div>
                        </div>
                    </div>
                    <div><strong>评审结论:</strong> ${enterprise.aiResult}</div>
                </div>

                <div style="text-align: right; margin-top: 30px;">
                    ${enterprise.status === 'pending' ?
                        `<button class="btn btn-success" onclick="approveEnterprise(${enterprise.id}); closeModal('enterprise-modal');" style="margin-right: 10px;">通过审核</button>
                         <button class="btn btn-danger" onclick="rejectEnterprise(${enterprise.id}); closeModal('enterprise-modal');" style="margin-right: 10px;">拒绝申请</button>` :
                        `<div style="color: #28a745; font-weight: 500; margin-bottom: 15px;">已于 ${enterprise.reviewTime} 审核通过</div>`
                    }
                    <button class="btn" onclick="closeModal('enterprise-modal')">关闭</button>
                </div>
            `;

            document.getElementById('enterprise-detail-content').innerHTML = detailHtml;
            openModal('enterprise-modal');
        }

        // 审核通过
        function approveEnterprise(id) {
            const enterprise = enterprises.find(e => e.id === id);
            if (enterprise) {
                enterprise.status = 'approved';
                enterprise.reviewTime = new Date().toISOString().split('T')[0];
                renderEnterpriseList();
                alert('审核通过！');
            }
        }

        // 拒绝申请
        function rejectEnterprise(id) {
            if (confirm('确定要拒绝这个申请吗？')) {
                enterprises = enterprises.filter(e => e.id !== id);
                renderEnterpriseList();
                alert('申请已拒绝！');
            }
        }
    </script>
</body>
</html>
