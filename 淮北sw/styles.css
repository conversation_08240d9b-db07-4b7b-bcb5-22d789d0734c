/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Microsoft YaHei", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

h1, h2, h3, h4 {
    font-weight: 700;
    line-height: 1.2;
}

img {
    max-width: 100%;
    height: auto;
}

a {
    text-decoration: none;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-primary {
    background-color: #2e7d32;
    color: white;
    border: none;
}

.btn-primary:hover {
    background-color: #1b5e20;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(46, 125, 50, 0.3);
}

.btn-outline {
    background-color: transparent;
    color: #2e7d32;
    border: 2px solid #2e7d32;
}

.btn-outline:hover {
    background-color: #2e7d32;
    color: white;
}

.btn-large {
    padding: 16px 32px;
    font-size: 1.1rem;
}

/* 头部样式 */
header {
    background: linear-gradient(135deg, #2e7d32, #4caf50);
    color: white;
    padding: 30px 0;
    text-align: center;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

header .tagline {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* 英雄区域 */
.hero {
    padding: 60px 0;
    background-color: #fff;
}

.hero .container {
    display: flex;
    align-items: center;
    gap: 40px;
}

.hero-content {
    flex: 1;
}

.hero-content h2 {
    font-size: 2.2rem;
    margin-bottom: 20px;
    color: #2e7d32;
}

.hero-content p {
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 30px;
}

.hero-buttons {
    display: flex;
    gap: 15px;
}

.hero-image {
    flex: 1;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* 功能区域 */
.features {
    padding: 80px 0;
    background-color: #f1f8e9;
}

.features h2 {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 50px;
    color: #2e7d32;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.feature-card {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    margin-bottom: 20px;
}

.feature-icon img {
    width: 70px;
    height: 70px;
}

.feature-card h3 {
    font-size: 1.4rem;
    margin-bottom: 15px;
    color: #2e7d32;
}

/* 文档格式区域 */
.document-formats {
    padding: 80px 0;
    background-color: #fff;
}

.document-formats h2 {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 50px;
    color: #2e7d32;
}

.format-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.format-group {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.format-group h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: #2e7d32;
    padding-bottom: 10px;
    border-bottom: 2px solid #e0e0e0;
}

.format-list {
    list-style: none;
}

.format-list li {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.format-list li:last-child {
    border-bottom: none;
}

.format-name {
    font-weight: bold;
    color: #2e7d32;
}

/* 搜索系统区域 */
.search-system {
    padding: 80px 0;
    background-color: #f1f8e9;
}

.search-system h2 {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 50px;
    color: #2e7d32;
}

.search-demo {
    display: flex;
    gap: 40px;
    align-items: flex-start;
}

.search-interface {
    flex: 2;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.search-header {
    padding: 20px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
}

.search-bar {
    display: flex;
    margin-bottom: 15px;
}

.search-bar input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px 0 0 6px;
    font-size: 1rem;
}

.search-button {
    background-color: #2e7d32;
    color: white;
    border: none;
    padding: 0 20px;
    border-radius: 0 6px 6px 0;
    cursor: pointer;
    font-weight: 600;
}

.search-filters {
    display: flex;
    gap: 15px;
}

.filter {
    padding: 5px 12px;
    border-radius: 20px;
    background-color: #e8f5e9;
    color: #2e7d32;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter.active {
    background-color: #2e7d32;
    color: white;
}

.search-results {
    padding: 10px;
}

.result-item {
    display: flex;
    padding: 15px;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s ease;
}

.result-item:hover {
    background-color: #f9f9f9;
}

.result-item:last-child {
    border-bottom: none;
}

.result-icon {
    margin-right: 15px;
}

.result-content h4 {
    margin-bottom: 5px;
    color: #2e7d32;
}

.result-content p {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 8px;
}

.result-tags {
    display: flex;
    gap: 8px;
}

.tag {
    padding: 3px 8px;
    border-radius: 4px;
    background-color: #e8f5e9;
    color: #2e7d32;
    font-size: 0.8rem;
}

.search-features {
    flex: 1;
    background-color: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.search-features h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: #2e7d32;
    padding-bottom: 10px;
    border-bottom: 2px solid #e0e0e0;
}

.feature-list {
    list-style: none;
}

.feature-list li {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.feature-list li:last-child {
    border-bottom: none;
}

.feature-highlight {
    font-weight: bold;
    color: #2e7d32;
}

/* 工作流程区域 */
.workflow {
    padding: 80px 0;
    background-color: #fff;
}

.workflow h2 {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 50px;
    color: #2e7d32;
}

.workflow-steps {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    position: relative;
}

.workflow-steps::before {
    content: "";
    position: absolute;
    top: 40px;
    left: 0;
    right: 0;
    height: 4px;
    background-color: #e8f5e9;
    z-index: 1;
}

.step {
    flex: 1;
    min-width: 200px;
    text-align: center;
    padding: 0 15px;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 80px;
    height: 80px;
    background-color: #2e7d32;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    font-weight: bold;
    margin: 0 auto 20px;
    box-shadow: 0 5px 15px rgba(46, 125, 50, 0.3);
}

.step-content h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: #2e7d32;
}

/* 应用场景区域 */
.application-scenarios {
    padding: 80px 0;
    background-color: #f1f8e9;
}

.application-scenarios h2 {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 50px;
    color: #2e7d32;
}

.scenario-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 30px;
}

.scenario-card {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    height: 200px;
}

.scenario-card img {
    width: 40%;
    height: 100%;
    object-fit: cover;
}

.scenario-content {
    padding: 25px;
    width: 60%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.scenario-content h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: #2e7d32;
}

/* CTA区域 */
.cta {
    padding: 80px 0;
    background: linear-gradient(135deg, #2e7d32, #4caf50);
    color: white;
    text-align: center;
}

.cta h2 {
    font-size: 2.2rem;
    margin-bottom: 20px;
}

.cta p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

/* 页脚 */
footer {
    background-color: #1b5e20;
    color: white;
    padding: 30px 0;
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .search-demo {
        flex-direction: column;
    }
    
    .scenario-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .hero .container {
        flex-direction: column;
    }
    
    .workflow-steps::before {
        display: none;
    }
    
    .step {
        margin-bottom: 30px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .hero-content h2 {
        font-size: 1.8rem;
    }
    
    .scenario-card {
        flex-direction: column;
        height: auto;
    }
    
    .scenario-card img {
        width: 100%;
        height: 200px;
    }
    
    .scenario-content {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .hero-buttons {
        flex-direction: column;
    }
    
    .search-filters {
        flex-wrap: wrap;
    }
}